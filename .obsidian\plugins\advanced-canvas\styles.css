/* src/styles.scss */
.properties-field > .setting-item-info {
  flex: 0;
  margin: 0;
  padding: var(--size-4-1) var(--size-4-2);
  border: var(--input-border-width) solid var(--background-modifier-border);
  border-radius: var(--input-radius) 0 0 var(--input-radius);
}
.properties-field > .setting-item-control > input {
  width: 100%;
  border-radius: 0 var(--input-radius) var(--input-radius) 0;
}
.ac-settings-heading {
  border-bottom: 1px solid var(--color-accent);
}
.ac-settings-heading:not(:first-child) {
  margin-top: var(--size-4-10) !important;
}
.ac-settings-heading:has(.checkbox-container:not(.is-enabled)) {
  border-bottom-color: var(--background-modifier-border-hover);
}
.ac-settings-heading .setting-item-description {
  margin-inline-end: 20px;
}
.settings-header-children {
  transform-origin: top center;
  transform: scaleY(1);
  transition: transform 0.2s ease-in-out;
}
.ac-settings-heading:has(.checkbox-container:not(.is-enabled)) + .settings-header-children {
  opacity: 0.5;
  pointer-events: none;
  height: 0;
  transform: scaleY(0);
}
details.setting-item[open] > summary {
  margin-bottom: 0.75em;
}
details.setting-item > *:not(summary) {
  padding-left: 1em;
  border-left: 1px solid var(--color-accent);
}
body.is-mobile .kofi-button.sticky {
  display: none;
}
.kofi-banner {
  position: relative;
  display: flex;
  flex-direction: column;
  padding: var(--size-4-3);
  background-color: var(--background-secondary);
  border: 1px solid var(--divider-color);
  border-radius: var(--radius-s);
}
.kofi-banner h1 {
  margin: 0;
  margin-bottom: 10px;
  font-size: var(--font-ui-large);
  font-weight: 600;
}
.kofi-banner .progress-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-items: center;
}
.kofi-banner .progress-container progress {
  background: transparent;
}
.kofi-banner .progress-container progress::-webkit-progress-bar {
  background-color: var(--background-modifier-border);
  border-radius: var(--input-radius);
}
.kofi-banner .progress-container progress::-webkit-progress-value {
  background-color: var(--color-accent);
  border-radius: var(--input-radius);
}
.kofi-banner .progress-container .hourly-rate {
  margin-left: 10px;
  font-weight: 600;
}
.kofi-banner .ac-kofi-button {
  align-self: flex-end;
  width: min-content;
  height: min-content;
  line-height: 0;
}
.kofi-banner .ac-kofi-button img {
  min-width: 100px;
  width: 25%;
  max-width: 200px;
}
.canvas-wrapper > .document-search-container {
  transform: translateZ(0);
  margin: 0;
}
.canvas-wrapper:not(.mod-readonly) .show-while-readonly {
  display: none;
}
.canvas-control-item[data-toggled=true] {
  background-color: var(--color-accent);
}
.canvas-control-item[data-toggled=true] svg {
  stroke: var(--text-on-accent);
}
.reactive-node,
.canvas-node[data-shape=database],
.canvas-node[data-shape=document],
.canvas-node[data-shape=predefined-process],
.canvas-node[data-shape=diamond] {
  --border-color: rgb(var(--canvas-color));
  --border-width: 3px;
  --box-shadow: none;
}
.reactive-node.is-focused,
.is-focused.canvas-node[data-shape=database],
.is-focused.canvas-node[data-shape=document],
.is-focused.canvas-node[data-shape=predefined-process],
.is-focused.canvas-node[data-shape=diamond],
.reactive-node.is-selected,
.is-selected.canvas-node[data-shape=database],
.is-selected.canvas-node[data-shape=document],
.is-selected.canvas-node[data-shape=predefined-process],
.is-selected.canvas-node[data-shape=diamond] {
  --border-color: var(--color-accent);
  --border-width: 5px;
  --box-shadow: var(--shadow-border-accent);
}
.reactive-node.is-themed,
.is-themed.canvas-node[data-shape=database],
.is-themed.canvas-node[data-shape=document],
.is-themed.canvas-node[data-shape=predefined-process],
.is-themed.canvas-node[data-shape=diamond] {
  --border-color: rgba(var(--canvas-color), 0.7);
}
.reactive-node.is-themed.is-focused,
.is-themed.is-focused.canvas-node[data-shape=database],
.is-themed.is-focused.canvas-node[data-shape=document],
.is-themed.is-focused.canvas-node[data-shape=predefined-process],
.is-themed.is-focused.canvas-node[data-shape=diamond],
.reactive-node.is-themed.is-selected,
.is-themed.is-selected.canvas-node[data-shape=database],
.is-themed.is-selected.canvas-node[data-shape=document],
.is-themed.is-selected.canvas-node[data-shape=predefined-process],
.is-themed.is-selected.canvas-node[data-shape=diamond] {
  --border-color: rgb(var(--canvas-color));
  --box-shadow: var(--shadow-border-themed);
}
.canvas-node[data-text-align=center] .markdown-preview-view {
  padding: 0 !important;
  overflow-y: initial;
}
.canvas-node[data-text-align=center] .markdown-preview-view .markdown-preview-section {
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 0 !important;
  text-align: center;
  vertical-align: middle;
}
.canvas-node[data-text-align=right] {
  text-align: right;
}
.canvas-node[data-shape] .canvas-node-container .markdown-preview-view.markdown-rendered {
  transform: unset;
}
.canvas-node[data-shape=pill] .canvas-node-container {
  border-radius: 5000px;
}
.canvas-node[data-shape=diamond] {
}
.canvas-node[data-shape=diamond].is-focused,
.canvas-node[data-shape=diamond].is-selected {
  border-radius: var(--radius-m);
  outline: 2px solid var(--color-accent);
  outline-offset: 5px;
}
.canvas-node[data-shape=diamond] .canvas-node-container {
  border: none;
  box-shadow: none !important;
}
.canvas-node[data-shape=diamond] .canvas-node-container:not(:has(.embed-iframe)) {
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 141.42135624 141.42135624' preserveAspectRatio='none'%3E%3Cstyle%3E rect %7B transform-origin: center; transform: rotate(45deg) scale(1.05); %7D %3C/style%3E%3Crect rx='8' x='20.71067812' y='20.71067812' width='100' height='100' /%3E%3C/svg%3E");
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 141.42135624 141.42135624' preserveAspectRatio='none'%3E%3Cstyle%3E rect %7B transform-origin: center; transform: rotate(45deg) scale(1.05); %7D %3C/style%3E%3Crect rx='8' x='20.71067812' y='20.71067812' width='100' height='100' /%3E%3C/svg%3E");
  mask-repeat: no-repeat;
  -webkit-mask-repeat: no-repeat;
  mask-size: 100%;
  -webkit-mask-size: 100%;
}
.canvas-node[data-shape=diamond] .canvas-node-container .canvas-node-placeholder::after {
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 141.42135624 141.42135624' preserveAspectRatio='none'%3E%3Cstyle%3E rect %7B transform-origin: center; transform: rotate(45deg) scale(1.05); %7D %3C/style%3E%3Crect rx='8' x='20.71067812' y='20.71067812' width='100' height='100' /%3E%3C/svg%3E");
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 141.42135624 141.42135624' preserveAspectRatio='none'%3E%3Cstyle%3E rect %7B transform-origin: center; transform: rotate(45deg) scale(1.05); %7D %3C/style%3E%3Crect rx='8' x='20.71067812' y='20.71067812' width='100' height='100' /%3E%3C/svg%3E");
  mask-repeat: no-repeat;
  -webkit-mask-repeat: no-repeat;
  mask-size: 100%;
  -webkit-mask-size: 100%;
}
.canvas-node[data-shape=diamond]::before {
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 141.42135624 141.42135624' preserveAspectRatio='none'%3E%3Cstyle%3E rect %7B transform-origin: center; transform: rotate(45deg) scale(1.05); %7D %3C/style%3E%3Crect rx='8' x='20.71067812' y='20.71067812' width='100' height='100' /%3E%3C/svg%3E");
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 141.42135624 141.42135624' preserveAspectRatio='none'%3E%3Cstyle%3E rect %7B transform-origin: center; transform: rotate(45deg) scale(1.05); %7D %3C/style%3E%3Crect rx='8' x='20.71067812' y='20.71067812' width='100' height='100' /%3E%3C/svg%3E");
  mask-repeat: no-repeat;
  -webkit-mask-repeat: no-repeat;
  mask-size: 100%;
  -webkit-mask-size: 100%;
  content: "";
  position: absolute;
  top: calc(var(--border-width) * -1);
  left: calc(var(--border-width) * -1);
  width: calc(100% + var(--border-width) * 2);
  height: calc(100% + var(--border-width) * 2);
  background-color: var(--border-color);
}
.canvas-node[data-shape=parallelogram] .canvas-node-container {
  transform: skewX(-20deg);
  backface-visibility: hidden;
}
.canvas-node[data-shape=parallelogram] .canvas-node-container .canvas-node-content .markdown-embed-content {
  transform: skewX(20deg);
}
.canvas-node[data-shape=circle] .canvas-node-container {
  border-radius: 50%;
}
.canvas-node[data-shape=circle] .canvas-node-container .markdown-preview-view {
  padding: 0 !important;
  overflow-y: initial;
}
.canvas-node[data-shape=predefined-process] .canvas-node-container .canvas-node-content {
  padding: 0 10px;
}
.canvas-node[data-shape=predefined-process] .canvas-node-container::before,
.canvas-node[data-shape=predefined-process] .canvas-node-container::after {
  content: "";
  z-index: 1;
  position: absolute;
  top: 0;
  width: 0;
  height: 100%;
  border-left: var(--border-width) solid var(--border-color);
}
.canvas-node[data-shape=predefined-process] .canvas-node-container::before {
  left: calc(10px - var(--border-width));
}
.canvas-node[data-shape=predefined-process] .canvas-node-container::after {
  right: calc(10px - var(--border-width));
}
.canvas-node[data-shape=document] {
  --border-width: 2.5px;
  filter: drop-shadow(0 var(--border-width) 0 var(--border-color)) drop-shadow(0 calc(var(--border-width) * -1) 0 var(--border-color));
}
.canvas-node[data-shape=document] .canvas-node-container {
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 75 45' preserveAspectRatio='none'%3E%3Cpath d='M75 0 75 39.375Q56.25 29.25 37.5 39.375 18.75 49.5 0 39.375L0 0Z' /%3E%3C/svg%3E");
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 75 45' preserveAspectRatio='none'%3E%3Cpath d='M75 0 75 39.375Q56.25 29.25 37.5 39.375 18.75 49.5 0 39.375L0 0Z' /%3E%3C/svg%3E");
  mask-repeat: no-repeat;
  -webkit-mask-repeat: no-repeat;
  mask-size: 100%;
  -webkit-mask-size: 100%;
  border: var(--border-width) solid var(--border-color);
  border-top: none;
  border-bottom: none;
}
.canvas-node[data-shape=document].is-focused,
.canvas-node[data-shape=document].is-selected {
  --border-width: 4px;
}
.canvas-node[data-shape=database] {
}
.canvas-node[data-shape=database] .canvas-node-container {
  border: var(--border-width) solid var(--border-color);
  border-bottom: 0;
  border-top: 0;
  border-radius: 0;
  box-shadow: none !important;
}
.canvas-node[data-shape=database] .canvas-node-container .canvas-node-placeholder {
  transform: translateY(25px);
}
.canvas-node[data-shape=database]::before,
.canvas-node[data-shape=database]::after {
  content: "";
  position: absolute;
  left: 0;
  box-sizing: border-box;
  width: 100%;
  height: 50px;
  border-radius: 50%;
  border: var(--border-width) solid var(--border-color);
  background-color: var(--background-primary);
}
.canvas-node[data-shape=database]::after {
  top: -25px;
}
.canvas-node[data-shape=database]::before {
  bottom: -25px;
}
.canvas-node[data-shape=database].is-themed .canvas-node-content {
  background-color: transparent;
}
.canvas-node[data-shape=database].is-themed:not(:has(.embed-iframe)) .canvas-node-container,
.canvas-node[data-shape=database].is-themed:not(:has(.embed-iframe))::after,
.canvas-node[data-shape=database].is-themed:not(:has(.embed-iframe))::before {
  box-shadow: inset 0 0 0 1000px rgba(var(--canvas-color), 0.07) !important;
}
.canvas-node[data-shape=database] .canvas-node-content:not(:has(.embed-iframe)) {
  transform: translateY(20px);
}
.canvas-node[data-shape=database]:has(.embed-iframe)::after {
  z-index: -1;
}
.canvas-node[data-border=dashed] .canvas-node-container {
  box-shadow: none;
  border-style: dashed;
}
.canvas-node[data-border=dotted] .canvas-node-container {
  box-shadow: none;
  border-style: dotted;
}
.canvas-node[data-border=invisible] {
  box-shadow: none;
}
.canvas-node[data-border=invisible]:not(.is-focused):not(.is-selected) .canvas-node-container {
  border-color: transparent !important;
}
.canvas-node[data-border=invisible] .canvas-node-label {
  display: none;
}
.canvas-node[data-border=invisible] .canvas-node-container {
  background-color: transparent;
  box-shadow: none;
}
.canvas-node[data-border][data-shape=predefined-process] {
  --border-width: 2px;
}
.canvas-node[data-border][data-shape=predefined-process] .is-focused,
.canvas-node[data-border][data-shape=predefined-process] .is-selected {
  --border-width: 2px;
}
.canvas-node[data-border=dashed][data-shape=predefined-process] .canvas-node-container::before,
.canvas-node[data-border=dashed][data-shape=predefined-process] .canvas-node-container::after {
  border-left: var(--border-width) dashed var(--border-color);
}
.canvas-node[data-border=dotted][data-shape=predefined-process] .canvas-node-container::before,
.canvas-node[data-border=dotted][data-shape=predefined-process] .canvas-node-container::after {
  border-left: var(--border-width) dotted var(--border-color);
}
.canvas-node[data-border][data-shape=document] .canvas-node-container {
  border-top: none;
  border-bottom: none;
}
.canvas-edges path[data-path=dotted] {
  stroke-dasharray: calc(3px * var(--zoom-multiplier));
}
.canvas-edges path[data-path=short-dashed] {
  stroke-dasharray: 9px;
}
.canvas-edges path[data-path=long-dashed] {
  stroke-dasharray: 18px;
}
.canvas-edges [data-arrow=triangle-outline] polygon,
.canvas-edges [data-arrow=diamond-outline] polygon,
.canvas-edges [data-arrow=circle-outline] polygon {
  fill: var(--canvas-background);
  stroke: rgb(var(--canvas-color));
  stroke-width: calc(3px * var(--zoom-multiplier));
}
.canvas-edges [data-arrow=thin-triangle] polygon {
  fill: transparent;
  stroke: rgb(var(--canvas-color));
  stroke-width: calc(4px * var(--zoom-multiplier));
}
.canvas.is-exporting {
  --zoom-multiplier: 1;
}
.canvas.is-exporting * {
  pointer-events: none !important;
  transition: none !important;
}
.canvas.is-exporting .collapse-button {
  display: none;
}
.canvas.is-exporting #watermark-ac {
  z-index: 9999999;
  position: absolute;
}
.canvas-wrapper[data-collapsible-groups-feature-enabled=true] .canvas.is-exporting .canvas-node .canvas-group-label {
  left: 0;
}
.progress-bar-modal-ac {
  margin-top: 0.75em;
}
.progress-bar-modal-ac.error .setting-progress-bar {
  color: var(--color-error);
}
.canvas-wrapper[data-disable-font-size-relative-to-zoom=true] {
  --zoom-multiplier: 1 !important;
}
.canvas-wrapper.mod-readonly[data-hide-background-grid-when-in-readonly=true] .canvas-background {
  visibility: hidden;
}
.collapse-button {
  position: absolute;
  left: 0;
  top: calc(-1 * var(--size-4-1) * var(--zoom-multiplier));
  padding: var(--size-4-1) var(--size-4-2);
  transform-origin: bottom left;
  transform: translate(0, -100%) scale(var(--zoom-multiplier));
  border-radius: var(--radius-s);
  color: var(--text-muted);
  background-color: rgba(var(--canvas-color), 0.1);
  font-size: 1.5em;
  line-height: 1;
  pointer-events: initial;
  cursor: pointer;
  transition: transform 500ms cubic-bezier(0.16, 1, 0.3, 1);
}
.canvas-wrapper[data-collapsible-groups-feature-enabled=true] .canvas-node .canvas-group-label {
  left: calc(40px * var(--zoom-multiplier));
}
.canvas-node[data-collapsed] .canvas-node-container {
  display: none;
}
.canvas-node[data-collapsed] .canvas-group-label {
  max-width: initial;
}
.canvas-wrapper[data-collapsed-group-preview-on-drag=true][data-is-dragging] .canvas-node[data-collapsed] .canvas-node-container {
  display: block;
  opacity: 0.5;
  border-style: dashed;
}
.canvas-wrapper[data-collapsed-group-preview-on-drag=true][data-is-dragging] .canvas-node[data-collapsed] .canvas-node-container .canvas-node-content {
  background-color: transparent;
}
.canvas-node-interaction-layer[data-target-collapsed] .canvas-node-resizer {
  pointer-events: none;
  cursor: inherit;
}
.canvas-node-interaction-layer[data-target-collapsed] .canvas-node-resizer .canvas-node-connection-point {
  display: none;
  pointer-events: none;
}
.canvas-wrapper[data-allow-floating-edge-creation=true] .canvas.is-connecting .canvas-node:not(.canvas-node-group)::after {
  all: unset;
  content: "";
  z-index: 100;
  position: absolute;
  top: 50%;
  left: 50%;
  width: max(10px, 100% - 50px * var(--zoom-multiplier) * 2);
  height: max(10px, 100% - 50px * var(--zoom-multiplier) * 2);
  transform: translate(-50%, -50%);
  border-radius: var(--radius-m);
  outline: calc(4px * var(--zoom-multiplier)) dashed hsla(var(--color-accent-hsl), 0.5);
}
.canvas-wrapper[data-allow-floating-edge-creation=true] .canvas.is-connecting .canvas-node:not(.canvas-node-group).hovering-floating-edge-zone::after {
  outline-color: var(--color-accent);
  outline-style: solid;
  background-color: hsla(var(--color-accent-hsl), 0.1);
}
.canvas-wrapper[data-focus-mode-enabled=true] .canvas:has(.canvas-node.is-focused) .canvas-node:not(.is-focused) {
  filter: blur(5px);
}
.canvas-wrapper[data-focus-mode-enabled=true] .canvas:has(.canvas-node.is-focused) .canvas-edges {
  filter: blur(5px);
}
.canvas-wrapper[data-focus-mode-enabled=true] .canvas:has(.canvas-node.is-focused) .canvas-path-label-wrapper {
  filter: blur(5px);
}
.canvas-wrapper.presentation-mode .canvas-controls {
  visibility: hidden;
}
.canvas-wrapper.presentation-mode .canvas-card-menu {
  visibility: hidden;
}
.canvas-wrapper:not(.presentation-mode) .canvas-node[data-is-start-node=true]::before {
  content: "Start";
  position: absolute;
  top: calc(-1 * var(--size-4-1) * var(--zoom-multiplier));
  right: 0;
  transform: translate(0, -100%) scale(var(--zoom-multiplier));
  transform-origin: bottom right;
  max-width: calc(100% / var(--zoom-multiplier));
  padding: var(--size-4-1) var(--size-4-2);
  font-size: 1em;
  border-radius: var(--radius-s);
  color: var(--color-green);
  background-color: rgba(var(--color-green-rgb), 0.1);
}
.canvas-node[data-is-portal-loaded=true] {
  pointer-events: all;
}
.canvas-node[data-is-portal-loaded=true]:not(.is-focused) {
  pointer-events: none;
}
.canvas-node[data-is-portal-loaded=true]:not(.is-focused) .canvas-node-label {
  pointer-events: all;
}
.canvas-node[data-is-portal-loaded=true] .canvas-node-container {
  background-color: transparent;
  border-style: dashed;
}
.canvas-node[data-is-portal-loaded=true] .canvas-node-container .canvas-node-content {
  display: none;
}
.canvas-node-interaction-layer[data-is-from-portal=true] .canvas-node-resizer {
  pointer-events: none;
  cursor: inherit;
}
.canvas-node-interaction-layer[data-is-from-portal=true] .canvas-node-resizer .canvas-node-connection-point {
  pointer-events: all;
}
