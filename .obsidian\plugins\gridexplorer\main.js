/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// main.ts
var main_exports = {};
__export(main_exports, {
  default: () => GridExplorerPlugin
});
module.exports = __toCommonJS(main_exports);
var import_obsidian15 = require("obsidian");

// src/GridView.ts
var import_obsidian12 = require("obsidian");

// src/FolderSelectionModal.ts
var import_obsidian = require("obsidian");

// src/translations.ts
function t(key) {
  const lang = window.localStorage.getItem("language");
  const translations = TRANSLATIONS[lang] || TRANSLATIONS["en"];
  return translations[key] || key;
}
var TRANSLATIONS = {
  "zh-TW": {
    // 通知訊息
    "bookmarks_plugin_disabled": "\u8ACB\u5148\u555F\u7528\u66F8\u7C64\u5916\u639B",
    // 按鈕和標籤
    "sorting": "\u6392\u5E8F\u65B9\u5F0F",
    "refresh": "\u91CD\u65B0\u6574\u7406",
    "reselect": "\u91CD\u65B0\u9078\u64C7\u4F4D\u7F6E",
    "go_up": "\u8FD4\u56DE\u4E0A\u5C64\u8CC7\u6599\u593E",
    "no_backlinks": "\u6C92\u6709\u53CD\u5411\u9023\u7D50",
    "search": "\u641C\u5C0B",
    "search_placeholder": "\u641C\u5C0B\u95DC\u9375\u5B57",
    "search_current_location_only": "\u50C5\u641C\u5C0B\u76EE\u524D\u4F4D\u7F6E",
    "search_media_files": "\u641C\u5C0B\u5A92\u9AD4\u6A94\u6848",
    "cancel": "\u53D6\u6D88",
    "new_note": "\u65B0\u589E\u7B46\u8A18",
    "new_folder": "\u65B0\u589E\u8CC7\u6599\u593E",
    "new_canvas": "\u65B0\u589E\u756B\u5E03",
    "delete_folder": "\u522A\u9664\u8CC7\u6599\u593E",
    "untitled": "\u672A\u547D\u540D",
    "files": "\u500B\u6A94\u6848",
    "add": "\u65B0\u589E",
    "root": "\u6839\u76EE\u9304",
    "more_options": "\u66F4\u591A\u9078\u9805",
    "add_tag_to_search": "\u52A0\u5165\u641C\u5C0B",
    "remove_tag_from_search": "\u5F9E\u641C\u5C0B\u4E2D\u79FB\u9664",
    "global_search": "\u5168\u57DF\u641C\u5C0B",
    "remove": "\u79FB\u9664",
    "edit": "\u7DE8\u8F2F",
    "delete": "\u522A\u9664",
    "save": "\u5132\u5B58",
    // 視圖標題
    "grid_view_title": "\u7DB2\u683C\u8996\u5716",
    "bookmarks_mode": "\u66F8\u7C64",
    "folder_mode": "\u8CC7\u6599\u593E",
    "search_results": "\u641C\u5C0B\u7D50\u679C",
    "backlinks_mode": "\u53CD\u5411\u9023\u7D50",
    "outgoinglinks_mode": "\u5916\u90E8\u9023\u7D50",
    "all_files_mode": "\u6240\u6709\u6A94\u6848",
    "recent_files_mode": "\u6700\u8FD1\u6A94\u6848",
    "random_note_mode": "\u96A8\u6A5F\u7B46\u8A18",
    "tasks_mode": "\u4EFB\u52D9",
    // 排序選項
    "sort_name_asc": "\u540D\u7A31 (A \u2192 Z)",
    "sort_name_desc": "\u540D\u7A31 (Z \u2192 A)",
    "sort_mtime_desc": "\u4FEE\u6539\u6642\u9593 (\u65B0 \u2192 \u820A)",
    "sort_mtime_asc": "\u4FEE\u6539\u6642\u9593 (\u820A \u2192 \u65B0)",
    "sort_ctime_desc": "\u5EFA\u7ACB\u6642\u9593 (\u65B0 \u2192 \u820A)",
    "sort_ctime_asc": "\u5EFA\u7ACB\u6642\u9593 (\u820A \u2192 \u65B0)",
    "sort_random": "\u96A8\u6A5F\u6392\u5E8F",
    // 設定
    "grid_view_settings": "\u7DB2\u683C\u8996\u5716\u8A2D\u5B9A",
    "media_files_settings": "\u5A92\u9AD4\u6A94\u6848\u8A2D\u5B9A",
    "show_media_files": "\u986F\u793A\u5A92\u9AD4\u6A94\u6848",
    "show_media_files_desc": "\u5728\u7DB2\u683C\u8996\u5716\u4E2D\u986F\u793A\u5A92\u9AD4\u6A94\u6848",
    "show_video_thumbnails": "\u986F\u793A\u5F71\u7247\u7E2E\u5716",
    "show_video_thumbnails_desc": "\u5728\u7DB2\u683C\u8996\u5716\u4E2D\u986F\u793A\u5F71\u7247\u7684\u7E2E\u5716\uFF0C\u95DC\u9589\u6642\u5C07\u986F\u793A\u64AD\u653E\u5716\u793A",
    "show_note_tags": "\u986F\u793A\u7B46\u8A18\u6A19\u7C64",
    "show_note_tags_desc": "\u5728\u7DB2\u683C\u8996\u5716\u4E2D\u986F\u793A\u7B46\u8A18\u7684\u6A19\u7C64",
    "ignored_folders": "\u5FFD\u7565\u7684\u8CC7\u6599\u593E",
    "ignored_folders_desc": "\u5728\u9019\u88E1\u8A2D\u5B9A\u8981\u5FFD\u7565\u7684\u8CC7\u6599\u593E",
    "add_ignored_folder": "\u65B0\u589E\u5FFD\u7565\u8CC7\u6599\u593E",
    "no_ignored_folders": "\u6C92\u6709\u5FFD\u7565\u7684\u8CC7\u6599\u593E\u3002",
    "ignored_folder_patterns": "\u4EE5\u5B57\u4E32\u5FFD\u7565\u8CC7\u6599\u593E\u548C\u6A94\u6848",
    "ignored_folder_patterns_desc": "\u4F7F\u7528\u5B57\u4E32\u6A21\u5F0F\u5FFD\u7565\u8CC7\u6599\u593E\u548C\u6A94\u6848\uFF08\u652F\u63F4\u6B63\u5247\u8868\u9054\u5F0F\uFF09",
    "add_ignored_folder_pattern": "\u65B0\u589E\u5FFD\u7565\u8CC7\u6599\u593E\u6A21\u5F0F",
    "ignored_folder_pattern_placeholder": "\u8F38\u5165\u8CC7\u6599\u593E\u540D\u7A31\u6216\u6B63\u5247\u8868\u9054\u5F0F",
    "no_ignored_folder_patterns": "\u6C92\u6709\u5FFD\u7565\u7684\u8CC7\u6599\u593E\u6A21\u5F0F\u3002",
    "default_sort_type": "\u9810\u8A2D\u6392\u5E8F\u6A21\u5F0F",
    "default_sort_type_desc": "\u8A2D\u5B9A\u958B\u555F\u7DB2\u683C\u8996\u5716\u6642\u7684\u9810\u8A2D\u6392\u5E8F\u6A21\u5F0F",
    "note_title_field": "\u7B46\u8A18\u6A19\u984C\u6B04\u4F4D\u540D\u7A31",
    "note_title_field_desc": "\u6307\u5B9A frontmatter \u4E2D\u7528\u65BC\u7B46\u8A18\u6A19\u984C\u7684\u6B04\u4F4D\u540D\u7A31",
    "note_summary_field": "\u7B46\u8A18\u6458\u8981\u6B04\u4F4D\u540D\u7A31",
    "note_summary_field_desc": "\u6307\u5B9A frontmatter \u4E2D\u7528\u65BC\u7B46\u8A18\u6458\u8981\u7684\u6B04\u4F4D\u540D\u7A31",
    "modified_date_field": '"\u4FEE\u6539\u6642\u9593"\u6B04\u4F4D\u540D\u7A31',
    "modified_date_field_desc": "\u6307\u5B9A frontmatter \u4E2D\u7528\u65BC\u7B46\u8A18\u4FEE\u6539\u6642\u9593\u7684\u6B04\u4F4D\u540D\u7A31",
    "created_date_field": '"\u5EFA\u7ACB\u6642\u9593"\u6B04\u4F4D\u540D\u7A31',
    "created_date_field_desc": "\u6307\u5B9A frontmatter \u4E2D\u7528\u65BC\u7B46\u8A18\u5EFA\u7ACB\u6642\u9593\u7684\u6B04\u4F4D\u540D\u7A31",
    "grid_item_width": "\u7DB2\u683C\u9805\u76EE\u5BEC\u5EA6",
    "grid_item_width_desc": "\u8A2D\u5B9A\u7DB2\u683C\u9805\u76EE\u7684\u5BEC\u5EA6",
    "grid_item_height": "\u7DB2\u683C\u9805\u76EE\u9AD8\u5EA6",
    "grid_item_height_desc": "\u8A2D\u5B9A\u7DB2\u683C\u9805\u76EE\u7684\u9AD8\u5EA6 (\u8A2D\u70BA0\u6642\u70BA\u81EA\u52D5\u8ABF\u6574)",
    "image_area_width": "\u5716\u7247\u5340\u57DF\u5BEC\u5EA6",
    "image_area_width_desc": "\u8A2D\u5B9A\u5716\u7247\u9810\u89BD\u5340\u57DF\u7684\u5BEC\u5EA6",
    "image_area_height": "\u5716\u7247\u5340\u57DF\u9AD8\u5EA6",
    "image_area_height_desc": "\u8A2D\u5B9A\u5716\u7247\u9810\u89BD\u5340\u57DF\u7684\u9AD8\u5EA6",
    "title_font_size": "\u6A19\u984C\u5B57\u9AD4\u5927\u5C0F",
    "title_font_size_desc": "\u8A2D\u5B9A\u6A19\u984C\u5B57\u9AD4\u7684\u5927\u5C0F",
    "summary_length": "\u6458\u8981\u9577\u5EA6",
    "summary_length_desc": "\u8A2D\u5B9A\u6458\u8981\u7684\u9577\u5EA6",
    "show_code_block_in_summary": "\u6458\u8981\u4E2D\u986F\u793ACodeBlock",
    "show_code_block_in_summary_desc": "\u8A2D\u5B9A\u662F\u5426\u5728\u6458\u8981\u4E2D\u986F\u793ACodeBlock\u7684\u5167\u5BB9",
    "enable_file_watcher": "\u555F\u7528\u6A94\u6848\u76E3\u63A7",
    "enable_file_watcher_desc": "\u555F\u7528\u5F8C\u6703\u81EA\u52D5\u5075\u6E2C\u6A94\u6848\u8B8A\u66F4\u4E26\u66F4\u65B0\u8996\u5716\uFF0C\u95DC\u9589\u5F8C\u9700\u624B\u52D5\u9EDE\u64CA\u91CD\u65B0\u6574\u7406\u6309\u9215",
    "intercept_all_tag_clicks": "\u6514\u622A\u6240\u6709\u6A19\u7C64\u9EDE\u64CA\u4E8B\u4EF6",
    "intercept_all_tag_clicks_desc": "\u555F\u7528\u5F8C\u6703\u6514\u622A\u6240\u6709\u6A19\u7C64\u9EDE\u64CA\u4E8B\u4EF6\uFF0C\u4E26\u5728\u7DB2\u683C\u8996\u5716\u4E2D\u6253\u958B\u6A19\u7C64",
    "reset_to_default": "\u91CD\u7F6E\u70BA\u9810\u8A2D\u503C",
    "reset_to_default_desc": "\u5C07\u6240\u6709\u8A2D\u5B9A\u91CD\u7F6E\u70BA\u9810\u8A2D\u503C",
    "settings_reset_notice": "\u8A2D\u5B9A\u503C\u5DF2\u91CD\u7F6E\u70BA\u9810\u8A2D\u503C",
    "ignored_folders_settings": "\u5FFD\u7565\u8CC7\u6599\u593E\u8A2D\u5B9A",
    "display_mode_settings": "\u986F\u793A\u6A21\u5F0F\u8A2D\u5B9A",
    "custom_mode_settings": "\u81EA\u8A02\u6A21\u5F0F\u8A2D\u5B9A",
    "add_custom_mode": "\u65B0\u589E\u81EA\u8A02\u6A21\u5F0F",
    "export": "\u532F\u51FA",
    "import": "\u532F\u5165",
    "no_custom_modes_to_export": "\u6C92\u6709\u53EF\u532F\u51FA\u7684\u81EA\u8A02\u6A21\u5F0F",
    "import_success": "\u81EA\u8A02\u6A21\u5F0F\u532F\u5165\u6210\u529F",
    "import_error": "\u532F\u5165\u5931\u6557\uFF1A\u7121\u6548\u7684\u6A94\u6848\u683C\u5F0F",
    "edit_custom_mode": "\u7DE8\u8F2F\u81EA\u8A02\u6A21\u5F0F",
    "custom_mode_icon": "\u5716\u793A",
    "custom_mode_icon_desc": "\u5728\u6A21\u5F0F\u9078\u55AE\u4E2D\u986F\u793A\u7684\u5716\u793A",
    "custom_mode_display_name": "\u986F\u793A\u540D\u7A31",
    "custom_mode_display_name_desc": "\u5728\u6A21\u5F0F\u9078\u55AE\u4E2D\u986F\u793A\u7684\u540D\u7A31",
    "custom_mode_dataview_code": "Dataviewjs \u4EE3\u78BC",
    "custom_mode_dataview_code_desc": "\u8F38\u5165 Dataviewjs \u4EE3\u78BC\u4EE5\u53D6\u5F97\u6A94\u6848\u5217\u8868",
    "display_name_cannot_be_empty": "\u986F\u793A\u540D\u7A31\u4E0D\u80FD\u70BA\u7A7A",
    "custom_mode": "\u81EA\u8A02\u6A21\u5F0F",
    "show_bookmarks_mode": "\u986F\u793A\u66F8\u7C64\u6A21\u5F0F",
    "show_search_mode": "\u986F\u793A\u641C\u5C0B\u7D50\u679C\u6A21\u5F0F",
    "show_backlinks_mode": "\u986F\u793A\u53CD\u5411\u9023\u7D50\u6A21\u5F0F",
    "show_outgoinglinks_mode": "\u986F\u793A\u5916\u90E8\u9023\u7D50\u6A21\u5F0F",
    "show_all_files_mode": "\u986F\u793A\u6240\u6709\u6A94\u6848\u6A21\u5F0F",
    "show_recent_files_mode": "\u986F\u793A\u6700\u8FD1\u6A94\u6848\u6A21\u5F0F",
    "recent_files_count": "\u6700\u8FD1\u6A94\u6848\u6A21\u5F0F\u986F\u793A\u7B46\u6578",
    "show_random_note_mode": "\u986F\u793A\u96A8\u6A5F\u7B46\u8A18\u6A21\u5F0F",
    "random_note_count": "\u96A8\u6A5F\u7B46\u8A18\u6A21\u5F0F\u986F\u793A\u7B46\u6578",
    "random_note_notes_only": "\u50C5\u7B46\u8A18",
    "random_note_include_media_files": "\u5305\u542B\u5A92\u9AD4\u6A94\u6848",
    "show_tasks_mode": "\u986F\u793A\u4EFB\u52D9\u6A21\u5F0F",
    "task_filter": "\u4EFB\u52D9\u5206\u985E",
    "uncompleted": "\u672A\u5B8C\u6210",
    "completed": "\u5DF2\u5B8C\u6210",
    "foldernote_display_settings": "\u8CC7\u6599\u593E\u7B46\u8A18\u986F\u793A\u8A2D\u5B9A",
    "foldernote_display_settings_desc": "\u8A2D\u5B9A\u8CC7\u6599\u593E\u7B46\u8A18\u7684\u986F\u793A\u65B9\u5F0F",
    "all": "\u5168\u90E8",
    "default": "\u9810\u8A2D",
    "hidden": "\u96B1\u85CF",
    // 顯示"返回上層資料夾"選項設定
    "show_parent_folder_item": "\u986F\u793A\u300C\u8FD4\u56DE\u4E0A\u5C64\u8CC7\u6599\u593E\u300D",
    "show_parent_folder_item_desc": "\u5728\u7DB2\u683C\u7684\u7B2C\u4E00\u9805\u986F\u793A\u300C\u8FD4\u56DE\u4E0A\u5C64\u8CC7\u6599\u593E\u300D\u9078\u9805",
    "parent_folder": "\u4E0A\u5C64\u8CC7\u6599\u593E",
    // 預設開啟位置設定
    "default_open_location": "\u9810\u8A2D\u958B\u555F\u4F4D\u7F6E",
    "default_open_location_desc": "\u8A2D\u5B9A\u7DB2\u683C\u8996\u5716\u9810\u8A2D\u958B\u555F\u7684\u4F4D\u7F6E",
    "open_in_left_sidebar": "\u958B\u5728\u5DE6\u5074\u908A\u6B04",
    "open_in_right_sidebar": "\u958B\u5728\u53F3\u5074\u908A\u6B04",
    "open_in_new_tab": "\u5728\u65B0\u5206\u9801\u958B\u555F",
    "reuse_existing_leaf": "\u91CD\u8907\u4F7F\u7528\u5DF2\u958B\u555F\u7684\u8996\u5716",
    "reuse_existing_leaf_desc": "\u958B\u555F\u7DB2\u683C\u8996\u5716\u6642\uFF0C\u512A\u5148\u4F7F\u7528\u5DF2\u958B\u555F\u7684\u8996\u5716\u800C\u975E\u5EFA\u7ACB\u65B0\u8996\u5716",
    "custom_document_extensions": "\u81EA\u8A02\u6587\u4EF6\u6A94\u6848\u526F\u6A94\u540D",
    "custom_document_extensions_desc": "\u984D\u5916\u7684\u6587\u4EF6\u526F\u6A94\u540D\uFF08\u7528\u9017\u865F\u5206\u9694\uFF0C\u4E0D\u542B\u9EDE\u865F\uFF09",
    "custom_document_extensions_placeholder": "\u4F8B\u5982\uFF1Atxt,doc,docx",
    "custom_folder_icon": "\u81EA\u8A02\u8CC7\u6599\u593E\u5716\u793A",
    "custom_folder_icon_desc": "\u81EA\u8A02\u8CC7\u6599\u593E\u5716\u793A\uFF08\u4F7F\u7528Emoji\uFF09",
    // 選擇資料夾對話框
    "select_folders": "\u9078\u64C7\u8CC7\u6599\u593E",
    "select_folders_to_ignore": "\u9078\u64C7\u8981\u5FFD\u7565\u7684\u8CC7\u6599\u593E",
    "open_grid_view": "\u958B\u555F\u7DB2\u683C\u8996\u5716",
    "open_in_grid_view": "\u5728\u7DB2\u683C\u8996\u5716\u4E2D\u958B\u555F",
    "open_note_in_grid_view": "\u5728\u7DB2\u683C\u8996\u5716\u4E2D\u958B\u555F\u7576\u524D\u7B46\u8A18",
    "open_backlinks_in_grid_view": "\u5728\u7DB2\u683C\u8996\u5716\u4E2D\u958B\u555F\u53CD\u5411\u9023\u7D50",
    "open_outgoinglinks_in_grid_view": "\u5728\u7DB2\u683C\u8996\u5716\u4E2D\u958B\u555F\u5916\u90E8\u9023\u7D50",
    "open_recent_files_in_grid_view": "\u5728\u6700\u8FD1\u6A94\u6848\u4E2D\u958B\u555F\u7576\u524D\u7B46\u8A18",
    "open_settings": "\u958B\u555F\u8A2D\u5B9A",
    "open_new_grid_view": "\u958B\u555F\u65B0\u7DB2\u683C\u8996\u5716",
    "open_in_new_grid_view": "\u5728\u65B0\u7DB2\u683C\u8996\u5716\u4E2D\u958B\u555F",
    "min_mode": "\u6700\u5C0F\u5316\u6A21\u5F0F",
    "show_ignored_folders": "\u986F\u793A\u5FFD\u7565\u7684\u8CC7\u6599\u593E",
    "delete_note": "\u522A\u9664\u6A94\u6848",
    "open_folder_note": "\u958B\u555F\u8CC7\u6599\u593E\u7B46\u8A18",
    "create_folder_note": "\u5EFA\u7ACB\u8CC7\u6599\u593E\u7B46\u8A18",
    "delete_folder_note": "\u522A\u9664\u8CC7\u6599\u593E\u7B46\u8A18",
    "edit_folder_note_settings": "\u7DE8\u8F2F\u8CC7\u6599\u593E\u7B46\u8A18\u8A2D\u5B9A",
    "ignore_folder": "\u5FFD\u7565\u6B64\u8CC7\u6599\u593E",
    "unignore_folder": "\u53D6\u6D88\u5FFD\u7565\u6B64\u8CC7\u6599\u593E",
    "searching": "\u641C\u5C0B\u4E2D...",
    "no_files": "\u6C92\u6709\u627E\u5230\u4EFB\u4F55\u6A94\u6848",
    "filter_folders": "\u7BE9\u9078\u8CC7\u6599\u593E...",
    // 資料夾筆記設定對話框
    "folder_note_settings": "\u8CC7\u6599\u593E\u7B46\u8A18\u8A2D\u5B9A",
    "folder_sort_type": "\u8CC7\u6599\u593E\u6392\u5E8F\u65B9\u5F0F",
    "folder_sort_type_desc": "\u8A2D\u5B9A\u6B64\u8CC7\u6599\u593E\u7684\u9810\u8A2D\u6392\u5E8F\u65B9\u5F0F",
    "folder_color": "\u8CC7\u6599\u593E\u984F\u8272",
    "folder_color_desc": "\u8A2D\u5B9A\u6B64\u8CC7\u6599\u593E\u7684\u986F\u793A\u984F\u8272",
    "folder_icon": "\u8CC7\u6599\u593E\u5716\u793A",
    "folder_icon_desc": "\u8A2D\u5B9A\u6B64\u8CC7\u6599\u593E\u7684\u986F\u793A\u5716\u793A",
    "default_sort": "\u4F7F\u7528\u9810\u8A2D\u6392\u5E8F",
    "no_color": "\u7121\u984F\u8272",
    "color_red": "\u7D05\u8272",
    "color_orange": "\u6A59\u8272",
    "color_yellow": "\u9EC3\u8272",
    "color_green": "\u7DA0\u8272",
    "color_cyan": "\u9752\u8272",
    "color_blue": "\u85CD\u8272",
    "color_purple": "\u7D2B\u8272",
    "color_pink": "\u7C89\u8272",
    "confirm": "\u78BA\u8A8D",
    "note_attribute_settings": "\u7B46\u8A18\u5C6C\u6027\u8A2D\u5B9A",
    "note_title": "\u7B46\u8A18\u6A19\u984C",
    "note_title_desc": "\u8A2D\u5B9A\u6B64\u7B46\u8A18\u7684\u986F\u793A\u6A19\u984C",
    "note_summary": "\u7B46\u8A18\u6458\u8981",
    "note_summary_desc": "\u8A2D\u5B9A\u6B64\u7B46\u8A18\u7684\u6458\u8981",
    "note_color": "\u7B46\u8A18\u984F\u8272",
    "note_color_desc": "\u8A2D\u5B9A\u6B64\u7B46\u8A18\u7684\u986F\u793A\u984F\u8272",
    "set_note_attribute": "\u8A2D\u5B9A\u7B46\u8A18\u5C6C\u6027",
    "rename_folder": "\u91CD\u65B0\u547D\u540D\u8CC7\u6599\u593E",
    "enter_new_folder_name": "\u8F38\u5165\u65B0\u8CC7\u6599\u593E\u540D\u7A31",
    "search_selection_in_grid_view": "\u5728\u7DB2\u683C\u8996\u5716\u4E2D\u641C\u5C0B...",
    "show_date_dividers": "\u986F\u793A\u65E5\u671F\u5206\u9694\u5668",
    "show_date_dividers_desc": "\u5728\u65E5\u671F\u76F8\u95DC\u6392\u5E8F\u6642\uFF0C\u5728\u4E0D\u540C\u5929\u7684\u7B2C\u4E00\u7B46\u4E4B\u524D\u986F\u793A\u65E5\u671F\u5206\u9694\u5668",
    "date_divider_format": "\u65E5\u671F\u5206\u9694\u5668\u683C\u5F0F",
    "date_divider_mode": "\u65E5\u671F\u5206\u9694\u5668",
    "date_divider_mode_desc": "\u9078\u64C7\u65E5\u671F\u5206\u9694\u5668\u7684\u986F\u793A\u6A21\u5F0F",
    "date_divider_mode_none": "\u4E0D\u4F7F\u7528",
    "date_divider_mode_year": "\u5E74",
    "date_divider_mode_month": "\u6708",
    "date_divider_mode_day": "\u65E5",
    "pinned": "\u7F6E\u9802",
    "pinned_desc": "\u5C07\u6B64\u6A94\u6848\u56FA\u5B9A\u5728\u6700\u4E0A\u65B9",
    "foldernote_pinned": "\u8CC7\u6599\u593E\u7B46\u8A18\u7F6E\u9802",
    "foldernote_pinned_desc": "\u5C07\u8CC7\u6599\u593E\u7B46\u8A18\u56FA\u5B9A\u5728\u6700\u4E0A\u65B9",
    "display_minimized": "\u6700\u5C0F\u5316\u986F\u793A",
    "display_minimized_desc": "\u5C07\u6B64\u7B46\u8A18\u4EE5\u6700\u5C0F\u5316\u65B9\u5F0F\u986F\u793A",
    // Quick Access Settings and Commands
    "quick_access_settings_title": "\u5FEB\u901F\u5B58\u53D6\u8A2D\u5B9A",
    "quick_access_folder_name": "\u5FEB\u901F\u5B58\u53D6\u8CC7\u6599\u593E",
    "quick_access_folder_desc": "\u8A2D\u5B9A\u300C\u958B\u555F\u5FEB\u901F\u5B58\u53D6\u8CC7\u6599\u593E\u300D\u547D\u4EE4\u6240\u4F7F\u7528\u7684\u8CC7\u6599\u593E",
    "quick_access_mode_name": "\u5FEB\u901F\u5B58\u53D6\u6A21\u5F0F",
    "quick_access_mode_desc": "\u8A2D\u5B9A\u300C\u958B\u555F\u5FEB\u901F\u5B58\u53D6\u6A21\u5F0F\u300D\u547D\u4EE4\u6240\u4F7F\u7528\u7684\u9810\u8A2D\u6A21\u5F0F",
    "use_quick_access_as_new_tab_view": "\u5C07\u5FEB\u901F\u5B58\u53D6\u4F5C\u70BA\u65B0\u5206\u9801\u4F7F\u7528",
    "use_quick_access_as_new_tab_view_desc": "\u5C07\u9810\u8A2D\u7684\u300C\u65B0\u5206\u9801\u300D\u756B\u9762\u66FF\u63DB\u70BA\u6240\u9078\u5FEB\u901F\u5B58\u53D6\u9078\u9805\uFF08\u8CC7\u6599\u593E\u6216\u6A21\u5F0F\uFF09\u7684\u7DB2\u683C\u6AA2\u8996\u3002\u6B64\u8A2D\u5B9A\u50C5\u5728\u300C\u9810\u8A2D\u958B\u555F\u4F4D\u7F6E\u300D\u8A2D\u70BA\u300C\u5728\u65B0\u5206\u9801\u958B\u555F\u300D\u6642\u6709\u6548\uFF01",
    "default_new_tab": "\u9810\u8A2D\u65B0\u5206\u9801",
    "use_quick_access_folder": "\u4F7F\u7528\u5FEB\u901F\u5B58\u53D6\u8CC7\u6599\u593E",
    "use_quick_access_mode": "\u4F7F\u7528\u5FEB\u901F\u5B58\u53D6\u6A21\u5F0F",
    "open_quick_access_folder": "\u958B\u555F\u5FEB\u901F\u5B58\u53D6\u8CC7\u6599\u593E",
    "open_quick_access_mode": "\u958B\u555F\u5FEB\u901F\u5B58\u53D6\u6A21\u5F0F"
  },
  "en": {
    // Notifications
    "bookmarks_plugin_disabled": "Please enable the Bookmarks plugin first",
    // Buttons and Labels
    "sorting": "Sort by",
    "refresh": "Refresh",
    "reselect": "Reselect",
    "go_up": "Go Up",
    "no_backlinks": "No backlinks",
    "search": "Search",
    "search_placeholder": "Search keyword",
    "search_current_location_only": "Search current location only",
    "search_media_files": "Search media files",
    "cancel": "Cancel",
    "new_note": "New note",
    "new_folder": "New folder",
    "new_canvas": "New canvas",
    "delete_folder": "Delete folder",
    "untitled": "Untitled",
    "files": "files",
    "add": "Add",
    "root": "Root",
    "more_options": "More options",
    "add_tag_to_search": "Add to search",
    "remove_tag_from_search": "Remove from search",
    "global_search": "Global search",
    "remove": "Remove",
    "edit": "Edit",
    "delete": "Delete",
    "save": "Save",
    // View Titles
    "grid_view_title": "Grid view",
    "bookmarks_mode": "Bookmarks",
    "folder_mode": "Folder",
    "search_results": "Search results",
    "backlinks_mode": "Backlinks",
    "outgoinglinks_mode": "Outgoing links",
    "all_files_mode": "All files",
    "recent_files_mode": "Recent files",
    "random_note_mode": "Random note",
    "tasks_mode": "Tasks",
    // Sort Options
    "sort_name_asc": "Name (A \u2192 Z)",
    "sort_name_desc": "Name (Z \u2192 A)",
    "sort_mtime_desc": "Modified (New \u2192 Old)",
    "sort_mtime_asc": "Modified (Old \u2192 New)",
    "sort_ctime_desc": "Created (New \u2192 Old)",
    "sort_ctime_asc": "Created (Old \u2192 New)",
    "sort_random": "Random",
    // Settings
    "grid_view_settings": "Grid view settings",
    "media_files_settings": "Media files settings",
    "show_media_files": "Show media files",
    "show_media_files_desc": "Display media files in the grid view",
    "show_video_thumbnails": "Show video thumbnails",
    "show_video_thumbnails_desc": "Display thumbnails for videos in the grid view, shows a play icon when disabled",
    "show_note_tags": "Show note tags",
    "show_note_tags_desc": "Display tags for notes in the grid view",
    "ignored_folders": "Ignored folders",
    "ignored_folders_desc": "Set folders to ignore here",
    "add_ignored_folder": "Add ignored folder",
    "no_ignored_folders": "No ignored folders.",
    "ignored_folder_patterns": "Ignore folders and files by pattern",
    "ignored_folder_patterns_desc": "Use string patterns to ignore folders and files (supports regular expressions)",
    "add_ignored_folder_pattern": "Add folder pattern",
    "ignored_folder_pattern_placeholder": "Enter folder name or regex pattern",
    "no_ignored_folder_patterns": "No ignored folder patterns.",
    "default_sort_type": "Default sort type",
    "default_sort_type_desc": "Set the default sorting method when opening Grid View",
    "note_title_field": "Note title field name",
    "note_title_field_desc": "Set the field name in frontmatter to use for the note title",
    "note_summary_field": "Note summary field name",
    "note_summary_field_desc": "Set the field name in frontmatter to use for the note summary",
    "modified_date_field": '"Modified date" field name',
    "modified_date_field_desc": "Set the field name in frontmatter to use for the modified date",
    "created_date_field": '"Created date" field name',
    "created_date_field_desc": "Set the field name in frontmatter to use for the created date",
    "grid_item_width": "Grid item width",
    "grid_item_width_desc": "Set the width of grid items",
    "grid_item_height": "Grid item height",
    "grid_item_height_desc": "Set the height of grid items (set to 0 to automatically adjust)",
    "image_area_width": "Image area width",
    "image_area_width_desc": "Set the width of the image preview area",
    "image_area_height": "Image area height",
    "image_area_height_desc": "Set the height of the image preview area",
    "title_font_size": "Title font size",
    "title_font_size_desc": "Set the size of the title font",
    "summary_length": "Summary length",
    "summary_length_desc": "Set the length of the summary",
    "show_code_block_in_summary": "Show code block in summary",
    "show_code_block_in_summary_desc": "Set whether to show code block in the summary",
    "enable_file_watcher": "Enable file watcher",
    "enable_file_watcher_desc": "When enabled, the view will automatically update when files change. If disabled, you need to click the refresh button manually",
    "intercept_all_tag_clicks": "Intercept all tag clicks",
    "intercept_all_tag_clicks_desc": "When enabled, all tag clicks will be intercepted and opened in the grid view",
    "reset_to_default": "Reset to default",
    "reset_to_default_desc": "Reset all settings to default values",
    "settings_reset_notice": "Settings have been reset to default values",
    "ignored_folders_settings": "Ignore folders settings",
    "display_mode_settings": "Display mode settings",
    "custom_mode_settings": "Custom Mode Settings",
    "add_custom_mode": "Add Custom Mode",
    "export": "Export",
    "import": "Import",
    "no_custom_modes_to_export": "No custom modes to export",
    "import_success": "Custom modes imported successfully",
    "import_error": "Import failed: Invalid file format",
    "edit_custom_mode": "Edit Custom Mode",
    "custom_mode_icon": "Icon",
    "custom_mode_icon_desc": "The icon displayed in the mode menu",
    "custom_mode_display_name": "Display Name",
    "custom_mode_display_name_desc": "The name displayed in the mode menu",
    "custom_mode_dataview_code": "Dataview Query",
    "custom_mode_dataview_code_desc": "Enter a Dataview query to get the list of files",
    "display_name_cannot_be_empty": "Display name cannot be empty",
    "custom_mode": "Custom Mode",
    "show_bookmarks_mode": "Show bookmarks mode",
    "show_search_mode": "Show search results mode",
    "show_backlinks_mode": "Show backlinks mode",
    "show_outgoinglinks_mode": "Show outgoing links mode",
    "show_all_files_mode": "Show all files mode",
    "show_recent_files_mode": "Show recent files mode",
    "recent_files_count": "Recent files count",
    "show_random_note_mode": "Show random note mode",
    "random_note_count": "Random note count",
    "random_note_notes_only": "Notes Only",
    "random_note_include_media_files": "Include Media Files",
    "show_tasks_mode": "Show tasks mode",
    "task_filter": "Task filter",
    "uncompleted": "Uncompleted",
    "completed": "Completed",
    "foldernote_display_settings": "Folder note display settings",
    "foldernote_display_settings_desc": "Set the display mode of folder notes",
    "all": "All",
    "default": "Default",
    "hidden": "Hidden",
    // Show "Parent Folder" option setting
    "show_parent_folder_item": 'Show "Parent Folder" item',
    "show_parent_folder_item_desc": 'Show a "Parent Folder" item as the first item in the grid',
    "parent_folder": "Parent Folder",
    // Default open location setting
    "default_open_location": "Default open location",
    "default_open_location_desc": "Set the default location to open the grid view",
    "open_in_left_sidebar": "Open in left sidebar",
    "open_in_right_sidebar": "Open in right sidebar",
    "open_in_new_tab": "Open in new tab",
    "reuse_existing_leaf": "Reuse existing view",
    "reuse_existing_leaf_desc": "When opening Grid View, prioritize using an existing view instead of creating a new one",
    "custom_document_extensions": "Custom Document Extensions",
    "custom_document_extensions_desc": "Additional document extensions (comma-separated, without dots)",
    "custom_document_extensions_placeholder": "e.g., txt,doc,docx",
    "custom_folder_icon": "Custom Folder Icon",
    "custom_folder_icon_desc": "Custom folder icon (use emoji)",
    // Select Folder Dialog
    "select_folders": "Select folder",
    "select_folders_to_ignore": "Select folders to ignore",
    "open_grid_view": "Open grid view",
    "open_in_grid_view": "Open in grid view",
    "open_note_in_grid_view": "Open note in grid view",
    "open_backlinks_in_grid_view": "Open backlinks in grid view",
    "open_outgoinglinks_in_grid_view": "Open outgoing links in grid view",
    "open_recent_files_in_grid_view": "Open current note in recent files",
    "open_settings": "Open settings",
    "open_new_grid_view": "Open new grid view",
    "open_in_new_grid_view": "Open in new grid view",
    "min_mode": "Minimize mode",
    "show_ignored_folders": "Show ignored folders",
    "delete_note": "Delete file",
    "open_folder_note": "Open folder note",
    "create_folder_note": "Create folder note",
    "delete_folder_note": "Delete folder note",
    "edit_folder_note_settings": "Edit folder note settings",
    "ignore_folder": "Ignore this folder",
    "unignore_folder": "Unignore this folder",
    "searching": "Searching...",
    "no_files": "No files found",
    "filter_folders": "Filter folders...",
    // Folder Note Settings Dialog
    "folder_note_settings": "Folder Note Settings",
    "folder_sort_type": "Folder Sort Type",
    "folder_sort_type_desc": "Set the default sort type for this folder",
    "folder_color": "Folder Color",
    "folder_color_desc": "Set the display color for this folder",
    "folder_icon": "Folder Icon",
    "folder_icon_desc": "Set the display icon for this folder",
    "default_sort": "Use Default Sort",
    "no_color": "No Color",
    "color_red": "Red",
    "color_orange": "Orange",
    "color_yellow": "Yellow",
    "color_green": "Green",
    "color_cyan": "Cyan",
    "color_blue": "Blue",
    "color_purple": "Purple",
    "color_pink": "Pink",
    "confirm": "Confirm",
    "note_attribute_settings": "Note attribute settings",
    "note_title": "Note title",
    "note_title_desc": "Set the display title for this note",
    "note_summary": "Note summary",
    "note_summary_desc": "Set the display summary for this note",
    "note_color": "Note color",
    "note_color_desc": "Set the display color for this note",
    "set_note_attribute": "Set note attribute",
    "rename_folder": "Rename folder",
    "enter_new_folder_name": "Enter new folder name",
    "search_selection_in_grid_view": "Search ... in grid view",
    "show_date_dividers": "Show date dividers",
    "show_date_dividers_desc": "Show date dividers before the first item of each different day when using date-related sorting",
    "date_divider_format": "Date divider format",
    "date_divider_mode": "Date divider",
    "date_divider_mode_desc": "Select the display mode for date dividers",
    "date_divider_mode_none": "None",
    "date_divider_mode_year": "Year",
    "date_divider_mode_month": "Month",
    "date_divider_mode_day": "Day",
    "pinned": "Pinned",
    "pinned_desc": "Pin this file at the top",
    "foldernote_pinned": "Folder note pinned",
    "foldernote_pinned_desc": "Pin this folder note at the top",
    "display_minimized": "Display minimized",
    "display_minimized_desc": "Show this note in minimized mode",
    // Quick Access Settings and Commands
    "quick_access_settings_title": "Quick Access Settings",
    "quick_access_folder_name": "Quick access folder",
    "quick_access_folder_desc": 'Set the folder used by the "Open quick access folder" command',
    "quick_access_mode_name": "Quick access mode",
    "quick_access_mode_desc": 'Set the default mode used by the "Open quick access mode" command',
    "use_quick_access_as_new_tab_view": "Use Quick Access as a new tab view",
    "use_quick_access_as_new_tab_view_desc": 'Replace the default "New Tab" view with a Grid View of the selected Quick Access option (folder or mode). Only works if default open location is set to "Open in new tab"!',
    "default_new_tab": "Default New Tab",
    "use_quick_access_folder": "Use quick access folder",
    "use_quick_access_mode": "Use quick access mode",
    "open_quick_access_folder": "Open quick access folder",
    "open_quick_access_mode": "Open quick access mode"
  },
  "zh": {
    // 通知信息
    "bookmarks_plugin_disabled": "\u8BF7\u5148\u542F\u7528\u4E66\u7B7E\u63D2\u4EF6",
    // 按钮和标签
    "sorting": "\u6392\u5E8F\u65B9\u5F0F",
    "refresh": "\u5237\u65B0",
    "reselect": "\u91CD\u65B0\u9009\u62E9\u4F4D\u7F6E",
    "go_up": "\u8FD4\u56DE\u4E0A\u7EA7\u6587\u4EF6\u5939",
    "no_backlinks": "\u6CA1\u6709\u53CD\u5411\u94FE\u63A5",
    "search": "\u641C\u7D22",
    "search_placeholder": "\u641C\u7D22\u5173\u952E\u5B57",
    "search_current_location_only": "\u4EC5\u641C\u7D22\u5F53\u524D\u4F4D\u7F6E",
    "search_media_files": "\u641C\u7D22\u5A92\u4F53\u6587\u4EF6",
    "cancel": "\u53D6\u6D88",
    "new_note": "\u65B0\u5EFA\u7B14\u8BB0",
    "new_folder": "\u65B0\u5EFA\u6587\u4EF6\u5939",
    "new_canvas": "\u65B0\u5EFA\u753B\u5E03",
    "delete_folder": "\u5220\u9664\u6587\u4EF6\u5939",
    "untitled": "\u672A\u547D\u540D",
    "files": "\u4E2A\u6587\u4EF6",
    "add": "\u6DFB\u52A0",
    "root": "\u6839\u76EE\u5F55",
    "more_options": "\u66F4\u591A\u9009\u9879",
    "add_tag_to_search": "\u52A0\u5165\u641C\u7D22",
    "remove_tag_from_search": "\u4ECE\u641C\u7D22\u4E2D\u79FB\u9664",
    "global_search": "\u5168\u57DF\u641C\u7D22",
    "remove": "\u79FB\u9664",
    "edit": "\u7F16\u8F91",
    "delete": "\u5220\u9664",
    "save": "\u4FDD\u5B58",
    // 视图标题
    "grid_view_title": "\u7F51\u683C\u89C6\u56FE",
    "bookmarks_mode": "\u4E66\u7B7E",
    "folder_mode": "\u6587\u4EF6\u5939",
    "search_results": "\u641C\u7D22\u7ED3\u679C",
    "backlinks_mode": "\u53CD\u5411\u94FE\u63A5",
    "outgoinglinks_mode": "\u5916\u90E8\u94FE\u63A5",
    "all_files_mode": "\u6240\u6709\u6587\u4EF6",
    "recent_files_mode": "\u6700\u8FD1\u6587\u4EF6",
    "random_note_mode": "\u968F\u673A\u7B14\u8BB0",
    "tasks_mode": "\u4EFB\u52A1",
    // 排序选项
    "sort_name_asc": "\u540D\u79F0 (A \u2192 Z)",
    "sort_name_desc": "\u540D\u79F0 (Z \u2192 A)",
    "sort_mtime_desc": "\u4FEE\u6539\u65F6\u95F4 (\u65B0 \u2192 \u65E7)",
    "sort_mtime_asc": "\u4FEE\u6539\u65F6\u95F4 (\u65E7 \u2192 \u65B0)",
    "sort_ctime_desc": "\u521B\u5EFA\u65F6\u95F4 (\u65B0 \u2192 \u65E7)",
    "sort_ctime_asc": "\u521B\u5EFA\u65F6\u95F4 (\u65E7 \u2192 \u65B0)",
    "sort_random": "\u968F\u673A\u6392\u5E8F",
    // 设置
    "grid_view_settings": "\u7F51\u683C\u89C6\u56FE\u8BBE\u7F6E",
    "media_files_settings": "\u5A92\u4F53\u6587\u4EF6\u8BBE\u7F6E",
    "show_media_files": "\u663E\u793A\u5A92\u4F53\u6587\u4EF6",
    "show_media_files_desc": "\u5728\u7F51\u683C\u89C6\u56FE\u4E2D\u663E\u793A\u5A92\u4F53\u6587\u4EF6",
    "show_video_thumbnails": "\u663E\u793A\u89C6\u9891\u7F29\u7565\u56FE",
    "show_video_thumbnails_desc": "\u5728\u7F51\u683C\u89C6\u56FE\u4E2D\u663E\u793A\u89C6\u9891\u7684\u7F29\u7565\u56FE\uFF0C\u5173\u95ED\u65F6\u5C06\u663E\u793A\u64AD\u653E\u56FE\u6807",
    "show_note_tags": "\u663E\u793A\u7B14\u8BB0\u6807\u7B7E",
    "show_note_tags_desc": "\u5728\u7F51\u683C\u89C6\u56FE\u4E2D\u663E\u793A\u7B14\u8BB0\u7684\u6807\u7B7E",
    "ignored_folders": "\u5FFD\u7565\u7684\u6587\u4EF6\u5939",
    "ignored_folders_desc": "\u5728\u8FD9\u91CC\u8BBE\u7F6E\u8981\u5FFD\u7565\u7684\u6587\u4EF6\u5939",
    "add_ignored_folder": "\u6DFB\u52A0\u5FFD\u7565\u6587\u4EF6\u5939",
    "no_ignored_folders": "\u6CA1\u6709\u5FFD\u7565\u7684\u6587\u4EF6\u5939\u3002",
    "ignored_folder_patterns": "\u4EE5\u5B57\u7B26\u4E32\u5FFD\u7565\u6587\u4EF6\u5939\u548C\u6587\u4EF6",
    "ignored_folder_patterns_desc": "\u4F7F\u7528\u5B57\u7B26\u4E32\u6A21\u5F0F\u5FFD\u7565\u6587\u4EF6\u5939\u548C\u6587\u4EF6\uFF08\u652F\u6301\u6B63\u5219\u8868\u8FBE\u5F0F\uFF09",
    "add_ignored_folder_pattern": "\u6DFB\u52A0\u5FFD\u7565\u6587\u4EF6\u5939\u6A21\u5F0F",
    "ignored_folder_pattern_placeholder": "\u8F93\u5165\u6587\u4EF6\u5939\u540D\u79F0\u6216\u6B63\u5219\u8868\u8FBE\u5F0F",
    "no_ignored_folder_patterns": "\u6CA1\u6709\u5FFD\u7565\u7684\u6587\u4EF6\u5939\u6A21\u5F0F\u3002",
    "default_sort_type": "\u9ED8\u8BA4\u6392\u5E8F\u6A21\u5F0F",
    "default_sort_type_desc": "\u8BBE\u7F6E\u6253\u5F00\u7F51\u683C\u89C6\u56FE\u65F6\u7684\u9ED8\u8BA4\u6392\u5E8F\u6A21\u5F0F",
    "note_title_field": "\u7B14\u8BB0\u6807\u9898\u5B57\u6BB5\u540D\u79F0",
    "note_title_field_desc": "\u8BBE\u7F6E frontmatter \u4E2D\u7528\u4E8E\u7B14\u8BB0\u6807\u9898\u7684\u5B57\u6BB5\u540D\u79F0",
    "note_summary_field": "\u7B14\u8BB0\u6458\u8981\u5B57\u6BB5\u540D\u79F0",
    "note_summary_field_desc": "\u8BBE\u7F6E frontmatter \u4E2D\u7528\u4E8E\u7B14\u8BB0\u6458\u8981\u7684\u5B57\u6BB5\u540D\u79F0",
    "modified_date_field": '"\u4FEE\u6539\u65F6\u95F4"\u5B57\u6BB5\u540D\u79F0',
    "modified_date_field_desc": "\u8BBE\u7F6E frontmatter \u4E2D\u7528\u4E8E\u7B14\u8BB0\u4FEE\u6539\u65F6\u95F4\u7684\u5B57\u6BB5\u540D\u79F0",
    "created_date_field": '"\u521B\u5EFA\u65F6\u95F4"\u5B57\u6BB5\u540D\u79F0',
    "created_date_field_desc": "\u8BBE\u7F6E frontmatter \u4E2D\u7528\u4E8E\u7B14\u8BB0\u521B\u5EFA\u65F6\u95F4\u7684\u5B57\u6BB5\u540D\u79F0",
    "grid_item_width": "\u7F51\u683C\u9879\u76EE\u5BBD\u5EA6",
    "grid_item_width_desc": "\u8BBE\u7F6E\u7F51\u683C\u9879\u76EE\u7684\u5BBD\u5EA6",
    "grid_item_height": "\u7F51\u683C\u9879\u76EE\u9AD8\u5EA6",
    "grid_item_height_desc": "\u8BBE\u7F6E\u7F51\u683C\u9879\u76EE\u7684\u9AD8\u5EA6 (\u8BBE\u4E3A0\u65F6\u4E3A\u81EA\u52A8\u8C03\u6574)",
    "image_area_width": "\u56FE\u7247\u533A\u57DF\u5BBD\u5EA6",
    "image_area_width_desc": "\u8BBE\u7F6E\u56FE\u7247\u9884\u89C8\u533A\u57DF\u7684\u5BBD\u5EA6",
    "image_area_height": "\u56FE\u7247\u533A\u57DF\u9AD8\u5EA6",
    "image_area_height_desc": "\u8BBE\u7F6E\u56FE\u7247\u9884\u89C8\u533A\u57DF\u7684\u9AD8\u5EA6",
    "title_font_size": "\u6807\u9898\u5B57\u4F53\u5927\u5C0F",
    "title_font_size_desc": "\u8BBE\u7F6E\u6807\u9898\u5B57\u4F53\u7684\u5927\u5C0F",
    "summary_length": "\u6458\u8981\u957F\u5EA6",
    "summary_length_desc": "\u8BBE\u7F6E\u6458\u8981\u7684\u957F\u5EA6",
    "show_code_block_in_summary": "\u6458\u8981\u4E2D\u663E\u793ACodeBlock",
    "show_code_block_in_summary_desc": "\u8BBE\u7F6E\u662F\u5426\u5728\u6458\u8981\u4E2D\u663E\u793ACodeBlock\u7684\u5167\u5BB9",
    "enable_file_watcher": "\u542F\u7528\u6587\u4EF6\u76D1\u63A7",
    "enable_file_watcher_desc": "\u542F\u7528\u540E\u4F1A\u81EA\u52A8\u68C0\u6D4B\u6587\u4EF6\u53D8\u66F4\u5E76\u66F4\u65B0\u89C6\u56FE\uFF0C\u5173\u95ED\u540E\u9700\u624B\u52A8\u70B9\u51FB\u5237\u65B0\u6309\u94AE",
    "intercept_all_tag_clicks": "\u62E6\u622A\u6240\u6709\u6807\u7B7E\u70B9\u51FB\u4E8B\u4EF6",
    "intercept_all_tag_clicks_desc": "\u542F\u7528\u540E\u4F1A\u62E6\u622A\u6240\u6709\u6807\u7B7E\u70B9\u51FB\u4E8B\u4EF6\uFF0C\u6539\u4E3A\u5728\u7F51\u683C\u89C6\u56FE\u4E2D\u6253\u5F00\u6807\u7B7E",
    "reset_to_default": "\u91CD\u7F6E\u4E3A\u9ED8\u8BA4\u503C",
    "reset_to_default_desc": "\u5C06\u6240\u6709\u8BBE\u7F6E\u91CD\u7F6E\u4E3A\u9ED8\u8BA4\u503C",
    "settings_reset_notice": "\u8BBE\u7F6E\u503C\u5DF2\u91CD\u7F6E\u4E3A\u9ED8\u8BA4\u503C",
    "ignored_folders_settings": "\u5FFD\u7565\u6587\u4EF6\u5939\u8BBE\u7F6E",
    "display_mode_settings": "\u663E\u793A\u6A21\u5F0F\u8BBE\u7F6E",
    "custom_mode_settings": "\u81EA\u5B9A\u4E49\u6A21\u5F0F\u8BBE\u7F6E",
    "add_custom_mode": "\u6DFB\u52A0\u81EA\u5B9A\u4E49\u6A21\u5F0F",
    "export": "\u5BFC\u51FA",
    "import": "\u5BFC\u5165",
    "no_custom_modes_to_export": "\u6CA1\u6709\u53EF\u5BFC\u51FA\u7684\u81EA\u5B9A\u4E49\u6A21\u5F0F",
    "import_success": "\u81EA\u5B9A\u4E49\u6A21\u5F0F\u5BFC\u5165\u6210\u529F",
    "import_error": "\u5BFC\u5165\u5931\u8D25\uFF1A\u65E0\u6548\u7684\u6587\u4EF6\u683C\u5F0F",
    "edit_custom_mode": "\u7F16\u8F91\u81EA\u5B9A\u4E49\u6A21\u5F0F",
    "custom_mode_icon": "\u56FE\u6807",
    "custom_mode_icon_desc": "\u5728\u6A21\u5F0F\u83DC\u5355\u4E2D\u663E\u793A\u7684\u56FE\u6807",
    "custom_mode_display_name": "\u663E\u793A\u540D\u79F0",
    "custom_mode_display_name_desc": "\u5728\u6A21\u5F0F\u83DC\u5355\u4E2D\u663E\u793A\u7684\u540D\u79F0",
    "custom_mode_dataview_code": "Dataviewjs \u4EE3\u7801",
    "custom_mode_dataview_code_desc": "\u8F93\u5165 Dataviewjs \u4EE3\u7801\u4EE5\u83B7\u53D6\u6587\u4EF6\u5217\u8868",
    "display_name_cannot_be_empty": "\u663E\u793A\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",
    "custom_mode": "\u81EA\u5B9A\u4E49\u6A21\u5F0F",
    "show_bookmarks_mode": "\u663E\u793A\u4E66\u7B7E\u6A21\u5F0F",
    "show_search_mode": "\u663E\u793A\u641C\u7D22\u7ED3\u679C\u6A21\u5F0F",
    "show_backlinks_mode": "\u663E\u793A\u53CD\u5411\u94FE\u63A5\u6A21\u5F0F",
    "show_outgoinglinks_mode": "\u663E\u793A\u5916\u90E8\u94FE\u63A5\u6A21\u5F0F",
    "show_all_files_mode": "\u663E\u793A\u6240\u6709\u6587\u4EF6\u6A21\u5F0F",
    "show_recent_files_mode": "\u663E\u793A\u6700\u8FD1\u6587\u4EF6\u6A21\u5F0F",
    "recent_files_count": "\u6700\u8FD1\u6587\u4EF6\u6A21\u5F0F\u663E\u793A\u7B14\u6570",
    "show_random_note_mode": "\u663E\u793A\u968F\u673A\u7B14\u8BB0\u6A21\u5F0F",
    "random_note_count": "\u968F\u673A\u7B14\u8BB0\u6A21\u5F0F\u663E\u793A\u7B14\u6570",
    "random_note_notes_only": "\u4EC5\u7B14\u8BB0",
    "random_note_include_media_files": "\u5305\u542B\u5A92\u4F53\u6587\u4EF6",
    "show_tasks_mode": "\u663E\u793A\u4EFB\u52A1\u6A21\u5F0F",
    "task_filter": "\u4EFB\u52A1\u5206\u7C7B",
    "uncompleted": "\u672A\u5B8C\u6210",
    "completed": "\u5DF2\u5B8C\u6210",
    "foldernote_display_settings": "\u6587\u4EF6\u5939\u7B14\u8BB0\u663E\u793A\u8BBE\u7F6E",
    "foldernote_display_settings_desc": "\u8BBE\u7F6E\u6587\u4EF6\u5939\u7B14\u8BB0\u7684\u663E\u793A\u65B9\u5F0F",
    "all": "\u5168\u90E8",
    "default": "\u9ED8\u8BA4",
    "hidden": "\u9690\u85CF",
    // 显示"返回上级文件夹"选项设置
    "show_parent_folder_item": "\u663E\u793A\u300C\u8FD4\u56DE\u4E0A\u7EA7\u6587\u4EF6\u5939\u300D",
    "show_parent_folder_item_desc": "\u5728\u7F51\u683C\u7684\u7B2C\u4E00\u9879\u663E\u793A\u300C\u8FD4\u56DE\u4E0A\u7EA7\u6587\u4EF6\u5939\u300D\u9009\u9879",
    "parent_folder": "\u4E0A\u7EA7\u6587\u4EF6\u5939",
    // 默认打开位置设置
    "default_open_location": "\u9ED8\u8BA4\u6253\u5F00\u4F4D\u7F6E",
    "default_open_location_desc": "\u8BBE\u7F6E\u7F51\u683C\u89C6\u56FE\u9ED8\u8BA4\u6253\u5F00\u7684\u4F4D\u7F6E",
    "open_in_left_sidebar": "\u5728\u5DE6\u4FA7\u8FB9\u680F\u6253\u5F00",
    "open_in_right_sidebar": "\u5728\u53F3\u4FA7\u8FB9\u680F\u6253\u5F00",
    "open_in_new_tab": "\u5728\u65B0\u6807\u7B7E\u9875\u6253\u5F00",
    "reuse_existing_leaf": "\u91CD\u590D\u4F7F\u7528\u5DF2\u6253\u5F00\u7684\u89C6\u56FE",
    "reuse_existing_leaf_desc": "\u6253\u5F00\u7F51\u683C\u89C6\u56FE\u65F6\uFF0C\u4F18\u5148\u4F7F\u7528\u5DF2\u6253\u5F00\u7684\u89C6\u56FE\u800C\u975E\u521B\u5EFA\u65B0\u89C6\u56FE",
    "custom_document_extensions": "\u81EA\u5B9A\u4E49\u6587\u4EF6\u6269\u5C55\u540D",
    "custom_document_extensions_desc": "\u989D\u5916\u7684\u6587\u4EF6\u6269\u5C55\u540D\uFF08\u7528\u9017\u53F7\u5206\u9694\uFF0C\u4E0D\u542B\u70B9\u53F7\uFF09",
    "custom_document_extensions_placeholder": "\u4F8B\u5982\uFF1Atxt,doc,docx",
    "custom_folder_icon": "\u81EA\u5B9A\u4E49\u6587\u4EF6\u5939\u56FE\u6807",
    "custom_folder_icon_desc": "\u81EA\u5B9A\u4E49\u6587\u4EF6\u5939\u56FE\u6807\uFF08\u4F7F\u7528Emoji\uFF09",
    // 选择文件夹对话框
    "select_folders": "\u9009\u62E9\u6587\u4EF6\u5939",
    "select_folders_to_ignore": "\u9009\u62E9\u8981\u5FFD\u7565\u7684\u6587\u4EF6\u5939",
    "open_grid_view": "\u6253\u5F00\u7F51\u683C\u89C6\u56FE",
    "open_in_grid_view": "\u5728\u7F51\u683C\u89C6\u56FE\u4E2D\u6253\u5F00",
    "open_note_in_grid_view": "\u5728\u7F51\u683C\u89C6\u56FE\u4E2D\u6253\u5F00\u5F53\u524D\u7B14\u8BB0",
    "open_backlinks_in_grid_view": "\u5728\u7F51\u683C\u89C6\u56FE\u4E2D\u6253\u5F00\u53CD\u5411\u94FE\u63A5",
    "open_outgoinglinks_in_grid_view": "\u5728\u7F51\u683C\u89C6\u56FE\u4E2D\u6253\u5F00\u5916\u90E8\u94FE\u63A5",
    "open_recent_files_in_grid_view": "\u5728\u6700\u8FD1\u6587\u4EF6\u4E2D\u6253\u5F00\u5F53\u524D\u7B14\u8BB0",
    "open_settings": "\u6253\u5F00\u8BBE\u7F6E",
    "open_new_grid_view": "\u6253\u5F00\u65B0\u7F51\u683C\u89C6\u56FE",
    "open_in_new_grid_view": "\u5728\u65B0\u7F51\u683C\u89C6\u56FE\u4E2D\u6253\u5F00",
    "min_mode": "\u6700\u5C0F\u5316\u6A21\u5F0F",
    "show_ignored_folders": "\u663E\u793A\u5FFD\u7565\u7684\u6587\u4EF6\u5939",
    "delete_note": "\u5220\u9664\u6587\u4EF6",
    "open_folder_note": "\u6253\u5F00\u6587\u4EF6\u5939\u7B14\u8BB0",
    "create_folder_note": "\u521B\u5EFA\u6587\u4EF6\u5939\u7B14\u8BB0",
    "delete_folder_note": "\u5220\u9664\u6587\u4EF6\u5939\u7B14\u8BB0",
    "edit_folder_note_settings": "\u7F16\u8F91\u6587\u4EF6\u5939\u7B14\u8BB0\u8BBE\u7F6E",
    "ignore_folder": "\u5FFD\u7565\u6B64\u6587\u4EF6\u5939",
    "unignore_folder": "\u53D6\u6D88\u5FFD\u7565\u6B64\u6587\u4EF6\u5939",
    "searching": "\u641C\u7D22\u4E2D...",
    "no_files": "\u6CA1\u6709\u627E\u5230\u4EFB\u4F55\u6587\u4EF6",
    "filter_folders": "\u7B5B\u9009\u6587\u4EF6\u5939...",
    // 文件夹笔记设置对话框
    "folder_note_settings": "\u6587\u4EF6\u5939\u7B14\u8BB0\u8BBE\u7F6E",
    "folder_sort_type": "\u6587\u4EF6\u5939\u6392\u5E8F\u65B9\u5F0F",
    "folder_sort_type_desc": "\u8BBE\u7F6E\u6B64\u6587\u4EF6\u5939\u7684\u9ED8\u8BA4\u6392\u5E8F\u65B9\u5F0F",
    "folder_color": "\u6587\u4EF6\u5939\u989C\u8272",
    "folder_color_desc": "\u8BBE\u7F6E\u6B64\u6587\u4EF6\u5939\u7684\u663E\u793A\u989C\u8272",
    "folder_icon": "\u6587\u4EF6\u5939\u56FE\u793A",
    "folder_icon_desc": "\u8BBE\u7F6E\u6B64\u6587\u4EF6\u5939\u7684\u663E\u793A\u56FE\u793A",
    "default_sort": "\u4F7F\u7528\u9ED8\u8BA4\u6392\u5E8F",
    "no_color": "\u65E0\u989C\u8272",
    "color_red": "\u7EA2\u8272",
    "color_orange": "\u6A59\u8272",
    "color_yellow": "\u9EC4\u8272",
    "color_green": "\u7EFF\u8272",
    "color_cyan": "\u9752\u8272",
    "color_blue": "\u84DD\u8272",
    "color_purple": "\u7D2B\u8272",
    "color_pink": "\u7C89\u8272",
    "confirm": "\u786E\u8BA4",
    "note_attribute_settings": "\u7B14\u8BB0\u5C5E\u6027\u8BBE\u7F6E",
    "note_title": "\u7B14\u8BB0\u6807\u9898",
    "note_title_desc": "\u8BBE\u7F6E\u6B64\u7B14\u8BB0\u7684\u663E\u793A\u6807\u9898",
    "note_summary": "\u7B14\u8BB0\u6458\u8981",
    "note_summary_desc": "\u8BBE\u7F6E\u6B64\u7B14\u8BB0\u7684\u663E\u793A\u6458\u8981",
    "note_color": "\u7B14\u8BB0\u989C\u8272",
    "note_color_desc": "\u8BBE\u7F6E\u6B64\u7B14\u8BB0\u7684\u663E\u793A\u989C\u8272",
    "set_note_attribute": "\u8BBE\u7F6E\u7B14\u8BB0\u5C5E\u6027",
    "rename_folder": "\u91CD\u65B0\u547D\u540D\u6587\u4EF6\u5939",
    "enter_new_folder_name": "\u8F93\u5165\u65B0\u6587\u4EF6\u5939\u540D\u79F0",
    "search_selection_in_grid_view": "\u5728\u7F51\u683C\u89C6\u56FE\u4E2D\u641C\u5BFB...",
    "show_date_dividers": "\u663E\u793A\u65E5\u671F\u5206\u9694\u5668",
    "show_date_dividers_desc": "\u5728\u65E5\u671F\u76F8\u5173\u6392\u5E8F\u65F6\uFF0C\u5728\u4E0D\u540C\u5929\u7684\u7B2C\u4E00\u6761\u4E4B\u524D\u663E\u793A\u65E5\u671F\u5206\u9694\u5668",
    "date_divider_format": "\u65E5\u671F\u5206\u9694\u5668\u683C\u5F0F",
    "date_divider_mode": "\u65E5\u671F\u5206\u9694\u5668",
    "date_divider_mode_desc": "\u9009\u62E9\u65E5\u671F\u5206\u9694\u5668\u7684\u663E\u793A\u6A21\u5F0F",
    "date_divider_mode_none": "\u4E0D\u4F7F\u7528",
    "date_divider_mode_year": "\u5E74",
    "date_divider_mode_month": "\u6708",
    "date_divider_mode_day": "\u65E5",
    "pinned": "\u7F6E\u9876",
    "pinned_desc": "\u5C06\u6B64\u6587\u4EF6\u56FA\u5B9A\u5728\u9876\u90E8",
    "foldernote_pinned": "\u6587\u4EF6\u5939\u7B14\u8BB0\u7F6E\u9876",
    "foldernote_pinned_desc": "\u5C06\u6587\u4EF6\u5939\u7B14\u8BB0\u56FA\u5B9A\u5728\u9876\u90E8",
    "display_minimized": "\u6700\u5C0F\u5316\u663E\u793A",
    "display_minimized_desc": "\u5C06\u6B64\u7B14\u8BB0\u4EE5\u6700\u5C0F\u5316\u65B9\u5F0F\u663E\u793A",
    // Quick Access Settings and Commands
    "quick_access_settings_title": "\u5FEB\u901F\u8BBF\u95EE\u8BBE\u7F6E",
    "quick_access_folder_name": "\u5FEB\u901F\u8BBF\u95EE\u6587\u4EF6\u5939",
    "quick_access_folder_desc": "\u8BBE\u7F6E\u201C\u6253\u5F00\u5FEB\u901F\u8BBF\u95EE\u6587\u4EF6\u5939\u201D\u547D\u4EE4\u4F7F\u7528\u7684\u6587\u4EF6\u5939",
    "quick_access_mode_name": "\u5FEB\u901F\u8BBF\u95EE\u6A21\u5F0F",
    "quick_access_mode_desc": "\u8BBE\u7F6E\u201C\u6253\u5F00\u5FEB\u901F\u8BBF\u95EE\u6A21\u5F0F\u201D\u547D\u4EE4\u4F7F\u7528\u7684\u9ED8\u8BA4\u6A21\u5F0F",
    "use_quick_access_as_new_tab_view": "\u5C06\u5FEB\u901F\u8BBF\u95EE\u7528\u4F5C\u65B0\u6807\u7B7E\u9875",
    "use_quick_access_as_new_tab_view_desc": "\u5C06\u9ED8\u8BA4\u7684\u201C\u65B0\u6807\u7B7E\u9875\u201D\u89C6\u56FE\u66FF\u6362\u4E3A\u6240\u9009\u5FEB\u901F\u8BBF\u95EE\u9009\u9879\uFF08\u6587\u4EF6\u5939\u6216\u6A21\u5F0F\uFF09\u7684\u7F51\u683C\u89C6\u56FE\u3002\u6B64\u8BBE\u7F6E\u4EC5\u5728\u201C\u9ED8\u8BA4\u6253\u5F00\u4F4D\u7F6E\u201D\u8BBE\u4E3A\u201C\u5728\u65B0\u6807\u7B7E\u9875\u6253\u5F00\u201D\u65F6\u751F\u6548\uFF01",
    "default_new_tab": "\u9ED8\u8BA4\u65B0\u6807\u7B7E\u9875",
    "use_quick_access_folder": "\u4F7F\u7528\u5FEB\u901F\u8BBF\u95EE\u6587\u4EF6\u5939",
    "use_quick_access_mode": "\u4F7F\u7528\u5FEB\u901F\u8BBF\u95EE\u6A21\u5F0F",
    "open_quick_access_folder": "\u6253\u5F00\u5FEB\u901F\u8BBF\u95EE\u6587\u4EF6\u5939",
    "open_quick_access_mode": "\u6253\u5F00\u5FEB\u901F\u8BBF\u95EE\u6A21\u5F0F"
  },
  "ja": {
    // 通知メッジ
    "bookmarks_plugin_disabled": "\u30D6\u30C3\u30AF\u30DE\u30FC\u30AF\u30D7\u30E9\u30B0\u30A4\u30F3\u3092\u6709\u52B9\u306B\u3057\u3066\u304F\u3060\u3055\u3044",
    // ボタンとラベル
    "sorting": "\u4E26\u3073\u66FF\u3048",
    "refresh": "\u66F4\u65B0",
    "reselect": "\u518D\u9078\u629E",
    "go_up": "\u4E0A\u306E\u968E\u5C64\u3078",
    "no_backlinks": "\u30D0\u30C3\u30AF\u30EA\u30F3\u30AF\u306A\u3057",
    "search": "\u691C\u7D22",
    "search_placeholder": "\u30AD\u30FC\u30EF\u30FC\u30C9\u691C\u7D22",
    "search_current_location_only": "\u73FE\u5728\u306E\u5834\u6240\u306E\u307F\u691C\u7D22",
    "search_media_files": "\u30E1\u30C7\u30A3\u30A2\u30D5\u30A1\u30A4\u30EB\u3092\u691C\u7D22",
    "cancel": "\u30AD\u30E3\u30F3\u30BB\u30EB",
    "new_note": "\u65B0\u898F\u30CE\u30FC\u30C8",
    "new_folder": "\u65B0\u898F\u30D5\u30A9\u30EB\u30C0",
    "new_canvas": "\u65B0\u898F\u30AD\u30E3\u30F3\u30D0\u30B9",
    "delete_folder": "\u524A\u9664\u30D5\u30A9\u30EB\u30C0",
    "untitled": "\u7121\u984C",
    "files": "\u30D5\u30A1\u30A4\u30EB",
    "add": "\u8FFD\u52A0",
    "root": "\u30EB\u30FC\u30C8",
    "more_options": "\u3082\u3063\u3068\u9078\u629E\u80A2",
    "add_tag_to_search": "\u691C\u7D22\u306B\u8FFD\u52A0",
    "remove_tag_from_search": "\u691C\u7D22\u304B\u3089\u524A\u9664",
    "global_search": "\u30B0\u30ED\u30FC\u30D0\u30EB\u691C\u7D22",
    "remove": "\u524A\u9664",
    "edit": "\u7DE8\u96C6",
    "delete": "\u524A\u9664",
    "save": "\u4FDD\u5B58",
    // ビュータイトル
    "grid_view_title": "\u30B0\u30EA\u30C3\u30C9\u30D3\u30E5\u30FC",
    "bookmarks_mode": "\u30D6\u30C3\u30AF\u30DE\u30FC\u30AF",
    "folder_mode": "\u30D5\u30A9\u30EB\u30C0",
    "search_results": "\u691C\u7D22\u7D50\u679C",
    "backlinks_mode": "\u30D0\u30C3\u30AF\u30EA\u30F3\u30AF",
    "outgoinglinks_mode": "\u30A2\u30A6\u30C8\u30B0\u30EA\u30F3\u30AF",
    "all_files_mode": "\u3059\u3079\u3066\u306E\u30D5\u30A1\u30A4\u30EB",
    "recent_files_mode": "\u6700\u8FD1\u306E\u30D5\u30A1\u30A4\u30EB",
    "random_note_mode": "\u30E9\u30F3\u30C0\u30E0\u30CE\u30FC\u30C8",
    "tasks_mode": "\u30BF\u30B9\u30AF",
    // 並べ替えオプション
    "sort_name_asc": "\u540D\u524D (A \u2192 Z)",
    "sort_name_desc": "\u540D\u524D (Z \u2192 A)",
    "sort_mtime_desc": "\u66F4\u65B0\u65E5\u6642 (\u65B0 \u2192 \u53E4)",
    "sort_mtime_asc": "\u66F4\u65B0\u65E5\u6642 (\u53E4 \u2192 \u65B0)",
    "sort_ctime_desc": "\u4F5C\u6210\u65E5\u6642 (\u65B0 \u2192 \u53E4)",
    "sort_ctime_asc": "\u4F5C\u6210\u65E5\u6642 (\u53E4 \u2192 \u65B0)",
    "sort_random": "\u30E9\u30F3\u30C0\u30E0",
    // 設定
    "grid_view_settings": "\u30B0\u30EA\u30C3\u30C9\u30D3\u30E5\u30FC\u8A2D\u5B9A",
    "media_files_settings": "\u30E1\u30C7\u30A3\u30A2\u30D5\u30A1\u30A4\u30EB\u8A2D\u5B9A",
    "show_media_files": "\u30E1\u30C7\u30A3\u30A2\u30D5\u30A1\u30A4\u30EB\u3092\u8868\u793A",
    "show_media_files_desc": "\u30B0\u30EA\u30C3\u30C9\u30D3\u30E5\u30FC\u3067\u30E1\u30C7\u30A3\u30A2\u30D5\u30A1\u30A4\u30EB\u3092\u8868\u793A\u3059\u308B",
    "show_video_thumbnails": "\u52D5\u753B\u306E\u30B5\u30E0\u30CD\u30A4\u30EB\u3092\u8868\u793A",
    "show_video_thumbnails_desc": "\u30B0\u30EA\u30C3\u30C9\u30D3\u30E5\u30FC\u3067\u52D5\u753B\u306E\u30B5\u30E0\u30CD\u30A4\u30EB\u3092\u8868\u793A\u3059\u308B\u3001\u7121\u52B9\u306E\u5834\u5408\u306F\u518D\u751F\u30A2\u30A4\u30B3\u30F3\u3092\u8868\u793A",
    "show_note_tags": "\u30CE\u30FC\u30C8\u306E\u30BF\u30B0\u3092\u8868\u793A",
    "show_note_tags_desc": "\u30B0\u30EA\u30C3\u30C9\u30D3\u30E5\u30FC\u3067\u30CE\u30FC\u30C8\u306E\u30BF\u30B0\u3092\u8868\u793A\u3059\u308B",
    "ignored_folders": "\u7121\u8996\u3059\u308B\u30D5\u30A9\u30EB\u30C0",
    "ignored_folders_desc": "\u7121\u8996\u3059\u308B\u30D5\u30A9\u30EB\u30C0\u3092\u8A2D\u5B9A\u3059\u308B",
    "add_ignored_folder": "\u7121\u8996\u3059\u308B\u30D5\u30A9\u30EB\u30C0\u3092\u8FFD\u52A0",
    "no_ignored_folders": "\u7121\u8996\u3059\u308B\u30D5\u30A9\u30EB\u30C0\u306F\u3042\u308A\u307E\u305B\u3093\u3002",
    "ignored_folder_patterns": "\u30D1\u30BF\u30FC\u30F3\u3067\u30D5\u30A9\u30EB\u30C0\u3068\u30D5\u30A1\u30A4\u30EB\u3092\u7121\u8996",
    "ignored_folder_patterns_desc": "\u6587\u5B57\u5217\u30D1\u30BF\u30FC\u30F3\u3092\u4F7F\u7528\u3057\u3066\u30D5\u30A9\u30EB\u30C0\u3068\u30D5\u30A1\u30A4\u30EB\u3092\u7121\u8996\u3059\u308B\uFF08\u6B63\u898F\u8868\u73FE\u3092\u30B5\u30DD\u30FC\u30C8\uFF09",
    "add_ignored_folder_pattern": "\u30D5\u30A9\u30EB\u30C0\u30D1\u30BF\u30FC\u30F3\u3092\u8FFD\u52A0",
    "ignored_folder_pattern_placeholder": "\u30D5\u30A9\u30EB\u30C0\u540D\u307E\u305F\u306F\u6B63\u898F\u8868\u73FE\u30D1\u30BF\u30FC\u30F3\u3092\u5165\u529B",
    "no_ignored_folder_patterns": "\u7121\u8996\u3059\u308B\u30D5\u30A9\u30EB\u30C0\u30D1\u30BF\u30FC\u30F3\u306F\u3042\u308A\u307E\u305B\u3093\u3002",
    "default_sort_type": "\u30C7\u30D5\u30A9\u30EB\u30C8\u306E\u4E26\u3073\u66FF\u3048",
    "default_sort_type_desc": "\u30B0\u30EA\u30C3\u30C9\u30D3\u30E5\u30FC\u3092\u958B\u3044\u305F\u3068\u304D\u306E\u30C7\u30D5\u30A9\u30EB\u30C8\u306E\u4E26\u3073\u66FF\u3048\u65B9\u6CD5\u3092\u8A2D\u5B9A",
    "note_title_field": "\u30CE\u30FC\u30C8\u30BF\u30A4\u30C8\u30EB\u30D5\u30A3\u30FC\u30EB\u30C9\u540D",
    "note_title_field_desc": "frontmatter\u3067\u30CE\u30FC\u30C8\u30BF\u30A4\u30C8\u30EB\u3068\u3057\u3066\u4F7F\u7528\u3059\u308B\u30D5\u30A3\u30FC\u30EB\u30C9\u540D\u3092\u8A2D\u5B9A",
    "note_summary_field": "\u30CE\u30FC\u30C8\u8981\u7D04\u30D5\u30A3\u30FC\u30EB\u30C9\u540D",
    "note_summary_field_desc": "frontmatter\u3067\u30CE\u30FC\u30C8\u8981\u7D04\u3068\u3057\u3066\u4F7F\u7528\u3059\u308B\u30D5\u30A3\u30FC\u30EB\u30C9\u540D\u3092\u8A2D\u5B9A",
    "modified_date_field": '"\u66F4\u65B0\u65E5"\u30D5\u30A3\u30FC\u30EB\u30C9\u540D',
    "modified_date_field_desc": "frontmatter\u3067\u66F4\u65B0\u65E5\u3068\u3057\u3066\u4F7F\u7528\u3059\u308B\u30D5\u30A3\u30FC\u30EB\u30C9\u540D\u3092\u8A2D\u5B9A",
    "created_date_field": '"\u4F5C\u6210\u65E5"\u30D5\u30A3\u30FC\u30EB\u30C9\u540D',
    "created_date_field_desc": "frontmatter\u3067\u4F5C\u6210\u65E5\u3068\u3057\u3066\u4F7F\u7528\u3059\u308B\u30D5\u30A3\u30FC\u30EB\u30C9\u540D\u3092\u8A2D\u5B9A",
    "grid_item_width": "\u30B0\u30EA\u30C3\u30C9\u30A2\u30A4\u30C6\u30E0\u306E\u5E45",
    "grid_item_width_desc": "\u30B0\u30EA\u30C3\u30C9\u30A2\u30A4\u30C6\u30E0\u306E\u5E45\u3092\u8A2D\u5B9A",
    "grid_item_height": "\u30B0\u30EA\u30C3\u30C9\u30A2\u30A4\u30C6\u30E0\u306E\u9AD8\u3055",
    "grid_item_height_desc": "\u30B0\u30EA\u30C3\u30C9\u30A2\u30A4\u30C6\u30E0\u306E\u9AD8\u3055\u3092\u8A2D\u5B9A\uFF080\u306B\u8A2D\u5B9A\u3059\u308B\u3068\u81EA\u52D5\u8ABF\u6574\uFF09",
    "image_area_width": "\u753B\u50CF\u30A8\u30EA\u30A2\u306E\u5E45",
    "image_area_width_desc": "\u753B\u50CF\u30D7\u30EC\u30D3\u30E5\u30FC\u30A8\u30EA\u30A2\u306E\u5E45\u3092\u8A2D\u5B9A",
    "image_area_height": "\u753B\u50CF\u30A8\u30EA\u30A2\u306E\u9AD8\u3055",
    "image_area_height_desc": "\u753B\u50CF\u30D7\u30EC\u30D3\u30E5\u30FC\u30A8\u30EA\u30A2\u306E\u9AD8\u3055\u3092\u8A2D\u5B9A",
    "title_font_size": "\u30BF\u30A4\u30C8\u30EB\u306E\u30D5\u30A9\u30F3\u30C8\u30B5\u30A4\u30BA",
    "title_font_size_desc": "\u30BF\u30A4\u30C8\u30EB\u306E\u30D5\u30A9\u30F3\u30C8\u30B5\u30A4\u30BA\u3092\u8A2D\u5B9A",
    "summary_length": "\u8981\u7D04\u306E\u9577\u3055",
    "summary_length_desc": "\u8981\u7D04\u306E\u9577\u3055\u3092\u8A2D\u5B9A",
    "show_code_block_in_summary": "\u8981\u7D04\u306BCodeBlock\u3092\u8868\u793A",
    "show_code_block_in_summary_desc": "\u8981\u7D04\u306BCodeBlock\u3092\u8868\u793A\u3059\u308B\u304B\u3069\u3046\u304B\u3092\u8A2D\u5B9A",
    "enable_file_watcher": "\u30D5\u30A1\u30A4\u30EB\u76E3\u8996\u3092\u6709\u52B9\u306B\u3059\u308B",
    "enable_file_watcher_desc": "\u6709\u52B9\u306B\u3059\u308B\u3068\u3001\u30D5\u30A1\u30A4\u30EB\u306E\u5909\u66F4\u3092\u81EA\u52D5\u7684\u306B\u691C\u51FA\u3057\u3066\u30D3\u30E5\u30FC\u3092\u66F4\u65B0\u3057\u307E\u3059\u3002\u7121\u52B9\u306E\u5834\u5408\u306F\u3001\u66F4\u65B0\u30DC\u30BF\u30F3\u3092\u624B\u52D5\u3067\u30AF\u30EA\u30C3\u30AF\u3059\u308B\u5FC5\u8981\u304C\u3042\u308A\u307E\u3059",
    "intercept_all_tag_clicks": "\u3059\u3079\u3066\u306E\u30BF\u30B0\u30AF\u30EA\u30C3\u30AF\u3092\u6514\u622A\u3059\u308B",
    "intercept_all_tag_clicks_desc": "\u6709\u52B9\u306B\u3059\u308B\u3068\u3001\u3059\u3079\u3066\u306E\u30BF\u30B0\u30AF\u30EA\u30C3\u30AF\u3092\u6514\u622A\u3057\u3001\u30B0\u30EA\u30C3\u30C9\u30D3\u30E5\u30FC\u3067\u30BF\u30B0\u3092\u958B\u304D\u307E\u3059",
    "reset_to_default": "\u30C7\u30D5\u30A9\u30EB\u30C8\u306B\u623B\u3059",
    "reset_to_default_desc": "\u3059\u3079\u3066\u306E\u8A2D\u5B9A\u3092\u30C7\u30D5\u30A9\u30EB\u30C8\u5024\u306B\u623B",
    "settings_reset_notice": "\u8A2D\u5B9A\u5024\u304C\u30C7\u30D5\u30A9\u30EB\u30C8\u5024\u306B\u30EA\u30BB\u30C3\u30C8\u3055\u308C\u307E\u3057\u305F",
    "ignored_folders_settings": "\u7121\u8996\u3059\u308B\u30D5\u30A9\u30EB\u30C0\u8A2D\u5B9A",
    "display_mode_settings": "\u8868\u793A\u30E2\u30FC\u30C9\u8A2D\u5B9A",
    "custom_mode_settings": "\u30AB\u30B9\u30BF\u30E0\u30E2\u30FC\u30C9\u8A2D\u5B9A",
    "add_custom_mode": "\u30AB\u30B9\u30BF\u30E0\u30E2\u30FC\u30C9\u3092\u8FFD\u52A0",
    "export": "\u30A8\u30AF\u30B9\u30DD\u30FC\u30C8",
    "import": "\u30A4\u30F3\u30DD\u30FC\u30C8",
    "no_custom_modes_to_export": "\u30A8\u30AF\u30B9\u30DD\u30FC\u30C8\u3059\u308B\u30AB\u30B9\u30BF\u30E0\u30E2\u30FC\u30C9\u304C\u3042\u308A\u307E\u305B\u3093",
    "import_success": "\u30AB\u30B9\u30BF\u30E0\u30E2\u30FC\u30C9\u3092\u30A4\u30F3\u30DD\u30FC\u30C8\u3057\u307E\u3057\u305F",
    "import_error": "\u30A4\u30F3\u30DD\u30FC\u30C8\u5931\u6557\uFF1A\u7121\u52B9\u306A\u30D5\u30A1\u30A4\u30EB\u5F62\u5F0F",
    "edit_custom_mode": "\u30AB\u30B9\u30BF\u30E0\u30E2\u30FC\u30C9\u3092\u7DE8\u96C6",
    "custom_mode_icon": "\u30A2\u30A4\u30B3\u30F3",
    "custom_mode_icon_desc": "\u30E2\u30FC\u30C9\u30E1\u30CB\u30E5\u30FC\u3067\u8868\u793A\u3059\u308B\u30A2\u30A4\u30B3\u30F3",
    "custom_mode_display_name": "\u8868\u793A\u540D",
    "custom_mode_display_name_desc": "\u30E2\u30FC\u30C9\u30E1\u30CB\u30E5\u30FC\u3067\u8868\u793A\u3059\u308B\u540D\u524D",
    "custom_mode_dataview_code": "Dataviewjs \u30B3\u30FC\u30C9",
    "custom_mode_dataview_code_desc": "\u30D5\u30A1\u30A4\u30EB\u30EA\u30B9\u30C8\u3092\u53D6\u5F97\u3059\u308B Dataviewjs \u30B3\u30FC\u30C9\u3092\u5165\u529B",
    "display_name_cannot_be_empty": "\u8868\u793A\u540D\u306F\u7A7A\u306B\u3067\u304D\u307E\u305B\u3093",
    "custom_mode": "\u30AB\u30B9\u30BF\u30E0\u30E2\u30FC\u30C9",
    "show_bookmarks_mode": "\u30D6\u30C3\u30AF\u30DE\u30FC\u30AF\u30E2\u30FC\u30C9\u3092\u8868\u793A",
    "show_search_mode": "\u691C\u7D22\u7D50\u679C\u30E2\u30FC\u30C9\u3092\u8868\u793A",
    "show_backlinks_mode": "\u30D0\u30C3\u30AF\u30EA\u30F3\u30AF\u30E2\u30FC\u30C9\u3092\u8868\u793A",
    "show_outgoinglinks_mode": "\u30A2\u30A6\u30C8\u30B0\u30EA\u30F3\u30AF\u30E2\u30FC\u30C9\u3092\u8868\u793A",
    "show_all_files_mode": "\u5168\u30D5\u30A1\u30A4\u30EB\u30E2\u30FC\u30C9\u3092\u8868\u793A",
    "show_recent_files_mode": "\u6700\u8FD1\u30D5\u30A1\u30A4\u30EB\u30E2\u30FC\u30C9\u3092\u8868\u793A",
    "recent_files_count": "\u6700\u8FD1\u30D5\u30A1\u30A4\u30EB\u30E2\u30FC\u30C9\u8868\u793A\u7B46\u6570",
    "show_random_note_mode": "\u30E9\u30F3\u30C0\u30E0\u30CE\u30FC\u30C8\u30E2\u30FC\u30C9\u3092\u8868\u793A",
    "random_note_count": "\u30E9\u30F3\u30C0\u30E0\u30CE\u30FC\u30C8\u30E2\u30FC\u30C9\u8868\u793A\u7B46\u6570",
    "random_note_notes_only": "\u30CE\u30FC\u30C8\u306E\u307F",
    "random_note_include_media_files": "\u30E1\u30C7\u30A3\u30A2\u30D5\u30A1\u30A4\u30EB\u3092\u542B\u3080",
    "show_tasks_mode": "\u30BF\u30B9\u30AF\u30E2\u30FC\u30C9\u3092\u8868\u793A",
    "task_filter": "\u30BF\u30B9\u30AF\u30D5\u30A3\u30EB\u30BF",
    "uncompleted": "\u672A\u5B8C\u4E86",
    "completed": "\u5B8C\u4E86",
    "foldernote_display_settings": "\u30D5\u30A9\u30EB\u30C0\u30CE\u30FC\u30C8\u306E\u8868\u793A\u8A2D\u5B9A",
    "foldernote_display_settings_desc": "\u30D5\u30A9\u30EB\u30C0\u30CE\u30FC\u30C8\u306E\u8868\u793A\u65B9\u6CD5\u3092\u8A2D\u5B9A\u3057\u307E\u3059",
    "all": "\u3059\u3079\u3066",
    "default": "\u30C7\u30D5\u30A9\u30EB\u30C8",
    "hidden": "\u96A0\u3059",
    // "親フォルダ"オプション設定を表示
    "show_parent_folder_item": "\u300C\u89AA\u30D5\u30A9\u30EB\u30C0\u300D\u9805\u76EE\u3092\u8868\u793A",
    "show_parent_folder_item_desc": "\u30B0\u30EA\u30C3\u30C9\u306E\u6700\u521D\u306E\u9805\u76EE\u3068\u3057\u3066\u300C\u89AA\u30D5\u30A9\u30EB\u30C0\u300D\u9805\u76EE\u3092\u8868\u793A\u3057\u307E\u3059",
    "parent_folder": "\u89AA\u30D5\u30A9\u30EB\u30C0",
    // 開く場所設定
    "default_open_location": "\u30C7\u30D5\u30A9\u30EB\u30C8\u306E\u958B\u304F\u5834\u6240",
    "default_open_location_desc": "\u30B0\u30EA\u30C3\u30C9\u30D3\u30E5\u30FC\u3092\u958B\u304F\u30C7\u30D5\u30A9\u30EB\u30C8\u306E\u5834\u6240\u3092\u8A2D\u5B9A",
    "open_in_left_sidebar": "\u5DE6\u30B5\u30A4\u30C9\u30D0\u30FC\u3067\u958B\u304F",
    "open_in_right_sidebar": "\u53F3\u30B5\u30A4\u30C9\u30D0\u30FC\u3067\u958B\u304F",
    "open_in_new_tab": "\u65B0\u3057\u3044\u30BF\u30D6\u3067\u958B\u304F",
    "reuse_existing_leaf": "\u65E2\u5B58\u306E\u30D3\u30E5\u30FC\u3092\u518D\u5229\u7528",
    "reuse_existing_leaf_desc": "\u30B0\u30EA\u30C3\u30C9\u30D3\u30E5\u30FC\u3092\u958B\u304F\u3068\u304D\u3001\u65B0\u3057\u3044\u30D3\u30E5\u30FC\u3092\u4F5C\u6210\u305B\u305A\u306B\u65E2\u5B58\u306E\u30D3\u30E5\u30FC\u3092\u512A\u5148\u4F7F\u7528",
    "custom_document_extensions": "\u30AB\u30B9\u30BF\u30E0\u6587\u66F8\u62E1\u5F35\u5B50",
    "custom_document_extensions_desc": "\u8FFD\u52A0\u306E\u6587\u66F8\u62E1\u5F35\u5B50\uFF08\u30AB\u30F3\u30DE\u533A\u5207\u308A\u3001\u30C9\u30C3\u30C8\u7121\u3057\uFF09",
    "custom_document_extensions_placeholder": "\u4F8B\uFF1Atxt,doc,docx",
    "custom_folder_icon": "\u30AB\u30B9\u30BF\u30E0\u30D5\u30A9\u30EB\u30C0\u30FC\u30A2\u30A4\u30B3\u30F3",
    "custom_folder_icon_desc": "\u30AB\u30B9\u30BF\u30E0\u30D5\u30A9\u30EB\u30C0\u30FC\u30A2\u30A4\u30B3\u30F3\uFF08Emoji\u3092\u4F7F\u7528\uFF09",
    // フォルダ選択ダイアログ
    "select_folders": "\u30D5\u30A9\u30EB\u30C0\u3092\u9078\u629E",
    "select_folders_to_ignore": "\u7121\u8996\u3059\u308B\u30D5\u30A9\u30EB\u30C0\u3092\u9078\u629E",
    "open_grid_view": "\u30B0\u30EA\u30C3\u30C9\u30D3\u30E5\u30FC\u3092\u958B\u304F",
    "open_in_grid_view": "\u30B0\u30EA\u30C3\u30C9\u30D3\u30E5\u30FC\u3067\u958B\u304F",
    "open_note_in_grid_view": "\u30B0\u30EA\u30C3\u30C9\u30D3\u30E5\u30FC\u3067\u73FE\u5728\u306E\u30CE\u30FC\u30C8\u3092\u958B\u304F",
    "open_backlinks_in_grid_view": "\u30B0\u30EA\u30C3\u30C9\u30D3\u30E5\u30FC\u3067\u30D0\u30C3\u30AF\u30EA\u30F3\u30AF\u3092\u958B\u304F",
    "open_outgoinglinks_in_grid_view": "\u30B0\u30EA\u30C3\u30C9\u30D3\u30E5\u30FC\u3067\u5916\u90E8\u30EA\u30F3\u30AF\u3092\u958B\u304F",
    "open_recent_files_in_grid_view": "\u6700\u8FD1\u306E\u30D5\u30A1\u30A4\u30EB\u3067\u73FE\u5728\u306E\u30CE\u30FC\u30C8\u3092\u958B\u304F",
    "open_settings": "\u8A2D\u5B9A\u3092\u958B\u304F",
    "open_new_grid_view": "\u65B0\u3057\u3044\u30B0\u30EA\u30C3\u30C9\u30D3\u30E5\u30FC\u3092\u958B\u304F",
    "open_in_new_grid_view": "\u65B0\u3057\u3044\u30B0\u30EA\u30C3\u30C9\u30D3\u30E5\u30FC\u3067\u958B\u304F",
    "min_mode": "\u6700\u5C0F\u5316\u30E2\u30FC\u30C9",
    "show_ignored_folders": "\u7121\u8996\u3059\u308B\u30D5\u30A9\u30EB\u30C0\u30FC\u3092\u8868\u793A",
    "delete_note": "\u30D5\u30A1\u30A4\u30EB\u3092\u524A\u9664",
    "open_folder_note": "\u30D5\u30A9\u30EB\u30C0\u30FC\u30CE\u30FC\u30C8\u3092\u958B\u304F",
    "create_folder_note": "\u30D5\u30A9\u30EB\u30C0\u30FC\u30CE\u30FC\u30C8\u3092\u4F5C\u6210",
    "delete_folder_note": "\u30D5\u30A9\u30EB\u30C0\u30FC\u30CE\u30FC\u30C8\u3092\u524A\u9664",
    "edit_folder_note_settings": "\u30D5\u30A9\u30EB\u30C0\u30FC\u30CE\u30FC\u30C8\u8A2D\u5B9A\u3092\u7DE8\u96C6",
    "ignore_folder": "\u3053\u306E\u30D5\u30A9\u30EB\u30C0\u30FC\u3092\u7121\u8996",
    "unignore_folder": "\u3053\u306E\u30D5\u30A9\u30EB\u30C0\u30FC\u3092\u7121\u8996\u89E3\u9664",
    "searching": "\u691C\u7D22\u4E2D...",
    "no_files": "\u30D5\u30A1\u30A4\u30EB\u304C\u898B\u3064\u304B\u308A\u307E\u305B\u3093",
    "filter_folders": "\u30D5\u30A9\u30EB\u30C0\u3092\u30D5\u30A3\u30EB\u30BF\u30EA\u30F3\u30B0...",
    // フォルダーノート設定ダイアログ
    "folder_note_settings": "\u30D5\u30A9\u30EB\u30C0\u30FC\u30CE\u30FC\u30C8\u8A2D\u5B9A",
    "folder_sort_type": "\u30D5\u30A9\u30EB\u30C0\u306E\u4E26\u3073\u66FF\u3048",
    "folder_sort_type_desc": "\u3053\u306E\u30D5\u30A9\u30EB\u30C0\u306E\u30C7\u30D5\u30A9\u30EB\u30C8\u306E\u4E26\u3073\u66FF\u3048\u65B9\u6CD5\u3092\u8A2D\u5B9A",
    "folder_color": "\u30D5\u30A9\u30EB\u30C0\u306E\u8272",
    "folder_color_desc": "\u3053\u306E\u30D5\u30A9\u30EB\u30C0\u306E\u8868\u793A\u8272\u3092\u8A2D\u5B9A",
    "folder_icon": "\u30D5\u30A9\u30EB\u30C0\u306E\u30A2\u30A4\u30B3\u30F3",
    "folder_icon_desc": "\u3053\u306E\u30D5\u30A9\u30EB\u30C0\u306E\u8868\u793A\u30A2\u30A4\u30B3\u30F3\u3092\u8A2D\u5B9A",
    "default_sort": "\u30C7\u30D5\u30A9\u30EB\u30C8\u306E\u4E26\u3073\u66FF\u3048",
    "no_color": "\u8272\u306A\u3057",
    "color_red": "\u8D64",
    "color_orange": "\u30AA\u30EC\u30F3\u30B8",
    "color_yellow": "\u9EC4",
    "color_green": "\u7DD1",
    "color_cyan": "\u30B7\u30A2\u30F3",
    "color_blue": "\u9752",
    "color_purple": "\u7D2B",
    "color_pink": "\u30D4\u30F3\u30AF",
    "confirm": "\u78BA\u8A8D",
    "note_attribute_settings": "\u30CE\u30FC\u30C8\u5C5E\u6027\u8A2D\u5B9A",
    "note_title": "\u30CE\u30FC\u30C8\u30BF\u30A4\u30C8\u30EB",
    "note_title_desc": "\u3053\u306E\u30CE\u30FC\u30C8\u306E\u8868\u793A\u30BF\u30A4\u30C8\u30EB\u3092\u8A2D\u5B9A",
    "note_summary": "\u30CE\u30FC\u30C8\u8981\u7D04",
    "note_summary_desc": "\u3053\u306E\u30CE\u30FC\u30C8\u306E\u8868\u793A\u8981\u7D04\u3092\u8A2D\u5B9A",
    "note_color": "\u30CE\u30FC\u30C8\u8272",
    "note_color_desc": "\u3053\u306E\u30CE\u30FC\u30C8\u306E\u8868\u793A\u8272\u3092\u8A2D\u5B9A",
    "set_note_attribute": "\u30CE\u30FC\u30C8\u5C5E\u6027\u8A2D\u5B9A",
    "rename_folder": "\u30D5\u30A9\u30EB\u30C0\u3092\u518D\u547D\u540D",
    "enter_new_folder_name": "\u65B0\u3057\u3044\u30D5\u30A9\u30EB\u30C0\u540D\u3092\u5165\u529B",
    "search_selection_in_grid_view": "... \u3092\u30B0\u30EA\u30C3\u30C9\u30D3\u30E5\u30FC\u3067\u691C\u7D22",
    "show_date_dividers": "\u65E5\u4ED8\u533A\u5207\u308A\u3092\u8868\u793A",
    "show_date_dividers_desc": "\u65E5\u4ED8\u95A2\u9023\u306E\u4E26\u3073\u66FF\u3048\u6642\u3001\u5404\u65E5\u306E\u6700\u521D\u306E\u30A2\u30A4\u30C6\u30E0\u306E\u524D\u306B\u65E5\u4ED8\u533A\u5207\u308A\u3092\u8868\u793A\u3059\u308B",
    "date_divider_format": "\u65E5\u4ED8\u533A\u5207\u308A\u30D5\u30A9\u30FC\u30DE\u30C3\u30C8",
    "date_divider_mode": "\u65E5\u4ED8\u533A\u5207\u308A",
    "date_divider_mode_desc": "\u65E5\u4ED8\u533A\u5207\u308A\u306E\u8868\u793A\u30E2\u30FC\u30C9\u3092\u9078\u629E",
    "date_divider_mode_none": "\u4F7F\u7528\u3057\u306A\u3044",
    "date_divider_mode_year": "\u5E74",
    "date_divider_mode_month": "\u6708",
    "date_divider_mode_day": "\u65E5",
    "pinned": "\u30D4\u30F3\u7559\u3081",
    "pinned_desc": "\u3053\u306E\u30D5\u30A1\u30A4\u30EB\u3092\u6700\u4E0A\u90E8\u306B\u56FA\u5B9A\u3059\u308B",
    "foldernote_pinned": "\u30D5\u30A9\u30EB\u30C0\u30CE\u30FC\u30C8\u3092\u30D4\u30F3\u7559\u3081",
    "foldernote_pinned_desc": "\u30D5\u30A9\u30EB\u30C0\u30FC\u30CE\u30FC\u30C8\u3092\u6700\u4E0A\u90E8\u306B\u56FA\u5B9A",
    "display_minimized": "\u6700\u5C0F\u5316\u8868\u793A",
    "display_minimized_desc": "\u3053\u306E\u30CE\u30FC\u30C8\u3092\u6700\u5C0F\u5316\u30E2\u30FC\u30C9\u3067\u8868\u793A",
    //TODO: please check the translation!
    // Quick Access Settings and Commands
    "quick_access_settings_title": "\u30AF\u30A4\u30C3\u30AF\u30A2\u30AF\u30BB\u30B9\u8A2D\u5B9A",
    "quick_access_folder_name": "\u30AF\u30A4\u30C3\u30AF\u30A2\u30AF\u30BB\u30B9\u30D5\u30A9\u30EB\u30C0\u30FC",
    "quick_access_folder_desc": "\u300C\u30AF\u30A4\u30C3\u30AF\u30A2\u30AF\u30BB\u30B9 \u30D5\u30A9\u30EB\u30C0\u30FC\u3092\u958B\u304F\u300D\u30B3\u30DE\u30F3\u30C9\u3067\u4F7F\u7528\u3059\u308B\u30D5\u30A9\u30EB\u30C0\u30FC\u3092\u8A2D\u5B9A\u3057\u307E\u3059",
    "quick_access_mode_name": "\u30AF\u30A4\u30C3\u30AF\u30A2\u30AF\u30BB\u30B9\u30E2\u30FC\u30C9",
    "quick_access_mode_desc": "\u300C\u30AF\u30A4\u30C3\u30AF\u30A2\u30AF\u30BB\u30B9\u30E2\u30FC\u30C9\u3092\u958B\u304F\u300D\u30B3\u30DE\u30F3\u30C9\u3067\u4F7F\u7528\u3059\u308B\u65E2\u5B9A\u306E\u30E2\u30FC\u30C9\u3092\u8A2D\u5B9A\u3057\u307E\u3059",
    "use_quick_access_as_new_tab_view": "\u30AF\u30A4\u30C3\u30AF\u30A2\u30AF\u30BB\u30B9\u3092\u65B0\u3057\u3044\u30BF\u30D6\u3068\u3057\u3066\u4F7F\u7528\u3059\u308B",
    "use_quick_access_as_new_tab_view_desc": "\u65E2\u5B9A\u306E\u300C\u65B0\u3057\u3044\u30BF\u30D6\u300D\u306E\u8868\u793A\u3092\u3001\u9078\u629E\u3057\u305F\u30AF\u30A4\u30C3\u30AF\u30A2\u30AF\u30BB\u30B9\u30AA\u30D7\u30B7\u30E7\u30F3\uFF08\u30D5\u30A9\u30EB\u30C0\u30FC\u307E\u305F\u306F\u30E2\u30FC\u30C9\uFF09\u306E\u30B0\u30EA\u30C3\u30C9\u8868\u793A\u306B\u7F6E\u304D\u63DB\u3048\u307E\u3059\u3002\u3053\u306E\u8A2D\u5B9A\u306F\u3001\u300C\u30C7\u30D5\u30A9\u30EB\u30C8\u306E\u958B\u304F\u5834\u6240\u300D\u304C\u300C\u65B0\u3057\u3044\u30BF\u30D6\u3067\u958B\u304F\u300D\u306B\u8A2D\u5B9A\u3055\u308C\u3066\u3044\u308B\u5834\u5408\u306B\u306E\u307F\u6709\u52B9\u3067\u3059\uFF01",
    "default_new_tab": "\u65E2\u5B9A\u306E\u65B0\u3057\u3044\u30BF\u30D6",
    "use_quick_access_folder": "\u30AF\u30A4\u30C3\u30AF\u30A2\u30AF\u30BB\u30B9\u30D5\u30A9\u30EB\u30C0\u30FC\u3092\u4F7F\u7528\u3059\u308B",
    "use_quick_access_mode": "\u30AF\u30A4\u30C3\u30AF\u30A2\u30AF\u30BB\u30B9\u30E2\u30FC\u30C9\u3092\u4F7F\u7528\u3059\u308B",
    "open_quick_access_folder": "\u30AF\u30A4\u30C3\u30AF\u30A2\u30AF\u30BB\u30B9 \u30D5\u30A9\u30EB\u30C0\u30FC\u3092\u958B\u304F",
    "open_quick_access_mode": "\u30AF\u30A4\u30C3\u30AF\u30A2\u30AF\u30BB\u30B9\u30E2\u30FC\u30C9\u3092\u958B\u304F"
  },
  "ru": {
    // Notifications
    "bookmarks_plugin_disabled": "\u041F\u043E\u0436\u0430\u043B\u0443\u0439\u0441\u0442\u0430, \u0441\u043D\u0430\u0447\u0430\u043B\u0430 \u0432\u043A\u043B\u044E\u0447\u0438\u0442\u0435 \u043F\u043B\u0430\u0433\u0438\u043D \u0417\u0430\u043A\u043B\u0430\u0434\u043A\u0438",
    // Buttons and Labels
    "sorting": "\u0421\u043E\u0440\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u043F\u043E",
    "refresh": "\u041E\u0431\u043D\u043E\u0432\u0438\u0442\u044C",
    "reselect": "\u041F\u0435\u0440\u0435\u0432\u044B\u0431\u0440\u0430\u0442\u044C",
    "go_up": "\u0412\u0432\u0435\u0440\u0445",
    "no_backlinks": "\u041D\u0435\u0442 \u043E\u0431\u0440\u0430\u0442\u043D\u044B\u0445 \u0441\u0441\u044B\u043B\u043E\u043A",
    "search": "\u041F\u043E\u0438\u0441\u043A",
    "search_placeholder": "\u041A\u043B\u044E\u0447\u0435\u0432\u043E\u0435 \u0441\u043B\u043E\u0432\u043E \u0434\u043B\u044F \u043F\u043E\u0438\u0441\u043A\u0430",
    "search_current_location_only": "\u0418\u0441\u043A\u0430\u0442\u044C \u0442\u043E\u043B\u044C\u043A\u043E \u0432 \u0442\u0435\u043A\u0443\u0449\u0435\u043C \u0440\u0430\u0441\u043F\u043E\u043B\u043E\u0436\u0435\u043D\u0438\u0438",
    "search_media_files": "\u0418\u0441\u043A\u0430\u0442\u044C \u043C\u0435\u0434\u0438\u0430\u0444\u0430\u0439\u043B\u044B",
    "cancel": "\u041E\u0442\u043C\u0435\u043D\u0430",
    "new_note": "\u041D\u043E\u0432\u0430\u044F \u0437\u0430\u043C\u0435\u0442\u043A\u0430",
    "new_folder": "\u041D\u043E\u0432\u0430\u044F \u043F\u0430\u043F\u043A\u0430",
    "new_canvas": "\u041D\u043E\u0432\u044B\u0439 \u043A\u0430\u043D\u0432\u0430\u0441",
    "delete_folder": "\u0423\u0434\u0430\u043B\u0438\u0442\u044C \u043F\u0430\u043F\u043A\u0443",
    "untitled": "\u0411\u0435\u0437 \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u044F",
    "files": "\u0444\u0430\u0439\u043B\u044B",
    "add": "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C",
    "root": "\u041A\u043E\u0440\u0435\u043D\u044C",
    "more_options": "\u0411\u043E\u043B\u044C\u0448\u0435 \u043E\u043F\u0446\u0438\u0439",
    "add_tag_to_search": "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0432 \u043F\u043E\u0438\u0441\u043A",
    "remove_tag_from_search": "\u0423\u0434\u0430\u043B\u0438\u0442\u044C \u0438\u0437 \u043F\u043E\u0438\u0441\u043A\u0430",
    "global_search": "\u0413\u043B\u043E\u0431\u0430\u043B\u044C\u043D\u044B\u0439 \u043F\u043E\u0438\u0441\u043A",
    "remove": "\u0423\u0434\u0430\u043B\u0438\u0442\u044C",
    "edit": "\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C",
    "delete": "\u0423\u0434\u0430\u043B\u0438\u0442\u044C",
    "save": "\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C",
    // View Titles
    "grid_view_title": "\u0421\u0435\u0442\u043E\u0447\u043D\u044B\u0439 \u0432\u0438\u0434",
    "bookmarks_mode": "\u0417\u0430\u043A\u043B\u0430\u0434\u043A\u0438",
    "folder_mode": "\u041F\u0430\u043F\u043A\u0430",
    "search_results": "\u0420\u0435\u0437\u0443\u043B\u044C\u0442\u0430\u0442\u044B \u043F\u043E\u0438\u0441\u043A\u0430",
    "backlinks_mode": "\u041E\u0431\u0440\u0430\u0442\u043D\u044B\u0435 \u0441\u0441\u044B\u043B\u043A\u0438",
    "outgoinglinks_mode": "\u0418\u0441\u0445\u043E\u0434\u044F\u0449\u0438\u0435 \u0441\u0441\u044B\u043B\u043A\u0438",
    "all_files_mode": "\u0412\u0441\u0435 \u0444\u0430\u0439\u043B\u044B",
    "recent_files_mode": "\u041D\u0435\u0434\u0430\u0432\u043D\u0438\u0435 \u0444\u0430\u0439\u043B\u044B",
    "random_note_mode": "\u0421\u043B\u0443\u0447\u0430\u0439\u043D\u0430\u044F \u0437\u0430\u043C\u0435\u0442\u043A\u0430",
    "tasks_mode": "\u0417\u0430\u0434\u0430\u0447\u0438",
    // Sort Options
    "sort_name_asc": "\u0418\u043C\u044F (\u0410 \u2192 \u042F)",
    "sort_name_desc": "\u0418\u043C\u044F (\u042F \u2192 \u0410)",
    "sort_mtime_desc": "\u0418\u0437\u043C\u0435\u043D\u0435\u043D\u043E (\u041D\u043E\u0432\u043E\u0435 \u2192 \u0421\u0442\u0430\u0440\u043E\u0435)",
    "sort_mtime_asc": "\u0418\u0437\u043C\u0435\u043D\u0435\u043D\u043E (\u0421\u0442\u0430\u0440\u043E\u0435 \u2192 \u041D\u043E\u0432\u043E\u0435)",
    "sort_ctime_desc": "\u0421\u043E\u0437\u0434\u0430\u043D\u043E (\u041D\u043E\u0432\u043E\u0435 \u2192 \u0421\u0442\u0430\u0440\u043E\u0435)",
    "sort_ctime_asc": "\u0421\u043E\u0437\u0434\u0430\u043D\u043E (\u0421\u0442\u0430\u0440\u043E\u0435 \u2192 \u041D\u043E\u0432\u043E\u0435)",
    "sort_random": "\u0421\u043B\u0443\u0447\u0430\u0439\u043D\u043E",
    // Settings
    "grid_view_settings": "\u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 \u0441\u0435\u0442\u043E\u0447\u043D\u043E\u0433\u043E \u0432\u0438\u0434\u0430",
    "media_files_settings": "\u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 \u043C\u0435\u0434\u0438\u0430\u0444\u0430\u0439\u043B\u043E\u0432",
    "show_media_files": "\u041F\u043E\u043A\u0430\u0437\u044B\u0432\u0430\u0442\u044C \u043C\u0435\u0434\u0438\u0430\u0444\u0430\u0439\u043B\u044B",
    "show_media_files_desc": "\u041E\u0442\u043E\u0431\u0440\u0430\u0436\u0430\u0442\u044C \u043C\u0435\u0434\u0438\u0430\u0444\u0430\u0439\u043B\u044B \u0432 \u0441\u0435\u0442\u043E\u0447\u043D\u043E\u043C \u0432\u0438\u0434\u0435",
    "show_video_thumbnails": "\u041F\u043E\u043A\u0430\u0437\u044B\u0432\u0430\u0442\u044C \u043C\u0438\u043D\u0438\u0430\u0442\u044E\u0440\u044B \u0432\u0438\u0434\u0435\u043E",
    "show_video_thumbnails_desc": "\u041E\u0442\u043E\u0431\u0440\u0430\u0436\u0430\u0442\u044C \u043C\u0438\u043D\u0438\u0430\u0442\u044E\u0440\u044B \u0434\u043B\u044F \u0432\u0438\u0434\u0435\u043E \u0432 \u0441\u0435\u0442\u043E\u0447\u043D\u043E\u043C \u0432\u0438\u0434\u0435, \u043F\u043E\u043A\u0430\u0437\u044B\u0432\u0430\u0435\u0442 \u0437\u043D\u0430\u0447\u043E\u043A \u0432\u043E\u0441\u043F\u0440\u043E\u0438\u0437\u0432\u0435\u0434\u0435\u043D\u0438\u044F, \u0435\u0441\u043B\u0438 \u043E\u0442\u043A\u043B\u044E\u0447\u0435\u043D\u043E",
    "show_note_tags": "\u041F\u043E\u043A\u0430\u0437\u044B\u0432\u0430\u0442\u044C \u0442\u0435\u0433\u0438 \u0437\u0430\u043C\u0435\u0442\u043E\u043A",
    "show_note_tags_desc": "\u041E\u0442\u043E\u0431\u0440\u0430\u0436\u0430\u0442\u044C \u0442\u0435\u0433\u0438 \u0434\u043B\u044F \u0437\u0430\u043C\u0435\u0442\u043E\u043A \u0432 \u0441\u0435\u0442\u043E\u0447\u043D\u043E\u043C \u0432\u0438\u0434\u0435",
    "ignored_folders": "\u0418\u0433\u043D\u043E\u0440\u0438\u0440\u0443\u0435\u043C\u044B\u0435 \u043F\u0430\u043F\u043A\u0438",
    "ignored_folders_desc": "\u0423\u043A\u0430\u0436\u0438\u0442\u0435 \u043F\u0430\u043F\u043A\u0438 \u0434\u043B\u044F \u0438\u0433\u043D\u043E\u0440\u0438\u0440\u043E\u0432\u0430\u043D\u0438\u044F \u0437\u0434\u0435\u0441\u044C",
    "add_ignored_folder": "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0438\u0433\u043D\u043E\u0440\u0438\u0440\u0443\u0435\u043C\u0443\u044E \u043F\u0430\u043F\u043A\u0443",
    "no_ignored_folders": "\u041D\u0435\u0442 \u0438\u0433\u043D\u043E\u0440\u0438\u0440\u0443\u0435\u043C\u044B\u0445 \u043F\u0430\u043F\u043E\u043A.",
    "ignored_folder_patterns": "\u0418\u0433\u043D\u043E\u0440\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u043F\u0430\u043F\u043A\u0438 \u0438 \u0444\u0430\u0439\u043B\u044B \u043F\u043E \u0448\u0430\u0431\u043B\u043E\u043D\u0443",
    "ignored_folder_patterns_desc": "\u0418\u0441\u043F\u043E\u043B\u044C\u0437\u0443\u0439\u0442\u0435 \u0441\u0442\u0440\u043E\u043A\u043E\u0432\u044B\u0435 \u0448\u0430\u0431\u043B\u043E\u043D\u044B \u0434\u043B\u044F \u0438\u0433\u043D\u043E\u0440\u0438\u0440\u043E\u0432\u0430\u043D\u0438\u044F \u043F\u0430\u043F\u043E\u043A \u0438 \u0444\u0430\u0439\u043B\u043E\u0432 (\u043F\u043E\u0434\u0434\u0435\u0440\u0436\u0438\u0432\u0430\u0435\u0442 \u0440\u0435\u0433\u0443\u043B\u044F\u0440\u043D\u044B\u0435 \u0432\u044B\u0440\u0430\u0436\u0435\u043D\u0438\u044F)",
    "add_ignored_folder_pattern": "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0448\u0430\u0431\u043B\u043E\u043D \u043F\u0430\u043F\u043A\u0438",
    "ignored_folder_pattern_placeholder": "\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u0438\u043C\u044F \u043F\u0430\u043F\u043A\u0438 \u0438\u043B\u0438 \u0448\u0430\u0431\u043B\u043E\u043D \u0440\u0435\u0433\u0443\u043B\u044F\u0440\u043D\u043E\u0433\u043E \u0432\u044B\u0440\u0430\u0436\u0435\u043D\u0438\u044F",
    "no_ignored_folder_patterns": "\u041D\u0435\u0442 \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u0432 \u0438\u0433\u043D\u043E\u0440\u0438\u0440\u0443\u0435\u043C\u044B\u0445 \u043F\u0430\u043F\u043E\u043A.",
    "default_sort_type": "\u0422\u0438\u043F \u0441\u043E\u0440\u0442\u0438\u0440\u043E\u0432\u043A\u0438 \u043F\u043E \u0443\u043C\u043E\u043B\u0447\u0430\u043D\u0438\u044E",
    "default_sort_type_desc": "\u0423\u0441\u0442\u0430\u043D\u043E\u0432\u0438\u0442\u0435 \u043C\u0435\u0442\u043E\u0434 \u0441\u043E\u0440\u0442\u0438\u0440\u043E\u0432\u043A\u0438 \u043F\u043E \u0443\u043C\u043E\u043B\u0447\u0430\u043D\u0438\u044E \u043F\u0440\u0438 \u043E\u0442\u043A\u0440\u044B\u0442\u0438\u0438 \u0441\u0435\u0442\u043E\u0447\u043D\u043E\u0433\u043E \u0432\u0438\u0434\u0430",
    "note_title_field": '\u0418\u043C\u044F \u043F\u043E\u043B\u044F "\u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 \u0437\u0430\u043C\u0435\u0442\u043A\u0438"',
    "note_title_field_desc": "\u0423\u043A\u0430\u0436\u0438\u0442\u0435 \u0438\u043C\u044F \u043F\u043E\u043B\u044F \u0432 \u043C\u0435\u0442\u0430\u0434\u0430\u043D\u043D\u044B\u0445 \u0434\u043B\u044F \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u043D\u0438\u044F \u0432 \u043A\u0430\u0447\u0435\u0441\u0442\u0432\u0435 \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u044F \u0437\u0430\u043C\u0435\u0442\u043A\u0438",
    "note_summary_field": '\u0418\u043C\u044F \u043F\u043E\u043B\u044F "\u041A\u0440\u0430\u0442\u043A\u043E\u0435 \u043E\u043F\u0438\u0441\u0430\u043D\u0438\u0435"',
    "note_summary_field_desc": "\u0423\u043A\u0430\u0436\u0438\u0442\u0435 \u0438\u043C\u044F \u043F\u043E\u043B\u044F \u0432 \u043C\u0435\u0442\u0430\u0434\u0430\u043D\u043D\u044B\u0445 \u0434\u043B\u044F \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u043D\u0438\u044F \u0432 \u043A\u0430\u0447\u0435\u0441\u0442\u0432\u0435 \u043A\u0440\u0430\u0442\u043A\u043E\u0433\u043E \u043E\u043F\u0438\u0441\u0430\u043D\u0438\u044F",
    "modified_date_field": '\u0418\u043C\u044F \u043F\u043E\u043B\u044F "\u0414\u0430\u0442\u0430 \u0438\u0437\u043C\u0435\u043D\u0435\u043D\u0438\u044F"',
    "modified_date_field_desc": "\u0423\u043A\u0430\u0436\u0438\u0442\u0435 \u0438\u043C\u044F \u043F\u043E\u043B\u044F \u0432 \u043C\u0435\u0442\u0430\u0434\u0430\u043D\u043D\u044B\u0445 \u0434\u043B\u044F \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u043D\u0438\u044F \u0432 \u043A\u0430\u0447\u0435\u0441\u0442\u0432\u0435 \u0434\u0430\u0442\u044B \u0438\u0437\u043C\u0435\u043D\u0435\u043D\u0438\u044F",
    "created_date_field": '\u0418\u043C\u044F \u043F\u043E\u043B\u044F "\u0414\u0430\u0442\u0430 \u0441\u043E\u0437\u0434\u0430\u043D\u0438\u044F"',
    "created_date_field_desc": "\u0423\u043A\u0430\u0436\u0438\u0442\u0435 \u0438\u043C\u044F \u043F\u043E\u043B\u044F \u0432 \u043C\u0435\u0442\u0430\u0434\u0430\u043D\u043D\u044B\u0445 \u0434\u043B\u044F \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u043D\u0438\u044F \u0432 \u043A\u0430\u0447\u0435\u0441\u0442\u0432\u0435 \u0434\u0430\u0442\u044B \u0441\u043E\u0437\u0434\u0430\u043D\u0438\u044F",
    "grid_item_width": "\u0428\u0438\u0440\u0438\u043D\u0430 \u044D\u043B\u0435\u043C\u0435\u043D\u0442\u0430 \u0441\u0435\u0442\u043A\u0438",
    "grid_item_width_desc": "\u0423\u0441\u0442\u0430\u043D\u043E\u0432\u0438\u0442\u0435 \u0448\u0438\u0440\u0438\u043D\u0443 \u044D\u043B\u0435\u043C\u0435\u043D\u0442\u043E\u0432 \u0441\u0435\u0442\u043A\u0438",
    "grid_item_height": "\u0412\u044B\u0441\u043E\u0442\u0430 \u044D\u043B\u0435\u043C\u0435\u043D\u0442\u0430 \u0441\u0435\u0442\u043A\u0438",
    "grid_item_height_desc": "\u0423\u0441\u0442\u0430\u043D\u043E\u0432\u0438\u0442\u0435 \u0432\u044B\u0441\u043E\u0442\u0443 \u044D\u043B\u0435\u043C\u0435\u043D\u0442\u043E\u0432 \u0441\u0435\u0442\u043A\u0438 (0 \u0434\u043B\u044F \u0430\u0432\u0442\u043E\u043C\u0430\u0442\u0438\u0447\u0435\u0441\u043A\u043E\u0439 \u043D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438)",
    "image_area_width": "\u0428\u0438\u0440\u0438\u043D\u0430 \u043E\u0431\u043B\u0430\u0441\u0442\u0438 \u0438\u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u044F",
    "image_area_width_desc": "\u0423\u0441\u0442\u0430\u043D\u043E\u0432\u0438\u0442\u0435 \u0448\u0438\u0440\u0438\u043D\u0443 \u043E\u0431\u043B\u0430\u0441\u0442\u0438 \u043F\u0440\u0435\u0434\u0432\u0430\u0440\u0438\u0442\u0435\u043B\u044C\u043D\u043E\u0433\u043E \u043F\u0440\u043E\u0441\u043C\u043E\u0442\u0440\u0430 \u0438\u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u044F",
    "image_area_height": "\u0412\u044B\u0441\u043E\u0442\u0430 \u043E\u0431\u043B\u0430\u0441\u0442\u0438 \u0438\u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u044F",
    "image_area_height_desc": "\u0423\u0441\u0442\u0430\u043D\u043E\u0432\u0438\u0442\u0435 \u0432\u044B\u0441\u043E\u0442\u0443 \u043E\u0431\u043B\u0430\u0441\u0442\u0438 \u043F\u0440\u0435\u0434\u0432\u0430\u0440\u0438\u0442\u0435\u043B\u044C\u043D\u043E\u0433\u043E \u043F\u0440\u043E\u0441\u043C\u043E\u0442\u0440\u0430 \u0438\u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u044F",
    "title_font_size": "\u0420\u0430\u0437\u043C\u0435\u0440 \u0448\u0440\u0438\u0444\u0442\u0430 \u0437\u0430\u0433\u043E\u043B\u043E\u0432\u043A\u0430",
    "title_font_size_desc": "\u0423\u0441\u0442\u0430\u043D\u043E\u0432\u0438\u0442\u0435 \u0440\u0430\u0437\u043C\u0435\u0440 \u0448\u0440\u0438\u0444\u0442\u0430 \u0437\u0430\u0433\u043E\u043B\u043E\u0432\u043A\u0430",
    "summary_length": "\u0414\u043B\u0438\u043D\u0430 \u043A\u0440\u0430\u0442\u043A\u043E\u0433\u043E \u043E\u043F\u0438\u0441\u0430\u043D\u0438\u044F",
    "summary_length_desc": "\u0423\u0441\u0442\u0430\u043D\u043E\u0432\u0438\u0442\u0435 \u0434\u043B\u0438\u043D\u0443 \u043A\u0440\u0430\u0442\u043A\u043E\u0433\u043E \u043E\u043F\u0438\u0441\u0430\u043D\u0438\u044F",
    "show_code_block_in_summary": "\u041F\u043E\u043A\u0430\u0437\u044B\u0432\u0430\u0442\u044C CodeBlock \u0432 \u043A\u0440\u0430\u0442\u043A\u043E\u043C \u043E\u043F\u0438\u0441\u0430\u043D\u0438\u0438",
    "show_code_block_in_summary_desc": "\u0423\u0441\u0442\u0430\u043D\u043E\u0432\u0438\u0442\u0435, \u0447\u0442\u043E\u0431\u044B \u043F\u043E\u043A\u0430\u0437\u044B\u0432\u0430\u0442\u044C CodeBlock \u0432 \u043A\u0440\u0430\u0442\u043A\u043E\u043C \u043E\u043F\u0438\u0441\u0430\u043D\u0438\u0438",
    "enable_file_watcher": "\u0412\u043A\u043B\u044E\u0447\u0438\u0442\u044C \u043D\u0430\u0431\u043B\u044E\u0434\u0435\u043D\u0438\u0435 \u0437\u0430 \u0444\u0430\u0439\u043B\u0430\u043C\u0438",
    "enable_file_watcher_desc": "\u041F\u0440\u0438 \u0432\u043A\u043B\u044E\u0447\u0435\u043D\u0438\u0438 \u0432\u0438\u0434 \u0431\u0443\u0434\u0435\u0442 \u0430\u0432\u0442\u043E\u043C\u0430\u0442\u0438\u0447\u0435\u0441\u043A\u0438 \u043E\u0431\u043D\u043E\u0432\u043B\u044F\u0442\u044C\u0441\u044F \u043F\u0440\u0438 \u0438\u0437\u043C\u0435\u043D\u0435\u043D\u0438\u0438 \u0444\u0430\u0439\u043B\u043E\u0432. \u0415\u0441\u043B\u0438 \u043E\u0442\u043A\u043B\u044E\u0447\u0435\u043D\u043E, \u043D\u0443\u0436\u043D\u043E \u0432\u0440\u0443\u0447\u043D\u0443\u044E \u043D\u0430\u0436\u0438\u043C\u0430\u0442\u044C \u043A\u043D\u043E\u043F\u043A\u0443 \u043E\u0431\u043D\u043E\u0432\u043B\u0435\u043D\u0438\u044F",
    "intercept_all_tag_clicks": "\u041F\u043E\u0439\u043C\u0430\u0442\u044C \u0432\u0441\u0435 \u043A\u043B\u0438\u043A\u0438 \u043F\u043E \u0442\u0435\u0433\u0443",
    "intercept_all_tag_clicks_desc": "\u041F\u0440\u0438 \u0432\u043A\u043B\u044E\u0447\u0435\u043D\u0438\u0438 \u0432\u0441\u0435 \u043A\u043B\u0438\u043A\u0438 \u043F\u043E \u0442\u0435\u0433\u0443 \u0431\u0443\u0434\u0443\u0442 \u043F\u043E\u0439\u043C\u0430\u0442\u044C\u0441\u044F \u0438 \u043E\u0442\u043A\u0440\u044B\u0442\u044C\u0441\u044F \u0432 \u0441\u0435\u0442\u043A\u0435",
    "reset_to_default": "\u0421\u0431\u0440\u043E\u0441\u0438\u0442\u044C \u043D\u0430 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u044F \u043F\u043E \u0443\u043C\u043E\u043B\u0447\u0430\u043D\u0438\u044E",
    "reset_to_default_desc": "\u0421\u0431\u0440\u043E\u0441\u0438\u0442\u044C \u0432\u0441\u0435 \u043D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 \u043D\u0430 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u044F \u043F\u043E \u0443\u043C\u043E\u043B\u0447\u0430\u043D\u0438\u044E",
    "settings_reset_notice": "\u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 \u0441\u0431\u0440\u043E\u0448\u0435\u043D\u044B \u043D\u0430 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u044F \u043F\u043E \u0443\u043C\u043E\u043B\u0447\u0430\u043D\u0438\u044E",
    "ignored_folders_settings": "\u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 \u0438\u0433\u043D\u043E\u0440\u0438\u0440\u0443\u0435\u043C\u044B\u0445 \u043F\u0430\u043F\u043E\u043A",
    "display_mode_settings": "\u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 \u0440\u0435\u0436\u0438\u043C\u0430 \u043E\u0442\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u044F",
    "custom_mode_settings": "\u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 \u0440\u0435\u0436\u0438\u043C\u0430 \u043E\u0442\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u044F",
    "add_custom_mode": "\u0414\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0440\u0435\u0436\u0438\u043C \u043E\u0442\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u044F",
    "export": "\u042D\u043A\u0441\u043F\u043E\u0440\u0442",
    "import": "\u0418\u043C\u043F\u043E\u0440\u0442",
    "no_custom_modes_to_export": "\u041D\u0435\u0442 \u0440\u0435\u0436\u0438\u043C\u043E\u0432 \u043E\u0442\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u044F \u0434\u043B\u044F \u044D\u043A\u0441\u043F\u043E\u0440\u0442\u0430",
    "import_success": "\u0420\u0435\u0436\u0438\u043C\u044B \u043E\u0442\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u044F \u0443\u0441\u043F\u0435\u0448\u043D\u043E \u0438\u043C\u043F\u043E\u0440\u0442\u0438\u0440\u043E\u0432\u0430\u043D\u044B",
    "import_error": "\u041E\u0448\u0438\u0431\u043A\u0430 \u0438\u043C\u043F\u043E\u0440\u0442\u0430: \u043D\u0435\u0434\u043E\u043F\u0443\u0441\u0442\u0438\u043C\u044B\u0439 \u0444\u043E\u0440\u043C\u0430\u0442 \u0444\u0430\u0439\u043B\u0430",
    "edit_custom_mode": "\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0440\u0435\u0436\u0438\u043C \u043E\u0442\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u044F",
    "custom_mode_icon": "\u0418\u043A\u043E\u043D\u043A\u0430",
    "custom_mode_icon_desc": "\u0418\u043A\u043E\u043D\u043A\u0430, \u043E\u0442\u043E\u0431\u0440\u0430\u0436\u0430\u0435\u043C\u0430\u044F \u0432 \u043C\u0435\u043D\u044E \u0440\u0435\u0436\u0438\u043C\u043E\u0432",
    "custom_mode_display_name": "\u041E\u0442\u043E\u0431\u0440\u0430\u0436\u0430\u0435\u043C\u043E\u0435 \u0438\u043C\u044F",
    "custom_mode_display_name_desc": "\u041E\u0442\u043E\u0431\u0440\u0430\u0436\u0430\u0435\u043C\u043E\u0435 \u0438\u043C\u044F \u0432 \u043C\u0435\u043D\u044E \u0440\u0435\u0436\u0438\u043C\u043E\u0432",
    "custom_mode_dataview_code": "\u041A\u043E\u0434 Dataview",
    "custom_mode_dataview_code_desc": "\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u043A\u043E\u0434 Dataview \u0434\u043B\u044F \u043F\u043E\u043B\u0443\u0447\u0435\u043D\u0438\u044F \u0441\u043F\u0438\u0441\u043A\u0430 \u0444\u0430\u0439\u043B\u043E\u0432",
    "display_name_cannot_be_empty": "\u041E\u0442\u043E\u0431\u0440\u0430\u0436\u0430\u0435\u043C\u043E\u0435 \u0438\u043C\u044F \u043D\u0435 \u043C\u043E\u0436\u0435\u0442 \u0431\u044B\u0442\u044C \u043F\u0443\u0441\u0442\u044B\u043C",
    "custom_mode": "\u0420\u0435\u0436\u0438\u043C \u043E\u0442\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u044F",
    "show_bookmarks_mode": "\u041F\u043E\u043A\u0430\u0437\u044B\u0432\u0430\u0442\u044C \u0440\u0435\u0436\u0438\u043C \u0437\u0430\u043A\u043B\u0430\u0434\u043E\u043A",
    "show_search_mode": "\u041F\u043E\u043A\u0430\u0437\u044B\u0432\u0430\u0442\u044C \u0440\u0435\u0436\u0438\u043C \u0440\u0435\u0437\u0443\u043B\u044C\u0442\u0430\u0442\u043E\u0432 \u043F\u043E\u0438\u0441\u043A\u0430",
    "show_backlinks_mode": "\u041F\u043E\u043A\u0430\u0437\u044B\u0432\u0430\u0442\u044C \u0440\u0435\u0436\u0438\u043C \u043E\u0431\u0440\u0430\u0442\u043D\u044B\u0445 \u0441\u0441\u044B\u043B\u043E\u043A",
    "show_outgoinglinks_mode": "\u041F\u043E\u043A\u0430\u0437\u044B\u0432\u0430\u0442\u044C \u0440\u0435\u0436\u0438\u043C \u0438\u0441\u0445\u043E\u0434\u044F\u0449\u0438\u0445 \u0441\u0441\u044B\u043B\u043E\u043A",
    "show_all_files_mode": "\u041F\u043E\u043A\u0430\u0437\u044B\u0432\u0430\u0442\u044C \u0440\u0435\u0436\u0438\u043C \u0432\u0441\u0435\u0445 \u0444\u0430\u0439\u043B\u043E\u0432",
    "show_recent_files_mode": "\u041F\u043E\u043A\u0430\u0437\u044B\u0432\u0430\u0442\u044C \u0440\u0435\u0436\u0438\u043C \u043D\u0435\u0434\u0430\u0432\u043D\u0438\u0445 \u0444\u0430\u0439\u043B\u043E\u0432",
    "recent_files_count": "\u041A\u043E\u043B\u0438\u0447\u0435\u0441\u0442\u0432\u043E \u043D\u0435\u0434\u0430\u0432\u043D\u0438\u0445 \u0444\u0430\u0439\u043B\u043E\u0432",
    "show_random_note_mode": "\u041F\u043E\u043A\u0430\u0437\u044B\u0432\u0430\u0442\u044C \u0440\u0435\u0436\u0438\u043C \u0441\u043B\u0443\u0447\u0430\u0439\u043D\u043E\u0439 \u0437\u0430\u043C\u0435\u0442\u043A\u0438",
    "random_note_count": "\u041A\u043E\u043B\u0438\u0447\u0435\u0441\u0442\u0432\u043E \u0441\u043B\u0443\u0447\u0430\u0439\u043D\u044B\u0445 \u0437\u0430\u043C\u0435\u0442\u043E\u043A",
    "random_note_notes_only": "\u0422\u043E\u043B\u044C\u043A\u043E \u0437\u0430\u043C\u0435\u0442\u043A\u0438",
    "random_note_include_media_files": "\u0412\u043A\u043B\u044E\u0447\u0438\u0442\u044C \u043C\u0435\u0434\u0438\u0430\u0444\u0430\u0439\u043B\u044B",
    "show_tasks_mode": "\u041F\u043E\u043A\u0430\u0437\u044B\u0432\u0430\u0442\u044C \u0440\u0435\u0436\u0438\u043C \u0437\u0430\u0434\u0430\u0447",
    "task_filter": "\u0424\u0438\u043B\u044C\u0442\u0440 \u0437\u0430\u0434\u0430\u0447",
    "uncompleted": "\u041D\u0435\u0437\u0430\u0432\u0435\u0440\u0448\u0435\u043D\u043D\u044B\u0435",
    "completed": "\u0417\u0430\u0432\u0435\u0440\u0448\u0435\u043D\u043D\u044B\u0435",
    "foldernote_display_settings": "\u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 \u043E\u0442\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u044F \u0437\u0430\u043C\u0435\u0442\u043E\u043A \u043F\u0430\u043F\u043E\u043A",
    "foldernote_display_settings_desc": "\u0423\u0441\u0442\u0430\u043D\u043E\u0432\u0438\u0442\u044C \u0440\u0435\u0436\u0438\u043C \u043E\u0442\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u044F \u0437\u0430\u043C\u0435\u0442\u043E\u043A \u043F\u0430\u043F\u043E\u043A",
    "all": "\u0412\u0441\u0435",
    "default": "\u041F\u043E \u0443\u043C\u043E\u043B\u0447\u0430\u043D\u0438\u044E",
    "hidden": "\u0421\u043A\u0440\u044B\u0442\u044B\u0435",
    // Show "Parent Folder" option setting
    "show_parent_folder_item": '\u041F\u043E\u043A\u0430\u0437\u044B\u0432\u0430\u0442\u044C \u044D\u043B\u0435\u043C\u0435\u043D\u0442 "\u0420\u043E\u0434\u0438\u0442\u0435\u043B\u044C\u0441\u043A\u0430\u044F \u043F\u0430\u043F\u043A\u0430"',
    "show_parent_folder_item_desc": '\u041F\u043E\u043A\u0430\u0437\u044B\u0432\u0430\u0442\u044C \u044D\u043B\u0435\u043C\u0435\u043D\u0442 "\u0420\u043E\u0434\u0438\u0442\u0435\u043B\u044C\u0441\u043A\u0430\u044F \u043F\u0430\u043F\u043A\u0430" \u043F\u0435\u0440\u0432\u044B\u043C \u0432 \u0441\u0435\u0442\u043A\u0435',
    "parent_folder": "\u0420\u043E\u0434\u0438\u0442\u0435\u043B\u044C\u0441\u043A\u0430\u044F \u043F\u0430\u043F\u043A\u0430",
    // Default open location setting
    "default_open_location": "\u041C\u0435\u0441\u0442\u043E \u043E\u0442\u043A\u0440\u044B\u0442\u0438\u044F \u043F\u043E \u0443\u043C\u043E\u043B\u0447\u0430\u043D\u0438\u044E",
    "default_open_location_desc": "\u0423\u0441\u0442\u0430\u043D\u043E\u0432\u0438\u0442\u0435 \u043C\u0435\u0441\u0442\u043E \u043E\u0442\u043A\u0440\u044B\u0442\u0438\u044F \u0441\u0435\u0442\u043E\u0447\u043D\u043E\u0433\u043E \u0432\u0438\u0434\u0430 \u043F\u043E \u0443\u043C\u043E\u043B\u0447\u0430\u043D\u0438\u044E",
    "open_in_left_sidebar": "\u041E\u0442\u043A\u0440\u044B\u0442\u044C \u0432 \u043B\u0435\u0432\u043E\u0439 \u0431\u043E\u043A\u043E\u0432\u043E\u0439 \u043F\u0430\u043D\u0435\u043B\u0438",
    "open_in_right_sidebar": "\u041E\u0442\u043A\u0440\u044B\u0442\u044C \u0432 \u043F\u0440\u0430\u0432\u043E\u0439 \u0431\u043E\u043A\u043E\u0432\u043E\u0439 \u043F\u0430\u043D\u0435\u043B\u0438",
    "open_in_new_tab": "\u041E\u0442\u043A\u0440\u044B\u0442\u044C \u0432 \u043D\u043E\u0432\u043E\u0439 \u0432\u043A\u043B\u0430\u0434\u043A\u0435",
    "reuse_existing_leaf": "\u0418\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u044C \u0441\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u044E\u0449\u0438\u0439 \u0432\u0438\u0434",
    "reuse_existing_leaf_desc": "\u041F\u0440\u0438 \u043E\u0442\u043A\u0440\u044B\u0442\u0438\u0438 \u0441\u0435\u0442\u043E\u0447\u043D\u043E\u0433\u043E \u0432\u0438\u0434\u0430 \u043F\u0440\u0435\u0434\u043F\u043E\u0447\u0442\u0438\u0442\u0435\u043B\u044C\u043D\u043E \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u044C \u0441\u0443\u0449\u0435\u0441\u0442\u0432\u0443\u044E\u0449\u0438\u0439 \u0432\u0438\u0434 \u0432\u043C\u0435\u0441\u0442\u043E \u0441\u043E\u0437\u0434\u0430\u043D\u0438\u044F \u043D\u043E\u0432\u043E\u0433\u043E",
    "custom_document_extensions": "\u041F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C\u0441\u043A\u0438\u0435 \u0440\u0430\u0441\u0448\u0438\u0440\u0435\u043D\u0438\u044F \u0434\u043E\u043A\u0443\u043C\u0435\u043D\u0442\u043E\u0432",
    "custom_document_extensions_desc": "\u0414\u043E\u043F\u043E\u043B\u043D\u0438\u0442\u0435\u043B\u044C\u043D\u044B\u0435 \u0440\u0430\u0441\u0448\u0438\u0440\u0435\u043D\u0438\u044F \u0434\u043E\u043A\u0443\u043C\u0435\u043D\u0442\u043E\u0432 (\u0447\u0435\u0440\u0435\u0437 \u0437\u0430\u043F\u044F\u0442\u0443\u044E, \u0431\u0435\u0437 \u0442\u043E\u0447\u0435\u043A)",
    "custom_document_extensions_placeholder": "\u043D\u0430\u043F\u0440\u0438\u043C\u0435\u0440, txt,doc,docx",
    "custom_folder_icon": "\u041F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C\u0441\u043A\u0438\u0439 \u0438\u043A\u043E\u043D\u043A\u0430 \u043F\u0430\u043F\u043A\u0438",
    "custom_folder_icon_desc": "\u041F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044C\u0441\u043A\u0430\u044F \u0438\u043A\u043E\u043D\u043A\u0430 \u043F\u0430\u043F\u043A\u0438 (\u0438\u0441\u043F\u043E\u043B\u044C\u0437\u0443\u0439\u0442\u0435 Emoji)",
    // Select Folder Dialog
    "select_folders": "\u0412\u044B\u0431\u0440\u0430\u0442\u044C \u043F\u0430\u043F\u043A\u0443",
    "select_folders_to_ignore": "\u0412\u044B\u0431\u0440\u0430\u0442\u044C \u043F\u0430\u043F\u043A\u0438 \u0434\u043B\u044F \u0438\u0433\u043D\u043E\u0440\u0438\u0440\u043E\u0432\u0430\u043D\u0438\u044F",
    "open_grid_view": "\u041E\u0442\u043A\u0440\u044B\u0442\u044C \u0441\u0435\u0442\u043E\u0447\u043D\u044B\u0439 \u0432\u0438\u0434",
    "open_in_grid_view": "\u041E\u0442\u043A\u0440\u044B\u0442\u044C \u0432 \u0441\u0435\u0442\u043E\u0447\u043D\u043E\u043C \u0432\u0438\u0434\u0435",
    "open_note_in_grid_view": "\u041E\u0442\u043A\u0440\u044B\u0442\u044C \u0437\u0430\u043C\u0435\u0442\u043A\u0443 \u0432 \u0441\u0435\u0442\u043E\u0447\u043D\u043E\u043C \u0432\u0438\u0434\u0435",
    "open_backlinks_in_grid_view": "\u041E\u0442\u043A\u0440\u044B\u0442\u044C \u043E\u0431\u0440\u0430\u0442\u043D\u044B\u0435 \u0441\u0441\u044B\u043B\u043A\u0438 \u0432 \u0441\u0435\u0442\u043E\u0447\u043D\u043E\u043C \u0432\u0438\u0434\u0435",
    "open_outgoinglinks_in_grid_view": "\u041E\u0442\u043A\u0440\u044B\u0442\u044C \u0438\u0441\u0445\u043E\u0434\u044F\u0449\u0438\u0435 \u0441\u0441\u044B\u043B\u043A\u0438 \u0432 \u0441\u0435\u0442\u043E\u0447\u043D\u043E\u043C \u0432\u0438\u0434\u0435",
    "open_recent_files_in_grid_view": "\u041E\u0442\u043A\u0440\u044B\u0442\u044C \u0442\u0435\u043A\u0443\u0449\u0443\u044E \u0437\u0430\u043C\u0435\u0442\u043A\u0443 \u0432 \u043D\u0435\u0434\u0430\u0432\u043D\u0438\u0445 \u0444\u0430\u0439\u043B\u0430\u0445",
    "open_settings": "\u041E\u0442\u043A\u0440\u044B\u0442\u044C \u043D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438",
    "open_new_grid_view": "\u041E\u0442\u043A\u0440\u044B\u0442\u044C \u043D\u043E\u0432\u044B\u0439 \u0441\u0435\u0442\u043E\u0447\u043D\u044B\u0439 \u0432\u0438\u0434",
    "open_in_new_grid_view": "\u041E\u0442\u043A\u0440\u044B\u0442\u044C \u0432 \u043D\u043E\u0432\u043E\u043C \u0441\u0435\u0442\u043E\u0447\u043D\u043E\u043C \u0432\u0438\u0434\u0435",
    "min_mode": "\u041C\u0438\u043D\u0438\u043C\u0438\u0437\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u0432\u0438\u0434",
    "show_ignored_folders": "\u041F\u043E\u043A\u0430\u0437\u0430\u0442\u044C \u0438\u0433\u043D\u043E\u0440\u0438\u0440\u0443\u0435\u043C\u044B\u0435 \u043F\u0430\u043F\u043A\u0438",
    "delete_note": "\u0423\u0434\u0430\u043B\u0438\u0442\u044C \u0444\u0430\u0439\u043B",
    "open_folder_note": "\u041E\u0442\u043A\u0440\u044B\u0442\u044C \u0437\u0430\u043C\u0435\u0442\u043A\u0443 \u043F\u0430\u043F\u043A\u0438",
    "create_folder_note": "\u0421\u043E\u0437\u0434\u0430\u0442\u044C \u0437\u0430\u043C\u0435\u0442\u043A\u0443 \u043F\u0430\u043F\u043A\u0438",
    "delete_folder_note": "\u0423\u0434\u0430\u043B\u0438\u0442\u044C \u0437\u0430\u043C\u0435\u0442\u043A\u0443 \u043F\u0430\u043F\u043A\u0438",
    "edit_folder_note_settings": "\u0420\u0435\u0434\u0430\u043A\u0442\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u043D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 \u0437\u0430\u043C\u0435\u0442\u043A\u0438 \u043F\u0430\u043F\u043A\u0438",
    "ignore_folder": "\u0418\u0433\u043D\u043E\u0440\u0438\u0440\u043E\u0432\u0430\u0442\u044C \u044D\u0442\u0443 \u043F\u0430\u043F\u043A\u0443",
    "unignore_folder": "\u041E\u0442\u043C\u0435\u043D\u0438\u0442\u044C \u0438\u0433\u043D\u043E\u0440\u0438\u0440\u043E\u0432\u0430\u043D\u0438\u0435 \u044D\u0442\u043E\u0439 \u043F\u0430\u043F\u043A\u0438",
    "searching": "\u041F\u043E\u0438\u0441\u043A...",
    "no_files": "\u0424\u0430\u0439\u043B\u044B \u043D\u0435 \u043D\u0430\u0439\u0434\u0435\u043D\u044B",
    "filter_folders": "\u0424\u0438\u043B\u044C\u0442\u0440\u043E\u0432\u0430\u0442\u044C \u043F\u0430\u043F\u043A\u0438...",
    // Folder Note Settings Dialog
    "folder_note_settings": "\u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 \u0437\u0430\u043C\u0435\u0442\u043A\u0438 \u043F\u0430\u043F\u043A\u0438",
    "folder_sort_type": "\u0422\u0438\u043F \u0441\u043E\u0440\u0442\u0438\u0440\u043E\u0432\u043A\u0438 \u043F\u0430\u043F\u043A\u0438",
    "folder_sort_type_desc": "\u0423\u0441\u0442\u0430\u043D\u043E\u0432\u0438\u0442\u0435 \u0442\u0438\u043F \u0441\u043E\u0440\u0442\u0438\u0440\u043E\u0432\u043A\u0438 \u043F\u043E \u0443\u043C\u043E\u043B\u0447\u0430\u043D\u0438\u044E \u0434\u043B\u044F \u044D\u0442\u043E\u0439 \u043F\u0430\u043F\u043A\u0438",
    "folder_color": "\u0426\u0432\u0435\u0442 \u043F\u0430\u043F\u043A\u0438",
    "folder_color_desc": "\u0423\u0441\u0442\u0430\u043D\u043E\u0432\u0438\u0442\u0435 \u0446\u0432\u0435\u0442 \u043E\u0442\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u044F \u0434\u043B\u044F \u044D\u0442\u043E\u0439 \u043F\u0430\u043F\u043A\u0438",
    "folder_icon": "\u0418\u043A\u043E\u043D\u043A\u0430 \u043F\u0430\u043F\u043A\u0438",
    "folder_icon_desc": "\u0423\u0441\u0442\u0430\u043D\u043E\u0432\u0438\u0442\u0435 \u0438\u043A\u043E\u043D\u043A\u0443 \u043E\u0442\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u044F \u0434\u043B\u044F \u044D\u0442\u043E\u0439 \u043F\u0430\u043F\u043A\u0438",
    "default_sort": "\u0418\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u044C \u0441\u043E\u0440\u0442\u0438\u0440\u043E\u0432\u043A\u0443 \u043F\u043E \u0443\u043C\u043E\u043B\u0447\u0430\u043D\u0438\u044E",
    "no_color": "\u0411\u0435\u0437 \u0446\u0432\u0435\u0442\u0430",
    "color_red": "\u041A\u0440\u0430\u0441\u043D\u044B\u0439",
    "color_orange": "\u041E\u0440\u0430\u043D\u0436\u0435\u0432\u044B\u0439",
    "color_yellow": "\u0416\u0435\u043B\u0442\u044B\u0439",
    "color_green": "\u0417\u0435\u043B\u0435\u043D\u044B\u0439",
    "color_cyan": "\u0413\u043E\u043B\u0443\u0431\u043E\u0439",
    "color_blue": "\u0421\u0438\u043D\u0438\u0439",
    "color_purple": "\u0424\u0438\u043E\u043B\u0435\u0442\u043E\u0432\u044B\u0439",
    "color_pink": "\u0420\u043E\u0437\u043E\u0432\u044B\u0439",
    "confirm": "\u041F\u043E\u0434\u0442\u0432\u0435\u0440\u0434\u0438\u0442\u044C",
    "note_attribute_settings": "\u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u043E\u0432 \u0437\u0430\u043C\u0435\u0442\u043A\u0438",
    "note_title": "\u041D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 \u0437\u0430\u043C\u0435\u0442\u043A\u0438",
    "note_title_desc": "\u0423\u0441\u0442\u0430\u043D\u043E\u0432\u0438\u0442\u0435 \u043E\u0442\u043E\u0431\u0440\u0430\u0436\u0430\u0435\u043C\u043E\u0435 \u043D\u0430\u0437\u0432\u0430\u043D\u0438\u0435 \u0434\u043B\u044F \u044D\u0442\u043E\u0439 \u0437\u0430\u043C\u0435\u0442\u043A\u0438",
    "note_summary": "\u041A\u0440\u0430\u0442\u043A\u0438\u0439 \u043E\u043F\u0438\u0441\u0430\u0442\u0435\u043B\u044C",
    "note_summary_desc": "\u0423\u0441\u0442\u0430\u043D\u043E\u0432\u0438\u0442\u0435 \u043E\u0442\u043E\u0431\u0440\u0430\u0436\u0430\u0435\u043C\u043E\u0435 \u043A\u0440\u0430\u0442\u043A\u0438\u0439 \u043E\u043F\u0438\u0441\u0430\u0442\u0435\u043B\u044C \u0434\u043B\u044F \u044D\u0442\u043E\u0439 \u0437\u0430\u043C\u0435\u0442\u043A\u0438",
    "note_color": "\u0426\u0432\u0435\u0442 \u0437\u0430\u043C\u0435\u0442\u043A\u0438",
    "note_color_desc": "\u0423\u0441\u0442\u0430\u043D\u043E\u0432\u0438\u0442\u0435 \u0446\u0432\u0435\u0442 \u043E\u0442\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u044F \u0434\u043B\u044F \u044D\u0442\u043E\u0439 \u0437\u0430\u043C\u0435\u0442\u043A\u0438",
    "set_note_attribute": "\u0423\u0441\u0442\u0430\u043D\u043E\u0432\u0438\u0442\u044C \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u044B \u0437\u0430\u043C\u0435\u0442\u043A\u0438",
    "rename_folder": "\u041F\u0435\u0440\u0435\u0438\u043C\u0435\u043D\u043E\u0432\u0430\u0442\u044C \u043F\u0430\u043F\u043A\u0443",
    "enter_new_folder_name": "\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u043D\u043E\u0432\u043E\u0435 \u0438\u043C\u044F \u043F\u0430\u043F\u043A\u0438",
    "search_selection_in_grid_view": "\u041F\u043E\u0438\u0441\u043A ... \u0432 \u0441\u0435\u0442\u043E\u0447\u043D\u043E\u043C \u0432\u0438\u0434\u0435",
    "show_date_dividers": "\u041F\u043E\u043A\u0430\u0437\u044B\u0432\u0430\u0442\u044C \u0440\u0430\u0437\u0434\u0435\u043B\u0438\u0442\u0435\u043B\u0438 \u0434\u0430\u0442",
    "show_date_dividers_desc": "\u041F\u043E\u043A\u0430\u0437\u044B\u0432\u0430\u0442\u044C \u0440\u0430\u0437\u0434\u0435\u043B\u0438\u0442\u0435\u043B\u0438 \u0434\u0430\u0442 \u043F\u0435\u0440\u0435\u0434 \u043F\u0435\u0440\u0432\u044B\u043C \u044D\u043B\u0435\u043C\u0435\u043D\u0442\u043E\u043C \u043A\u0430\u0436\u0434\u043E\u0433\u043E \u043D\u043E\u0432\u043E\u0433\u043E \u0434\u043D\u044F \u043F\u0440\u0438 \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u043D\u0438\u0438 \u0441\u043E\u0440\u0442\u0438\u0440\u043E\u0432\u043A\u0438 \u043F\u043E \u0434\u0430\u0442\u0435",
    "date_divider_format": "\u0424\u043E\u0440\u043C\u0430\u0442 \u0440\u0430\u0437\u0434\u0435\u043B\u0438\u0442\u0435\u043B\u044F \u0434\u0430\u0442",
    "date_divider_mode": "\u0420\u0430\u0437\u0434\u0435\u043B\u0438\u0442\u0435\u043B\u044C \u0434\u0430\u0442",
    "date_divider_mode_desc": "\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0440\u0435\u0436\u0438\u043C \u043E\u0442\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u044F \u0440\u0430\u0437\u0434\u0435\u043B\u0438\u0442\u0435\u043B\u0435\u0439 \u0434\u0430\u0442",
    "date_divider_mode_none": "\u041D\u0435\u0442",
    "date_divider_mode_year": "\u0413\u043E\u0434",
    "date_divider_mode_month": "\u041C\u0435\u0441\u044F\u0446",
    "date_divider_mode_day": "\u0414\u0435\u043D\u044C",
    "pinned": "\u0417\u0430\u043A\u0440\u0435\u043F\u043B\u0435\u043D\u043E",
    "pinned_desc": "\u0417\u0430\u043A\u0440\u0435\u043F\u0438\u0442\u044C \u0444\u0430\u0439\u043B \u043D\u0430\u0432\u0435\u0440\u0445\u0443",
    "foldernote_pinned": "\u0417\u0430\u043A\u0440\u0435\u043F\u0438\u0442\u044C \u0437\u0430\u043C\u0435\u0442\u043A\u0438 \u043F\u0430\u043F\u043A\u0438",
    "foldernote_pinned_desc": "\u0417\u0430\u043A\u0440\u0435\u043F\u0438\u0442\u044C \u0437\u0430\u043C\u0435\u0442\u043A\u0438 \u043F\u0430\u043F\u043A\u0438 \u0432\u0432\u0435\u0440\u0445\u0443",
    "display_minimized": "\u041C\u0438\u043D\u0438\u043C\u0430\u043B\u044C\u043D\u044B\u0439 \u0432\u0438\u0434",
    "display_minimized_desc": "\u041F\u043E\u043A\u0430\u0437\u044B\u0432\u0430\u0442\u044C \u044D\u0442\u0443 \u0437\u0430\u043C\u0435\u0442\u043A\u0443 \u0432 \u043C\u0438\u043D\u0438\u043C\u0430\u043B\u044C\u043D\u043E\u043C \u0440\u0435\u0436\u0438\u043C\u0435",
    // Quick Access Settings and Commands
    "quick_access_settings_title": "\u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438 \u0411\u044B\u0441\u0442\u0440\u043E\u0433\u043E \u0434\u043E\u0441\u0442\u0443\u043F\u0430",
    "quick_access_folder_name": "\u041F\u0430\u043F\u043A\u0430 \u0431\u044B\u0441\u0442\u0440\u043E\u0433\u043E \u0434\u043E\u0441\u0442\u0443\u043F\u0430",
    "quick_access_folder_desc": "\u0423\u0441\u0442\u0430\u043D\u043E\u0432\u0438\u0442\u0435 \u043F\u0430\u043F\u043A\u0443, \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u0443\u0435\u043C\u0443\u044E \u043A\u043E\u043C\u0430\u043D\u0434\u043E\u0439 \xAB\u041E\u0442\u043A\u0440\u044B\u0442\u044C \u043F\u0430\u043F\u043A\u0443 \u0431\u044B\u0441\u0442\u0440\u043E\u0433\u043E \u0434\u043E\u0441\u0442\u0443\u043F\u0430\xBB",
    "quick_access_mode_name": "\u0420\u0435\u0436\u0438\u043C \u0431\u044B\u0441\u0442\u0440\u043E\u0433\u043E \u0434\u043E\u0441\u0442\u0443\u043F\u0430",
    "quick_access_mode_desc": "\u0423\u0441\u0442\u0430\u043D\u043E\u0432\u0438\u0442\u0435 \u0440\u0435\u0436\u0438\u043C \u043F\u043E \u0443\u043C\u043E\u043B\u0447\u0430\u043D\u0438\u044E, \u0438\u0441\u043F\u043E\u043B\u044C\u0437\u0443\u0435\u043C\u044B\u0439 \u043A\u043E\u043C\u0430\u043D\u0434\u043E\u0439 \xAB\u041E\u0442\u043A\u0440\u044B\u0442\u044C \u0431\u044B\u0441\u0442\u0440\u044B\u0439 \u0434\u043E\u0441\u0442\u0443\u043F \u043F\u043E \u0440\u0435\u0436\u0438\u043C\u0443\xBB",
    "use_quick_access_as_new_tab_view": "\u0418\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u044C \u0411\u044B\u0441\u0442\u0440\u044B\u0439 \u0434\u043E\u0441\u0442\u0443\u043F \u043A\u0430\u043A \u043D\u043E\u0432\u0443\u044E \u0432\u043A\u043B\u0430\u0434\u043A\u0443",
    "use_quick_access_as_new_tab_view_desc": "\u0417\u0430\u043C\u0435\u043D\u0438\u0442\u0435 \u0441\u0442\u0430\u043D\u0434\u0430\u0440\u0442\u043D\u044B\u0439 \u0432\u0438\u0434 \xAB\u041D\u043E\u0432\u0430\u044F \u0432\u043A\u043B\u0430\u0434\u043A\u0430\xBB \u043D\u0430 \u0441\u0435\u0442\u043E\u0447\u043D\u044B\u0439 \u0432\u0438\u0434 \u0432\u044B\u0431\u0440\u0430\u043D\u043D\u043E\u0439 \u043E\u043F\u0446\u0438\u0438 \u0411\u044B\u0441\u0442\u0440\u043E\u0433\u043E \u0434\u043E\u0441\u0442\u0443\u043F\u0430 (\u043F\u0430\u043F\u043A\u0438 \u0438\u043B\u0438 \u0440\u0435\u0436\u0438\u043C\u0430). \u0420\u0430\u0431\u043E\u0442\u0430\u0435\u0442, \u0442\u043E\u043B\u044C\u043A\u043E \u0435\u0441\u043B\u0438 \xAB\u041C\u0435\u0441\u0442\u043E \u043E\u0442\u043A\u0440\u044B\u0442\u0438\u044F \u043F\u043E \u0443\u043C\u043E\u043B\u0447\u0430\u043D\u0438\u044E\xBB \u0443\u0441\u0442\u0430\u043D\u043E\u0432\u043B\u0435\u043D\u043E \u043D\u0430 \xAB\u041E\u0442\u043A\u0440\u044B\u0442\u044C \u0432 \u043D\u043E\u0432\u043E\u0439 \u0432\u043A\u043B\u0430\u0434\u043A\u0435\xBB!",
    "default_new_tab": "\u041F\u043E \u0443\u043C\u043E\u043B\u0447\u0430\u043D\u0438\u044E",
    "use_quick_access_folder": "\u0418\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u044C \u043F\u0430\u043F\u043A\u0443 \u0431\u044B\u0441\u0442\u0440\u043E\u0433\u043E \u0434\u043E\u0441\u0442\u0443\u043F\u0430",
    "use_quick_access_mode": "\u0418\u0441\u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u044C \u0440\u0435\u0436\u0438\u043C \u0431\u044B\u0441\u0442\u0440\u043E\u0433\u043E \u0434\u043E\u0441\u0442\u0443\u043F\u0430",
    "open_quick_access_folder": "\u041E\u0442\u043A\u0440\u044B\u0442\u044C \u043F\u0430\u043F\u043A\u0443 \u0431\u044B\u0441\u0442\u0440\u043E\u0433\u043E \u0434\u043E\u0441\u0442\u0443\u043F\u0430",
    "open_quick_access_mode": "\u041E\u0442\u043A\u0440\u044B\u0442\u044C \u0431\u044B\u0441\u0442\u0440\u044B\u0439 \u0434\u043E\u0441\u0442\u0443\u043F \u043F\u043E \u0440\u0435\u0436\u0438\u043C\u0443"
  },
  "uk": {
    // Notifications
    "bookmarks_plugin_disabled": "\u0411\u0443\u0434\u044C \u043B\u0430\u0441\u043A\u0430, \u0441\u043F\u043E\u0447\u0430\u0442\u043A\u0443 \u0443\u0432\u0456\u043C\u043A\u043D\u0456\u0442\u044C \u043F\u043B\u0430\u0433\u0456\u043D \u0417\u0430\u043A\u043B\u0430\u0434\u043A\u0438",
    // Buttons and Labels
    "sorting": "\u0421\u043E\u0440\u0442\u0443\u0432\u0430\u0442\u0438 \u0437\u0430",
    "refresh": "\u041E\u043D\u043E\u0432\u0438\u0442\u0438",
    "reselect": "\u041F\u0435\u0440\u0435\u043E\u0431\u0440\u0430\u0442\u0438",
    "go_up": "\u0412\u0433\u043E\u0440\u0443",
    "no_backlinks": "\u041D\u0435\u043C\u0430\u0454 \u0437\u0432\u043E\u0440\u043E\u0442\u043D\u0438\u0445 \u043F\u043E\u0441\u0438\u043B\u0430\u043D\u044C",
    "search": "\u041F\u043E\u0448\u0443\u043A",
    "search_placeholder": "\u041A\u043B\u044E\u0447\u043E\u0432\u0435 \u0441\u043B\u043E\u0432\u043E \u0434\u043B\u044F \u043F\u043E\u0448\u0443\u043A\u0443",
    "search_current_location_only": "\u0428\u0443\u043A\u0430\u0442\u0438 \u043B\u0438\u0448\u0435 \u0432 \u043F\u043E\u0442\u043E\u0447\u043D\u043E\u043C\u0443 \u0440\u043E\u0437\u0442\u0430\u0448\u0443\u0432\u0430\u043D\u043D\u0456",
    "search_media_files": "\u0428\u0443\u043A\u0430\u0442\u0438 \u043C\u0435\u0434\u0456\u0430\u0444\u0430\u0439\u043B\u0438",
    "cancel": "\u0421\u043A\u0430\u0441\u0443\u0432\u0430\u0442\u0438",
    "new_note": "\u041D\u043E\u0432\u0430 \u043D\u043E\u0442\u0430\u0442\u043A\u0430",
    "new_folder": "\u041D\u043E\u0432\u0430 \u043F\u0430\u043F\u043A\u0430",
    "new_canvas": "\u041D\u043E\u0432\u0438\u0439 \u043A\u0430\u043D\u0432\u0430\u0441",
    "delete_folder": "\u0412\u0438\u0434\u0430\u043B\u0438\u0442\u0438 \u043F\u0430\u043F\u043A\u0443",
    "untitled": "\u0411\u0435\u0437 \u043D\u0430\u0437\u0432\u0438",
    "files": "\u0444\u0430\u0439\u043B\u0438",
    "add": "\u0414\u043E\u0434\u0430\u0442\u0438",
    "root": "\u041A\u043E\u0440\u0435\u043D\u044C",
    "more_options": "\u0411\u0456\u043B\u044C\u0448\u0435 \u043E\u043F\u0446\u0456\u0439",
    "add_tag_to_search": "\u0414\u043E\u0434\u0430\u0442\u0438 \u0434\u043E \u043F\u043E\u0448\u0443\u043A\u0443",
    "remove_tag_from_search": "\u0412\u0438\u0434\u0430\u043B\u0438\u0442\u0438 \u0437 \u043F\u043E\u0448\u0443\u043A\u0443",
    "global_search": "\u0413\u043B\u043E\u0431\u0430\u043B\u044C\u043D\u0438\u0439 \u043F\u043E\u0448\u0443\u043A",
    "remove": "\u0412\u0438\u0434\u0430\u043B\u0438\u0442\u0438",
    "edit": "\u0420\u0435\u0434\u0430\u0433\u0443\u0432\u0430\u0442\u0438",
    "delete": "\u0412\u0438\u0434\u0430\u043B\u0438\u0442\u0438",
    "save": "\u0417\u0431\u0435\u0440\u0435\u0433\u0442\u0438",
    // View Titles
    "grid_view_title": "\u0421\u0456\u0442\u043A\u043E\u0432\u0438\u0439 \u0432\u0438\u0433\u043B\u044F\u0434",
    "bookmarks_mode": "\u0417\u0430\u043A\u043B\u0430\u0434\u043A\u0438",
    "folder_mode": "\u041F\u0430\u043F\u043A\u0430",
    "search_results": "\u0420\u0435\u0437\u0443\u043B\u044C\u0442\u0430\u0442\u0438 \u043F\u043E\u0448\u0443\u043A\u0443",
    "backlinks_mode": "\u0417\u0432\u043E\u0440\u043E\u0442\u043D\u0456 \u043F\u043E\u0441\u0438\u043B\u0430\u043D\u043D\u044F",
    "outgoinglinks_mode": "\u0412\u0438\u0445\u0456\u0434\u043D\u0456 \u043F\u043E\u0441\u0438\u043B\u0430\u043D\u043D\u044F",
    "all_files_mode": "\u0423\u0441\u0456 \u0444\u0430\u0439\u043B\u0438",
    "recent_files_mode": "\u041E\u0441\u0442\u0430\u043D\u043D\u0456 \u0444\u0430\u0439\u043B\u0438",
    "random_note_mode": "\u0412\u0438\u043F\u0430\u0434\u043A\u043E\u0432\u0430 \u043D\u043E\u0442\u0430\u0442\u043A\u0430",
    "tasks_mode": "\u0417\u0430\u0434\u0430\u0447\u0456",
    // Sort Options
    "sort_name_asc": "\u041D\u0430\u0437\u0432\u0430 (\u0410 \u2192 \u042F)",
    "sort_name_desc": "\u041D\u0430\u0437\u0432\u0430 (\u042F \u2192 \u0410)",
    "sort_mtime_desc": "\u0417\u043C\u0456\u043D\u0435\u043D\u043E (\u041D\u043E\u0432\u0435 \u2192 \u0421\u0442\u0430\u0440\u0435)",
    "sort_mtime_asc": "\u0417\u043C\u0456\u043D\u0435\u043D\u043E (\u0421\u0442\u0430\u0440\u0435 \u2192 \u041D\u043E\u0432\u0435)",
    "sort_ctime_desc": "\u0421\u0442\u0432\u043E\u0440\u0435\u043D\u043E (\u041D\u043E\u0432\u0435 \u2192 \u0421\u0442\u0430\u0440\u0435)",
    "sort_ctime_asc": "\u0421\u0442\u0432\u043E\u0440\u0435\u043D\u043E (\u0421\u0442\u0430\u0440\u0435 \u2192 \u041D\u043E\u0432\u0435)",
    "sort_random": "\u0412\u0438\u043F\u0430\u0434\u043A\u043E\u0432\u043E",
    // Settings
    "grid_view_settings": "\u041D\u0430\u043B\u0430\u0448\u0442\u0443\u0432\u0430\u043D\u043D\u044F \u0441\u0456\u0442\u043A\u043E\u0432\u043E\u0433\u043E \u0432\u0438\u0433\u043B\u044F\u0434\u0443",
    "media_files_settings": "\u041D\u0430\u043B\u0430\u0448\u0442\u0443\u0432\u0430\u043D\u043D\u044F \u043C\u0435\u0434\u0456\u0430\u0444\u0430\u0439\u043B\u0456\u0432",
    "show_media_files": "\u041F\u043E\u043A\u0430\u0437\u0443\u0432\u0430\u0442\u0438 \u043C\u0435\u0434\u0456\u0430\u0444\u0430\u0439\u043B\u0438",
    "show_media_files_desc": "\u0412\u0456\u0434\u043E\u0431\u0440\u0430\u0436\u0430\u0442\u0438 \u043C\u0435\u0434\u0456\u0430\u0444\u0430\u0439\u043B\u0438 \u0443 \u0441\u0456\u0442\u043A\u043E\u0432\u043E\u043C\u0443 \u0432\u0438\u0433\u043B\u044F\u0434\u0456",
    "show_video_thumbnails": "\u041F\u043E\u043A\u0430\u0437\u0443\u0432\u0430\u0442\u0438 \u043C\u0456\u043D\u0456\u0430\u0442\u044E\u0440\u0438 \u0432\u0456\u0434\u0435\u043E",
    "show_video_thumbnails_desc": "\u0412\u0456\u0434\u043E\u0431\u0440\u0430\u0436\u0430\u0442\u0438 \u043C\u0456\u043D\u0456\u0430\u0442\u044E\u0440\u0438 \u0434\u043B\u044F \u0432\u0456\u0434\u0435\u043E \u0443 \u0441\u0456\u0442\u043A\u043E\u0432\u043E\u043C\u0443 \u0432\u0438\u0433\u043B\u044F\u0434\u0456, \u043F\u043E\u043A\u0430\u0437\u0443\u0454 \u0456\u043A\u043E\u043D\u043A\u0443 \u0432\u0456\u0434\u0442\u0432\u043E\u0440\u0435\u043D\u043D\u044F, \u044F\u043A\u0449\u043E \u0432\u0438\u043C\u043A\u043D\u0435\u043D\u043E",
    "show_note_tags": "\u041F\u043E\u043A\u0430\u0437\u0443\u0432\u0430\u0442\u0438 \u0442\u0435\u0433\u0438 \u043D\u043E\u0442\u0430\u0442\u043E\u043A",
    "show_note_tags_desc": "\u0412\u0456\u0434\u043E\u0431\u0440\u0430\u0436\u0430\u0442\u0438 \u0442\u0435\u0433\u0438 \u0434\u043B\u044F \u043D\u043E\u0442\u0430\u0442\u043E\u043A \u0443 \u0441\u0456\u0442\u043A\u043E\u0432\u043E\u043C\u0443 \u0432\u0438\u0433\u043B\u044F\u0434\u0456",
    "ignored_folders": "\u0406\u0433\u043D\u043E\u0440\u043E\u0432\u0430\u043D\u0456 \u043F\u0430\u043F\u043A\u0438",
    "ignored_folders_desc": "\u0412\u043A\u0430\u0436\u0456\u0442\u044C \u043F\u0430\u043F\u043A\u0438 \u0434\u043B\u044F \u0456\u0433\u043D\u043E\u0440\u0443\u0432\u0430\u043D\u043D\u044F \u0442\u0443\u0442",
    "add_ignored_folder": "\u0414\u043E\u0434\u0430\u0442\u0438 \u0456\u0433\u043D\u043E\u0440\u043E\u0432\u0430\u043D\u0443 \u043F\u0430\u043F\u043A\u0443",
    "no_ignored_folders": "\u041D\u0435\u043C\u0430\u0454 \u0456\u0433\u043D\u043E\u0440\u043E\u0432\u0430\u043D\u0438\u0445 \u043F\u0430\u043F\u043E\u043A.",
    "ignored_folder_patterns": "\u0406\u0433\u043D\u043E\u0440\u0443\u0432\u0430\u0442\u0438 \u043F\u0430\u043F\u043A\u0438 \u0442\u0430 \u0444\u0430\u0439\u043B\u0438 \u0437\u0430 \u0448\u0430\u0431\u043B\u043E\u043D\u043E\u043C",
    "ignored_folder_patterns_desc": "\u0412\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0439\u0442\u0435 \u0440\u044F\u0434\u043A\u043E\u0432\u0456 \u0448\u0430\u0431\u043B\u043E\u043D\u0438 \u0434\u043B\u044F \u0456\u0433\u043D\u043E\u0440\u0443\u0432\u0430\u043D\u043D\u044F \u043F\u0430\u043F\u043E\u043A \u0456 \u0444\u0430\u0439\u043B\u0456\u0432 (\u043F\u0456\u0434\u0442\u0440\u0438\u043C\u0443\u0454 \u0440\u0435\u0433\u0443\u043B\u044F\u0440\u043D\u0456 \u0432\u0438\u0440\u0430\u0437\u0438)",
    "add_ignored_folder_pattern": "\u0414\u043E\u0434\u0430\u0442\u0438 \u0448\u0430\u0431\u043B\u043E\u043D \u043F\u0430\u043F\u043A\u0438",
    "ignored_folder_pattern_placeholder": "\u0412\u0432\u0435\u0434\u0456\u0442\u044C \u043D\u0430\u0437\u0432\u0443 \u043F\u0430\u043F\u043A\u0438 \u0430\u0431\u043E \u0448\u0430\u0431\u043B\u043E\u043D \u0440\u0435\u0433\u0443\u043B\u044F\u0440\u043D\u043E\u0433\u043E \u0432\u0438\u0440\u0430\u0437\u0443",
    "no_ignored_folder_patterns": "\u041D\u0435\u043C\u0430\u0454 \u0448\u0430\u0431\u043B\u043E\u043D\u0456\u0432 \u0456\u0433\u043D\u043E\u0440\u043E\u0432\u0430\u043D\u0438\u0445 \u043F\u0430\u043F\u043E\u043A.",
    "default_sort_type": "\u0422\u0438\u043F \u0441\u043E\u0440\u0442\u0443\u0432\u0430\u043D\u043D\u044F \u0437\u0430 \u0437\u0430\u043C\u043E\u0432\u0447\u0443\u0432\u0430\u043D\u043D\u044F\u043C",
    "default_sort_type_desc": "\u0412\u0441\u0442\u0430\u043D\u043E\u0432\u0456\u0442\u044C \u043C\u0435\u0442\u043E\u0434 \u0441\u043E\u0440\u0442\u0443\u0432\u0430\u043D\u043D\u044F \u0437\u0430 \u0437\u0430\u043C\u043E\u0432\u0447\u0443\u0432\u0430\u043D\u043D\u044F\u043C \u043F\u0440\u0438 \u0432\u0456\u0434\u043A\u0440\u0438\u0442\u0442\u0456 \u0441\u0456\u0442\u043A\u043E\u0432\u043E\u0433\u043E \u0432\u0438\u0433\u043B\u044F\u0434\u0443",
    "note_title_field": '\u041D\u0430\u0437\u0432\u0430 \u043F\u043E\u043B\u044F "\u041D\u0430\u0437\u0432\u0430 \u043D\u043E\u0442\u0430\u0442\u043A\u0438"',
    "note_title_field_desc": "\u0412\u043A\u0430\u0436\u0456\u0442\u044C \u043D\u0430\u0437\u0432\u0443 \u043F\u043E\u043B\u044F \u0432 \u043C\u0435\u0442\u0430\u0434\u0430\u043D\u0438\u0445 \u0434\u043B\u044F \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u0430\u043D\u043D\u044F \u044F\u043A \u043D\u0430\u0437\u0432\u0438 \u043D\u043E\u0442\u0430\u0442\u043A\u0438",
    "note_summary_field": '\u041D\u0430\u0437\u0432\u0430 \u043F\u043E\u043B\u044F "\u041A\u0440\u0430\u0442\u043A\u0438\u0439 \u043E\u043F\u0438\u0441"',
    "note_summary_field_desc": "\u0412\u043A\u0430\u0436\u0456\u0442\u044C \u043D\u0430\u0437\u0432\u0443 \u043F\u043E\u043B\u044F \u0432 \u043C\u0435\u0442\u0430\u0434\u0430\u043D\u0438\u0445 \u0434\u043B\u044F \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u0430\u043D\u043D\u044F \u044F\u043A \u043A\u0440\u0430\u0442\u043A\u043E\u0433\u043E \u043E\u043F\u0438\u0441\u0443",
    "modified_date_field": '\u041D\u0430\u0437\u0432\u0430 \u043F\u043E\u043B\u044F "\u0414\u0430\u0442\u0430 \u0437\u043C\u0456\u043D\u0438"',
    "modified_date_field_desc": "\u0412\u043A\u0430\u0436\u0456\u0442\u044C \u043D\u0430\u0437\u0432\u0443 \u043F\u043E\u043B\u044F \u0432 \u043C\u0435\u0442\u0430\u0434\u0430\u043D\u0438\u0445 \u0434\u043B\u044F \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u0430\u043D\u043D\u044F \u044F\u043A \u0434\u0430\u0442\u0438 \u0437\u043C\u0456\u043D\u0438",
    "created_date_field": '\u041D\u0430\u0437\u0432\u0430 \u043F\u043E\u043B\u044F "\u0414\u0430\u0442\u0430 \u0441\u0442\u0432\u043E\u0440\u0435\u043D\u043D\u044F"',
    "created_date_field_desc": "\u0412\u043A\u0430\u0436\u0456\u0442\u044C \u043D\u0430\u0437\u0432\u0443 \u043F\u043E\u043B\u044F \u0432 \u043C\u0435\u0442\u0430\u0434\u0430\u043D\u0438\u0445 \u0434\u043B\u044F \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u0430\u043D\u043D\u044F \u044F\u043A \u0434\u0430\u0442\u0438 \u0441\u0442\u0432\u043E\u0440\u0435\u043D\u043D\u044F",
    "grid_item_width": "\u0428\u0438\u0440\u0438\u043D\u0430 \u0435\u043B\u0435\u043C\u0435\u043D\u0442\u0430 \u0441\u0456\u0442\u043A\u0438",
    "grid_item_width_desc": "\u0412\u0441\u0442\u0430\u043D\u043E\u0432\u0456\u0442\u044C \u0448\u0438\u0440\u0438\u043D\u0443 \u0435\u043B\u0435\u043C\u0435\u043D\u0442\u0456\u0432 \u0441\u0456\u0442\u043A\u0438",
    "grid_item_height": "\u0412\u0438\u0441\u043E\u0442\u0430 \u0435\u043B\u0435\u043C\u0435\u043D\u0442\u0430 \u0441\u0456\u0442\u043A\u0438",
    "grid_item_height_desc": "\u0412\u0441\u0442\u0430\u043D\u043E\u0432\u0456\u0442\u044C \u0432\u0438\u0441\u043E\u0442\u0443 \u0435\u043B\u0435\u043C\u0435\u043D\u0442\u0456\u0432 \u0441\u0456\u0442\u043A\u0438 (0 \u0434\u043B\u044F \u0430\u0432\u0442\u043E\u043C\u0430\u0442\u0438\u0447\u043D\u043E\u0457 \u043D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438)",
    "image_area_width": "\u0428\u0438\u0440\u0438\u043D\u0430 \u043E\u0431\u043B\u0430\u0441\u0442\u0456 \u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u043D\u044F",
    "image_area_width_desc": "\u0412\u0441\u0442\u0430\u043D\u043E\u0432\u0456\u0442\u044C \u0448\u0438\u0440\u0438\u043D\u0443 \u043E\u0431\u043B\u0430\u0441\u0442\u0456 \u043F\u043E\u043F\u0435\u0440\u0435\u0434\u043D\u044C\u043E\u0433\u043E \u043F\u0435\u0440\u0435\u0433\u043B\u044F\u0434\u0443 \u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u043D\u044F",
    "image_area_height": "\u0412\u0438\u0441\u043E\u0442\u0430 \u043E\u0431\u043B\u0430\u0441\u0442\u0456 \u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u043D\u044F",
    "image_area_height_desc": "\u0412\u0441\u0442\u0430\u043D\u043E\u0432\u0456\u0442\u044C \u0432\u0438\u0441\u043E\u0442\u0443 \u043E\u0431\u043B\u0430\u0441\u0442\u0456 \u043F\u043E\u043F\u0435\u0440\u0435\u0434\u043D\u044C\u043E\u0433\u043E \u043F\u0435\u0440\u0435\u0433\u043B\u044F\u0434\u0443 \u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u043D\u044F",
    "title_font_size": "\u0420\u043E\u0437\u043C\u0456\u0440 \u0448\u0440\u0438\u0444\u0442\u0443 \u0437\u0430\u0433\u043E\u043B\u043E\u0432\u043A\u0430",
    "title_font_size_desc": "\u0412\u0441\u0442\u0430\u043D\u043E\u0432\u0456\u0442\u044C \u0440\u043E\u0437\u043C\u0456\u0440 \u0448\u0440\u0438\u0444\u0442\u0443 \u0437\u0430\u0433\u043E\u043B\u043E\u0432\u043A\u0430",
    "summary_length": "\u0414\u043E\u0432\u0436\u0438\u043D\u0430 \u043A\u043E\u0440\u043E\u0442\u043A\u043E\u0433\u043E \u043E\u043F\u0438\u0441\u0443",
    "summary_length_desc": "\u0412\u0441\u0442\u0430\u043D\u043E\u0432\u0456\u0442\u044C \u0434\u043E\u0432\u0436\u0438\u043D\u0443 \u043A\u043E\u0440\u043E\u0442\u043A\u043E\u0433\u043E \u043E\u043F\u0438\u0441\u0443",
    "show_code_block_in_summary": "\u041F\u043E\u043A\u0430\u0437\u0443\u0432\u0430\u0442\u0438 CodeBlock \u0432 \u043A\u043E\u0440\u043E\u0442\u043A\u043E\u043C\u0443 \u043E\u043F\u0438\u0441\u0456",
    "show_code_block_in_summary_desc": "\u0412\u0441\u0442\u0430\u043D\u043E\u0432\u0456\u0442\u044C, \u0449\u043E\u0431 \u043F\u043E\u043A\u0430\u0437\u0443\u0432\u0430\u0442\u0438 CodeBlock \u0432 \u043A\u043E\u0440\u043E\u0442\u043A\u043E\u043C\u0443 \u043E\u043F\u0438\u0441\u0456",
    "enable_file_watcher": "\u0423\u0432\u0456\u043C\u043A\u043D\u0443\u0442\u0438 \u0441\u043F\u043E\u0441\u0442\u0435\u0440\u0435\u0436\u0435\u043D\u043D\u044F \u0437\u0430 \u0444\u0430\u0439\u043B\u0430\u043C\u0438",
    "enable_file_watcher_desc": "\u041F\u0440\u0438 \u0443\u0432\u0456\u043C\u043A\u043D\u0435\u043D\u043D\u0456 \u0432\u0438\u0433\u043B\u044F\u0434 \u0430\u0432\u0442\u043E\u043C\u0430\u0442\u0438\u0447\u043D\u043E \u043E\u043D\u043E\u0432\u043B\u044E\u0432\u0430\u0442\u0438\u043C\u0435\u0442\u044C\u0441\u044F \u043F\u0440\u0438 \u0437\u043C\u0456\u043D\u0456 \u0444\u0430\u0439\u043B\u0456\u0432. \u042F\u043A\u0449\u043E \u0432\u0438\u043C\u043A\u043D\u0435\u043D\u043E, \u043F\u043E\u0442\u0440\u0456\u0431\u043D\u043E \u0432\u0440\u0443\u0447\u043D\u0443 \u043D\u0430\u0442\u0438\u0441\u043A\u0430\u0442\u0438 \u043A\u043D\u043E\u043F\u043A\u0443 \u043E\u043D\u043E\u0432\u043B\u0435\u043D\u043D\u044F",
    "intercept_all_tag_clicks": "\u041F\u043E\u0439\u043C\u0430\u0442\u0438 \u0432\u0441\u0456 \u043A\u043B\u0456\u043A\u0438 \u043F\u043E \u0442\u0435\u0433\u0443",
    "intercept_all_tag_clicks_desc": "\u041F\u0440\u0438 \u0443\u0432\u0456\u043C\u043A\u043D\u0435\u043D\u043D\u0456 \u0432\u0441\u0456 \u043A\u043B\u0456\u043A\u0438 \u043F\u043E \u0442\u0435\u0433\u0443 \u0431\u0443\u0434\u0443\u0442\u044C \u043F\u043E\u0439\u043C\u0430\u043D\u0456 \u0442\u0430 \u0432\u0456\u0434\u043E\u0431\u0440\u0430\u0436\u0430\u0442\u0438\u0441\u044F \u0432 \u0441\u0435\u0442\u0446\u0456",
    "reset_to_default": "\u0421\u043A\u0438\u043D\u0443\u0442\u0438 \u0434\u043E \u0441\u0442\u0430\u043D\u0434\u0430\u0440\u0442\u043D\u0438\u0445 \u0437\u043D\u0430\u0447\u0435\u043D\u044C",
    "reset_to_default_desc": "\u0421\u043A\u0438\u043D\u0443\u0442\u0438 \u0432\u0441\u0456 \u043D\u0430\u043B\u0430\u0448\u0442\u0443\u0432\u0430\u043D\u043D\u044F \u0434\u043E \u0441\u0442\u0430\u043D\u0434\u0430\u0440\u0442\u043D\u0438\u0445 \u0437\u043D\u0430\u0447\u0435\u043D\u044C",
    "settings_reset_notice": "\u041D\u0430\u043B\u0430\u0448\u0442\u0443\u0432\u0430\u043D\u043D\u044F \u0441\u043A\u0438\u043D\u0443\u0442\u043E \u0434\u043E \u0441\u0442\u0430\u043D\u0434\u0430\u0440\u0442\u043D\u0438\u0445 \u0437\u043D\u0430\u0447\u0435\u043D\u044C",
    "ignored_folders_settings": "\u041D\u0430\u043B\u0430\u0448\u0442\u0443\u0432\u0430\u043D\u043D\u044F \u0456\u0433\u043D\u043E\u0440\u043E\u0432\u0430\u043D\u0438\u0445 \u043F\u0430\u043F\u043E\u043A",
    "display_mode_settings": "\u041D\u0430\u043B\u0430\u0448\u0442\u0443\u0432\u0430\u043D\u043D\u044F \u0440\u0435\u0436\u0438\u043C\u0443 \u0432\u0456\u0434\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u043D\u044F",
    "custom_mode_settings": "\u041D\u0430\u043B\u0430\u0448\u0442\u0443\u0432\u0430\u043D\u043D\u044F \u0440\u0435\u0436\u0438\u043C\u0443 \u0432\u0456\u0434\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u043D\u044F",
    "add_custom_mode": "\u0414\u043E\u0434\u0430\u0442\u0438 \u0440\u0435\u0436\u0438\u043C \u0432\u0456\u0434\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u043D\u044F",
    "export": "\u0415\u043A\u0441\u043F\u043E\u0440\u0442",
    "import": "\u0406\u043C\u043F\u043E\u0440\u0442",
    "no_custom_modes_to_export": "\u041D\u0435\u043C\u0430\u0454 \u0440\u0435\u0436\u0438\u043C\u0456\u0432 \u0432\u0456\u0434\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u043D\u044F \u0434\u043B\u044F \u0435\u043A\u0441\u043F\u043E\u0440\u0442\u0443",
    "import_success": "\u0420\u0435\u0436\u0438\u043C\u0438 \u0432\u0456\u0434\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u043D\u044F \u0443\u0441\u043F\u0456\u0448\u043D\u043E \u0456\u043C\u043F\u043E\u0440\u0442\u043E\u0432\u0430\u043D\u0456",
    "import_error": "\u041F\u043E\u043C\u0438\u043B\u043A\u0430 \u0456\u043C\u043F\u043E\u0440\u0442\u0443: \u043D\u0435\u0434\u043E\u043F\u0443\u0441\u0442\u0438\u043C\u0438\u0439 \u0444\u043E\u0440\u043C\u0430\u0442 \u0444\u0430\u0439\u043B\u0443",
    "edit_custom_mode": "\u0420\u0435\u0434\u0430\u0433\u0443\u0432\u0430\u0442\u0438 \u0440\u0435\u0436\u0438\u043C \u0432\u0456\u0434\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u043D\u044F",
    "custom_mode_icon": "\u0406\u043A\u043E\u043D\u043A\u0430",
    "custom_mode_icon_desc": "\u0406\u043A\u043E\u043D\u043A\u0430, \u0432\u0456\u0434\u043E\u0431\u0440\u0430\u0436\u0430\u0454\u0442\u044C\u0441\u044F \u0432 \u043C\u0435\u043D\u044E \u0440\u0435\u0436\u0438\u043C\u0456\u0432",
    "custom_mode_display_name": "\u0412\u0456\u0434\u043E\u0431\u0440\u0430\u0436\u0430\u0454\u0442\u044C\u0441\u044F \u0456\u043C`\u044F",
    "custom_mode_display_name_desc": "\u0412\u0456\u0434\u043E\u0431\u0440\u0430\u0436\u0430\u0454\u0442\u044C\u0441\u044F \u0456\u043C`\u044F \u0432 \u043C\u0435\u043D\u044E \u0440\u0435\u0436\u0438\u043C\u0456\u0432",
    "custom_mode_dataview_code": "\u041A\u043E\u0434 Dataview",
    "custom_mode_dataview_code_desc": "\u0412\u0432\u0435\u0434\u0456\u0442\u044C \u043A\u043E\u0434 Dataview \u0434\u043B\u044F \u043E\u0442\u0440\u0438\u043C\u0430\u043D\u043D\u044F \u0441\u043F\u0438\u0441\u043A\u0443 \u0444\u0430\u0439\u043B\u0456\u0432",
    "display_name_cannot_be_empty": "\u0412\u0456\u0434\u043E\u0431\u0440\u0430\u0436\u0430\u0454\u0442\u044C\u0441\u044F \u0456\u043C`\u044F \u043D\u0435 \u043C\u043E\u0436\u0435 \u0431\u0443\u0442\u0438 \u043F\u043E\u0440\u043E\u0436\u043D\u0456\u043C",
    "custom_mode": "\u0420\u0435\u0436\u0438\u043C \u0432\u0456\u0434\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u043D\u044F",
    "show_bookmarks_mode": "\u041F\u043E\u043A\u0430\u0437\u0443\u0432\u0430\u0442\u0438 \u0440\u0435\u0436\u0438\u043C \u0437\u0430\u043A\u043B\u0430\u0434\u043E\u043A",
    "show_search_mode": "\u041F\u043E\u043A\u0430\u0437\u0443\u0432\u0430\u0442\u0438 \u0440\u0435\u0436\u0438\u043C \u0440\u0435\u0437\u0443\u043B\u044C\u0442\u0430\u0442\u0456\u0432 \u043F\u043E\u0448\u0443\u043A\u0443",
    "show_backlinks_mode": "\u041F\u043E\u043A\u0430\u0437\u0443\u0432\u0430\u0442\u0438 \u0440\u0435\u0436\u0438\u043C \u0437\u0432\u043E\u0440\u043E\u0442\u043D\u0438\u0445 \u043F\u043E\u0441\u0438\u043B\u0430\u043D\u044C",
    "show_outgoinglinks_mode": "\u041F\u043E\u043A\u0430\u0437\u0443\u0432\u0430\u0442\u0438 \u0440\u0435\u0436\u0438\u043C \u0432\u0438\u0445\u0456\u0434\u043D\u0438\u0445 \u043F\u043E\u0441\u0438\u043B\u0430\u043D\u044C",
    "show_all_files_mode": "\u041F\u043E\u043A\u0430\u0437\u0443\u0432\u0430\u0442\u0438 \u0440\u0435\u0436\u0438\u043C \u0443\u0441\u0456\u0445 \u0444\u0430\u0439\u043B\u0456\u0432",
    "show_recent_files_mode": "\u041F\u043E\u043A\u0430\u0437\u0443\u0432\u0430\u0442\u0438 \u0440\u0435\u0436\u0438\u043C \u043E\u0441\u0442\u0430\u043D\u043D\u0456\u0445 \u0444\u0430\u0439\u043B\u0456\u0432",
    "recent_files_count": "\u041A\u0456\u043B\u044C\u043A\u0456\u0441\u0442\u044C \u043E\u0441\u0442\u0430\u043D\u043D\u0456\u0445 \u0444\u0430\u0439\u043B\u0456\u0432",
    "show_random_note_mode": "\u041F\u043E\u043A\u0430\u0437\u0443\u0432\u0430\u0442\u0438 \u0440\u0435\u0436\u0438\u043C \u0432\u0438\u043F\u0430\u0434\u043A\u043E\u0432\u043E\u0457 \u043D\u043E\u0442\u0430\u0442\u043A\u0438",
    "random_note_count": "\u041A\u0456\u043B\u044C\u043A\u0456\u0441\u0442\u044C \u0432\u0438\u043F\u0430\u0434\u043A\u043E\u0432\u0438\u0445 \u043D\u043E\u0442\u0430\u0442\u043E\u043A",
    "random_note_notes_only": "\u0422\u0456\u043B\u044C\u043A\u0438 \u043D\u043E\u0442\u0430\u0442\u043A\u0438",
    "random_note_include_media_files": "\u0412\u043A\u043B\u044E\u0447\u0438\u0442\u0438 \u043C\u0435\u0434\u0456\u0430\u0444\u0430\u0439\u043B\u0438",
    "show_tasks_mode": "\u041F\u043E\u043A\u0430\u0437\u0443\u0432\u0430\u0442\u0438 \u0440\u0435\u0436\u0438\u043C \u0437\u0430\u0432\u0434\u0430\u043D\u044C",
    "task_filter": "\u0424\u0456\u043B\u044C\u0442\u0440 \u0437\u0430\u0432\u0434\u0430\u043D\u044C",
    "uncompleted": "\u041D\u0435\u0437\u0430\u0432\u0435\u0440\u0448\u0435\u043D\u0456",
    "completed": "\u0417\u0430\u0432\u0435\u0440\u0448\u0435\u043D\u0456",
    "foldernote_display_settings": "\u041D\u0430\u043B\u0430\u0448\u0442\u0443\u0432\u0430\u043D\u043D\u044F \u0432\u0456\u0434\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u043D\u044F \u043D\u043E\u0442\u0430\u0442\u043E\u043A \u043F\u0430\u043F\u043A\u0438",
    "foldernote_display_settings_desc": "\u0412\u0441\u0442\u0430\u043D\u043E\u0432\u0456\u0442\u044C \u0440\u0435\u0436\u0438\u043C \u0432\u0456\u0434\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u043D\u044F \u043D\u043E\u0442\u0430\u0442\u043E\u043A \u043F\u0430\u043F\u043A\u0438",
    "all": "\u0412\u0441\u0456",
    "default": "\u041F\u043E \u0443\u043C\u043E\u043B\u0447\u0430\u043D\u0438\u044E",
    "hidden": "\u0421\u0445\u043E\u0432\u0430\u0442\u0438",
    // Show "Parent Folder" option setting
    "show_parent_folder_item": '\u041F\u043E\u043A\u0430\u0437\u0443\u0432\u0430\u0442\u0438 \u0435\u043B\u0435\u043C\u0435\u043D\u0442 "\u0411\u0430\u0442\u044C\u043A\u0456\u0432\u0441\u044C\u043A\u0430 \u043F\u0430\u043F\u043A\u0430"',
    "show_parent_folder_item_desc": '\u041F\u043E\u043A\u0430\u0437\u0443\u0432\u0430\u0442\u0438 \u0435\u043B\u0435\u043C\u0435\u043D\u0442 "\u0411\u0430\u0442\u044C\u043A\u0456\u0432\u0441\u044C\u043A\u0430 \u043F\u0430\u043F\u043A\u0430" \u043F\u0435\u0440\u0448\u0438\u043C \u0443 \u0441\u0456\u0442\u0446\u0456',
    "parent_folder": "\u0411\u0430\u0442\u044C\u043A\u0456\u0432\u0441\u044C\u043A\u0430 \u043F\u0430\u043F\u043A\u0430",
    // Default open location setting
    "default_open_location": "\u041C\u0456\u0441\u0446\u0435 \u0432\u0456\u0434\u043A\u0440\u0438\u0442\u0442\u044F \u0437\u0430 \u0437\u0430\u043C\u043E\u0432\u0447\u0443\u0432\u0430\u043D\u043D\u044F\u043C",
    "default_open_location_desc": "\u0412\u0441\u0442\u0430\u043D\u043E\u0432\u0456\u0442\u044C \u043C\u0456\u0441\u0446\u0435 \u0432\u0456\u0434\u043A\u0440\u0438\u0442\u0442\u044F \u0441\u0456\u0442\u043A\u043E\u0432\u043E\u0433\u043E \u0432\u0438\u0433\u043B\u044F\u0434\u0443 \u0437\u0430 \u0437\u0430\u043C\u043E\u0432\u0447\u0443\u0432\u0430\u043D\u043D\u044F\u043C",
    "open_in_left_sidebar": "\u0412\u0456\u0434\u043A\u0440\u0438\u0442\u0438 \u0432 \u043B\u0456\u0432\u0456\u0439 \u0431\u0456\u0447\u043D\u0456\u0439 \u043F\u0430\u043D\u0435\u043B\u0456",
    "open_in_right_sidebar": "\u0412\u0456\u0434\u043A\u0440\u0438\u0442\u0438 \u0432 \u043F\u0440\u0430\u0432\u0456\u0439 \u0431\u0456\u0447\u043D\u0456\u0439 \u043F\u0430\u043D\u0435\u043B\u0456",
    "open_in_new_tab": "\u0412\u0456\u0434\u043A\u0440\u0438\u0442\u0438 \u0432 \u043D\u043E\u0432\u0456\u0439 \u0432\u043A\u043B\u0430\u0434\u0446\u0456",
    "reuse_existing_leaf": "\u0412\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0432\u0430\u0442\u0438 \u043D\u0430\u044F\u0432\u043D\u0438\u0439 \u0432\u0438\u0433\u043B\u044F\u0434",
    "reuse_existing_leaf_desc": "\u041F\u0440\u0438 \u0432\u0456\u0434\u043A\u0440\u0438\u0442\u0442\u0456 \u0441\u0456\u0442\u043A\u043E\u0432\u043E\u0433\u043E \u0432\u0438\u0433\u043B\u044F\u0434\u0443 \u043D\u0430\u0434\u0430\u0432\u0430\u0442\u0438 \u043F\u0435\u0440\u0435\u0432\u0430\u0433\u0443 \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u0430\u043D\u043D\u044E \u043D\u0430\u044F\u0432\u043D\u043E\u0433\u043E \u0432\u0438\u0433\u043B\u044F\u0434\u0443 \u0437\u0430\u043C\u0456\u0441\u0442\u044C \u0441\u0442\u0432\u043E\u0440\u0435\u043D\u043D\u044F \u043D\u043E\u0432\u043E\u0433\u043E",
    "custom_document_extensions": "\u041A\u043E\u0440\u0438\u0441\u0442\u0443\u0432\u0430\u0446\u044C\u043A\u0456 \u0440\u043E\u0437\u0448\u0438\u0440\u0435\u043D\u043D\u044F \u0434\u043E\u043A\u0443\u043C\u0435\u043D\u0442\u0456\u0432",
    "custom_document_extensions_desc": "\u0414\u043E\u0434\u0430\u0442\u043A\u043E\u0432\u0456 \u0440\u043E\u0437\u0448\u0438\u0440\u0435\u043D\u043D\u044F \u0434\u043E\u043A\u0443\u043C\u0435\u043D\u0442\u0456\u0432 (\u0447\u0435\u0440\u0435\u0437 \u043A\u043E\u043C\u0443, \u0431\u0435\u0437 \u043A\u0440\u0430\u043F\u043E\u043A)",
    "custom_document_extensions_placeholder": "\u043D\u0430\u043F\u0440\u0438\u043A\u043B\u0430\u0434, txt,doc,docx",
    "custom_folder_icon": "\u041A\u043E\u0440\u0438\u0441\u0442\u0443\u0432\u0430\u0446\u044C\u043A\u0430 \u0456\u043A\u043E\u043D\u043A\u0430 \u043F\u0430\u043F\u043A\u0438",
    "custom_folder_icon_desc": "\u041A\u043E\u0440\u0438\u0441\u0442\u0443\u0432\u0430\u0446\u044C\u043A\u0430 \u0456\u043A\u043E\u043D\u043A\u0430 \u043F\u0430\u043F\u043A\u0438 (\u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0439\u0442\u0435 Emoji)",
    // Select Folder Dialog
    "select_folders": "\u0412\u0438\u0431\u0440\u0430\u0442\u0438 \u043F\u0430\u043F\u043A\u0443",
    "select_folders_to_ignore": "\u0412\u0438\u0431\u0440\u0430\u0442\u0438 \u043F\u0430\u043F\u043A\u0438 \u0434\u043B\u044F \u0456\u0433\u043D\u043E\u0440\u0443\u0432\u0430\u043D\u043D\u044F",
    "open_grid_view": "\u0412\u0456\u0434\u043A\u0440\u0438\u0442\u0438 \u0441\u0456\u0442\u043A\u043E\u0432\u0438\u0439 \u0432\u0438\u0433\u043B\u044F\u0434",
    "open_in_grid_view": "\u0412\u0456\u0434\u043A\u0440\u0438\u0442\u0438 \u0432 \u0441\u0456\u0442\u043A\u043E\u0432\u043E\u043C\u0443 \u0432\u0438\u0433\u043B\u044F\u0434\u0456",
    "open_note_in_grid_view": "\u0412\u0456\u0434\u043A\u0440\u0438\u0442\u0438 \u043D\u043E\u0442\u0430\u0442\u043A\u0443 \u0432 \u0441\u0456\u0442\u043A\u043E\u0432\u043E\u043C\u0443 \u0432\u0438\u0433\u043B\u044F\u0434\u0456",
    "open_backlinks_in_grid_view": "\u0412\u0456\u0434\u043A\u0440\u0438\u0442\u0438 \u0437\u0432\u043E\u0440\u043E\u0442\u043D\u0456 \u043F\u043E\u0441\u0438\u043B\u0430\u043D\u043D\u044F \u0432 \u0441\u0456\u0442\u043A\u043E\u0432\u043E\u043C\u0443 \u0432\u0438\u0433\u043B\u044F\u0434\u0456",
    "open_outgoinglinks_in_grid_view": "\u0412\u0456\u0434\u043A\u0440\u0438\u0442\u0438 \u0432\u0438\u0445\u0456\u0434\u043D\u0456 \u043F\u043E\u0441\u0438\u043B\u0430\u043D\u043D\u044F \u0432 \u0441\u0456\u0442\u043A\u043E\u0432\u043E\u043C\u0443 \u0432\u0438\u0433\u043B\u044F\u0434\u0456",
    "open_recent_files_in_grid_view": "\u0412\u0456\u0434\u043A\u0440\u0438\u0442\u0438 \u043F\u043E\u0442\u043E\u0447\u043D\u0443 \u043D\u043E\u0442\u0430\u0442\u043A\u0443 \u0432 \u043E\u0441\u0442\u0430\u043D\u043D\u0456\u0445 \u0444\u0430\u0439\u043B\u0430\u0445",
    "open_settings": "\u0412\u0456\u0434\u043A\u0440\u0438\u0442\u0438 \u043D\u0430\u043B\u0430\u0448\u0442\u0443\u0432\u0430\u043D\u043D\u044F",
    "open_new_grid_view": "\u0412\u0456\u0434\u043A\u0440\u0438\u0442\u0438 \u043D\u043E\u0432\u0438\u0439 \u0441\u0456\u0442\u043A\u043E\u0432\u0438\u0439 \u0432\u0438\u0433\u043B\u044F\u0434",
    "open_in_new_grid_view": "\u0412\u0456\u0434\u043A\u0440\u0438\u0442\u0438 \u0432 \u043D\u043E\u0432\u043E\u043C\u0443 \u0441\u0456\u0442\u043A\u043E\u0432\u043E\u043C\u0443 \u0432\u0438\u0433\u043B\u044F\u0434\u0456",
    "min_mode": "\u041C\u0456\u043D\u0456\u043C\u0456\u0437\u0443\u0432\u0430\u0442\u0438 \u0432\u0438\u0433\u043B\u044F\u0434",
    "show_ignored_folders": "\u041F\u043E\u043A\u0430\u0437\u0430\u0442\u0438 \u0456\u0433\u043D\u043E\u0440\u043E\u0432\u0430\u043D\u0456 \u043F\u0430\u043F\u043A\u0438",
    "delete_note": "\u0412\u0438\u0434\u0430\u043B\u0438\u0442\u0438 \u0444\u0430\u0439\u043B",
    "open_folder_note": "\u0412\u0456\u0434\u043A\u0440\u0438\u0442\u0438 \u043D\u043E\u0442\u0430\u0442\u043A\u0443 \u043F\u0430\u043F\u043A\u0438",
    "create_folder_note": "\u0421\u0442\u0432\u043E\u0440\u0438\u0442\u0438 \u043D\u043E\u0442\u0430\u0442\u043A\u0443 \u043F\u0430\u043F\u043A\u0438",
    "delete_folder_note": "\u0412\u0438\u0434\u0430\u043B\u0438\u0442\u0438 \u043D\u043E\u0442\u0430\u0442\u043A\u0443 \u043F\u0430\u043F\u043A\u0438",
    "edit_folder_note_settings": "\u0420\u0435\u0434\u0430\u0433\u0443\u0432\u0430\u0442\u0438 \u043D\u0430\u043B\u0430\u0448\u0442\u0443\u0432\u0430\u043D\u043D\u044F \u043D\u043E\u0442\u0430\u0442\u043A\u0438 \u043F\u0430\u043F\u043A\u0438",
    "ignore_folder": "\u0406\u0433\u043D\u043E\u0440\u0443\u0432\u0430\u0442\u0438 \u0446\u044E \u043F\u0430\u043F\u043A\u0443",
    "unignore_folder": "\u0412\u0456\u0434\u043C\u0456\u043D\u0438\u0442\u0438 \u0456\u0433\u043D\u043E\u0440\u0443\u0432\u0430\u043D\u043D\u044F \u0446\u0456\u0454\u0457 \u043F\u0430\u043F\u043A\u0438",
    "searching": "\u041F\u043E\u0448\u0443\u043A...",
    "no_files": "\u0424\u0430\u0439\u043B\u0438 \u043D\u0435 \u0437\u043D\u0430\u0439\u0434\u0435\u043D\u043E",
    "filter_folders": "\u0424\u0456\u043B\u044C\u0442\u0440\u0443\u0432\u0430\u0442\u0438 \u043F\u0430\u043F\u043A\u0438...",
    // Folder Note Settings Dialog
    "folder_note_settings": "\u041D\u0430\u043B\u0430\u0448\u0442\u0443\u0432\u0430\u043D\u043D\u044F \u043D\u043E\u0442\u0430\u0442\u043A\u0438 \u043F\u0430\u043F\u043A\u0438",
    "folder_sort_type": "\u0422\u0438\u043F \u0441\u043E\u0440\u0442\u0443\u0432\u0430\u043D\u043D\u044F \u043F\u0430\u043F\u043A\u0438",
    "folder_sort_type_desc": "\u0412\u0441\u0442\u0430\u043D\u043E\u0432\u0456\u0442\u044C \u0442\u0438\u043F \u0441\u043E\u0440\u0442\u0443\u0432\u0430\u043D\u043D\u044F \u0437\u0430 \u0437\u0430\u043C\u043E\u0432\u0447\u0443\u0432\u0430\u043D\u043D\u044F\u043C \u0434\u043B\u044F \u0446\u0456\u0454\u0457 \u043F\u0430\u043F\u043A\u0438",
    "folder_color": "\u041A\u043E\u043B\u0456\u0440 \u043F\u0430\u043F\u043A\u0438",
    "folder_color_desc": "\u0412\u0441\u0442\u0430\u043D\u043E\u0432\u0456\u0442\u044C \u043A\u043E\u043B\u0456\u0440 \u0432\u0456\u0434\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u043D\u044F \u0434\u043B\u044F \u0446\u0456\u0454\u0457 \u043F\u0430\u043F\u043A\u0438",
    "folder_icon": "\u0406\u043A\u043E\u043D\u043A\u0430 \u043F\u0430\u043F\u043A\u0438",
    "folder_icon_desc": "\u0412\u0441\u0442\u0430\u043D\u043E\u0432\u0456\u0442\u044C \u0456\u043A\u043E\u043D\u043A\u0443 \u0432\u0456\u0434\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u043D\u044F \u0434\u043B\u044F \u0446\u0456\u0454\u0457 \u043F\u0430\u043F\u043A\u0438",
    "default_sort": "\u0412\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0432\u0430\u0442\u0438 \u0441\u043E\u0440\u0442\u0443\u0432\u0430\u043D\u043D\u044F \u0437\u0430 \u0437\u0430\u043C\u043E\u0432\u0447\u0443\u0432\u0430\u043D\u043D\u044F\u043C",
    "no_color": "\u0411\u0435\u0437 \u043A\u043E\u043B\u044C\u043E\u0440\u0443",
    "color_red": "\u0427\u0435\u0440\u0432\u043E\u043D\u0438\u0439",
    "color_orange": "\u041F\u043E\u043C\u0430\u0440\u0430\u043D\u0447\u0435\u0432\u0438\u0439",
    "color_yellow": "\u0416\u043E\u0432\u0442\u0438\u0439",
    "color_green": "\u0417\u0435\u043B\u0435\u043D\u0438\u0439",
    "color_cyan": "\u0411\u0456\u0440\u044E\u0437\u043E\u0432\u0438\u0439",
    "color_blue": "\u0421\u0438\u043D\u0456\u0439",
    "color_purple": "\u0424\u0456\u043E\u043B\u0435\u0442\u043E\u0432\u0438\u0439",
    "color_pink": "\u0420\u043E\u0436\u0435\u0432\u0438\u0439",
    "confirm": "\u041F\u0456\u0434\u0442\u0432\u0435\u0440\u0434\u0438\u0442\u0438",
    "note_attribute_settings": "\u041D\u0430\u043B\u0430\u0448\u0442\u0443\u0432\u0430\u043D\u043D\u044F \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u0456\u0432 \u043D\u043E\u0442\u0430\u0442\u043A\u0438",
    "note_title": "\u041D\u0430\u0437\u0432\u0430 \u043D\u043E\u0442\u0430\u0442\u043A\u0438",
    "note_title_desc": "\u0412\u0441\u0442\u0430\u043D\u043E\u0432\u0456\u0442\u044C \u043D\u0430\u0437\u0432\u0443 \u0432\u0456\u0434\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u043D\u044F \u0434\u043B\u044F \u0446\u0456\u0454\u0457 \u043D\u043E\u0442\u0430\u0442\u043A\u0438",
    "note_summary": "\u041A\u0440\u0430\u0442\u043A\u0438\u0439 \u043E\u043F\u0438\u0441",
    "note_summary_desc": "\u0412\u0441\u0442\u0430\u043D\u043E\u0432\u0456\u0442\u044C \u043A\u0440\u0430\u0442\u043A\u0438\u0439 \u043E\u043F\u0438\u0441 \u0434\u043B\u044F \u0446\u0456\u0454\u0457 \u043D\u043E\u0442\u0430\u0442\u043A\u0438",
    "note_color": "\u041A\u043E\u043B\u0456\u0440 \u043D\u043E\u0442\u0430\u0442\u043A\u0438",
    "note_color_desc": "\u0412\u0441\u0442\u0430\u043D\u043E\u0432\u0456\u0442\u044C \u043A\u043E\u043B\u0456\u0440 \u0432\u0456\u0434\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u043D\u044F \u0434\u043B\u044F \u0446\u0456\u0454\u0457 \u043D\u043E\u0442\u0430\u0442\u043A\u0438",
    "set_note_attribute": "\u0412\u0441\u0442\u0430\u043D\u043E\u0432\u0438\u0442\u0438 \u0430\u0442\u0440\u0438\u0431\u0443\u0442\u0438 \u043D\u043E\u0442\u0430\u0442\u043A\u0438",
    "rename_folder": "\u041F\u0435\u0440\u0435\u0439\u043C\u0435\u043D\u0443\u0432\u0430\u0442\u0438 \u043F\u0430\u043F\u043A\u0443",
    "enter_new_folder_name": "\u0412\u0432\u0435\u0434\u0456\u0442\u044C \u043D\u043E\u0432\u0443 \u043D\u0430\u0437\u0432\u0443 \u043F\u0430\u043F\u043A\u0438",
    "search_selection_in_grid_view": "\u041F\u043E\u0448\u0443\u043A ... \u0443 \u0441\u0456\u0442\u043A\u043E\u0432\u043E\u043C\u0443 \u0432\u0438\u0433\u043B\u044F\u0434\u0456",
    "show_date_dividers": "\u041F\u043E\u043A\u0430\u0437\u0443\u0432\u0430\u0442\u0438 \u0440\u043E\u0437\u0434\u0456\u043B\u044C\u043D\u0438\u043A\u0438 \u0434\u0430\u0442",
    "show_date_dividers_desc": "\u041F\u043E\u043A\u0430\u0437\u0443\u0432\u0430\u0442\u0438 \u0440\u043E\u0437\u0434\u0456\u043B\u044C\u043D\u0438\u043A\u0438 \u0434\u0430\u0442 \u043F\u0435\u0440\u0435\u0434 \u043F\u0435\u0440\u0448\u0438\u043C \u0435\u043B\u0435\u043C\u0435\u043D\u0442\u043E\u043C \u043A\u043E\u0436\u043D\u043E\u0433\u043E \u043D\u043E\u0432\u043E\u0433\u043E \u0434\u043D\u044F \u043F\u0440\u0438 \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u0430\u043D\u043D\u0456 \u0441\u043E\u0440\u0442\u0443\u0432\u0430\u043D\u043D\u044F \u0437\u0430 \u0434\u0430\u0442\u043E\u044E",
    "date_divider_format": "\u0424\u043E\u0440\u043C\u0430\u0442 \u0440\u043E\u0437\u0434\u0456\u043B\u044C\u043D\u0438\u043A\u0430 \u0434\u0430\u0442",
    "date_divider_mode": "\u0420\u043E\u0437\u0434\u0456\u043B\u044C\u043D\u0438\u043A \u0434\u0430\u0442",
    "date_divider_mode_desc": "\u0412\u0438\u0431\u0435\u0440\u0456\u0442\u044C \u0440\u0435\u0436\u0438\u043C \u0432\u0456\u0434\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u043D\u044F \u0440\u043E\u0437\u0434\u0456\u043B\u044C\u043D\u0438\u043A\u0456\u0432 \u0434\u0430\u0442",
    "date_divider_mode_none": "\u041D\u0435\u043C\u0430\u0454",
    "date_divider_mode_year": "\u0420\u0456\u043A",
    "date_divider_mode_month": "\u041C\u0456\u0441\u044F\u0446\u044C",
    "date_divider_mode_day": "\u0414\u0435\u043D\u044C",
    "pinned": "\u0417\u0430\u043A\u0440\u0456\u043F\u043B\u0435\u043D\u043E",
    "pinned_desc": "\u0417\u0430\u043A\u0440\u0456\u043F\u0438\u0442\u0438 \u0444\u0430\u0439\u043B \u0443\u0433\u043E\u0440\u0456",
    "foldernote_pinned": "\u0417\u0430\u043A\u0440\u0456\u043F\u0438\u0442\u0438 \u043D\u043E\u0442\u0430\u0442\u043A\u0438 \u043F\u0430\u043F\u043A\u0438",
    "foldernote_pinned_desc": "\u0417\u0430\u043A\u0440\u0456\u043F\u0438\u0442\u0438 \u043D\u043E\u0442\u0430\u0442\u043A\u0438 \u043F\u0430\u043F\u043A\u0438 \u0443\u0433\u043E\u0440\u0456",
    "display_minimized": "\u041C\u0456\u043D\u0456\u043C\u0456\u0437\u043E\u0432\u0430\u043D\u0438\u0439 \u0432\u0438\u0433\u043B\u044F\u0434",
    "display_minimized_desc": "\u041F\u043E\u043A\u0430\u0437\u0443\u0432\u0430\u0442\u0438 \u0446\u044E \u043D\u043E\u0442\u0430\u0442\u043A\u0443 \u0443 \u043C\u0456\u043D\u0456\u043C\u0456\u0437\u043E\u0432\u0430\u043D\u043E\u043C\u0443 \u0440\u0435\u0436\u0438\u043C\u0456",
    // Quick Access Settings and Commands
    "quick_access_settings_title": "\u041D\u0430\u043B\u0430\u0448\u0442\u0443\u0432\u0430\u043D\u043D\u044F \u0428\u0432\u0438\u0434\u043A\u043E\u0433\u043E \u0434\u043E\u0441\u0442\u0443\u043F\u0443",
    "quick_access_folder_name": "\u041F\u0430\u043F\u043A\u0430 \u0448\u0432\u0438\u0434\u043A\u043E\u0433\u043E \u0434\u043E\u0441\u0442\u0443\u043F\u0443",
    "quick_access_folder_desc": "\u0412\u0441\u0442\u0430\u043D\u043E\u0432\u0456\u0442\u044C \u043F\u0430\u043F\u043A\u0443, \u0449\u043E \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0454\u0442\u044C\u0441\u044F \u043A\u043E\u043C\u0430\u043D\u0434\u043E\u044E \xAB\u0412\u0456\u0434\u043A\u0440\u0438\u0442\u0438 \u043F\u0430\u043F\u043A\u0443 \u0448\u0432\u0438\u0434\u043A\u043E\u0433\u043E \u0434\u043E\u0441\u0442\u0443\u043F\u0443\xBB",
    "quick_access_mode_name": "\u0420\u0435\u0436\u0438\u043C \u0448\u0432\u0438\u0434\u043A\u043E\u0433\u043E \u0434\u043E\u0441\u0442\u0443\u043F\u0443",
    "quick_access_mode_desc": "\u0412\u0441\u0442\u0430\u043D\u043E\u0432\u0456\u0442\u044C \u0440\u0435\u0436\u0438\u043C \u0437\u0430 \u0437\u0430\u043C\u043E\u0432\u0447\u0443\u0432\u0430\u043D\u043D\u044F\u043C, \u0449\u043E \u0432\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0454\u0442\u044C\u0441\u044F \u043A\u043E\u043C\u0430\u043D\u0434\u043E\u044E \xAB\u0412\u0456\u0434\u043A\u0440\u0438\u0442\u0438 \u0448\u0432\u0438\u0434\u043A\u0438\u0439 \u0434\u043E\u0441\u0442\u0443\u043F \u0437\u0430 \u0440\u0435\u0436\u0438\u043C\u043E\u043C\xBB",
    "use_quick_access_as_new_tab_view": "\u0412\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0432\u0430\u0442\u0438 \u0428\u0432\u0438\u0434\u043A\u0438\u0439 \u0434\u043E\u0441\u0442\u0443\u043F \u044F\u043A \u043D\u043E\u0432\u0443 \u0432\u043A\u043B\u0430\u0434\u043A\u0443",
    "use_quick_access_as_new_tab_view_desc": "\u0417\u0430\u043C\u0456\u043D\u0456\u0442\u044C \u0441\u0442\u0430\u043D\u0434\u0430\u0440\u0442\u043D\u0438\u0439 \u0432\u0438\u0434 \xAB\u041D\u043E\u0432\u0430 \u0432\u043A\u043B\u0430\u0434\u043A\u0430\xBB \u043D\u0430 \u0441\u0456\u0442\u043A\u043E\u0432\u0438\u0439 \u0432\u0438\u0434 \u043E\u0431\u0440\u0430\u043D\u043E\u0457 \u043E\u043F\u0446\u0456\u0457 \u0428\u0432\u0438\u0434\u043A\u043E\u0433\u043E \u0434\u043E\u0441\u0442\u0443\u043F\u0443 (\u043F\u0430\u043F\u043A\u0438 \u0430\u0431\u043E \u0440\u0435\u0436\u0438\u043C\u0443). \u041F\u0440\u0430\u0446\u044E\u0454, \u043B\u0438\u0448\u0435 \u044F\u043A\u0449\u043E \xAB\u041C\u0456\u0441\u0446\u0435 \u0432\u0456\u0434\u043A\u0440\u0438\u0442\u0442\u044F \u0437\u0430 \u0437\u0430\u043C\u043E\u0432\u0447\u0443\u0432\u0430\u043D\u043D\u044F\u043C\xBB \u0432\u0441\u0442\u0430\u043D\u043E\u0432\u043B\u0435\u043D\u043E \u043D\u0430 \xAB\u0412\u0456\u0434\u043A\u0440\u0438\u0442\u0438 \u0432 \u043D\u043E\u0432\u0456\u0439 \u0432\u043A\u043B\u0430\u0434\u0446\u0456\xBB!",
    "default_new_tab": "\u0417\u0430 \u0437\u0430\u043C\u043E\u0432\u0447\u0443\u0432\u0430\u043D\u043D\u044F\u043C",
    "use_quick_access_folder": "\u0412\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0432\u0430\u0442\u0438 \u043F\u0430\u043F\u043A\u0443 \u0448\u0432\u0438\u0434\u043A\u043E\u0433\u043E \u0434\u043E\u0441\u0442\u0443\u043F\u0443",
    "use_quick_access_mode": "\u0412\u0438\u043A\u043E\u0440\u0438\u0441\u0442\u043E\u0432\u0443\u0432\u0430\u0442\u0438 \u0440\u0435\u0436\u0438\u043C \u0448\u0432\u0438\u0434\u043A\u043E\u0433\u043E \u0434\u043E\u0441\u0442\u0443\u043F\u0443",
    "open_quick_access_folder": "\u0412\u0456\u0434\u043A\u0440\u0438\u0442\u0438 \u043F\u0430\u043F\u043A\u0443 \u0448\u0432\u0438\u0434\u043A\u043E\u0433\u043E \u0434\u043E\u0441\u0442\u0443\u043F\u0443",
    "open_quick_access_mode": "\u0412\u0456\u0434\u043A\u0440\u0438\u0442\u0438 \u0448\u0432\u0438\u0434\u043A\u0438\u0439 \u0434\u043E\u0441\u0442\u0443\u043F \u0437\u0430 \u0440\u0435\u0436\u0438\u043C\u043E\u043C"
  }
};

// src/FolderSelectionModal.ts
function showFolderSelectionModal(app, plugin, activeView) {
  new FolderSelectionModal(app, plugin, activeView).open();
}
var FolderSelectionModal = class extends import_obsidian.Modal {
  constructor(app, plugin, activeView) {
    super(app);
    this.folderOptions = [];
    this.selectedIndex = -1;
    this.plugin = plugin;
    this.activeView = activeView;
  }
  onOpen() {
    const { contentEl } = this;
    contentEl.empty();
    const searchContainer = contentEl.createEl("div", {
      cls: "ge-folder-search-container"
    });
    this.searchInput = searchContainer.createEl("input", {
      cls: "ge-folder-search-input",
      attr: {
        type: "text",
        placeholder: t("filter_folders"),
        ...import_obsidian.Platform.isMobile && { tabindex: "1" }
      }
    });
    this.folderOptionsContainer = contentEl.createEl("div", {
      cls: "ge-folder-options-container",
      attr: import_obsidian.Platform.isMobile ? { tabindex: "0" } : {}
    });
    this.searchInput.addEventListener("input", () => {
      const searchTerm = this.searchInput.value.toLowerCase();
      this.filterFolderOptions(searchTerm);
    });
    this.searchInput.addEventListener("keydown", this.handleKeyDown.bind(this));
    const enabledCustomModes = this.plugin.settings.customModes.filter((m) => {
      var _a;
      return (_a = m.enabled) != null ? _a : true;
    });
    if (enabledCustomModes.length > 0) {
      enabledCustomModes.forEach((mode) => {
        const customOption = this.folderOptionsContainer.createEl("div", {
          cls: "ge-grid-view-folder-option",
          text: `${mode.icon} ${mode.displayName}`
        });
        customOption.addEventListener("click", () => {
          if (this.activeView) {
            this.activeView.setSource(mode.internalName, "", true);
          } else {
            this.plugin.activateView(mode.internalName);
          }
          this.close();
        });
        this.folderOptions.push(customOption);
      });
    }
    if (this.plugin.settings.showBookmarksMode) {
      const bookmarksPlugin = this.app.internalPlugins.plugins.bookmarks;
      if (bookmarksPlugin == null ? void 0 : bookmarksPlugin.enabled) {
        const bookmarkOption = this.folderOptionsContainer.createEl("div", {
          cls: "ge-grid-view-folder-option",
          text: `\u{1F4D1} ${t("bookmarks_mode")}`
        });
        bookmarkOption.addEventListener("click", () => {
          if (this.activeView) {
            this.activeView.setSource("bookmarks", "", true);
          } else {
            this.plugin.activateView("bookmarks");
          }
          this.close();
        });
        this.folderOptions.push(bookmarkOption);
      }
    }
    if (this.plugin.settings.showSearchMode) {
      const searchLeaf = this.app.workspace.getLeavesOfType("search")[0];
      if (searchLeaf) {
        const searchView = searchLeaf.view;
        const searchInputEl = searchView.searchComponent ? searchView.searchComponent.inputEl : null;
        if (searchInputEl) {
          if (searchInputEl.value.trim().length > 0) {
            const searchOption = this.folderOptionsContainer.createEl("div", {
              cls: "ge-grid-view-folder-option",
              text: `\u{1F50D} ${t("search_results")}: ${searchInputEl.value}`
            });
            searchOption.addEventListener("click", () => {
              if (this.activeView) {
                this.activeView.setSource("search", "", true);
              } else {
                this.plugin.activateView("search");
              }
              this.close();
            });
            this.folderOptions.push(searchOption);
          }
        }
      }
    }
    if (this.plugin.settings.showBacklinksMode) {
      const activeFile = this.app.workspace.getActiveFile();
      if (activeFile) {
        const activeFileName = activeFile ? `: ${activeFile.basename}` : "";
        const backlinksOption = this.folderOptionsContainer.createEl("div", {
          cls: "ge-grid-view-folder-option",
          text: `\u{1F517} ${t("backlinks_mode")}${activeFileName}`
        });
        backlinksOption.addEventListener("click", () => {
          if (this.activeView) {
            this.activeView.setSource("backlinks", "", true);
          } else {
            this.plugin.activateView("backlinks");
          }
          this.close();
        });
        this.folderOptions.push(backlinksOption);
      }
    }
    if (this.plugin.settings.showOutgoinglinksMode) {
      const activeFile = this.app.workspace.getActiveFile();
      if (activeFile) {
        const activeFileName = activeFile ? `: ${activeFile.basename}` : "";
        const outgoinglinksOption = this.folderOptionsContainer.createEl("div", {
          cls: "ge-grid-view-folder-option",
          text: `\u{1F517} ${t("outgoinglinks_mode")}${activeFileName}`
        });
        outgoinglinksOption.addEventListener("click", () => {
          if (this.activeView) {
            this.activeView.setSource("outgoinglinks", "", true);
          } else {
            this.plugin.activateView("outgoinglinks");
          }
          this.close();
        });
        this.folderOptions.push(outgoinglinksOption);
      }
    }
    if (this.plugin.settings.showRecentFilesMode) {
      const recentFilesOption = this.folderOptionsContainer.createEl("div", {
        cls: "ge-grid-view-folder-option",
        text: `\u{1F4C5} ${t("recent_files_mode")}`
      });
      recentFilesOption.addEventListener("click", () => {
        if (this.activeView) {
          this.activeView.setSource("recent-files", "", true);
        } else {
          this.plugin.activateView("recent-files");
        }
        this.close();
      });
      this.folderOptions.push(recentFilesOption);
    }
    if (this.plugin.settings.showAllFilesMode) {
      const allFilesOption = this.folderOptionsContainer.createEl("div", {
        cls: "ge-grid-view-folder-option",
        text: `\u{1F4D4} ${t("all_files_mode")}`
      });
      allFilesOption.addEventListener("click", () => {
        if (this.activeView) {
          this.activeView.setSource("all-files", "", true);
        } else {
          this.plugin.activateView("all-files");
        }
        this.close();
      });
      this.folderOptions.push(allFilesOption);
    }
    if (this.plugin.settings.showRandomNoteMode) {
      const randomNoteOption = this.folderOptionsContainer.createEl("div", {
        cls: "ge-grid-view-folder-option",
        text: `\u{1F3B2} ${t("random_note_mode")}`
      });
      randomNoteOption.addEventListener("click", () => {
        if (this.activeView) {
          this.activeView.setSource("random-note", "", true);
        } else {
          this.plugin.activateView("random-note");
        }
        this.close();
      });
      this.folderOptions.push(randomNoteOption);
    }
    if (this.plugin.settings.showTasksMode) {
      const tasksOption = this.folderOptionsContainer.createEl("div", {
        cls: "ge-grid-view-folder-option",
        text: `\u2611\uFE0F ${t("tasks_mode")}`
      });
      tasksOption.addEventListener("click", () => {
        if (this.activeView) {
          this.activeView.setSource("tasks", "", true);
        } else {
          this.plugin.activateView("tasks");
        }
        this.close();
      });
      this.folderOptions.push(tasksOption);
    }
    const rootFolderOption = this.folderOptionsContainer.createEl("div", {
      cls: "ge-grid-view-folder-option",
      text: `\u{1F4C1} /`
    });
    rootFolderOption.addEventListener("click", () => {
      if (this.activeView) {
        this.activeView.setSource("folder", "/", true);
      } else {
        this.plugin.activateView("folder", "/");
      }
      this.close();
    });
    this.folderOptions.push(rootFolderOption);
    const folders = this.app.vault.getAllFolders().filter((folder) => {
      return !this.plugin.settings.ignoredFolders.some(
        (ignoredPath) => folder.path === ignoredPath || folder.path.startsWith(ignoredPath + "/")
      );
    }).sort((a, b) => a.path.localeCompare(b.path));
    folders.forEach((folder) => {
      const depth = (folder.path.match(/\//g) || []).length;
      const displayName = folder.path.split("/").pop() || "/";
      const folderOption = this.folderOptionsContainer.createEl("div", {
        cls: "ge-grid-view-folder-option",
        attr: {
          "data-depth": depth.toString(),
          "data-path": folder.path
        }
      });
      const prefixSpan = document.createElement("span");
      prefixSpan.className = "ge-folder-tree-prefix";
      prefixSpan.textContent = depth > 0 ? "   ".repeat(depth - 1) + "\u2514 " : "";
      folderOption.appendChild(prefixSpan);
      const icon = document.createElement("span");
      icon.textContent = "\u{1F4C1} ";
      folderOption.appendChild(icon);
      const nameSpan = document.createElement("span");
      nameSpan.textContent = displayName;
      folderOption.appendChild(nameSpan);
      folderOption.addEventListener("click", () => {
        if (this.activeView) {
          this.activeView.setSource("folder", folder.path, true);
        } else {
          this.plugin.activateView("folder", folder.path);
        }
        this.close();
      });
      this.folderOptions.push(folderOption);
    });
    this.folderOptions.forEach((option, index) => {
      option.addEventListener("mouseenter", () => {
        this.updateSelection(index);
      });
    });
  }
  // 處理鍵盤事件
  handleKeyDown(event) {
    const visibleOptions = this.getVisibleOptions();
    if (visibleOptions.length === 0)
      return;
    switch (event.key) {
      case "ArrowDown":
        event.preventDefault();
        this.moveSelection(1, visibleOptions);
        break;
      case "ArrowUp":
        event.preventDefault();
        this.moveSelection(-1, visibleOptions);
        break;
      case "Enter":
        event.preventDefault();
        if (this.selectedIndex >= 0) {
          const selectedOption = this.folderOptions[this.selectedIndex];
          if (selectedOption && selectedOption.style.display !== "none") {
            selectedOption.click();
          }
        }
        break;
      case "Escape":
        this.close();
        break;
    }
  }
  // 移動選擇
  moveSelection(direction, visibleOptions) {
    let currentVisibleIndex = -1;
    if (this.selectedIndex >= 0) {
      const selectedOption = this.folderOptions[this.selectedIndex];
      currentVisibleIndex = visibleOptions.indexOf(selectedOption);
    }
    let newVisibleIndex = currentVisibleIndex + direction;
    if (newVisibleIndex < 0) {
      newVisibleIndex = visibleOptions.length - 1;
    } else if (newVisibleIndex >= visibleOptions.length) {
      newVisibleIndex = 0;
    }
    if (newVisibleIndex >= 0 && newVisibleIndex < visibleOptions.length) {
      const newSelectedOption = visibleOptions[newVisibleIndex];
      const newIndex = this.folderOptions.indexOf(newSelectedOption);
      this.updateSelection(newIndex);
      newSelectedOption.scrollIntoView({ block: "nearest" });
    }
  }
  // 更新選擇
  updateSelection(index) {
    if (this.selectedIndex >= 0 && this.selectedIndex < this.folderOptions.length) {
      this.folderOptions[this.selectedIndex].removeClass("ge-selected-option");
    }
    this.selectedIndex = index;
    if (this.selectedIndex >= 0 && this.selectedIndex < this.folderOptions.length) {
      this.folderOptions[this.selectedIndex].addClass("ge-selected-option");
    }
  }
  // 獲取當前可見的選項
  getVisibleOptions() {
    return this.folderOptions.filter(
      (option) => option.style.display !== "none"
    );
  }
  // 篩選資料夾選項
  filterFolderOptions(searchTerm) {
    let hasVisibleOptions = false;
    this.folderOptions.forEach((option) => {
      var _a, _b;
      const text = ((_a = option.textContent) == null ? void 0 : _a.toLowerCase()) || "";
      const fullPath = ((_b = option.getAttribute("data-path")) == null ? void 0 : _b.toLowerCase()) || "";
      if (searchTerm === "" || text.includes(searchTerm) || fullPath.includes(searchTerm)) {
        option.style.display = "block";
        hasVisibleOptions = true;
      } else {
        option.style.display = "none";
      }
    });
    this.updateSelection(-1);
    if (hasVisibleOptions) {
      const visibleOptions = this.getVisibleOptions();
      if (visibleOptions.length > 0) {
        const firstVisibleIndex = this.folderOptions.indexOf(visibleOptions[0]);
        this.updateSelection(firstVisibleIndex);
      }
    }
  }
  onClose() {
    const { contentEl } = this;
    contentEl.empty();
  }
};

// src/mediaUtils.ts
var import_obsidian2 = require("obsidian");
async function findFirstImageInNote(app, content) {
  try {
    const internalMatch = content.match(/(?:!?\[\[(.*?\.(?:jpg|jpeg|png|gif|webp))(?:\|.*?)?\]\]|!\[(.*?)\]\(\s*(\S+?(?:\.(?:jpg|jpeg|png|gif|webp)|format=(?:jpg|jpeg|png|gif|webp))[^\s)]*)\s*(?:\s+["'][^"']*["'])?\s*\))/i);
    if (internalMatch) {
      return processMediaLink(app, internalMatch);
    } else {
      return null;
    }
  } catch (error) {
    console.error("Error finding image in note:", error);
    return null;
  }
}
function processMediaLink(app, linkText) {
  const internalMatch = linkText[0].match(/!?\[\[(.*?)\]\]/);
  if (internalMatch) {
    if (linkText[1]) {
      const file = app.metadataCache.getFirstLinkpathDest(linkText[1], "");
      if (file) {
        return app.vault.getResourcePath(file);
      }
    }
    return null;
  }
  const markdownMatch = linkText[0].match(/!?\[(.*?)\]\((.*?)\)/);
  if (markdownMatch) {
    if (linkText[3]) {
      const url = linkText[3];
      if (url.startsWith("http")) {
        return url;
      } else {
        const file = app.metadataCache.getFirstLinkpathDest(url, "");
        if (!file) {
          const fileByPath = app.vault.getAbstractFileByPath(url);
          if (fileByPath instanceof import_obsidian2.TFile) {
            return app.vault.getResourcePath(fileByPath);
          }
        } else {
          return app.vault.getResourcePath(file);
        }
      }
    }
  }
  return null;
}

// src/MediaModal.ts
var import_obsidian4 = require("obsidian");

// src/fileUtils.ts
var import_obsidian3 = require("obsidian");
var IMAGE_EXTENSIONS = /* @__PURE__ */ new Set(["jpg", "jpeg", "png", "gif", "webp", "avif", "bmp", "svg"]);
var VIDEO_EXTENSIONS = /* @__PURE__ */ new Set(["mp4", "webm", "mov", "avi", "mkv", "ogv"]);
var AUDIO_EXTENSIONS = /* @__PURE__ */ new Set(["flac", "m4a", "mp3", "ogg", "wav", "3gp"]);
var DOCUMENT_EXTENSIONS = /* @__PURE__ */ new Set(["md", "pdf", "canvas", "base"]);
var customDocumentExtensions = [];
function updateCustomDocumentExtensions(settings) {
  if (settings.customDocumentExtensions) {
    customDocumentExtensions = settings.customDocumentExtensions.split(",").map((ext) => ext.trim().toLowerCase()).filter((ext) => ext.length > 0);
  } else {
    customDocumentExtensions = [];
  }
}
function isDocumentFile(file) {
  const extension = file.extension.toLowerCase();
  return DOCUMENT_EXTENSIONS.has(extension) || customDocumentExtensions.includes(extension);
}
function isImageFile(file) {
  return IMAGE_EXTENSIONS.has(file.extension.toLowerCase());
}
function isVideoFile(file) {
  return VIDEO_EXTENSIONS.has(file.extension.toLowerCase());
}
function isAudioFile(file) {
  return AUDIO_EXTENSIONS.has(file.extension.toLowerCase());
}
function isMediaFile(file) {
  return isImageFile(file) || isVideoFile(file) || isAudioFile(file);
}
function sortFiles(files, gridView) {
  const app = gridView.app;
  const settings = gridView.plugin.settings;
  const sortType = gridView.folderSortType ? gridView.folderSortType : gridView.sortType;
  const isNonDateSort = ["name-asc", "name-desc", "random"].includes(sortType);
  const hasModifiedField = !!settings.modifiedDateField;
  const hasCreatedField = !!settings.createdDateField;
  const hasAnyDateField = hasModifiedField || hasCreatedField;
  const shouldUseSimpleSort = isNonDateSort || !hasAnyDateField;
  if (shouldUseSimpleSort) {
    if (sortType === "name-asc") {
      return files.sort((a, b) => a.basename.localeCompare(b.basename, void 0, { numeric: true, sensitivity: "base" }));
    } else if (sortType === "name-desc") {
      return files.sort((a, b) => b.basename.localeCompare(a.basename, void 0, { numeric: true, sensitivity: "base" }));
    } else if (sortType === "mtime-desc") {
      return files.sort((a, b) => b.stat.mtime - a.stat.mtime);
    } else if (sortType === "mtime-asc") {
      return files.sort((a, b) => a.stat.mtime - b.stat.mtime);
    } else if (sortType === "ctime-desc") {
      return files.sort((a, b) => b.stat.ctime - a.stat.ctime);
    } else if (sortType === "ctime-asc") {
      return files.sort((a, b) => a.stat.ctime - b.stat.ctime);
    } else if (sortType === "random") {
      return files.sort(() => Math.random() - 0.5);
    } else {
      return files;
    }
  }
  const filesWithDates = files.map((file) => {
    const shouldReadMetadata = file.extension === "md";
    const metadata = shouldReadMetadata ? app.metadataCache.getFileCache(file) : null;
    return {
      file,
      mDate: (() => {
        if (metadata == null ? void 0 : metadata.frontmatter) {
          const fieldName = settings.modifiedDateField;
          const dateStr = metadata.frontmatter[fieldName];
          if (dateStr) {
            const date = new Date(dateStr);
            if (!isNaN(date.getTime())) {
              return date.getTime();
            }
          }
        }
        return file.stat.mtime;
      })(),
      cDate: (() => {
        if (metadata == null ? void 0 : metadata.frontmatter) {
          const fieldName = settings.createdDateField;
          const dateStr = metadata.frontmatter[fieldName];
          if (dateStr) {
            const date = new Date(dateStr);
            if (!isNaN(date.getTime())) {
              return date.getTime();
            }
          }
        }
        return file.stat.ctime;
      })()
    };
  });
  if (sortType === "mtime-desc") {
    return filesWithDates.sort((a, b) => b.mDate - a.mDate).map((item) => item.file);
  } else if (sortType === "mtime-asc") {
    return filesWithDates.sort((a, b) => a.mDate - b.mDate).map((item) => item.file);
  } else if (sortType === "ctime-desc") {
    return filesWithDates.sort((a, b) => b.cDate - a.cDate).map((item) => item.file);
  } else if (sortType === "ctime-asc") {
    return filesWithDates.sort((a, b) => a.cDate - b.cDate).map((item) => item.file);
  } else {
    return files;
  }
}
function ignoredFiles(files, gridView) {
  const settings = gridView.plugin.settings;
  return files.filter((file) => {
    if (gridView.showIgnoredFolders)
      return true;
    const isInIgnoredFolder = settings.ignoredFolders.some(
      (folder) => file.path.startsWith(`${folder}/`)
    );
    if (isInIgnoredFolder) {
      return false;
    }
    if (settings.ignoredFolderPatterns && settings.ignoredFolderPatterns.length > 0) {
      const matchesIgnoredPattern = settings.ignoredFolderPatterns.some((pattern) => {
        try {
          if (/[\^\$\*\+\?\(\)\[\]\{\}\|\\]/.test(pattern)) {
            const regex = new RegExp(pattern);
            return regex.test(file.path);
          } else {
            return file.path.toLowerCase().includes(pattern.toLowerCase());
          }
        } catch (error) {
          return file.path.toLowerCase().includes(pattern.toLowerCase());
        }
      });
      return !matchesIgnoredPattern;
    }
    return true;
  });
}
async function getFiles(gridView, includeMediaFiles) {
  var _a, _b, _c;
  const app = gridView.app;
  const settings = gridView.plugin.settings;
  const sourceMode = gridView.sourceMode;
  const sourcePath = gridView.sourcePath;
  if (sourceMode === "folder" && sourcePath) {
    const folder = app.vault.getAbstractFileByPath(sourcePath);
    if (folder instanceof import_obsidian3.TFolder) {
      const files = folder.children.filter((file) => {
        if (!(file instanceof import_obsidian3.TFile))
          return false;
        const allowMedia = settings.showMediaFiles && (!gridView.searchQuery || includeMediaFiles);
        if (isDocumentFile(file) || allowMedia && isMediaFile(file)) {
          return true;
        }
        return false;
      });
      return sortFiles(files, gridView);
    }
    return [];
  } else if (sourceMode === "search") {
    const globalSearchPlugin = app.internalPlugins.getPluginById("global-search");
    if (globalSearchPlugin == null ? void 0 : globalSearchPlugin.instance) {
      const searchLeaf = app.workspace.getLeavesOfType("search")[0];
      if (searchLeaf && searchLeaf.view && searchLeaf.view.dom) {
        const resultDomLookup = searchLeaf.view.dom.resultDomLookup;
        if (resultDomLookup) {
          const files = Array.from(resultDomLookup.keys()).filter((file) => file instanceof import_obsidian3.TFile);
          return sortFiles(files, gridView);
        }
      }
    }
    return [];
  } else if (sourceMode === "backlinks") {
    const activeFile = app.workspace.getActiveFile();
    if (!activeFile) {
      return [];
    }
    const backlinks = /* @__PURE__ */ new Set();
    const resolvedLinks = app.metadataCache.resolvedLinks;
    for (const [sourcePath2, links] of Object.entries(resolvedLinks)) {
      if (Object.keys(links).includes(activeFile.path)) {
        const sourceFile = app.vault.getAbstractFileByPath(sourcePath2);
        if (sourceFile) {
          backlinks.add(sourceFile);
        }
      }
    }
    return sortFiles(Array.from(backlinks), gridView);
  } else if (sourceMode === "outgoinglinks") {
    const activeFile = app.workspace.getActiveFile();
    if (!activeFile) {
      return [];
    }
    const outgoingLinks = /* @__PURE__ */ new Set();
    const resolvedLinks = app.metadataCache.resolvedLinks;
    const fileLinks = resolvedLinks[activeFile.path];
    if (fileLinks) {
      for (const targetPath of Object.keys(fileLinks)) {
        const targetFile = app.vault.getAbstractFileByPath(targetPath);
        if (targetFile && (isDocumentFile(targetFile) || settings.showMediaFiles && isMediaFile(targetFile))) {
          outgoingLinks.add(targetFile);
        }
      }
    }
    if (settings.showMediaFiles) {
      try {
        const content = await app.vault.cachedRead(activeFile);
        const frontMatterInfo = (0, import_obsidian3.getFrontMatterInfo)(content);
        const contentWithoutFrontmatter = content.substring(frontMatterInfo.contentStart);
        const mediaMatches = Array.from(contentWithoutFrontmatter.matchAll(/(?:!\[\[(.*?)(?:\|.*?)?\]\]|!\[(.*?)\]\((.*?)\))/g));
        for (const match of mediaMatches) {
          let mediaPath = match[1] || match[3];
          if (mediaPath) {
            mediaPath = mediaPath.split("|")[0].trim();
            const mediaFile = app.metadataCache.getFirstLinkpathDest(mediaPath, activeFile.path);
            if (mediaFile && isMediaFile(mediaFile)) {
              outgoingLinks.add(mediaFile);
            }
          }
        }
      } catch (e) {
      }
    }
    return sortFiles(Array.from(outgoingLinks), gridView);
  } else if (sourceMode === "bookmarks") {
    const bookmarksPlugin = app.internalPlugins.plugins.bookmarks;
    if (!(bookmarksPlugin == null ? void 0 : bookmarksPlugin.enabled)) {
      return [];
    }
    const bookmarks = bookmarksPlugin.instance.items;
    const bookmarkedFiles = /* @__PURE__ */ new Set();
    const processBookmarkItem = (item) => {
      if (item.type === "file") {
        const file = app.vault.getAbstractFileByPath(item.path);
        if (file instanceof import_obsidian3.TFile) {
          if (isDocumentFile(file) || settings.showMediaFiles && isMediaFile(file)) {
            bookmarkedFiles.add(file);
          }
        }
      } else if (item.type === "group" && item.items) {
        item.items.forEach(processBookmarkItem);
      }
    };
    bookmarks.forEach(processBookmarkItem);
    return Array.from(bookmarkedFiles);
  } else if (sourceMode === "tasks") {
    const filesWithTasks = /* @__PURE__ */ new Set();
    const dv = (_a = this.app.plugins.plugins.dataview) == null ? void 0 : _a.api;
    const tasksPlugin = app.plugins.plugins["obsidian-tasks-plugin"];
    if (tasksPlugin) {
      try {
        const allTasks = tasksPlugin.getTasks ? tasksPlugin.getTasks() : [];
        for (const task of allTasks) {
          const file = app.vault.getAbstractFileByPath(task.path);
          if (!(file instanceof import_obsidian3.TFile))
            continue;
          if (gridView.taskFilter === "uncompleted" && !task.isDone) {
            filesWithTasks.add(file);
          } else if (gridView.taskFilter === "completed" && task.isDone) {
            filesWithTasks.add(file);
          } else if (gridView.taskFilter === "all") {
            filesWithTasks.add(file);
          }
        }
      } catch (error) {
        console.error("Error getting tasks from Tasks plugin:", error);
        return [];
      }
    } else if (dv) {
      try {
        const tasks = dv.pages().file.tasks;
        let filteredTasks;
        if (gridView.taskFilter === "uncompleted") {
          filteredTasks = tasks.where((t2) => !t2.completed);
        } else if (gridView.taskFilter === "completed") {
          filteredTasks = tasks.where((t2) => t2.completed);
        } else {
          filteredTasks = tasks;
        }
        for (const task of filteredTasks.array()) {
          const file = app.vault.getAbstractFileByPath(task.path);
          if (file instanceof import_obsidian3.TFile) {
            filesWithTasks.add(file);
          }
        }
      } catch (error) {
        console.error("Error getting tasks from Dataview:", error);
        return [];
      }
    } else {
      const markdownFiles = app.vault.getMarkdownFiles();
      for (const file of markdownFiles) {
        try {
          const content = await app.vault.cachedRead(file);
          let shouldAdd = false;
          if (gridView.taskFilter === "uncompleted") {
            shouldAdd = /^[\s]*[-*]\s*\[\s*\](?![^\[]*\[\s*[^\s\]]+\]).*$/m.test(content);
          } else if (gridView.taskFilter === "completed") {
            const hasCompleted = /^[\s]*[-*]\s*\[x\](?![^\[]*\[\s*[^\s\]]+\]).*$/m.test(content);
            const hasIncomplete = /^[\s]*[-*]\s*\[\s*\](?![^\[]*\[\s*[^\s\]]+\]).*$/m.test(content);
            shouldAdd = hasCompleted && !hasIncomplete;
          } else if (gridView.taskFilter === "all") {
            const hasIncomplete = /^[\s]*[-*]\s*\[\s*\](?![^\[]*\[\s*[^\s\]]+\]).*$/m.test(content);
            const hasCompleted = /^[\s]*[-*]\s*\[x\](?![^\[]*\[\s*[^\s\]]+\]).*$/m.test(content);
            shouldAdd = hasIncomplete || hasCompleted;
          }
          if (shouldAdd) {
            filesWithTasks.add(file);
          }
        } catch (error) {
          console.error(`Error reading file ${file.path}:`, error);
          return [];
        }
      }
    }
    return sortFiles(Array.from(filesWithTasks), gridView);
  } else if (sourceMode.startsWith("custom-")) {
    const dvApi = (_b = app.plugins.plugins.dataview) == null ? void 0 : _b.api;
    if (!dvApi) {
      new import_obsidian3.Notice("Dataview plugin is not enabled.");
      return [];
    }
    const mode = settings.customModes.find((m) => m.internalName === sourceMode);
    if (!mode) {
      new import_obsidian3.Notice(`Custom mode ${sourceMode} not found.`);
      return [];
    }
    try {
      const activeFile = app.workspace.getActiveFile();
      if (activeFile) {
        dvApi.current = () => dvApi.page(activeFile.path);
      }
      const func = new Function("app", "dv", mode.dataviewCode);
      const dvPagesResult = func(app, dvApi);
      const dvPages = Array.isArray(dvPagesResult) ? dvPagesResult : Array.from(dvPagesResult || []);
      if (!dvPages || dvPages.length === 0) {
        return [];
      }
      const files = /* @__PURE__ */ new Set();
      for (const page of dvPages) {
        if ((_c = page.file) == null ? void 0 : _c.path) {
          const file = app.vault.getAbstractFileByPath(page.file.path);
          if (file instanceof import_obsidian3.TFile) {
            files.add(file);
          }
        }
      }
      return Array.from(files);
    } catch (error) {
      console.error("Grid Explorer: Error executing Dataview query.", error);
      return [];
    }
  } else if (sourceMode === "all-files") {
    const allVaultFiles = app.vault.getFiles().filter((file) => {
      if (isDocumentFile(file) || settings.showMediaFiles && includeMediaFiles && isMediaFile(file)) {
        return true;
      }
      return false;
    });
    return sortFiles(allVaultFiles, gridView);
  } else if (sourceMode === "recent-files") {
    const recentFiles = app.vault.getFiles().filter((file) => {
      if (isDocumentFile(file) || settings.showMediaFiles && includeMediaFiles && isMediaFile(file)) {
        return true;
      }
      return false;
    });
    const sortType = gridView.sortType;
    gridView.sortType = "mtime-desc";
    const sortedFiles = sortFiles(recentFiles, gridView);
    gridView.sortType = sortType;
    return sortedFiles;
  } else if (sourceMode === "random-note") {
    const randomFiles = app.vault.getFiles().filter((file) => {
      if (isDocumentFile(file) || settings.showMediaFiles && includeMediaFiles && isMediaFile(file)) {
        return true;
      }
      return false;
    }).sort(() => Math.random() - 0.5);
    return randomFiles;
  } else {
    return [];
  }
}

// src/MediaModal.ts
var MediaModal = class extends import_obsidian4.Modal {
  // 儲存 GridView 實例的引用
  constructor(app, file, mediaFiles, gridView) {
    super(app);
    this.currentMediaElement = null;
    this.isZoomed = false;
    this.handleWheel = null;
    this.file = file;
    this.mediaFiles = mediaFiles;
    this.currentIndex = this.mediaFiles.findIndex((f) => f.path === file.path);
    this.gridView = gridView;
    this.modalEl.addClass("ge-media-modal");
  }
  onOpen() {
    const { contentEl } = this;
    contentEl.empty();
    contentEl.style.width = "100%";
    contentEl.style.height = "100%";
    contentEl.addClass("ge-media-modal-content");
    const mediaView = contentEl.createDiv("ge-media-view");
    const closeButton = contentEl.createDiv("ge-media-close-button");
    (0, import_obsidian4.setIcon)(closeButton, "x");
    closeButton.addEventListener("click", (e) => {
      e.stopPropagation();
      this.close();
    });
    const prevArea = contentEl.createDiv("ge-media-prev-area");
    const nextArea = contentEl.createDiv("ge-media-next-area");
    const mediaContainer = mediaView.createDiv("ge-media-container");
    mediaContainer.addEventListener("click", (e) => {
      if (e.target === mediaContainer) {
        this.close();
      }
    });
    prevArea.addEventListener("click", (e) => {
      e.stopPropagation();
      this.showPrevMedia();
    });
    nextArea.addEventListener("click", (e) => {
      e.stopPropagation();
      this.showNextMedia();
    });
    contentEl.addEventListener("wheel", (e) => {
      if (!this.isZoomed) {
        e.preventDefault();
        if (e.deltaY > 0) {
          this.showNextMedia();
        } else {
          this.showPrevMedia();
        }
      }
    });
    this.scope.register(null, "ArrowLeft", () => {
      this.showPrevMedia();
      return false;
    });
    this.scope.register(null, "ArrowRight", () => {
      this.showNextMedia();
      return false;
    });
    this.showMediaAtIndex(this.currentIndex);
  }
  onClose() {
    const { contentEl } = this;
    contentEl.empty();
    if (this.handleWheel) {
      const mediaView = contentEl.querySelector(".ge-media-view");
      if (mediaView) {
        mediaView.removeEventListener("wheel", this.handleWheel);
      }
      this.handleWheel = null;
    }
    if (this.gridView) {
      const currentFile = this.mediaFiles[this.currentIndex];
      const gridItemIndex = this.gridView.gridItems.findIndex(
        (item) => item.dataset.filePath === currentFile.path
      );
      if (gridItemIndex >= 0) {
        this.gridView.hasKeyboardFocus = true;
        this.gridView.selectItem(gridItemIndex);
      }
    }
  }
  // 顯示指定索引的媒體檔案
  showMediaAtIndex(index) {
    if (index < 0 || index >= this.mediaFiles.length)
      return;
    const { contentEl } = this;
    const mediaContainer = contentEl.querySelector(".ge-media-container");
    if (!mediaContainer)
      return;
    this.currentIndex = index;
    if (this.handleWheel) {
      const mediaView = contentEl.querySelector(".ge-media-view");
      if (mediaView) {
        mediaView.removeEventListener("wheel", this.handleWheel);
      }
      this.handleWheel = null;
    }
    this.isZoomed = false;
    const mediaFile = this.mediaFiles[index];
    if (isImageFile(mediaFile)) {
      const img = document.createElement("img");
      img.className = "ge-fullscreen-image";
      img.style.display = "none";
      img.src = this.app.vault.getResourcePath(mediaFile);
      img.onload = () => {
        if (this.currentMediaElement) {
          this.currentMediaElement.remove();
        }
        this.currentMediaElement = img;
        this.resetImageStyles(img);
        img.style.display = "";
      };
      mediaContainer.appendChild(img);
      img.addEventListener("click", (event) => {
        event.stopPropagation();
        this.toggleImageZoom(img);
      });
    } else if (isVideoFile(mediaFile) || isAudioFile(mediaFile)) {
      if (this.currentMediaElement) {
        this.currentMediaElement.remove();
      }
      const video = document.createElement("video");
      video.className = "ge-fullscreen-video";
      video.controls = true;
      video.autoplay = true;
      video.src = this.app.vault.getResourcePath(mediaFile);
      mediaContainer.appendChild(video);
      this.currentMediaElement = video;
    }
    const oldFileNameElement = mediaContainer.querySelector(".ge-fullscreen-file-name");
    if (oldFileNameElement) {
      oldFileNameElement.remove();
    }
    if (isAudioFile(mediaFile)) {
      const fileName = mediaFile.name;
      const fileNameElement = document.createElement("div");
      fileNameElement.className = "ge-fullscreen-file-name";
      fileNameElement.textContent = fileName;
      mediaContainer.appendChild(fileNameElement);
    }
  }
  // 顯示下一個媒體檔案
  showNextMedia() {
    const nextIndex = (this.currentIndex + 1) % this.mediaFiles.length;
    this.showMediaAtIndex(nextIndex);
  }
  // 顯示上一個媒體檔案
  showPrevMedia() {
    const prevIndex = (this.currentIndex - 1 + this.mediaFiles.length) % this.mediaFiles.length;
    this.showMediaAtIndex(prevIndex);
  }
  // 重設圖片樣式
  resetImageStyles(img) {
    const mediaView = this.contentEl.querySelector(".ge-media-view");
    if (!mediaView)
      return;
    img.style.width = "auto";
    img.style.height = "auto";
    img.style.maxWidth = "100vw";
    img.style.maxHeight = "100vh";
    img.style.position = "absolute";
    img.style.left = "50%";
    img.style.top = "50%";
    img.style.transform = "translate(-50%, -50%)";
    img.style.cursor = "zoom-in";
    mediaView.style.overflowX = "hidden";
    mediaView.style.overflowY = "hidden";
    img.onload = () => {
      if (mediaView.clientWidth > mediaView.clientHeight) {
        if (img.naturalHeight < mediaView.clientHeight) {
          img.style.height = "100%";
        }
      } else {
        if (img.naturalWidth < mediaView.clientWidth) {
          img.style.width = "100%";
        }
      }
    };
    if (img.complete) {
      if (mediaView.clientWidth > mediaView.clientHeight) {
        if (img.naturalHeight < mediaView.clientHeight) {
          img.style.height = "100%";
        }
      } else {
        if (img.naturalWidth < mediaView.clientWidth) {
          img.style.width = "100%";
        }
      }
    }
  }
  // 切換圖片縮放
  toggleImageZoom(img) {
    const mediaView = this.contentEl.querySelector(".ge-media-view");
    if (!mediaView)
      return;
    if (!this.isZoomed) {
      if (mediaView.clientWidth > mediaView.clientHeight) {
        if (img.naturalHeight < mediaView.clientHeight) {
          img.style.maxWidth = "none";
        }
      } else {
        if (img.naturalWidth < mediaView.clientWidth) {
          img.style.maxHeight = "none";
        }
      }
      if (img.offsetWidth < mediaView.clientWidth) {
        img.style.width = "100vw";
        img.style.height = "auto";
        mediaView.style.overflowX = "hidden";
        mediaView.style.overflowY = "scroll";
      } else {
        img.style.width = "auto";
        img.style.height = "100vh";
        mediaView.style.overflowX = "scroll";
        mediaView.style.overflowY = "hidden";
        this.handleWheel = (event) => {
          event.preventDefault();
          mediaView.scrollLeft += event.deltaY;
        };
        mediaView.addEventListener("wheel", this.handleWheel);
      }
      img.style.maxWidth = "none";
      img.style.maxHeight = "none";
      img.style.position = "relative";
      img.style.left = "0";
      img.style.top = "0";
      img.style.margin = "auto";
      img.style.transform = "none";
      img.style.cursor = "zoom-out";
      this.isZoomed = true;
    } else {
      if (this.handleWheel) {
        mediaView.removeEventListener("wheel", this.handleWheel);
        this.handleWheel = null;
      }
      this.resetImageStyles(img);
      this.isZoomed = false;
    }
  }
};

// src/FolderNoteSettingsModal.ts
var import_obsidian5 = require("obsidian");
function showFolderNoteSettingsModal(app, plugin, folder, gridView) {
  new FolderNoteSettingsModal(app, plugin, folder, gridView).open();
}
var FolderNoteSettingsModal = class extends import_obsidian5.Modal {
  constructor(app, plugin, folder, gridView) {
    super(app);
    this.settings = {
      sort: "",
      color: "",
      icon: "\u{1F4C1}",
      isPinned: false
    };
    this.existingFile = null;
    this.plugin = plugin;
    this.folder = folder;
    this.gridView = gridView;
    const notePath = `${folder.path}/${folder.name}.md`;
    const noteFile = this.app.vault.getAbstractFileByPath(notePath);
    if (noteFile instanceof import_obsidian5.TFile) {
      this.existingFile = noteFile;
    }
  }
  async onOpen() {
    const { contentEl } = this;
    contentEl.empty();
    if (this.existingFile) {
      await this.loadExistingSettings();
    }
    new import_obsidian5.Setting(contentEl).setName(t("folder_note_settings")).setHeading();
    new import_obsidian5.Setting(contentEl).setName(t("folder_sort_type")).setDesc(t("folder_sort_type_desc")).addDropdown((dropdown) => {
      dropdown.addOption("", t("default_sort")).addOption("name-asc", t("sort_name_asc")).addOption("name-desc", t("sort_name_desc")).addOption("mtime-desc", t("sort_mtime_desc")).addOption("mtime-asc", t("sort_mtime_asc")).addOption("ctime-desc", t("sort_ctime_desc")).addOption("ctime-asc", t("sort_ctime_asc")).addOption("random", t("sort_random")).setValue(this.settings.sort).onChange((value) => {
        this.settings.sort = value;
      });
    });
    new import_obsidian5.Setting(contentEl).setName(t("folder_color")).setDesc(t("folder_color_desc")).addDropdown((dropdown) => {
      dropdown.addOption("", t("no_color")).addOption("red", t("color_red")).addOption("orange", t("color_orange")).addOption("yellow", t("color_yellow")).addOption("green", t("color_green")).addOption("cyan", t("color_cyan")).addOption("blue", t("color_blue")).addOption("purple", t("color_purple")).addOption("pink", t("color_pink")).setValue(this.settings.color).onChange((value) => {
        this.settings.color = value;
      });
    });
    const customFolderIcon = this.plugin.settings.customFolderIcon;
    new import_obsidian5.Setting(contentEl).setName(t("folder_icon")).setDesc(t("folder_icon_desc")).addText((text) => {
      text.setPlaceholder(customFolderIcon).setValue(this.settings.icon || customFolderIcon).onChange((value) => {
        this.settings.icon = value || customFolderIcon;
      });
    });
    new import_obsidian5.Setting(contentEl).setName(t("foldernote_pinned")).setDesc(t("foldernote_pinned_desc")).addToggle((toggle) => {
      toggle.setValue(this.settings.isPinned).onChange((value) => {
        this.settings.isPinned = value;
      });
    });
    const buttonSetting = new import_obsidian5.Setting(contentEl);
    buttonSetting.addButton((button) => {
      button.setButtonText(t("confirm")).setCta().onClick(() => {
        this.saveFolderNote();
        this.close();
      });
    });
  }
  // 讀取現有筆記的設定
  async loadExistingSettings() {
    var _a;
    if (!this.existingFile)
      return;
    try {
      const fileCache = this.app.metadataCache.getFileCache(this.existingFile);
      if (fileCache && fileCache.frontmatter) {
        if ("sort" in fileCache.frontmatter) {
          this.settings.sort = fileCache.frontmatter.sort || "";
        }
        if ("color" in fileCache.frontmatter) {
          this.settings.color = fileCache.frontmatter.color || "";
        }
        if ("icon" in fileCache.frontmatter) {
          this.settings.icon = fileCache.frontmatter.icon || "\u{1F4C1}";
        }
        if (((_a = fileCache.frontmatter) == null ? void 0 : _a.pinned) && Array.isArray(fileCache.frontmatter.pinned)) {
          this.settings.isPinned = fileCache.frontmatter.pinned.some((item) => {
            if (!item)
              return false;
            const pinnedName = item.toString();
            const pinnedNameWithoutExt = pinnedName.replace(/\.\w+$/, "");
            return pinnedNameWithoutExt === this.folder.name;
          });
        }
      }
    } catch (error) {
      console.error("\u7121\u6CD5\u8B80\u53D6\u8CC7\u6599\u593E\u7B46\u8A18\u8A2D\u5B9A", error);
    }
  }
  // 儲存或更新資料夾筆記
  async saveFolderNote() {
    const notePath = `${this.folder.path}/${this.folder.name}.md`;
    try {
      let file;
      if (this.existingFile) {
        file = this.existingFile;
      } else {
        file = await this.app.vault.create(notePath, "");
        await this.app.workspace.getLeaf().openFile(file);
      }
      await this.app.fileManager.processFrontMatter(file, (frontmatter) => {
        if (this.settings.sort) {
          frontmatter["sort"] = this.settings.sort;
        } else {
          delete frontmatter["sort"];
        }
        if (this.settings.color) {
          frontmatter["color"] = this.settings.color;
        } else {
          delete frontmatter["color"];
        }
        if (this.settings.icon && this.settings.icon !== "\u{1F4C1}") {
          frontmatter["icon"] = this.settings.icon;
        } else {
          delete frontmatter["icon"];
        }
        const folderName = `${this.folder.name}.md`;
        if (this.settings.isPinned) {
          if (Array.isArray(frontmatter["pinned"])) {
            if (!frontmatter["pinned"].includes(folderName)) {
              frontmatter["pinned"] = [folderName, ...frontmatter["pinned"]];
            }
          } else {
            frontmatter["pinned"] = [folderName];
          }
        } else if (Array.isArray(frontmatter["pinned"])) {
          frontmatter["pinned"] = frontmatter["pinned"].filter(
            (item) => item !== folderName
          );
          if (frontmatter["pinned"].length === 0) {
            delete frontmatter["pinned"];
          }
        }
      });
      this.app.metadataCache.getFileCache(file);
      setTimeout(() => {
        this.app.workspace.getLeavesOfType("grid-view").forEach((leaf) => {
          if (leaf.view instanceof GridView) {
            leaf.view.render();
          }
        });
      }, 200);
    } catch (error) {
      console.error("\u7121\u6CD5\u5132\u5B58\u8CC7\u6599\u593E\u7B46\u8A18", error);
    }
  }
  onClose() {
    const { contentEl } = this;
    contentEl.empty();
  }
};

// src/NoteSettingsModal.ts
var import_obsidian6 = require("obsidian");
function showNoteSettingsModal(app, plugin, file) {
  new NoteSettingsModal(app, plugin, file).open();
}
var NoteSettingsModal = class extends import_obsidian6.Modal {
  // 記錄初始的 isPinned 狀態
  constructor(app, plugin, file) {
    super(app);
    this.settings = {
      title: "",
      summary: "",
      color: "",
      isPinned: false,
      isMinimized: false
    };
    this.initialIsPinned = false;
    this.plugin = plugin;
    this.files = Array.isArray(file) ? file : [file];
  }
  async onOpen() {
    const { contentEl } = this;
    contentEl.empty();
    await this.loadAttributes();
    if (this.files.length > 1) {
      new import_obsidian6.Setting(contentEl).setName(t("note_attribute_settings") + ` (${this.files.length} ${t("files")})`).setHeading();
    } else {
      new import_obsidian6.Setting(contentEl).setName(t("note_attribute_settings") + `: ${this.files[0].basename}`).setHeading();
    }
    if (this.files.length === 1 && this.files[0].extension === "md") {
      new import_obsidian6.Setting(contentEl).setName(t("note_title")).setDesc(t("note_title_desc")).addText((text) => {
        text.setValue(this.settings.title);
        text.onChange((value) => {
          this.settings.title = value;
        });
      });
      new import_obsidian6.Setting(contentEl).setName(t("note_summary")).setDesc(t("note_summary_desc")).addText((text) => {
        text.setValue(this.settings.summary);
        text.onChange((value) => {
          this.settings.summary = value;
        });
      });
    }
    if (this.files[0].extension === "md") {
      new import_obsidian6.Setting(contentEl).setName(t("note_color")).setDesc(t("note_color_desc")).addDropdown((dropdown) => {
        dropdown.addOption("", t("no_color")).addOption("red", t("color_red")).addOption("orange", t("color_orange")).addOption("yellow", t("color_yellow")).addOption("green", t("color_green")).addOption("cyan", t("color_cyan")).addOption("blue", t("color_blue")).addOption("purple", t("color_purple")).addOption("pink", t("color_pink")).setValue(this.settings.color).onChange((value) => {
          this.settings.color = value;
        });
      });
    }
    if (this.files[0].parent && this.files[0].parent !== this.app.vault.getRoot()) {
      new import_obsidian6.Setting(contentEl).setName(t("pinned")).setDesc(t("pinned_desc")).addToggle((toggle) => {
        toggle.setValue(this.settings.isPinned).onChange((value) => {
          this.settings.isPinned = value;
        });
      });
    }
    if (this.files[0].extension === "md") {
      new import_obsidian6.Setting(contentEl).setName(t("display_minimized")).setDesc(t("display_minimized_desc")).addToggle((toggle) => {
        toggle.setValue(this.settings.isMinimized).onChange((value) => {
          this.settings.isMinimized = value;
        });
      });
    }
    const buttonSetting = new import_obsidian6.Setting(contentEl);
    buttonSetting.addButton((button) => {
      button.setButtonText(t("confirm")).setCta().onClick(() => {
        this.saveAttributes();
        this.close();
      });
    });
  }
  // 讀取現有筆記的設定
  async loadAttributes() {
    var _a;
    try {
      if (this.files.length === 1) {
        const fileCache = this.app.metadataCache.getFileCache(this.files[0]);
        if (fileCache && fileCache.frontmatter) {
          const titleField = this.plugin.settings.noteTitleField || "title";
          if ("title" in fileCache.frontmatter) {
            this.settings.title = fileCache.frontmatter[titleField] || "";
          }
          const summaryField = this.plugin.settings.noteSummaryField || "summary";
          if ("summary" in fileCache.frontmatter) {
            this.settings.summary = fileCache.frontmatter[summaryField] || "";
          }
        }
      }
      if (this.files.length > 0) {
        const fileCache = this.app.metadataCache.getFileCache(this.files[0]);
        if (fileCache && fileCache.frontmatter) {
          if ("color" in fileCache.frontmatter) {
            this.settings.color = fileCache.frontmatter.color || "";
          }
          if (fileCache.frontmatter.display === "minimized") {
            this.settings.isMinimized = true;
          }
        }
      }
      const folder = this.files[0].parent;
      if (folder && folder !== this.app.vault.getRoot()) {
        const notePath = `${folder.path}/${folder.name}.md`;
        const noteFile = this.app.vault.getAbstractFileByPath(notePath);
        if (noteFile instanceof import_obsidian6.TFile) {
          const fm = (_a = this.app.metadataCache.getFileCache(noteFile)) == null ? void 0 : _a.frontmatter;
          if (fm && Array.isArray(fm["pinned"])) {
            this.settings.isPinned = fm["pinned"].includes(this.files[0].name);
          }
        }
        this.initialIsPinned = this.settings.isPinned;
      }
    } catch (error) {
      console.error("\u7121\u6CD5\u8B80\u53D6\u7B46\u8A18\u5C6C\u6027\u8A2D\u5B9A", error);
    }
  }
  // 儲存筆記屬性設定
  async saveAttributes() {
    try {
      if (this.files.length === 1 && this.files[0].extension === "md") {
        await this.app.fileManager.processFrontMatter(this.files[0], (frontmatter) => {
          const titleField = this.plugin.settings.noteTitleField || "title";
          if (this.settings.title) {
            frontmatter[titleField] = this.settings.title;
          } else {
            delete frontmatter[titleField];
          }
          const summaryField = this.plugin.settings.noteSummaryField || "summary";
          if (this.settings.summary) {
            frontmatter[summaryField] = this.settings.summary;
          } else {
            delete frontmatter[summaryField];
          }
          if (this.settings.color) {
            frontmatter["color"] = this.settings.color;
          } else {
            delete frontmatter["color"];
          }
          if (this.settings.isMinimized) {
            frontmatter["display"] = "minimized";
          } else {
            if (frontmatter["display"] === "minimized")
              delete frontmatter["display"];
          }
        });
      }
      if (this.files.length > 1) {
        for (const file of this.files) {
          if (file.extension === "md") {
            await this.app.fileManager.processFrontMatter(file, (frontmatter) => {
              if (this.settings.color) {
                frontmatter["color"] = this.settings.color;
              } else {
                delete frontmatter["color"];
              }
              if (this.settings.isMinimized) {
                frontmatter["display"] = "minimized";
              } else {
                if (frontmatter["display"] === "minimized")
                  delete frontmatter["display"];
              }
            });
          }
        }
      }
      setTimeout(() => {
      }, 200);
      if (this.initialIsPinned !== this.settings.isPinned) {
        for (const file of this.files) {
          const folder = file.parent;
          if (!folder || folder === this.app.vault.getRoot())
            continue;
          const notePath = `${folder.path}/${folder.name}.md`;
          let noteFile = this.app.vault.getAbstractFileByPath(notePath);
          if (!(noteFile instanceof import_obsidian6.TFile)) {
            const initialFrontmatter = this.settings.isPinned ? `pinned:
  - ${file.name}
` : "";
            const initialContent = `---
${initialFrontmatter}---
`;
            noteFile = await this.app.vault.create(notePath, initialContent);
          }
          await this.app.fileManager.processFrontMatter(noteFile, (frontmatter) => {
            let list = Array.isArray(frontmatter["pinned"]) ? frontmatter["pinned"] : [];
            if (this.settings.isPinned) {
              if (!list.includes(file.name))
                list.push(file.name);
            } else {
              list = list.filter((n) => n !== file.name);
            }
            if (list.length > 0) {
              frontmatter["pinned"] = list;
            } else {
              delete frontmatter["pinned"];
            }
          });
        }
      }
      setTimeout(() => {
        this.app.workspace.getLeavesOfType("grid-view").forEach((leaf) => {
          if (leaf.view instanceof GridView) {
            leaf.view.render();
          }
        });
      }, 200);
    } catch (error) {
      console.error("\u7121\u6CD5\u5132\u5B58\u7B46\u8A18\u5C6C\u6027\u8A2D\u5B9A", error);
    }
  }
};

// src/FolderRenameModal.ts
var import_obsidian7 = require("obsidian");
function showFolderRenameModal(app, plugin, folder, gridView) {
  new FolderRenameModal(app, plugin, folder, gridView).open();
}
var FolderRenameModal = class extends import_obsidian7.Modal {
  constructor(app, plugin, folder, gridView) {
    super(app);
    this.plugin = plugin;
    this.folder = folder;
    this.gridView = gridView;
    this.newName = folder.name;
  }
  onOpen() {
    const { contentEl } = this;
    contentEl.empty();
    new import_obsidian7.Setting(contentEl).setName(t("rename_folder")).setDesc(t("enter_new_folder_name")).addText((text) => {
      text.setValue(this.folder.name).onChange((value) => {
        this.newName = value;
      });
    });
    new import_obsidian7.Setting(contentEl).addButton((button) => {
      button.setButtonText(t("confirm")).setCta().onClick(() => {
        this.renameFolder();
        this.close();
      });
    }).addButton((button) => {
      button.setButtonText(t("cancel")).onClick(() => {
        this.close();
      });
    });
  }
  async renameFolder() {
    try {
      const parentPath = this.folder.parent ? this.folder.parent.path : "";
      const newPath = (0, import_obsidian7.normalizePath)(parentPath ? `${parentPath}/${this.newName}` : this.newName);
      await this.app.fileManager.renameFile(this.folder, newPath);
      setTimeout(() => {
        this.gridView.render();
      }, 100);
    } catch (error) {
      console.error("Failed to rename folder", error);
    }
  }
  onClose() {
    const { contentEl } = this;
    contentEl.empty();
  }
};

// src/SearchModal.ts
var import_obsidian8 = require("obsidian");
var import_obsidian9 = require("obsidian");
var SearchModal = class extends import_obsidian8.Modal {
  constructor(app, gridView, defaultQuery) {
    super(app);
    this.gridView = gridView;
    this.defaultQuery = defaultQuery;
  }
  onOpen() {
    var _a, _b;
    const { contentEl } = this;
    contentEl.empty();
    new import_obsidian8.Setting(contentEl).setName(t("search")).setHeading();
    const searchContainer = contentEl.createDiv("ge-search-container");
    const searchInputWrapper = searchContainer.createDiv("ge-search-input-wrapper");
    const tagDisplayArea = searchInputWrapper.createDiv("ge-search-tag-display-area");
    const searchInput = searchInputWrapper.createEl("input", {
      type: "text",
      value: this.defaultQuery,
      placeholder: t("search_placeholder"),
      cls: "ge-search-input"
    });
    const inputContainer = searchInputWrapper.createDiv("ge-input-container");
    inputContainer.appendChild(searchInput);
    const clearButton = inputContainer.createDiv("ge-search-clear-button");
    clearButton.style.display = this.defaultQuery ? "flex" : "none";
    (0, import_obsidian9.setIcon)(clearButton, "x");
    const tagSuggestionContainer = contentEl.createDiv("ge-search-tag-suggestions");
    tagSuggestionContainer.style.display = "none";
    const allTagsArr = Object.keys(((_b = (_a = this.app.metadataCache).getTags) == null ? void 0 : _b.call(_a)) || {}).map((t2) => t2.substring(1));
    let tagSuggestions = [];
    let selectedSuggestionIndex = -1;
    const updateTagSuggestions = () => {
      const match = searchInput.value.substring(0, searchInput.selectionStart || 0).match(/#([^#\s]*)$/);
      if (!match) {
        tagSuggestionContainer.style.display = "none";
        tagSuggestionContainer.empty();
        selectedSuggestionIndex = -1;
        return;
      }
      const query = match[1].toLowerCase();
      tagSuggestions = allTagsArr.filter((t2) => t2.toLowerCase().startsWith(query)).slice(0, 10);
      if (tagSuggestions.length === 0) {
        tagSuggestionContainer.style.display = "none";
        selectedSuggestionIndex = -1;
        return;
      }
      tagSuggestionContainer.empty();
      tagSuggestions.forEach((tag, idx) => {
        const item = tagSuggestionContainer.createDiv("ge-search-tag-suggestion-item");
        item.textContent = `#${tag}`;
        if (idx === selectedSuggestionIndex)
          item.addClass("is-selected");
        item.addEventListener("mousedown", (e) => {
          e.preventDefault();
          applySuggestion(idx);
        });
      });
      tagSuggestionContainer.style.display = "block";
    };
    const applySuggestion = (index) => {
      if (index < 0 || index >= tagSuggestions.length)
        return;
      const value = searchInput.value.trim();
      const cursor = searchInput.selectionStart || 0;
      const beforeMatch = value.substring(0, cursor).replace(/#([^#\\s]*)$/, `#${tagSuggestions[index]} `);
      const afterCursor = value.substring(cursor);
      searchInput.value = beforeMatch + afterCursor;
      searchInput.value = searchInput.value.trim();
      const newCursorPos = beforeMatch.length;
      searchInput.setSelectionRange(newCursorPos, newCursorPos);
      tagSuggestionContainer.style.display = "none";
      tagSuggestionContainer.empty();
      selectedSuggestionIndex = -1;
      clearButton.style.display = searchInput.value ? "flex" : "none";
      renderTagButtons();
    };
    searchInput.addEventListener("input", () => {
      clearButton.style.display = searchInput.value ? "flex" : "none";
      updateTagSuggestions();
      renderTagButtons();
    });
    searchInput.addEventListener("keydown", (e) => {
      if (tagSuggestionContainer.style.display === "none")
        return;
      if (e.key === "ArrowDown") {
        e.preventDefault();
        selectedSuggestionIndex = (selectedSuggestionIndex + 1) % tagSuggestions.length;
        updateTagSuggestions();
      } else if (e.key === "ArrowUp") {
        e.preventDefault();
        selectedSuggestionIndex = (selectedSuggestionIndex - 1 + tagSuggestions.length) % tagSuggestions.length;
        updateTagSuggestions();
      } else if (e.key === "Enter") {
        if (selectedSuggestionIndex >= 0) {
          e.preventDefault();
          applySuggestion(selectedSuggestionIndex);
        }
      }
    });
    clearButton.addEventListener("click", () => {
      searchInput.value = "";
      clearButton.style.display = "none";
      tagDisplayArea.empty();
      tagDisplayArea.style.display = "none";
      searchInput.focus();
    });
    const searchOptionsContainer = contentEl.createDiv("ge-search-options-container");
    const searchScopeContainer = searchOptionsContainer.createDiv("ge-search-scope-container");
    const searchScopeCheckbox = searchScopeContainer.createEl("input", {
      type: "checkbox",
      cls: "ge-search-scope-checkbox"
    });
    searchScopeCheckbox.checked = !this.gridView.searchAllFiles;
    searchScopeContainer.createEl("span", {
      text: t("search_current_location_only"),
      cls: "ge-search-scope-label"
    });
    if (this.gridView.sourceMode === "random-note") {
      searchScopeContainer.style.display = "none";
      searchScopeCheckbox.checked = false;
    }
    const searchMediaFilesContainer = searchOptionsContainer.createDiv("ge-search-media-files-container");
    const searchMediaFilesCheckbox = searchMediaFilesContainer.createEl("input", {
      type: "checkbox",
      cls: "ge-search-media-files-checkbox"
    });
    searchMediaFilesCheckbox.checked = this.gridView.searchMediaFiles;
    searchMediaFilesContainer.createEl("span", {
      text: t("search_media_files"),
      cls: "ge-search-media-files-label"
    });
    if (!this.gridView.plugin.settings.showMediaFiles || this.gridView.sourceMode === "backlinks") {
      searchMediaFilesContainer.style.display = "none";
      searchMediaFilesCheckbox.checked = false;
      this.gridView.searchMediaFiles = false;
    }
    searchScopeContainer.addEventListener("click", (e) => {
      if (e.target !== searchScopeCheckbox) {
        searchScopeCheckbox.checked = !searchScopeCheckbox.checked;
        this.gridView.searchAllFiles = !searchScopeCheckbox.checked;
      }
    });
    searchMediaFilesContainer.addEventListener("click", (e) => {
      if (e.target !== searchMediaFilesCheckbox) {
        searchMediaFilesCheckbox.checked = !searchMediaFilesCheckbox.checked;
        this.gridView.searchMediaFiles = !searchMediaFilesCheckbox.checked;
      }
    });
    searchScopeCheckbox.addEventListener("change", () => {
      this.gridView.searchAllFiles = !searchScopeCheckbox.checked;
    });
    searchMediaFilesCheckbox.addEventListener("change", () => {
      this.gridView.searchMediaFiles = !searchMediaFilesCheckbox.checked;
    });
    const buttonContainer = contentEl.createDiv("ge-button-container");
    const searchButton = buttonContainer.createEl("button", {
      text: t("search")
    });
    const cancelButton = buttonContainer.createEl("button", {
      text: t("cancel")
    });
    const renderTagButtons = () => {
      tagDisplayArea.empty();
      const inputValue = searchInput.value.trim();
      if (!inputValue) {
        tagDisplayArea.style.display = "none";
        return;
      }
      const terms = inputValue.split(/\s+/);
      if (terms.length === 0) {
        tagDisplayArea.style.display = "none";
        return;
      }
      tagDisplayArea.style.display = "flex";
      let currentIndex = 0;
      const termPositions = [];
      terms.forEach((term) => {
        if (!term)
          return;
        const startIndex = inputValue.indexOf(term, currentIndex);
        if (startIndex === -1)
          return;
        const endIndex = startIndex + term.length;
        termPositions.push({
          term,
          startIndex,
          endIndex
        });
        currentIndex = endIndex;
      });
      termPositions.forEach((termInfo) => {
        const tagButton = tagDisplayArea.createDiv("ge-search-tag-button");
        tagButton.textContent = termInfo.term;
        if (termInfo.term.startsWith("#")) {
          tagButton.addClass("is-tag");
        }
        const deleteButton = tagButton.createDiv("ge-search-tag-delete-button");
        (0, import_obsidian9.setIcon)(deleteButton, "x");
        deleteButton.addEventListener("click", (e) => {
          e.stopPropagation();
          const newValue = inputValue.substring(0, termInfo.startIndex) + inputValue.substring(termInfo.endIndex);
          searchInput.value = newValue.trim();
          const inputEvent = new Event("input", { bubbles: true });
          searchInput.dispatchEvent(inputEvent);
          searchInput.focus();
        });
      });
    };
    const performSearch = () => {
      this.gridView.searchQuery = searchInput.value;
      this.gridView.searchAllFiles = !searchScopeCheckbox.checked;
      this.gridView.searchMediaFiles = searchMediaFilesCheckbox.checked;
      this.gridView.clearSelection();
      this.gridView.render(true);
      this.gridView.app.workspace.requestSaveLayout();
      this.close();
    };
    searchButton.addEventListener("click", performSearch);
    searchInput.addEventListener("keypress", (e) => {
      if (e.key === "Enter") {
        performSearch();
      }
    });
    cancelButton.addEventListener("click", () => {
      this.close();
    });
    renderTagButtons();
    searchInput.focus();
    searchInput.setSelectionRange(searchInput.value.length, searchInput.value.length);
  }
  onClose() {
    const { contentEl } = this;
    contentEl.empty();
  }
};
function showSearchModal(app, gridView, defaultQuery = "") {
  new SearchModal(app, gridView, defaultQuery).open();
}

// src/FileWatcher.ts
var import_obsidian10 = require("obsidian");
var FileWatcher = class {
  // 用於去抖動 render()
  constructor(plugin, gridView) {
    this.renderTimer = null;
    // 以 200ms 去抖動的方式排程 render，避免短時間內大量重繪
    this.scheduleRender = () => {
      if (this.renderTimer !== null) {
        clearTimeout(this.renderTimer);
      }
      this.renderTimer = window.setTimeout(() => {
        this.gridView.render();
        this.renderTimer = null;
      }, 200);
    };
    this.plugin = plugin;
    this.gridView = gridView;
    this.app = plugin.app;
  }
  registerFileWatcher() {
    if (!this.plugin.settings.enableFileWatcher) {
      return;
    }
    this.plugin.registerEvent(
      this.app.vault.on("create", (file) => {
        if (file instanceof import_obsidian10.TFile) {
          if (this.gridView.searchQuery !== "" && this.gridView.searchAllFiles) {
            this.scheduleRender();
            return;
          }
          if (this.gridView.sourceMode === "random-note") {
            return;
          } else if (this.gridView.sourceMode === "recent-files") {
            if (isDocumentFile(file) || isMediaFile(file) && this.gridView.randomNoteIncludeMedia) {
              this.scheduleRender();
            }
          } else if (this.gridView.sourceMode === "folder") {
            if (this.gridView.sourcePath) {
              const fileDirPath = file.path.split("/").slice(0, -1).join("/") || "/";
              if (fileDirPath === this.gridView.sourcePath) {
                this.scheduleRender();
              }
            }
          } else if (this.gridView.sourceMode === "backlinks") {
            if (isDocumentFile(file)) {
              this.scheduleRender();
            }
          } else {
            this.scheduleRender();
          }
        }
      })
    );
    this.plugin.registerEvent(
      this.app.vault.on("delete", (file) => {
        if (file instanceof import_obsidian10.TFile) {
          if (this.gridView) {
            const gridItemIndex = this.gridView.gridItems.findIndex(
              (item) => item.dataset.filePath === file.path
            );
            if (gridItemIndex >= 0) {
              this.gridView.gridItems[gridItemIndex].remove();
              this.gridView.gridItems.splice(gridItemIndex, 1);
              const gridContainer = this.gridView.containerEl.querySelector(".ge-grid-container");
              this.cleanupDateDividers(gridContainer);
            }
          }
        }
      })
    );
    this.plugin.registerEvent(
      this.app.vault.on("rename", (file, oldPath) => {
        if (file instanceof import_obsidian10.TFile) {
          const fileDirPath = file.path.split("/").slice(0, -1).join("/") || "/";
          const oldDirPath = oldPath.split("/").slice(0, -1).join("/") || "/";
          if (fileDirPath !== oldDirPath) {
            if (this.gridView.sourceMode === "folder") {
              if (this.gridView.sourcePath && this.gridView.searchQuery === "") {
                if (fileDirPath === this.gridView.sourcePath || oldDirPath === this.gridView.sourcePath) {
                  this.scheduleRender();
                  return;
                }
              }
            }
          }
          if (this.gridView) {
            const gridItemIndex = this.gridView.gridItems.findIndex(
              (item) => item.dataset.filePath === oldPath
            );
            if (gridItemIndex >= 0) {
              const getitle = this.gridView.gridItems[gridItemIndex].querySelector(".ge-grid-item .ge-title");
              if (getitle) {
                this.gridView.gridItems[gridItemIndex].dataset.filePath = file.path;
                getitle.textContent = file.basename;
                getitle.setAttribute("title", file.basename);
              }
            }
          }
        }
      })
    );
    this.plugin.registerEvent(
      this.app.internalPlugins.plugins.bookmarks.instance.on("changed", () => {
        if (this.gridView.sourceMode === "bookmarks") {
          this.scheduleRender();
        }
      })
    );
    this.plugin.registerEvent(
      this.app.workspace.on("file-open", (file) => {
        if (file instanceof import_obsidian10.TFile && this.gridView.searchQuery === "") {
          const sourceMode = this.gridView.sourceMode;
          if (sourceMode === "backlinks" || sourceMode === "outgoinglinks") {
            this.scheduleRender();
            return;
          }
          if (sourceMode.startsWith("custom-")) {
            const mode = this.plugin.settings.customModes.find((m) => m.internalName === sourceMode);
            if (mode && mode.dataviewCode.includes("dv.current")) {
              this.scheduleRender();
            }
          }
        }
      })
    );
  }
  // 清理日期分隔線
  cleanupDateDividers(container) {
    if (!container)
      return;
    const dateDividers = Array.from(container.querySelectorAll(".ge-date-divider"));
    for (let i = dateDividers.length - 1; i >= 0; i--) {
      const currentDivider = dateDividers[i];
      const nextDivider = dateDividers[i + 1];
      let nextElement = currentDivider.nextElementSibling;
      let hasItemsBetween = false;
      while (nextElement && (!nextDivider || nextElement !== nextDivider)) {
        if (!nextElement.classList.contains("ge-date-divider")) {
          hasItemsBetween = true;
          break;
        }
        nextElement = nextElement.nextElementSibling;
      }
      if (!nextDivider) {
        hasItemsBetween = currentDivider.nextElementSibling !== null;
      }
      if (!hasItemsBetween) {
        currentDivider.remove();
      }
    }
  }
};

// src/FloatingAudioPlayer.ts
var import_obsidian11 = require("obsidian");
var _FloatingAudioPlayer = class {
  constructor(app, file) {
    this.isDragging = false;
    this.offsetX = 0;
    this.offsetY = 0;
    this.isTouchEvent = false;
    this.app = app;
    this.currentFile = file;
    this.boundHandleDragStartMouse = this.handleDragStartMouse.bind(this);
    this.boundHandleDragStartTouch = this.handleDragStartTouch.bind(this);
    this.boundHandleDragMoveMouse = this.handleDragMoveMouse.bind(this);
    this.boundHandleDragMoveTouch = this.handleDragMoveTouch.bind(this);
    this.boundHandleDragEndMouse = this.handleDragEndMouse.bind(this);
    this.boundHandleDragEndTouch = this.handleDragEndTouch.bind(this);
    this.boundClose = this.close.bind(this);
    this.buildUI();
    this.setupDragEvents();
  }
  // --- 靜態方法：開啟或取得播放器 ---
  static open(app, file) {
    if (_FloatingAudioPlayer.players.has(file.path)) {
      const existingPlayer = _FloatingAudioPlayer.players.get(file.path);
      existingPlayer.focus();
      return existingPlayer;
    }
    if (_FloatingAudioPlayer.players.size > 0) {
      const firstPlayer = _FloatingAudioPlayer.players.values().next().value;
      firstPlayer.updatePlayer(file);
      firstPlayer.focus();
      return firstPlayer;
    }
    const newPlayer = new _FloatingAudioPlayer(app, file);
    _FloatingAudioPlayer.players.set(file.path, newPlayer);
    newPlayer.show();
    return newPlayer;
  }
  // --- Private UI 和事件設定方法 ---
  buildUI() {
    this.containerEl = document.createElement("div");
    this.containerEl.className = "ge-floating-audio-player";
    this.containerEl.setAttribute("data-file", this.currentFile.path);
    this.audioEl = document.createElement("audio");
    this.audioEl.controls = true;
    this.audioEl.src = this.app.vault.getResourcePath(this.currentFile);
    this.titleEl = document.createElement("div");
    this.titleEl.className = "ge-audio-title";
    this.titleEl.textContent = this.currentFile.basename;
    this.closeButtonEl = document.createElement("div");
    this.closeButtonEl.className = "ge-audio-close-button";
    (0, import_obsidian11.setIcon)(this.closeButtonEl, "x");
    this.closeButtonEl.addEventListener("click", this.boundClose);
    this.handleEl = document.createElement("div");
    this.handleEl.className = "ge-audio-handle";
    this.containerEl.appendChild(this.handleEl);
    this.containerEl.appendChild(this.titleEl);
    this.containerEl.appendChild(this.audioEl);
    this.containerEl.appendChild(this.closeButtonEl);
  }
  setupDragEvents() {
    this.handleEl.addEventListener("mousedown", this.boundHandleDragStartMouse);
    this.handleEl.addEventListener("touchstart", this.boundHandleDragStartTouch, { passive: true });
    document.addEventListener("mousemove", this.boundHandleDragMoveMouse);
    document.addEventListener("touchmove", this.boundHandleDragMoveTouch, { passive: false });
    document.addEventListener("mouseup", this.boundHandleDragEndMouse);
    document.addEventListener("touchend", this.boundHandleDragEndTouch);
  }
  removeDragEvents() {
    this.handleEl.removeEventListener("mousedown", this.boundHandleDragStartMouse);
    this.handleEl.removeEventListener("touchstart", this.boundHandleDragStartTouch);
    document.removeEventListener("mousemove", this.boundHandleDragMoveMouse);
    document.removeEventListener("touchmove", this.boundHandleDragMoveTouch);
    document.removeEventListener("mouseup", this.boundHandleDragEndMouse);
    document.removeEventListener("touchend", this.boundHandleDragEndTouch);
  }
  // --- Private 事件處理器 ---
  handleDragStartMouse(e) {
    if (this.isTouchEvent)
      return;
    this.isDragging = true;
    this.offsetX = e.clientX - this.containerEl.getBoundingClientRect().left;
    this.offsetY = e.clientY - this.containerEl.getBoundingClientRect().top;
    this.containerEl.classList.add("ge-audio-dragging");
  }
  handleDragStartTouch(e) {
    this.isTouchEvent = true;
    this.isDragging = true;
    const touch = e.touches[0];
    this.offsetX = touch.clientX - this.containerEl.getBoundingClientRect().left;
    this.offsetY = touch.clientY - this.containerEl.getBoundingClientRect().top;
    this.containerEl.classList.add("ge-audio-dragging");
  }
  handleDragMoveMouse(e) {
    if (!this.isDragging || this.isTouchEvent)
      return;
    this.movePlayer(e.clientX, e.clientY);
  }
  handleDragMoveTouch(e) {
    if (!this.isDragging)
      return;
    const touch = e.touches[0];
    this.movePlayer(touch.clientX, touch.clientY);
    e.preventDefault();
  }
  handleDragEndMouse() {
    if (this.isTouchEvent)
      return;
    this.isDragging = false;
    this.containerEl.classList.remove("ge-audio-dragging");
  }
  handleDragEndTouch() {
    this.isDragging = false;
    this.isTouchEvent = false;
    this.containerEl.classList.remove("ge-audio-dragging");
  }
  movePlayer(clientX, clientY) {
    const x = clientX - this.offsetX;
    const y = clientY - this.offsetY;
    this.containerEl.style.left = `${x}px`;
    this.containerEl.style.top = `${y}px`;
  }
  // --- Public 方法 ---
  show() {
    document.body.appendChild(this.containerEl);
    const rect = this.containerEl.getBoundingClientRect();
    this.containerEl.style.left = `${window.innerWidth - rect.width - 20}px`;
    this.containerEl.style.top = `${window.innerHeight - rect.height - 20}px`;
    this.audioEl.play();
  }
  close() {
    this.removeDragEvents();
    this.containerEl.remove();
    _FloatingAudioPlayer.players.delete(this.currentFile.path);
  }
  focus() {
    this.containerEl.scrollIntoView({ behavior: "smooth", block: "center" });
    this.containerEl.style.transition = "box-shadow 0.1s ease-in-out";
    this.containerEl.style.boxShadow = "0 0 10px 2px var(--interactive-accent)";
    setTimeout(() => {
      this.containerEl.style.boxShadow = "";
    }, 300);
  }
  // 更新播放器以播放新檔案
  updatePlayer(newFile) {
    _FloatingAudioPlayer.players.delete(this.currentFile.path);
    this.currentFile = newFile;
    _FloatingAudioPlayer.players.set(this.currentFile.path, this);
    this.containerEl.setAttribute("data-file", this.currentFile.path);
    this.audioEl.src = this.app.vault.getResourcePath(this.currentFile);
    this.titleEl.textContent = this.currentFile.basename;
    this.audioEl.play();
  }
};
var FloatingAudioPlayer = _FloatingAudioPlayer;
// 使用靜態 Map 來追蹤已開啟的播放器實例 (以檔案路徑為 key)
FloatingAudioPlayer.players = /* @__PURE__ */ new Map();

// src/GridView.ts
var GridView = class extends import_obsidian12.ItemView {
  // 任務分類
  constructor(leaf, plugin) {
    super(leaf);
    this.sourceMode = "";
    // 模式選擇
    this.sourcePath = "";
    // 排序模式
    this.folderSortType = "";
    // 資料夾排序模式
    this.searchQuery = "";
    // 搜尋關鍵字
    this.searchAllFiles = true;
    // 是否搜尋所有筆記
    this.searchMediaFiles = false;
    // 是否搜尋媒體檔案
    this.randomNoteIncludeMedia = false;
    // 隨機筆記是否包含圖片和影片
    this.selectedItemIndex = -1;
    // 當前選中的項目索引
    this.selectedItems = /* @__PURE__ */ new Set();
    // 存儲多選的項目索引
    this.gridItems = [];
    // 存儲所有網格項目的引用
    this.hasKeyboardFocus = false;
    this.recentSources = [];
    // 歷史記錄
    this.minMode = false;
    // 最小模式
    this.showIgnoredFolders = false;
    // 顯示忽略資料夾
    this.pinnedList = [];
    // 置頂清單
    this.taskFilter = "uncompleted";
    this.plugin = plugin;
    this.containerEl.addClass("ge-grid-view-container");
    this.sortType = this.plugin.settings.defaultSortType;
    if (this.plugin.settings.enableFileWatcher) {
      this.fileWatcher = new FileWatcher(plugin, this);
      this.fileWatcher.registerFileWatcher();
    }
    this.registerDomEvent(document, "keydown", (event) => {
      if (this.app.workspace.getActiveViewOfType(GridView) === this) {
        this.handleKeyDown(event);
      }
    });
  }
  getViewType() {
    return "grid-view";
  }
  getIcon() {
    if (this.sourceMode.startsWith("custom-")) {
      return "puzzle";
    } else if (this.sourceMode === "bookmarks") {
      return "bookmark";
    } else if (this.sourceMode === "search") {
      return "search";
    } else if (this.sourceMode === "backlinks") {
      return "links-coming-in";
    } else if (this.sourceMode === "outgoinglinks") {
      return "links-going-out";
    } else if (this.sourceMode === "all-files") {
      return "book-text";
    } else if (this.sourceMode === "recent-files") {
      return "calendar-days";
    } else if (this.sourceMode === "random-note") {
      return "dice";
    } else if (this.sourceMode === "tasks") {
      return "square-check-big";
    } else if (this.sourceMode === "folder") {
      return "folder";
    } else {
      return "grid";
    }
  }
  getDisplayText() {
    if (this.sourceMode.startsWith("custom-")) {
      const mode = this.plugin.settings.customModes.find((m) => m.internalName === this.sourceMode);
      return mode ? mode.displayName : t("custom_mode");
    } else if (this.sourceMode === "") {
      return t("grid_view_title");
    } else if (this.sourceMode === "bookmarks") {
      return t("bookmarks_mode");
    } else if (this.sourceMode === "search") {
      return t("search_results");
    } else if (this.sourceMode === "backlinks") {
      return t("backlinks_mode");
    } else if (this.sourceMode === "outgoinglinks") {
      return t("outgoinglinks_mode");
    } else if (this.sourceMode === "all-files") {
      return t("all_files_mode");
    } else if (this.sourceMode === "recent-files") {
      return t("recent_files_mode");
    } else if (this.sourceMode === "random-note") {
      return t("random_note_mode");
    } else if (this.sourceMode === "tasks") {
      return t("tasks_mode");
    } else if (this.sourceMode === "folder") {
      if (this.sourcePath === "/") {
        return t("root");
      }
      return this.sourcePath;
    } else {
      return "";
    }
  }
  // 將來源加入歷史記錄（LRU 去重）
  // 1. 若已有相同紀錄先移除，確保唯一
  // 2. 插入到陣列開頭，代表最新使用
  // 3. 超過上限時裁切
  pushHistory(mode, path) {
    const sanitizedPath = path != null ? path : "";
    const key = JSON.stringify({ mode, path: sanitizedPath });
    const existingIndex = this.recentSources.indexOf(key);
    if (existingIndex !== -1) {
      this.recentSources.splice(existingIndex, 1);
    }
    this.recentSources.unshift(key);
    const limit = 10;
    if (this.recentSources.length > limit) {
      this.recentSources.length = limit;
    }
  }
  async setSource(mode, path = "", resetScroll = false, recordHistory = true) {
    var _a;
    if (this.sourceMode && recordHistory) {
      this.pushHistory(this.sourceMode, this.sourcePath);
    }
    this.folderSortType = "";
    this.pinnedList = [];
    if (mode === "folder") {
      const folderName = path.split("/").pop() || "";
      const mdFilePath = `${path}/${folderName}.md`;
      const mdFile = this.app.vault.getAbstractFileByPath(mdFilePath);
      if (mdFile instanceof import_obsidian12.TFile) {
        const metadata = (_a = this.app.metadataCache.getFileCache(mdFile)) == null ? void 0 : _a.frontmatter;
        this.folderSortType = metadata == null ? void 0 : metadata.sort;
      }
    }
    if (mode !== "")
      this.sourceMode = mode;
    if (path !== "")
      this.sourcePath = path;
    if (this.sourceMode === "")
      this.sourceMode = "folder";
    if (this.sourcePath === "")
      this.sourcePath = "/";
    this.render(resetScroll);
    this.app.workspace.requestSaveLayout();
  }
  async render(resetScroll = false) {
    var _a;
    const scrollContainer = this.containerEl.children[1];
    const scrollTop = resetScroll ? 0 : scrollContainer ? scrollContainer.scrollTop : 0;
    let selectedFilePath = null;
    if (this.selectedItemIndex >= 0 && this.selectedItemIndex < this.gridItems.length) {
      const selectedItem = this.gridItems[this.selectedItemIndex];
      selectedFilePath = selectedItem.dataset.filePath || null;
    }
    this.containerEl.empty();
    const headerButtonsDiv = this.containerEl.createDiv("ge-header-buttons");
    headerButtonsDiv.addEventListener("click", (event) => {
      if (event.target === headerButtonsDiv) {
        event.preventDefault();
        const gridContainer = this.containerEl.querySelector(".ge-grid-container");
        if (gridContainer) {
          gridContainer.scrollTo({
            top: 0,
            behavior: "smooth"
          });
        }
      }
    });
    const newNoteButton = headerButtonsDiv.createEl("button", { attr: { "aria-label": t("new_note") } });
    (0, import_obsidian12.setIcon)(newNoteButton, "square-pen");
    newNoteButton.addEventListener("click", (event) => {
      event.preventDefault();
      const menu = new import_obsidian12.Menu();
      menu.addItem((item) => {
        item.setTitle(t("new_note")).setIcon("square-pen").onClick(async () => {
          let newFileName = `${t("untitled")}.md`;
          let newFilePath = !this.sourcePath || this.sourcePath === "/" ? newFileName : `${this.sourcePath}/${newFileName}`;
          let counter = 1;
          while (this.app.vault.getAbstractFileByPath(newFilePath)) {
            newFileName = `${t("untitled")} ${counter}.md`;
            newFilePath = !this.sourcePath || this.sourcePath === "/" ? newFileName : `${this.sourcePath}/${newFileName}`;
            counter++;
          }
          try {
            const newFile = await this.app.vault.create(newFilePath, "");
            await this.app.workspace.getLeaf().openFile(newFile);
          } catch (error) {
            console.error("An error occurred while creating a new note:", error);
          }
        });
      });
      menu.addItem((item) => {
        item.setTitle(t("new_folder")).setIcon("folder").onClick(async () => {
          let newFolderName = `${t("untitled")}`;
          let newFolderPath = !this.sourcePath || this.sourcePath === "/" ? newFolderName : `${this.sourcePath}/${newFolderName}`;
          let counter = 1;
          while (this.app.vault.getAbstractFileByPath(newFolderPath)) {
            newFolderName = `${t("untitled")} ${counter}`;
            newFolderPath = !this.sourcePath || this.sourcePath === "/" ? newFolderName : `${this.sourcePath}/${newFolderName}`;
            counter++;
          }
          try {
            await this.app.vault.createFolder(newFolderPath);
            this.render(false);
          } catch (error) {
            console.error("An error occurred while creating a new folder:", error);
          }
        });
      });
      menu.addItem((item) => {
        item.setTitle(t("new_canvas")).setIcon("layout-dashboard").onClick(async () => {
          let newFileName = `${t("untitled")}.canvas`;
          let newFilePath = !this.sourcePath || this.sourcePath === "/" ? newFileName : `${this.sourcePath}/${newFileName}`;
          let counter = 1;
          while (this.app.vault.getAbstractFileByPath(newFilePath)) {
            newFileName = `${t("untitled")} ${counter}.canvas`;
            newFilePath = !this.sourcePath || this.sourcePath === "/" ? newFileName : `${this.sourcePath}/${newFileName}`;
            counter++;
          }
          try {
            const newFile = await this.app.vault.create(newFilePath, "");
            await this.app.workspace.getLeaf().openFile(newFile);
          } catch (error) {
            console.error("An error occurred while creating a new canvas:", error);
          }
        });
      });
      menu.showAtMouseEvent(event);
    });
    const reselectButton = headerButtonsDiv.createEl("button", { attr: { "aria-label": t("reselect") } });
    reselectButton.addEventListener("click", () => {
      showFolderSelectionModal(this.app, this.plugin, this);
    });
    (0, import_obsidian12.setIcon)(reselectButton, "grid");
    reselectButton.addEventListener("contextmenu", (event) => {
      if (this.recentSources.length > 0) {
        event.preventDefault();
        const menu = new import_obsidian12.Menu();
        this.recentSources.forEach((sourceInfoStr, index) => {
          try {
            const sourceInfo = JSON.parse(sourceInfoStr);
            const { mode, path } = sourceInfo;
            let displayText = "";
            let icon = "";
            switch (mode) {
              case "folder":
                displayText = path || "/";
                icon = "folder";
                break;
              case "bookmarks":
                displayText = t("bookmarks_mode");
                icon = "bookmark";
                break;
              case "search":
                displayText = t("search_results");
                icon = "search";
                break;
              case "backlinks":
                displayText = t("backlinks_mode");
                icon = "links-coming-in";
                break;
              case "outgoinglinks":
                displayText = t("outgoinglinks_mode");
                icon = "links-going-out";
                break;
              case "all-files":
                displayText = t("all_files_mode");
                icon = "book-text";
                break;
              case "recent-files":
                displayText = t("recent_files_mode");
                icon = "calendar-days";
                break;
              case "random-note":
                displayText = t("random_note_mode");
                icon = "dice";
                break;
              case "tasks":
                displayText = t("tasks_mode");
                icon = "square-check-big";
                break;
              default:
                if (mode.startsWith("custom-")) {
                  const customMode = this.plugin.settings.customModes.find((m) => m.internalName === mode);
                  displayText = customMode ? customMode.displayName : t("custom_mode");
                  icon = "puzzle";
                } else {
                  displayText = mode;
                  icon = "grid";
                }
            }
            menu.addItem((item) => {
              item.setTitle(`${displayText}`).setIcon(`${icon}`).onClick(() => {
                const clickedIndex = this.recentSources.findIndex((source) => {
                  const parsed = JSON.parse(source);
                  return parsed.mode === mode && parsed.path === path;
                });
                if (clickedIndex !== -1) {
                  this.recentSources = this.recentSources.slice(clickedIndex + 1);
                }
                this.setSource(mode, path, true, false);
              });
            });
          } catch (error) {
            console.error("Failed to parse source info:", error);
          }
        });
        menu.showAtMouseEvent(event);
      }
    });
    const refreshButton = headerButtonsDiv.createEl("button", { attr: { "aria-label": t("refresh") } });
    refreshButton.addEventListener("click", () => {
      if (this.sortType === "random") {
        this.clearSelection();
      }
      this.render();
    });
    (0, import_obsidian12.setIcon)(refreshButton, "refresh-ccw");
    if (this.sourceMode !== "bookmarks" && this.sourceMode !== "recent-files" && this.sourceMode !== "random-note" && !this.sourceMode.startsWith("custom-")) {
      const sortButton = headerButtonsDiv.createEl("button", { attr: { "aria-label": t("sorting") } });
      sortButton.addEventListener("click", (evt) => {
        const menu = new import_obsidian12.Menu();
        const sortOptions = [
          { value: "name-asc", label: t("sort_name_asc"), icon: "a-arrow-up" },
          { value: "name-desc", label: t("sort_name_desc"), icon: "a-arrow-down" },
          { value: "mtime-desc", label: t("sort_mtime_desc"), icon: "clock" },
          { value: "mtime-asc", label: t("sort_mtime_asc"), icon: "clock" },
          { value: "ctime-desc", label: t("sort_ctime_desc"), icon: "calendar" },
          { value: "ctime-asc", label: t("sort_ctime_asc"), icon: "calendar" },
          { value: "random", label: t("sort_random"), icon: "dice" }
        ];
        sortOptions.forEach((option) => {
          menu.addItem((item) => {
            item.setTitle(option.label).setIcon(option.icon).setChecked((this.folderSortType || this.sortType) === option.value).onClick(() => {
              this.sortType = option.value;
              this.folderSortType = "";
              this.render();
              this.app.workspace.requestSaveLayout();
            });
          });
        });
        menu.showAtMouseEvent(evt);
      });
      (0, import_obsidian12.setIcon)(sortButton, "arrow-up-narrow-wide");
    }
    const searchButtonContainer = headerButtonsDiv.createDiv("ge-search-button-container");
    const searchButton = searchButtonContainer.createEl("button", {
      cls: "search-button",
      attr: { "aria-label": t("search") }
    });
    (0, import_obsidian12.setIcon)(searchButton, "search");
    searchButton.addEventListener("click", () => {
      showSearchModal(this.app, this, "");
    });
    if (this.searchQuery) {
      searchButton.style.display = "none";
      const searchTextContainer = searchButtonContainer.createDiv("ge-search-text-container");
      const searchText = searchTextContainer.createEl("span", { cls: "ge-search-text", text: this.searchQuery });
      searchText.style.cursor = "pointer";
      searchText.addEventListener("click", () => {
        showSearchModal(this.app, this, this.searchQuery);
      });
      const clearButton = searchTextContainer.createDiv("ge-clear-button");
      (0, import_obsidian12.setIcon)(clearButton, "x");
      clearButton.addEventListener("click", (e) => {
        e.stopPropagation();
        this.searchQuery = "";
        this.clearSelection();
        this.render();
        this.app.workspace.requestSaveLayout();
      });
    }
    if (this.searchQuery === "") {
      const moreOptionsButton = headerButtonsDiv.createEl("button", { attr: { "aria-label": t("more_options") } });
      (0, import_obsidian12.setIcon)(moreOptionsButton, "ellipsis-vertical");
      const menu = new import_obsidian12.Menu();
      menu.addItem((item) => {
        item.setTitle(t("open_new_grid_view")).setIcon("grid").onClick(() => {
          const { workspace } = this.app;
          let leaf = null;
          workspace.getLeavesOfType("grid-view");
          switch (this.plugin.settings.defaultOpenLocation) {
            case "left":
              leaf = workspace.getLeftLeaf(false);
              break;
            case "right":
              leaf = workspace.getRightLeaf(false);
              break;
            case "tab":
            default:
              leaf = workspace.getLeaf("tab");
              break;
          }
          if (!leaf) {
            leaf = workspace.getLeaf("tab");
          }
          leaf.setViewState({ type: "grid-view", active: true });
          if (leaf.view instanceof GridView) {
            leaf.view.setSource("folder", "/");
          }
          workspace.revealLeaf(leaf);
        });
      });
      menu.addSeparator();
      if (this.sourceMode === "folder" && this.sourcePath && this.sourcePath !== "/") {
        const folder = this.app.vault.getAbstractFileByPath(this.sourcePath);
        const folderName = this.sourcePath.split("/").pop() || "";
        const notePath = `${this.sourcePath}/${folderName}.md`;
        const noteFile = this.app.vault.getAbstractFileByPath(notePath);
        if (noteFile instanceof import_obsidian12.TFile) {
          menu.addItem((item) => {
            item.setTitle(t("open_folder_note")).setIcon("panel-left-open").onClick(() => {
              this.app.workspace.getLeaf().openFile(noteFile);
            });
          });
          menu.addItem((item) => {
            item.setTitle(t("edit_folder_note_settings")).setIcon("settings-2").onClick(() => {
              if (folder instanceof import_obsidian12.TFolder) {
                showFolderNoteSettingsModal(this.app, this.plugin, folder, this);
              }
            });
          });
          menu.addItem((item) => {
            item.setTitle(t("delete_folder_note")).setIcon("folder-x").onClick(() => {
              this.app.fileManager.trashFile(noteFile);
            });
          });
        } else {
          menu.addItem((item) => {
            item.setTitle(t("create_folder_note")).setIcon("file-cog").onClick(() => {
              if (folder instanceof import_obsidian12.TFolder) {
                showFolderNoteSettingsModal(this.app, this.plugin, folder, this);
              }
            });
          });
        }
        menu.addSeparator();
      }
      if ((this.sourceMode === "all-files" || this.sourceMode === "recent-files" || this.sourceMode === "random-note") && this.plugin.settings.showMediaFiles && this.searchQuery === "") {
        menu.addItem((item) => {
          item.setTitle(t("random_note_notes_only")).setIcon("file-text").setChecked(!this.randomNoteIncludeMedia).onClick(() => {
            this.randomNoteIncludeMedia = false;
            this.render();
          });
        });
        menu.addItem((item) => {
          item.setTitle(t("random_note_include_media_files")).setIcon("file-image").setChecked(this.randomNoteIncludeMedia).onClick(() => {
            this.randomNoteIncludeMedia = true;
            this.render();
          });
        });
        menu.addSeparator();
      }
      if (this.sourceMode === "tasks" && this.searchQuery === "") {
        menu.addItem((item) => {
          item.setTitle(t("uncompleted")).setChecked(this.taskFilter === "uncompleted").setIcon("square").onClick(() => {
            this.taskFilter = "uncompleted";
            this.render();
          });
        });
        menu.addItem((item) => {
          item.setTitle(t("completed")).setChecked(this.taskFilter === "completed").setIcon("square-check-big").onClick(() => {
            this.taskFilter = "completed";
            this.render();
          });
        });
        menu.addItem((item) => {
          item.setTitle(t("all")).setChecked(this.taskFilter === "all").setIcon("square-asterisk").onClick(() => {
            this.taskFilter = "all";
            this.render();
          });
        });
        menu.addSeparator();
      }
      menu.addItem((item) => {
        item.setTitle(t("min_mode")).setIcon("minimize-2").setChecked(this.minMode).onClick(() => {
          this.minMode = !this.minMode;
          this.app.workspace.requestSaveLayout();
          this.render();
        });
      });
      menu.addItem((item) => {
        item.setTitle(t("show_ignored_folders")).setIcon("folder-open-dot").setChecked(this.showIgnoredFolders).onClick(() => {
          this.showIgnoredFolders = !this.showIgnoredFolders;
          this.app.workspace.requestSaveLayout();
          this.render();
        });
      });
      menu.addSeparator();
      menu.addItem((item) => {
        item.setTitle(t("open_settings")).setIcon("settings").onClick(() => {
          this.app.setting.open();
          this.app.setting.openTabById(this.plugin.manifest.id);
        });
      });
      moreOptionsButton.addEventListener("click", (event) => {
        menu.showAtMouseEvent(event);
      });
    }
    if (this.sourceMode === "folder" && (this.searchQuery === "" || this.searchQuery && !this.searchAllFiles) && this.sourcePath !== "/") {
      const pathParts = this.sourcePath.split("/");
      const parentPath = pathParts.slice(0, -1).join("/") || "/";
      let parentFolderName = pathParts.slice(-2, -1)[0] || "/";
      const currentFolderName = pathParts.pop() || t("root");
      if (parentPath === "/" || parentFolderName === "/" || parentFolderName === "") {
        parentFolderName = t("root");
      }
      const folderNameContainer = this.containerEl.createDiv("ge-foldername-content");
      const customFolderIcon = this.plugin.settings.customFolderIcon;
      const parentFolderLink = folderNameContainer.createEl("a", {
        text: `${customFolderIcon} ${parentFolderName}`.trim(),
        cls: "ge-parent-folder-link"
      });
      parentFolderLink.addEventListener("click", (event) => {
        event.preventDefault();
        event.stopPropagation();
        this.setSource("folder", parentPath, true);
        this.clearSelection();
      });
      parentFolderLink.addEventListener("contextmenu", (event) => {
        event.preventDefault();
        event.stopPropagation();
        const menu = new import_obsidian12.Menu();
        const pathParts2 = parentPath.split("/").filter((part) => part.trim() !== "");
        const paths = [];
        let pathAccumulator = "";
        pathParts2.forEach((part) => {
          pathAccumulator = pathAccumulator ? `${pathAccumulator}/${part}` : part;
          paths.push({
            name: part,
            path: pathAccumulator
          });
        });
        if (paths.length > 0) {
          const current = paths[paths.length - 1];
          menu.addItem((item) => {
            item.setTitle(current.name).setIcon("folder-open").onClick(() => {
              this.setSource("folder", current.path, true);
              this.clearSelection();
            });
            return item;
          });
        }
        for (let i = paths.length - 2; i >= 0; i--) {
          const path = paths[i];
          menu.addItem((item) => {
            item.setTitle(path.name).setIcon("folder").onClick(() => {
              this.setSource("folder", path.path, true);
              this.clearSelection();
            });
            return item;
          });
        }
        if (paths.length > 0) {
          menu.addSeparator();
        }
        menu.addItem((item) => {
          item.setTitle(t("root")).setIcon("folder").onClick(() => {
            this.setSource("folder", "/", true);
            this.clearSelection();
          });
          return item;
        });
        menu.showAtMouseEvent(event);
      });
      folderNameContainer.createEl("span", { text: " > " });
      folderNameContainer.createEl("span", { text: currentFolderName });
      if (import_obsidian12.Platform.isDesktop) {
        parentFolderLink.addEventListener("dragover", (event) => {
          event.preventDefault();
          event.dataTransfer.dropEffect = "move";
          parentFolderLink.addClass("ge-dragover");
        });
        parentFolderLink.addEventListener("dragleave", () => {
          parentFolderLink.removeClass("ge-dragover");
        });
        parentFolderLink.addEventListener("drop", async (event) => {
          var _a2, _b;
          event.preventDefault();
          parentFolderLink.removeClass("ge-dragover");
          const parentPath2 = this.sourcePath.split("/").slice(0, -1).join("/") || "/";
          if (!parentPath2)
            return;
          const folder = this.app.vault.getAbstractFileByPath(parentPath2);
          if (!(folder instanceof import_obsidian12.TFolder))
            return;
          const filesData = (_a2 = event.dataTransfer) == null ? void 0 : _a2.getData("application/obsidian-grid-explorer-files");
          if (filesData) {
            try {
              const filePaths = JSON.parse(filesData);
              for (const filePath2 of filePaths) {
                const file2 = this.app.vault.getAbstractFileByPath(filePath2);
                if (file2 instanceof import_obsidian12.TFile) {
                  const newPath = (0, import_obsidian12.normalizePath)(`${parentPath2}/${file2.name}`);
                  await this.app.fileManager.renameFile(file2, newPath);
                }
              }
            } catch (error) {
              console.error("An error occurred while moving multiple files to parent folder:", error);
            }
            return;
          }
          const filePath = (_b = event.dataTransfer) == null ? void 0 : _b.getData("text/plain");
          if (!filePath)
            return;
          const cleanedFilePath = filePath.replace(/!?\[\[(.*?)\]\]/, "$1");
          const file = this.app.vault.getAbstractFileByPath(cleanedFilePath);
          if (file instanceof import_obsidian12.TFile) {
            try {
              const newPath = (0, import_obsidian12.normalizePath)(`${parentPath2}/${file.name}`);
              await this.app.fileManager.renameFile(file, newPath);
              this.render();
            } catch (error) {
              console.error("An error occurred while moving the file to parent folder:", error);
            }
          }
        });
      }
    } else if (!(this.searchQuery !== "" && this.searchAllFiles)) {
      const folderNameContainer = this.containerEl.createDiv("ge-foldername-content");
      let modeName = "";
      let modeIcon = "";
      switch (this.sourceMode) {
        case "bookmarks":
          modeIcon = "\u{1F4D1}";
          modeName = t("bookmarks_mode");
          break;
        case "search":
          modeIcon = "\u{1F50D}";
          modeName = t("search_results");
          const searchLeaf = this.app.workspace.getLeavesOfType("search")[0];
          if (searchLeaf) {
            const searchView = searchLeaf.view;
            const searchInputEl = searchView.searchComponent ? searchView.searchComponent.inputEl : null;
            const currentQuery = searchInputEl == null ? void 0 : searchInputEl.value.trim();
            if (currentQuery && currentQuery.length > 0) {
              modeName += `: ${currentQuery}`;
            } else if (this.searchQuery) {
              modeName += `: ${this.searchQuery}`;
            }
          }
          break;
        case "backlinks":
          modeIcon = "\u{1F517}";
          modeName = t("backlinks_mode");
          const activeFile = this.app.workspace.getActiveFile();
          if (activeFile) {
            modeName += `: ${activeFile.basename}`;
          }
          break;
        case "outgoinglinks":
          modeIcon = "\u{1F517}";
          modeName = t("outgoinglinks_mode");
          const currentFile = this.app.workspace.getActiveFile();
          if (currentFile) {
            modeName += `: ${currentFile.basename}`;
          }
          break;
        case "recent-files":
          modeIcon = "\u{1F4C5}";
          modeName = t("recent_files_mode");
          break;
        case "all-files":
          modeIcon = "\u{1F4D4}";
          modeName = t("all_files_mode");
          break;
        case "random-note":
          modeIcon = "\u{1F3B2}";
          modeName = t("random_note_mode");
          break;
        case "tasks":
          modeIcon = "\u2611\uFE0F";
          modeName = t("tasks_mode");
          break;
        default:
          if (this.sourceMode.startsWith("custom-")) {
            const mode = this.plugin.settings.customModes.find((m) => m.internalName === this.sourceMode);
            modeIcon = mode ? mode.icon : "\u{1F9E9}";
            modeName = mode ? mode.displayName : t("custom_mode");
          } else {
            modeIcon = "\u{1F4C1}";
            if (this.sourcePath && this.sourcePath !== "/") {
              modeName = this.sourcePath.split("/").pop() || this.sourcePath;
            } else {
              modeName = t("root");
            }
          }
      }
      folderNameContainer.createEl("span", {
        text: `${modeIcon} ${modeName}`.trim(),
        cls: "ge-mode-title"
      });
      switch (this.sourceMode) {
        case "tasks":
          folderNameContainer.createEl("span", { text: " > " });
          folderNameContainer.createEl("span", { text: t(`${this.taskFilter}`) });
          break;
        default:
          break;
      }
    } else if (this.searchQuery !== "" && this.searchAllFiles) {
      const folderNameContainer = this.containerEl.createDiv("ge-foldername-content");
      folderNameContainer.createEl("span", {
        text: `\u{1F50D} ${t("global_search")}`,
        cls: "ge-mode-title"
      });
    }
    const contentEl = this.containerEl.createDiv("view-content");
    if (this.sourceMode === "folder" && this.sourcePath !== "/") {
      this.pinnedList = [];
      const folderPath = this.sourcePath;
      if (!folderPath || folderPath === "/")
        return;
      const folderName = folderPath.split("/").pop() || "";
      const notePath = `${folderPath}/${folderName}.md`;
      const noteFile = this.app.vault.getAbstractFileByPath(notePath);
      if (noteFile instanceof import_obsidian12.TFile) {
        const metadata = (_a = this.app.metadataCache.getFileCache(noteFile)) == null ? void 0 : _a.frontmatter;
        if (metadata) {
          if (Array.isArray(metadata["pinned"])) {
            if (this.plugin.settings.folderNoteDisplaySettings === "pinned") {
              this.pinnedList = metadata["pinned"].filter((name) => name !== `${folderName}.md`);
              this.pinnedList.unshift(`${folderName}.md`);
            } else {
              this.pinnedList = metadata["pinned"];
            }
          } else if (this.plugin.settings.folderNoteDisplaySettings === "pinned") {
            this.pinnedList = [`${folderName}.md`];
          }
        } else {
          this.pinnedList = [];
        }
      }
    }
    ;
    await this.grid_render();
    this.leaf.updateHeader();
    if (scrollContainer && !resetScroll) {
      contentEl.scrollTop = scrollTop;
    }
    if (selectedFilePath && this.hasKeyboardFocus) {
      const newIndex = this.gridItems.findIndex((item) => item.dataset.filePath === selectedFilePath);
      if (newIndex >= 0) {
        this.selectItem(newIndex);
      }
    }
  }
  async grid_render() {
    var _a, _b, _c;
    const container = this.containerEl.querySelector(".view-content");
    container.empty();
    container.addClass("ge-grid-container");
    container.style.setProperty("--grid-item-width", this.plugin.settings.gridItemWidth + "px");
    if (this.plugin.settings.gridItemHeight === 0 || this.minMode) {
      container.style.setProperty("--grid-item-height", "100%");
    } else {
      container.style.setProperty("--grid-item-height", this.plugin.settings.gridItemHeight + "px");
    }
    container.style.setProperty("--image-area-width", this.plugin.settings.imageAreaWidth + "px");
    container.style.setProperty("--image-area-height", this.plugin.settings.imageAreaHeight + "px");
    container.style.setProperty("--title-font-size", this.plugin.settings.titleFontSize + "em");
    container.addEventListener("click", (event) => {
      if (event.target === container) {
        this.clearSelection();
        this.hasKeyboardFocus = false;
      }
    });
    this.gridItems = [];
    if (this.sourceMode === "bookmarks" && !((_a = this.app.internalPlugins.plugins.bookmarks) == null ? void 0 : _a.enabled)) {
      new import_obsidian12.Notice(t("bookmarks_plugin_disabled"));
      return;
    }
    if (this.sourceMode === "backlinks" && !this.app.workspace.getActiveFile()) {
      const noFilesDiv = container.createDiv("ge-no-files");
      noFilesDiv.setText(t("no_backlinks"));
      if (this.plugin.statusBarItem) {
        this.plugin.statusBarItem.setText("");
      }
      return;
    }
    if (this.sourceMode === "folder" && this.searchQuery === "") {
      const currentFolder = this.app.vault.getAbstractFileByPath(this.sourcePath || "/");
      if (currentFolder instanceof import_obsidian12.TFolder) {
        const subfolders = currentFolder.children.filter((child) => {
          if (!(child instanceof import_obsidian12.TFolder))
            return false;
          if (this.showIgnoredFolders)
            return true;
          const isInIgnoredFolders = this.plugin.settings.ignoredFolders.some(
            (folder) => child.path === folder || child.path.startsWith(folder + "/")
          );
          if (isInIgnoredFolders)
            return false;
          if (this.plugin.settings.ignoredFolderPatterns && this.plugin.settings.ignoredFolderPatterns.length > 0) {
            const matchesIgnoredPattern = this.plugin.settings.ignoredFolderPatterns.some((pattern) => {
              try {
                if (/[\^\$\*\+\?\(\)\[\]\{\}\|\\]/.test(pattern)) {
                  const regex = new RegExp(pattern);
                  return regex.test(child.path);
                } else {
                  return child.name.toLowerCase().includes(pattern.toLowerCase());
                }
              } catch (error) {
                return child.name.toLowerCase().includes(pattern.toLowerCase());
              }
            });
            if (matchesIgnoredPattern)
              return false;
          }
          return true;
        }).sort((a, b) => a.name.localeCompare(b.name));
        for (const folder of subfolders) {
          const folderEl = container.createDiv("ge-grid-item ge-folder-item");
          this.gridItems.push(folderEl);
          folderEl.dataset.folderPath = folder.path;
          const contentArea = folderEl.createDiv("ge-content-area");
          const titleContainer = contentArea.createDiv("ge-title-container");
          const customFolderIcon = this.plugin.settings.customFolderIcon;
          titleContainer.createEl("span", { cls: "ge-title", text: `${customFolderIcon} ${folder.name}`.trim() });
          titleContainer.setAttribute("title", folder.name);
          const notePath = `${folder.path}/${folder.name}.md`;
          const noteFile = this.app.vault.getAbstractFileByPath(notePath);
          if (noteFile instanceof import_obsidian12.TFile) {
            const noteIcon = titleContainer.createEl("span", {
              cls: "ge-note-button"
            });
            (0, import_obsidian12.setIcon)(noteIcon, "panel-left-open");
            noteIcon.addEventListener("click", (e) => {
              e.stopPropagation();
              this.app.workspace.getLeaf().openFile(noteFile);
            });
            const metadata = (_b = this.app.metadataCache.getFileCache(noteFile)) == null ? void 0 : _b.frontmatter;
            const colorValue = metadata == null ? void 0 : metadata.color;
            if (colorValue) {
              folderEl.setAttribute("style", `
                                background-color: rgba(var(--color-${colorValue}-rgb), 0.2);
                                border-color: rgba(var(--color-${colorValue}-rgb), 0.5);
                            `);
            }
            const iconValue = metadata == null ? void 0 : metadata.icon;
            if (iconValue) {
              const title = folderEl.querySelector(".ge-title");
              if (title) {
                title.textContent = `${iconValue} ${folder.name}`;
              }
            }
          }
          folderEl.addEventListener("click", () => {
            this.setSource("folder", folder.path, true);
            this.clearSelection();
          });
          folderEl.addEventListener("contextmenu", (event) => {
            event.preventDefault();
            const menu = new import_obsidian12.Menu();
            menu.addItem((item) => {
              item.setTitle(t("open_in_new_grid_view")).setIcon("grid").onClick(() => {
                const { workspace } = this.app;
                let leaf = null;
                workspace.getLeavesOfType("grid-view");
                switch (this.plugin.settings.defaultOpenLocation) {
                  case "left":
                    leaf = workspace.getLeftLeaf(false);
                    break;
                  case "right":
                    leaf = workspace.getRightLeaf(false);
                    break;
                  case "tab":
                  default:
                    leaf = workspace.getLeaf("tab");
                    break;
                }
                if (!leaf) {
                  leaf = workspace.getLeaf("tab");
                }
                leaf.setViewState({ type: "grid-view", active: true });
                if (leaf.view instanceof GridView) {
                  leaf.view.setSource("folder", folder.path);
                }
                workspace.revealLeaf(leaf);
              });
            });
            menu.addSeparator();
            const notePath2 = `${folder.path}/${folder.name}.md`;
            let noteFile2 = this.app.vault.getAbstractFileByPath(notePath2);
            if (noteFile2 instanceof import_obsidian12.TFile) {
              menu.addItem((item) => {
                item.setTitle(t("open_folder_note")).setIcon("panel-left-open").onClick(() => {
                  this.app.workspace.getLeaf().openFile(noteFile2);
                });
              });
              menu.addItem((item) => {
                item.setTitle(t("edit_folder_note_settings")).setIcon("settings-2").onClick(() => {
                  if (folder instanceof import_obsidian12.TFolder) {
                    showFolderNoteSettingsModal(this.app, this.plugin, folder, this);
                  }
                });
              });
              menu.addItem((item) => {
                item.setTitle(t("delete_folder_note")).setIcon("folder-x").onClick(() => {
                  this.app.fileManager.trashFile(noteFile2);
                });
              });
            } else {
              menu.addItem((item) => {
                item.setTitle(t("create_folder_note")).setIcon("file-cog").onClick(() => {
                  if (folder instanceof import_obsidian12.TFolder) {
                    showFolderNoteSettingsModal(this.app, this.plugin, folder, this);
                  }
                });
              });
            }
            menu.addSeparator();
            if (!this.plugin.settings.ignoredFolders.includes(folder.path)) {
              menu.addItem((item) => {
                item.setTitle(t("ignore_folder")).setIcon("folder-x").onClick(() => {
                  this.plugin.settings.ignoredFolders.push(folder.path);
                  this.plugin.saveSettings();
                });
              });
            } else {
              menu.addItem((item) => {
                item.setTitle(t("unignore_folder")).setIcon("folder-up").onClick(() => {
                  this.plugin.settings.ignoredFolders = this.plugin.settings.ignoredFolders.filter((path) => path !== folder.path);
                  this.plugin.saveSettings();
                });
              });
            }
            menu.addItem((item) => {
              item.setTitle(t("rename_folder")).setIcon("file-cog").onClick(() => {
                if (folder instanceof import_obsidian12.TFolder) {
                  showFolderRenameModal(this.app, this.plugin, folder, this);
                }
              });
            });
            menu.addItem((item) => {
              item.setWarning(true);
              item.setTitle(t("delete_folder")).setIcon("trash").onClick(async () => {
                if (folder instanceof import_obsidian12.TFolder) {
                  await this.app.fileManager.trashFile(folder);
                  setTimeout(() => {
                    this.render();
                  }, 100);
                }
              });
            });
            menu.showAtMouseEvent(event);
          });
        }
        if (subfolders.length > 0) {
          container.createDiv("ge-break");
        }
      }
    }
    let loadingDiv = null;
    if (this.searchQuery || this.sourceMode === "tasks") {
      loadingDiv = container.createDiv({ text: t("searching"), cls: "ge-loading-indicator" });
    }
    let files = [];
    let fileIndexMap = /* @__PURE__ */ new Map();
    if (this.searchQuery) {
      let allFiles = [];
      if (this.searchAllFiles) {
        allFiles = this.app.vault.getFiles().filter(
          (file) => isDocumentFile(file) || isMediaFile(file) && this.searchMediaFiles
        );
      } else {
        allFiles = await getFiles(this, this.searchMediaFiles);
        if (this.sourceMode === "recent-files") {
          allFiles = ignoredFiles(allFiles, this).slice(0, this.plugin.settings.recentFilesCount);
        } else if (this.sourceMode === "bookmarks") {
          allFiles = allFiles.filter(
            (file) => isDocumentFile(file) || isMediaFile(file) && this.searchMediaFiles
          );
          allFiles.forEach((file, index) => {
            fileIndexMap.set(file, index);
          });
        } else if (this.sourceMode.startsWith("custom-")) {
          allFiles.forEach((file, index) => {
            fileIndexMap.set(file, index);
          });
        } else if (this.sourceMode === "search") {
          allFiles = allFiles.filter(
            (file) => isDocumentFile(file) || isMediaFile(file) && this.searchMediaFiles
          );
        }
      }
      const searchTerms = this.searchQuery.toLowerCase().split(/\s+/).filter((term) => term.trim() !== "");
      const tagTerms = searchTerms.filter((term) => term.startsWith("#")).map((term) => term.substring(1));
      const normalTerms = searchTerms.filter((term) => !term.startsWith("#"));
      await Promise.all(
        allFiles.map(async (file) => {
          const fileName = file.name.toLowerCase();
          const matchesFileName = normalTerms.length === 0 || normalTerms.every((term) => fileName.includes(term));
          if (tagTerms.length > 0 && normalTerms.length === 0 && file.extension !== "md") {
            return;
          }
          if (tagTerms.length === 0) {
            if (matchesFileName) {
              files.push(file);
            } else if (file.extension === "md") {
              const content = (await this.app.vault.cachedRead(file)).toLowerCase();
              const matchesContent = normalTerms.every((term) => content.includes(term));
              if (matchesContent) {
                files.push(file);
              }
            }
            return;
          }
          if (file.extension === "md") {
            const fileCache = this.app.metadataCache.getFileCache(file);
            let matchesTags = false;
            if (fileCache) {
              const collectedTags = [];
              if (Array.isArray(fileCache.tags)) {
                for (const t2 of fileCache.tags) {
                  if (t2 && t2.tag) {
                    const clean = t2.tag.toLowerCase().replace(/^#/, "");
                    collectedTags.push(...clean.split(/\s+/).filter((st) => st.trim() !== ""));
                  }
                }
              }
              if (fileCache.frontmatter && fileCache.frontmatter.tags) {
                const fmTags = fileCache.frontmatter.tags;
                if (typeof fmTags === "string") {
                  collectedTags.push(
                    ...fmTags.split(/[,\s]+/).map((t2) => t2.toLowerCase().replace(/^#/, "")).filter((t2) => t2.trim() !== "")
                  );
                } else if (Array.isArray(fmTags)) {
                  for (const t2 of fmTags) {
                    if (typeof t2 === "string") {
                      const clean = t2.toLowerCase().replace(/^#/, "");
                      collectedTags.push(...clean.split(/\s+/).filter((st) => st.trim() !== ""));
                    }
                  }
                }
              }
              matchesTags = tagTerms.every((tag) => collectedTags.includes(tag));
            }
            if (matchesTags) {
              if (matchesFileName) {
                files.push(file);
              } else if (normalTerms.length > 0) {
                const content = (await this.app.vault.cachedRead(file)).toLowerCase();
                const matchesContent = normalTerms.every((term) => content.includes(term));
                if (matchesContent) {
                  files.push(file);
                }
              } else {
                files.push(file);
              }
            }
          }
        })
      );
      if (this.sourceMode === "recent-files") {
        const sortType = this.sortType;
        this.sortType = "mtime-desc";
        files = sortFiles(files, this);
        this.sortType = sortType;
      } else if (this.sourceMode === "bookmarks") {
        files.sort((a, b) => {
          var _a2, _b2;
          const indexA = (_a2 = fileIndexMap.get(a)) != null ? _a2 : Number.MAX_SAFE_INTEGER;
          const indexB = (_b2 = fileIndexMap.get(b)) != null ? _b2 : Number.MAX_SAFE_INTEGER;
          return indexA - indexB;
        });
      } else if (this.sourceMode === "random-note") {
        const sortType = this.sortType;
        this.sortType = "random";
        files = sortFiles(files, this);
        this.sortType = sortType;
      } else if (this.sourceMode.startsWith("custom-")) {
        files.sort((a, b) => {
          var _a2, _b2;
          const indexA = (_a2 = fileIndexMap.get(a)) != null ? _a2 : Number.MAX_SAFE_INTEGER;
          const indexB = (_b2 = fileIndexMap.get(b)) != null ? _b2 : Number.MAX_SAFE_INTEGER;
          return indexA - indexB;
        });
      } else {
        files = sortFiles(files, this);
      }
      files = ignoredFiles(files, this);
    } else {
      files = await getFiles(this, this.randomNoteIncludeMedia);
      files = ignoredFiles(files, this);
      if (this.sourceMode === "recent-files") {
        files = files.slice(0, this.plugin.settings.recentFilesCount);
      }
      if (this.sourceMode === "random-note") {
        files = files.slice(0, this.plugin.settings.randomNoteCount);
      }
    }
    if (loadingDiv) {
      loadingDiv.remove();
    }
    if (files.length === 0) {
      const noFilesDiv = container.createDiv("ge-no-files");
      if (this.sourceMode !== "backlinks") {
        noFilesDiv.setText(t("no_files"));
      } else {
        noFilesDiv.setText(t("no_backlinks"));
      }
      if (this.plugin.statusBarItem) {
        this.plugin.statusBarItem.setText("");
      }
      return;
    }
    if (this.pinnedList.length > 0 && this.sourceMode === "folder") {
      const pinnedFiles = files.filter((f) => this.pinnedList.includes(f.name));
      pinnedFiles.sort((a, b) => this.pinnedList.indexOf(a.name) - this.pinnedList.indexOf(b.name));
      const otherFiles = files.filter((f) => !this.pinnedList.includes(f.name));
      files = [...pinnedFiles, ...otherFiles];
    }
    if (this.sourceMode === "folder" && this.sourcePath !== "/") {
      if (this.plugin.settings.folderNoteDisplaySettings === "hidden") {
        const currentFolder = this.app.vault.getAbstractFileByPath(this.sourcePath);
        if (currentFolder instanceof import_obsidian12.TFolder) {
          const folderName = currentFolder.name;
          files = files.filter((f) => f.name !== `${folderName}.md`);
        }
      }
    }
    const observer = new IntersectionObserver((entries, observer2) => {
      entries.forEach(async (entry) => {
        var _a2, _b2, _c2;
        if (entry.isIntersecting) {
          const fileEl = entry.target;
          const filePath = fileEl.dataset.filePath;
          if (!filePath)
            return;
          const file = this.app.vault.getAbstractFileByPath(filePath);
          if (!(file instanceof import_obsidian12.TFile))
            return;
          let imageUrl = "";
          const contentArea = fileEl.querySelector(".ge-content-area");
          if (!contentArea.hasAttribute("data-loaded")) {
            if (file.extension === "md") {
              let summaryLength = this.plugin.settings.summaryLength;
              if (summaryLength < 50) {
                summaryLength = 100;
                this.plugin.settings.summaryLength = 100;
                this.plugin.saveSettings();
              }
              const content = await this.app.vault.cachedRead(file);
              const frontMatterInfo = (0, import_obsidian12.getFrontMatterInfo)(content);
              let metadata = void 0;
              if (frontMatterInfo.exists) {
                metadata = (_a2 = this.app.metadataCache.getFileCache(file)) == null ? void 0 : _a2.frontmatter;
              }
              let pEl = null;
              if (!this.minMode) {
                const summaryField = this.plugin.settings.noteSummaryField || "summary";
                const summaryValue = metadata == null ? void 0 : metadata[summaryField];
                if (summaryValue) {
                  pEl = contentArea.createEl("p", { text: summaryValue.trim() });
                } else {
                  let contentWithoutFrontmatter = "";
                  if (summaryLength < 500) {
                    contentWithoutFrontmatter = content.substring(frontMatterInfo.contentStart).slice(0, 500);
                  } else {
                    contentWithoutFrontmatter = content.substring(frontMatterInfo.contentStart).slice(0, summaryLength + summaryLength);
                  }
                  let contentWithoutMediaLinks = "";
                  if (this.plugin.settings.showCodeBlocksInSummary) {
                    contentWithoutMediaLinks = contentWithoutFrontmatter;
                  } else {
                    contentWithoutMediaLinks = contentWithoutFrontmatter.replace(/```[\s\S]*?```\n/g, "").replace(/```[\s\S]*$/, "");
                  }
                  contentWithoutMediaLinks = contentWithoutMediaLinks.replace(/<!--[\s\S]*?-->/g, "").replace(/!?\[([^\]]*)\]\([^)]+\)|!?\[\[([^\]]+)\]\]/g, (match, p1, p2) => {
                    var _a3;
                    const linkText = p1 || p2 || "";
                    if (!linkText)
                      return "";
                    const extension = ((_a3 = linkText.split(".").pop()) == null ? void 0 : _a3.toLowerCase()) || "";
                    return IMAGE_EXTENSIONS.has(extension) || VIDEO_EXTENSIONS.has(extension) ? "" : linkText;
                  });
                  if (contentWithoutMediaLinks.startsWith("# ") || contentWithoutMediaLinks.startsWith("## ") || contentWithoutMediaLinks.startsWith("### ")) {
                    contentWithoutMediaLinks = contentWithoutMediaLinks.split("\n").slice(1).join("\n");
                  }
                  if (!this.plugin.settings.showCodeBlocksInSummary) {
                    contentWithoutMediaLinks = contentWithoutMediaLinks.replace(/[>|\-#*]/g, "").trim();
                  }
                  const preview = contentWithoutMediaLinks.slice(0, summaryLength) + (contentWithoutMediaLinks.length > summaryLength ? "..." : "");
                  pEl = contentArea.createEl("p", { text: preview.trim() });
                }
              }
              const titleEl = fileEl.querySelector(".ge-title");
              if (titleEl && pEl) {
                titleEl.setAttribute("title", `${titleEl.textContent}
${pEl.textContent}` || "");
              }
              if (frontMatterInfo.exists) {
                const colorValue = metadata == null ? void 0 : metadata.color;
                if (colorValue) {
                  fileEl.setAttribute("style", `
                                        background-color: rgba(var(--color-${colorValue}-rgb), 0.2);
                                        border-color: rgba(var(--color-${colorValue}-rgb), 0.5);
                                    `);
                  if (pEl) {
                    pEl.style.color = `rgba(var(--color-${colorValue}-rgb), 0.7)`;
                  }
                }
                const titleField = this.plugin.settings.noteTitleField || "title";
                const titleValue = metadata == null ? void 0 : metadata[titleField];
                if (titleValue) {
                  if (titleEl) {
                    titleEl.textContent = titleValue;
                  }
                }
                const displayValue = metadata == null ? void 0 : metadata.display;
                if (displayValue === "minimized") {
                  if (pEl) {
                    pEl.remove();
                  }
                  const imageAreaEl = fileEl.querySelector(".ge-image-area");
                  if (imageAreaEl) {
                    imageAreaEl.remove();
                  }
                  fileEl.style.height = "100%";
                }
              }
              imageUrl = await findFirstImageInNote(this.app, content);
            } else {
              if (!this.minMode) {
                contentArea.createEl("p", { text: file.extension.toUpperCase() });
              }
            }
            if (file.extension === "md" && this.plugin.settings.showNoteTags && !this.minMode) {
              const fileCache = this.app.metadataCache.getFileCache(file);
              const displaySetting = (_b2 = fileCache == null ? void 0 : fileCache.frontmatter) == null ? void 0 : _b2.display;
              if (displaySetting !== "minimized") {
                const allTags = /* @__PURE__ */ new Set();
                let frontmatterTags = ((_c2 = fileCache == null ? void 0 : fileCache.frontmatter) == null ? void 0 : _c2.tags) || [];
                if (typeof frontmatterTags === "string") {
                  frontmatterTags.split(/[,\s]+/).filter((tag) => tag.trim() !== "").forEach((tag) => allTags.add(tag));
                } else if (Array.isArray(frontmatterTags)) {
                  frontmatterTags.forEach((tag) => {
                    if (typeof tag === "string") {
                      if (tag.includes(" ")) {
                        tag.split(/\s+/).filter((subTag) => subTag.trim() !== "").forEach((subTag) => allTags.add(subTag));
                      } else {
                        allTags.add(tag);
                      }
                    }
                  });
                }
                const cacheTags = (fileCache == null ? void 0 : fileCache.tags) || [];
                cacheTags.forEach((tagObj) => {
                  const tag = tagObj.tag.startsWith("#") ? tagObj.tag.substring(1) : tagObj.tag;
                  allTags.add(tag);
                });
                if (allTags.size > 0) {
                  const tagsContainer = contentArea.createDiv("ge-tags-container");
                  const containerWidth = tagsContainer.getBoundingClientRect().width;
                  const tagWidth = 70;
                  const maxTags = Math.floor(containerWidth / tagWidth);
                  const displayTags = Array.from(allTags).slice(0, maxTags);
                  displayTags.forEach((tag) => {
                    const tagEl = tagsContainer.createEl("span", {
                      cls: "ge-tag",
                      text: tag.startsWith("#") ? tag : `#${tag}`
                    });
                    tagEl.addEventListener("contextmenu", (e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      const tagText = tag.startsWith("#") ? tag : `#${tag}`;
                      const menu = new import_obsidian12.Menu();
                      if (!this.searchQuery.includes(tagText)) {
                        menu.addItem(
                          (item) => item.setTitle(t("add_tag_to_search")).setIcon("circle-plus").onClick(() => {
                            this.searchQuery += ` ${tagText}`;
                            this.render(true);
                            return false;
                          })
                        );
                      }
                      if (this.searchQuery.includes(tagText)) {
                        menu.addItem(
                          (item) => item.setTitle(t("remove_tag_from_search")).setIcon("circle-minus").onClick(() => {
                            this.searchQuery = this.searchQuery.replace(tagText, "");
                            this.render(true);
                            return false;
                          })
                        );
                      }
                      menu.showAtPosition({
                        x: e.clientX,
                        y: e.clientY
                      });
                    });
                    tagEl.addEventListener("click", (e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      const tagText = tag.startsWith("#") ? tag : `#${tag}`;
                      if (this.searchQuery === tagText) {
                        return;
                      }
                      this.searchQuery = tagText;
                      this.render(true);
                      return false;
                    });
                  });
                }
              }
            }
            contentArea.setAttribute("data-loaded", "true");
          }
          if (!this.minMode) {
            const imageArea = fileEl.querySelector(".ge-image-area");
            if (imageArea && !imageArea.hasAttribute("data-loaded")) {
              if (isImageFile(file)) {
                const img = imageArea.createEl("img");
                img.src = this.app.vault.getResourcePath(file);
                imageArea.setAttribute("data-loaded", "true");
              } else if (isVideoFile(file)) {
                if (this.plugin.settings.showVideoThumbnails) {
                  const video = imageArea.createEl("video");
                  video.src = this.app.vault.getResourcePath(file);
                } else {
                  const videoThumb = imageArea.createDiv("ge-video-thumbnail");
                  (0, import_obsidian12.setIcon)(videoThumb, "play-circle");
                }
                imageArea.setAttribute("data-loaded", "true");
              } else if (file.extension === "md") {
                if (imageUrl) {
                  const img = imageArea.createEl("img");
                  img.src = imageUrl;
                  imageArea.setAttribute("data-loaded", "true");
                } else {
                  imageArea.remove();
                }
              } else {
                imageArea.remove();
              }
            }
          }
          observer2.unobserve(fileEl);
        }
      });
    }, {
      root: container,
      rootMargin: "50px",
      // 預先載入視窗外 50px 的內容
      threshold: 0.1
    });
    if (files.length > 0) {
      const dateDividerMode = this.plugin.settings.dateDividerMode || "none";
      const sortType = this.folderSortType ? this.folderSortType : this.sortType;
      const shouldShowDateDividers = dateDividerMode !== "none" && (sortType.startsWith("mtime-") || sortType.startsWith("ctime-")) && this.sourceMode !== "random-note" && this.sourceMode !== "bookmarks" && !this.sourceMode.startsWith("custom-");
      let lastDateString = "";
      let pinDividerAdded = false;
      let blankDividerAdded = false;
      for (const file of files) {
        if (!pinDividerAdded && this.pinnedList.includes(file.name)) {
          const pinDivider = container.createDiv("ge-pin-divider");
          pinDivider.textContent = `\u{1F4CC} ${t("pinned")}`;
          pinDividerAdded = true;
          if (import_obsidian12.Platform.isIosApp) {
            pinDivider.style.width = "calc(100% - 16px)";
          }
        }
        if (pinDividerAdded && !blankDividerAdded && !this.pinnedList.includes(file.name)) {
          container.createDiv("ge-break");
          blankDividerAdded = true;
        }
        if (shouldShowDateDividers && !this.pinnedList.includes(file.name)) {
          let timestamp = 0;
          if (sortType.startsWith("mtime-") || sortType.startsWith("ctime-")) {
            const isModifiedTime = sortType.startsWith("mtime-") || this.sourceMode === "recent-files";
            let frontMatterDate = null;
            if (file.extension === "md") {
              const metadata = this.app.metadataCache.getFileCache(file);
              if (metadata == null ? void 0 : metadata.frontmatter) {
                const fieldName = isModifiedTime ? this.plugin.settings.modifiedDateField : this.plugin.settings.createdDateField;
                if (fieldName && metadata.frontmatter[fieldName]) {
                  const dateStr = metadata.frontmatter[fieldName];
                  const date = new Date(dateStr);
                  if (!isNaN(date.getTime())) {
                    frontMatterDate = date;
                  }
                }
              }
            }
            if (frontMatterDate) {
              timestamp = frontMatterDate.getTime();
            } else {
              timestamp = isModifiedTime ? file.stat.mtime : file.stat.ctime;
            }
          }
          const fileDate = new Date(timestamp);
          let currentDateString = "";
          if (dateDividerMode === "year") {
            currentDateString = fileDate.getFullYear().toString();
          } else if (dateDividerMode === "month") {
            const year = fileDate.getFullYear();
            const month = fileDate.getMonth() + 1;
            currentDateString = `${year}-${month.toString().padStart(2, "0")}`;
          } else {
            currentDateString = fileDate.toLocaleDateString();
          }
          if (currentDateString !== lastDateString) {
            lastDateString = currentDateString;
            const dateDivider = container.createDiv("ge-date-divider");
            dateDivider.textContent = currentDateString;
            if (import_obsidian12.Platform.isIosApp) {
              dateDivider.style.width = "calc(100% - 16px)";
            }
          }
        }
        const fileEl = container.createDiv("ge-grid-item");
        this.gridItems.push(fileEl);
        fileEl.dataset.filePath = file.path;
        const parentPath = ((_c = file.parent) == null ? void 0 : _c.path) || "";
        const parentName = parentPath.split("/").pop() || "";
        const fileName = file.basename;
        if (parentName === fileName) {
          fileEl.addClass("ge-foldernote");
        }
        const contentArea = fileEl.createDiv("ge-content-area");
        const titleContainer = contentArea.createDiv("ge-title-container");
        const extension = file.extension.toLowerCase();
        if (isImageFile(file)) {
          const iconContainer = titleContainer.createDiv("ge-icon-container ge-img");
          (0, import_obsidian12.setIcon)(iconContainer, "image");
        } else if (isVideoFile(file)) {
          const iconContainer = titleContainer.createDiv("ge-icon-container ge-video");
          (0, import_obsidian12.setIcon)(iconContainer, "play-circle");
        } else if (isAudioFile(file)) {
          const iconContainer = titleContainer.createDiv("ge-icon-container ge-audio");
          (0, import_obsidian12.setIcon)(iconContainer, "music");
        } else if (extension === "pdf") {
          const iconContainer = titleContainer.createDiv("ge-icon-container ge-pdf");
          (0, import_obsidian12.setIcon)(iconContainer, "paperclip");
        } else if (extension === "canvas") {
          const iconContainer = titleContainer.createDiv("ge-icon-container ge-canvas");
          (0, import_obsidian12.setIcon)(iconContainer, "layout-dashboard");
        } else if (extension === "base") {
          const iconContainer = titleContainer.createDiv("ge-icon-container ge-base");
          (0, import_obsidian12.setIcon)(iconContainer, "layout-list");
        } else if (extension === "md" || extension === "txt") {
          const iconContainer = titleContainer.createDiv("ge-icon-container");
          (0, import_obsidian12.setIcon)(iconContainer, "file-text");
        } else {
          const iconContainer = titleContainer.createDiv("ge-icon-container");
          (0, import_obsidian12.setIcon)(iconContainer, "file");
        }
        const shouldShowExtension = this.minMode && file.extension.toLowerCase() !== "md";
        const displayText = shouldShowExtension ? `${file.basename}.${file.extension}` : file.basename;
        const titleEl = titleContainer.createEl("span", { cls: "ge-title", text: displayText });
        titleEl.setAttribute("title", displayText);
        if (!this.minMode) {
          fileEl.createDiv("ge-image-area");
        }
        observer.observe(fileEl);
        fileEl.addEventListener("click", (event) => {
          const index = this.gridItems.indexOf(fileEl);
          if (index < 0)
            return;
          if (event.ctrlKey || event.metaKey) {
            if (this.selectedItemIndex !== -1) {
              this.selectItem(index, true);
              this.hasKeyboardFocus = true;
            } else {
              if (isMediaFile(file)) {
                if (isAudioFile(file)) {
                  FloatingAudioPlayer.open(this.app, file);
                } else {
                  this.openMediaFile(file, files);
                }
              } else {
                this.app.workspace.getLeaf(true).openFile(file);
              }
            }
            event.preventDefault();
            return;
          } else if (event.shiftKey) {
            this.handleRangeSelection(index);
            this.hasKeyboardFocus = true;
            event.preventDefault();
            return;
          } else {
            this.selectItem(index);
            this.hasKeyboardFocus = true;
            if (isMediaFile(file)) {
              if (isAudioFile(file)) {
                FloatingAudioPlayer.open(this.app, file);
              } else {
                this.openMediaFile(file, files);
              }
            } else {
              this.app.workspace.getLeaf().openFile(file);
            }
          }
        });
        fileEl.addEventListener("mousedown", (event) => {
          if (event.button === 1) {
            event.preventDefault();
          }
        });
        fileEl.addEventListener("mouseup", (event) => {
          if (event.button === 1) {
            event.preventDefault();
            if (!isMediaFile(file)) {
              this.app.workspace.getLeaf(true).openFile(file);
            }
          }
        });
        if (import_obsidian12.Platform.isDesktop) {
          fileEl.setAttribute("draggable", "true");
          fileEl.addEventListener("dragstart", (event) => {
            var _a2, _b2, _c2, _d;
            const index = this.gridItems.indexOf(fileEl);
            if (index >= 0) {
              if (!this.selectedItems.has(index)) {
                this.selectItem(index);
              }
            }
            const selectedFiles = this.getSelectedFiles();
            if (selectedFiles.length > 1) {
              const fileList = selectedFiles.map((f) => {
                const isMedia = isMediaFile(f);
                return isMedia ? `![[${f.path}]]` : `[[${f.path}]]`;
              }).join("\n");
              (_a2 = event.dataTransfer) == null ? void 0 : _a2.setData("text/plain", fileList);
              (_b2 = event.dataTransfer) == null ? void 0 : _b2.setData(
                "application/obsidian-grid-explorer-files",
                JSON.stringify(selectedFiles.map((f) => f.path))
              );
            } else {
              const isMedia = isMediaFile(file);
              const mdLink = isMedia ? `![[${file.path}]]` : `[[${file.path}]]`;
              (_c2 = event.dataTransfer) == null ? void 0 : _c2.setData("text/plain", mdLink);
              (_d = event.dataTransfer) == null ? void 0 : _d.setData(
                "application/obsidian-grid-explorer-files",
                JSON.stringify([file.path])
              );
            }
            event.dataTransfer.effectAllowed = "all";
            fileEl.addClass("ge-dragging");
          });
          fileEl.addEventListener("dragend", () => {
            fileEl.removeClass("ge-dragging");
          });
        }
        fileEl.addEventListener("contextmenu", (event) => {
          event.preventDefault();
          const menu = new import_obsidian12.Menu();
          const index = this.gridItems.indexOf(fileEl);
          if (index >= 0) {
            if (!this.selectedItems.has(index)) {
              this.selectItem(index);
            }
          }
          const selectedFiles = this.getSelectedFiles();
          if (selectedFiles.length > 1) {
            this.app.workspace.trigger("files-menu", menu, selectedFiles);
            const allMdFiles = selectedFiles.every((file2) => file2.extension === "md");
            if (allMdFiles) {
              menu.addItem((item) => {
                item.setTitle(t("set_note_attribute")).setIcon("palette").onClick(() => {
                  showNoteSettingsModal(this.app, this.plugin, selectedFiles);
                });
              });
            }
          } else {
            this.app.workspace.trigger("file-menu", menu, file);
          }
          menu.addItem((item) => {
            var _a2, _b2;
            (_b2 = (_a2 = item.setTitle(t("open_in_new_tab")).setIcon("external-link")).setSection) == null ? void 0 : _b2.call(_a2, "open").onClick(() => {
              if (selectedFiles.length > 1) {
                const documentFiles = selectedFiles.filter((f) => isDocumentFile(f));
                for (const docFile of documentFiles) {
                  this.app.workspace.getLeaf(true).openFile(docFile);
                }
              } else {
                this.app.workspace.getLeaf(true).openFile(file);
              }
            });
          });
          menu.addItem((item) => {
            item.setWarning(true);
            item.setTitle(t("delete_note")).setIcon("trash").onClick(async () => {
              if (selectedFiles.length > 1) {
                for (const f of selectedFiles) {
                  await this.app.fileManager.trashFile(f);
                }
              } else {
                await this.app.fileManager.trashFile(file);
              }
              this.clearSelection();
            });
          });
          menu.showAtMouseEvent(event);
        });
      }
    }
    if (import_obsidian12.Platform.isDesktop) {
      const folderItems = this.containerEl.querySelectorAll(".ge-folder-item");
      folderItems.forEach((folderItem) => {
        folderItem.addEventListener("dragover", (event) => {
          event.preventDefault();
          event.dataTransfer.dropEffect = "move";
          folderItem.addClass("ge-dragover");
        });
        folderItem.addEventListener("dragleave", () => {
          folderItem.removeClass("ge-dragover");
        });
        folderItem.addEventListener("drop", async (event) => {
          var _a2, _b2;
          event.preventDefault();
          folderItem.removeClass("ge-dragover");
          const filesDataString = (_a2 = event.dataTransfer) == null ? void 0 : _a2.getData("application/obsidian-grid-explorer-files");
          if (filesDataString) {
            try {
              const filePaths = JSON.parse(filesDataString);
              const folderPath2 = folderItem.dataset.folderPath;
              if (!folderPath2)
                return;
              const folder2 = this.app.vault.getAbstractFileByPath(folderPath2);
              if (!(folder2 instanceof import_obsidian12.TFolder))
                return;
              for (const path of filePaths) {
                const file2 = this.app.vault.getAbstractFileByPath(path);
                if (file2 instanceof import_obsidian12.TFile) {
                  try {
                    const newPath = (0, import_obsidian12.normalizePath)(`${folderPath2}/${file2.name}`);
                    await this.app.fileManager.renameFile(file2, newPath);
                  } catch (error) {
                    console.error(`An error occurred while moving the file ${file2.path}:`, error);
                  }
                }
              }
              return;
            } catch (error) {
              console.error("Error parsing dragged files data:", error);
            }
          }
          const filePath = (_b2 = event.dataTransfer) == null ? void 0 : _b2.getData("text/plain");
          if (!filePath)
            return;
          const cleanedFilePath = filePath.replace(/!?\[\[(.*?)\]\]/, "$1");
          const folderPath = folderItem.dataset.folderPath;
          if (!folderPath)
            return;
          const file = this.app.vault.getAbstractFileByPath(cleanedFilePath);
          const folder = this.app.vault.getAbstractFileByPath(folderPath);
          if (file instanceof import_obsidian12.TFile && folder instanceof import_obsidian12.TFolder) {
            try {
              const newPath = (0, import_obsidian12.normalizePath)(`${folderPath}/${file.name}`);
              await this.app.fileManager.renameFile(file, newPath);
            } catch (error) {
              console.error("An error occurred while moving the file:", error);
            }
          }
        });
      });
    }
    if (this.plugin.statusBarItem) {
      this.plugin.statusBarItem.setText(`${files.length} ${t("files")}`);
    }
  }
  // 處理鍵盤導航
  handleKeyDown(event) {
    if (this.gridItems.length === 0)
      return;
    if (document.querySelector(".modal-container"))
      return;
    let newIndex = this.selectedItemIndex;
    if (this.selectedItemIndex === -1 && ["ArrowRight", "ArrowLeft", "ArrowDown", "ArrowUp", "Home", "End"].includes(event.key)) {
      this.hasKeyboardFocus = true;
      this.selectItem(0);
      event.preventDefault();
      return;
    }
    switch (event.key) {
      case "ArrowRight":
        if (event.altKey) {
          if (this.selectedItemIndex >= 0 && this.selectedItemIndex < this.gridItems.length) {
            this.gridItems[this.selectedItemIndex].click();
          }
        }
        newIndex = Math.min(this.gridItems.length - 1, this.selectedItemIndex + 1);
        this.hasKeyboardFocus = true;
        event.preventDefault();
        break;
      case "ArrowLeft":
        if (event.altKey) {
          if (this.sourceMode === "folder" && this.sourcePath && this.sourcePath !== "/") {
            const parentPath = this.sourcePath.split("/").slice(0, -1).join("/") || "/";
            this.setSource("folder", parentPath, true);
            this.clearSelection();
            event.preventDefault();
          }
          break;
        }
        newIndex = Math.max(0, this.selectedItemIndex - 1);
        this.hasKeyboardFocus = true;
        event.preventDefault();
        break;
      case "ArrowDown":
        if (this.selectedItemIndex >= 0) {
          const currentItem = this.gridItems[this.selectedItemIndex];
          const currentRect = currentItem.getBoundingClientRect();
          const currentCenterX = currentRect.left + currentRect.width / 2;
          const currentBottom = currentRect.bottom;
          let closestItem = -1;
          let minDistance = Number.MAX_VALUE;
          let minVerticalDistance = Number.MAX_VALUE;
          for (let i = 0; i < this.gridItems.length; i++) {
            if (i === this.selectedItemIndex)
              continue;
            const itemRect = this.gridItems[i].getBoundingClientRect();
            const itemCenterX = itemRect.left + itemRect.width / 2;
            const itemTop = itemRect.top;
            if (itemTop <= currentBottom)
              continue;
            const horizontalDistance = Math.abs(itemCenterX - currentCenterX);
            const verticalDistance = itemTop - currentBottom;
            if (verticalDistance < minVerticalDistance || verticalDistance === minVerticalDistance && horizontalDistance < minDistance) {
              minVerticalDistance = verticalDistance;
              minDistance = horizontalDistance;
              closestItem = i;
            }
          }
          if (closestItem !== -1) {
            newIndex = closestItem;
          } else {
            newIndex = this.gridItems.length - 1;
          }
        } else {
          newIndex = 0;
        }
        this.hasKeyboardFocus = true;
        event.preventDefault();
        break;
      case "ArrowUp":
        if (event.altKey) {
          if (this.sourceMode === "folder" && this.sourcePath && this.sourcePath !== "/") {
            const parentPath = this.sourcePath.split("/").slice(0, -1).join("/") || "/";
            this.setSource("folder", parentPath, true);
            this.clearSelection();
            event.preventDefault();
          }
          break;
        }
        if (this.selectedItemIndex >= 0) {
          const currentItem = this.gridItems[this.selectedItemIndex];
          const currentRect = currentItem.getBoundingClientRect();
          const currentCenterX = currentRect.left + currentRect.width / 2;
          const currentTop = currentRect.top;
          let closestItem = -1;
          let minDistance = Number.MAX_VALUE;
          let minVerticalDistance = Number.MAX_VALUE;
          for (let i = 0; i < this.gridItems.length; i++) {
            if (i === this.selectedItemIndex)
              continue;
            const itemRect = this.gridItems[i].getBoundingClientRect();
            const itemCenterX = itemRect.left + itemRect.width / 2;
            const itemBottom = itemRect.bottom;
            if (itemBottom >= currentTop)
              continue;
            const horizontalDistance = Math.abs(itemCenterX - currentCenterX);
            const verticalDistance = currentTop - itemBottom;
            if (verticalDistance < minVerticalDistance || verticalDistance === minVerticalDistance && horizontalDistance < minDistance) {
              minVerticalDistance = verticalDistance;
              minDistance = horizontalDistance;
              closestItem = i;
            }
          }
          if (closestItem !== -1) {
            newIndex = closestItem;
          } else {
            newIndex = 0;
          }
        } else {
          newIndex = 0;
        }
        this.hasKeyboardFocus = true;
        event.preventDefault();
        break;
      case "Home":
        newIndex = 0;
        this.hasKeyboardFocus = true;
        event.preventDefault();
        break;
      case "End":
        newIndex = this.gridItems.length - 1;
        this.hasKeyboardFocus = true;
        event.preventDefault();
        break;
      case "Enter":
        if (this.selectedItemIndex >= 0 && this.selectedItemIndex < this.gridItems.length) {
          this.gridItems[this.selectedItemIndex].click();
        }
        this.clearSelection();
        event.preventDefault();
        break;
      case "Backspace":
        if (this.sourceMode === "folder" && this.sourcePath && this.sourcePath !== "/") {
          const parentPath = this.sourcePath.split("/").slice(0, -1).join("/") || "/";
          this.setSource("folder", parentPath, true);
          this.clearSelection();
          event.preventDefault();
        }
        break;
      case "Escape":
        if (this.selectedItemIndex >= 0) {
          this.hasKeyboardFocus = false;
          this.clearSelection();
          event.preventDefault();
        }
        break;
    }
    if (newIndex !== this.selectedItemIndex) {
      this.selectItem(newIndex);
    }
  }
  // 清除選中狀態
  clearSelection() {
    this.gridItems.forEach((item) => {
      item.removeClass("ge-selected-item");
    });
    this.selectedItemIndex = -1;
    this.selectedItems.clear();
  }
  // 選中指定索引的項目
  selectItem(index, multiSelect = false) {
    if (!multiSelect) {
      this.gridItems.forEach((item) => {
        item.removeClass("ge-selected-item");
      });
      this.selectedItems.clear();
    }
    if (index >= 0 && index < this.gridItems.length) {
      this.selectedItemIndex = index;
      const selectedItem = this.gridItems[index];
      if (multiSelect && this.selectedItems.has(index)) {
        selectedItem.removeClass("ge-selected-item");
        this.selectedItems.delete(index);
        if (this.selectedItems.size === 0) {
          this.selectedItemIndex = -1;
        } else {
          this.selectedItemIndex = Array.from(this.selectedItems).pop() || -1;
        }
      } else {
        selectedItem.addClass("ge-selected-item");
        this.selectedItems.add(index);
      }
      selectedItem.scrollIntoView({
        behavior: "smooth",
        block: "nearest"
      });
    }
  }
  // 處理範圍選擇（Shift 鍵）
  handleRangeSelection(index) {
    if (this.selectedItemIndex === -1) {
      this.selectItem(index);
      return;
    }
    const startIndex = Math.min(this.selectedItemIndex, index);
    const endIndex = Math.max(this.selectedItemIndex, index);
    this.gridItems.forEach((item) => {
      item.removeClass("ge-selected-item");
    });
    this.selectedItems.clear();
    for (let i = startIndex; i <= endIndex; i++) {
      this.gridItems[i].addClass("ge-selected-item");
      this.selectedItems.add(i);
    }
    this.selectedItemIndex = index;
  }
  // 獲取所有選中項目的檔案
  getSelectedFiles() {
    const files = [];
    this.selectedItems.forEach((index) => {
      const fileEl = this.gridItems[index];
      const filePath = fileEl.dataset.filePath;
      if (filePath) {
        const file = this.app.vault.getAbstractFileByPath(filePath);
        if (file instanceof import_obsidian12.TFile) {
          files.push(file);
        }
      }
    });
    return files;
  }
  // 開啟媒體檔案
  openMediaFile(file, mediaFiles) {
    const getMediaFilesPromise = mediaFiles ? Promise.resolve(mediaFiles.filter((f) => isMediaFile(f))) : getFiles(this, this.randomNoteIncludeMedia).then((allFiles) => allFiles.filter((f) => isMediaFile(f)));
    getMediaFilesPromise.then((filteredMediaFiles) => {
      const currentIndex = filteredMediaFiles.findIndex((f) => f.path === file.path);
      if (currentIndex === -1)
        return;
      const mediaModal = new MediaModal(this.app, file, filteredMediaFiles, this);
      mediaModal.open();
    });
  }
  // 保存視圖狀態
  getState() {
    return {
      type: "grid-view",
      state: {
        sourceMode: this.sourceMode,
        sourcePath: this.sourcePath,
        sortType: this.sortType,
        folderSortType: this.folderSortType,
        searchQuery: this.searchQuery,
        searchAllFiles: this.searchAllFiles,
        searchMediaFiles: this.searchMediaFiles,
        randomNoteIncludeMedia: this.randomNoteIncludeMedia,
        minMode: this.minMode,
        showIgnoredFolders: this.showIgnoredFolders
      }
    };
  }
  // 讀取視圖狀態
  async setState(state) {
    var _a, _b, _c, _d, _e;
    if (state.state) {
      this.sourceMode = state.state.sourceMode || "";
      this.sourcePath = state.state.sourcePath || null;
      this.sortType = state.state.sortType || "mtime-desc";
      this.folderSortType = state.state.folderSortType || "";
      this.searchQuery = state.state.searchQuery || "";
      this.searchAllFiles = (_a = state.state.searchAllFiles) != null ? _a : true;
      this.searchMediaFiles = (_b = state.state.searchMediaFiles) != null ? _b : false;
      this.randomNoteIncludeMedia = (_c = state.state.randomNoteIncludeMedia) != null ? _c : false;
      this.minMode = (_d = state.state.minMode) != null ? _d : false;
      this.showIgnoredFolders = (_e = state.state.showIgnoredFolders) != null ? _e : false;
      this.render();
    }
  }
};

// src/settings.ts
var import_obsidian14 = require("obsidian");

// src/CustomModeModal.ts
var import_obsidian13 = require("obsidian");
var CustomModeModal = class extends import_obsidian13.Modal {
  constructor(app, plugin, mode, onSubmit) {
    super(app);
    this.plugin = plugin;
    this.mode = mode;
    this.onSubmit = onSubmit;
  }
  onOpen() {
    var _a;
    const { contentEl } = this;
    contentEl.empty();
    contentEl.createEl("h2", { text: this.mode ? t("edit_custom_mode") : t("add_custom_mode") });
    let icon = this.mode ? this.mode.icon : "\u{1F9E9}";
    let displayName = this.mode ? this.mode.displayName : "";
    let dataviewCode = this.mode ? this.mode.dataviewCode : "";
    let enabled = this.mode ? (_a = this.mode.enabled) != null ? _a : true : true;
    new import_obsidian13.Setting(contentEl).setName(t("custom_mode_icon")).setDesc(t("custom_mode_icon_desc")).addText((text) => {
      text.setValue(icon).onChange((value) => {
        icon = value || "\u{1F9E9}";
      });
    });
    new import_obsidian13.Setting(contentEl).setName(t("custom_mode_display_name")).setDesc(t("custom_mode_display_name_desc")).addText((text) => {
      text.setValue(displayName).onChange((value) => {
        displayName = value;
      });
    });
    new import_obsidian13.Setting(contentEl).setName(t("custom_mode_dataview_code")).setDesc(t("custom_mode_dataview_code_desc")).addTextArea((text) => {
      text.setValue(dataviewCode).onChange((value) => {
        dataviewCode = value;
      });
      text.inputEl.setAttr("rows", 10);
      text.inputEl.style.width = "100%";
    });
    new import_obsidian13.Setting(contentEl).addButton((button) => {
      button.setButtonText(t("save")).setCta().onClick(() => {
        if (!displayName.trim()) {
          new import_obsidian13.Notice(t("display_name_cannot_be_empty"));
          return;
        }
        const internalName = this.mode ? this.mode.internalName : `custom-${Date.now()}`;
        this.onSubmit({
          internalName,
          icon,
          displayName,
          dataviewCode,
          enabled
        });
        this.close();
      });
    });
  }
  onClose() {
    const { contentEl } = this;
    contentEl.empty();
  }
};

// src/settings.ts
var DEFAULT_SETTINGS = {
  ignoredFolders: [],
  ignoredFolderPatterns: [],
  // 預設以字串忽略的資料夾模式
  defaultSortType: "mtime-desc",
  // 預設排序模式：修改時間倒序
  gridItemWidth: 300,
  // 網格項目寬度，預設 300
  gridItemHeight: 0,
  // 網格項目高度，預設 0
  imageAreaWidth: 100,
  // 圖片區域寬度，預設 100
  imageAreaHeight: 100,
  // 圖片區域高度，預設 100
  titleFontSize: 1,
  // 筆記標題的字型大小，預設 1.0
  summaryLength: 100,
  // 筆記摘要的字數，預設 100
  enableFileWatcher: true,
  // 預設啟用檔案監控
  showMediaFiles: true,
  // 預設顯示圖片和影片
  showVideoThumbnails: true,
  // 預設顯示影片縮圖
  defaultOpenLocation: "tab",
  // 預設開啟位置：新分頁
  reuseExistingLeaf: false,
  // 預設不重用現有的網格視圖
  showBookmarksMode: true,
  // 預設顯示書籤模式
  showSearchMode: true,
  // 預設顯示搜尋結果模式
  showBacklinksMode: true,
  // 預設顯示反向連結模式
  showOutgoinglinksMode: false,
  // 預設不顯示外部連結模式
  showAllFilesMode: false,
  // 預設不顯示所有檔案模式
  showRandomNoteMode: true,
  // 預設顯示隨機筆記模式
  showRecentFilesMode: true,
  // 預設顯示最近筆記模式
  showTasksMode: false,
  // 預設不顯示任務模式
  recentFilesCount: 30,
  // 預設最近筆記模式顯示的筆數
  randomNoteCount: 10,
  // 預設隨機筆記模式顯示的筆數
  customFolderIcon: "\u{1F4C1}",
  // 自訂資料夾圖示
  customDocumentExtensions: "",
  // 自訂文件副檔名（用逗號分隔）
  recentSources: [],
  // 預設最近的瀏覽記錄
  noteTitleField: "title",
  // 筆記標題的欄位名稱
  noteSummaryField: "summary",
  // 筆記摘要的欄位名稱
  modifiedDateField: "",
  // 修改時間的欄位名稱
  createdDateField: "",
  // 建立時間的欄位名稱
  showNoteTags: false,
  // 預設不顯示筆記標籤
  dateDividerMode: "none",
  // 預設不使用日期分隔器
  showCodeBlocksInSummary: false,
  // 預設不在摘要中顯示程式碼區塊
  folderNoteDisplaySettings: "default",
  // 預設不處理資料夾筆記
  interceptAllTagClicks: false,
  // 預設不攔截所有tag點擊事件
  customModes: [
    {
      internalName: "custom-1750837329297",
      icon: "\u{1F9E9}",
      displayName: "My Books (Sample)",
      dataviewCode: 'return dv.pages("#Book");'
    }
  ],
  // 自訂模式
  quickAccessCommandPath: "",
  // Path used by "Open quick access folder" command
  useQuickAccessAsNewTabMode: "default",
  quickAccessModeType: "all-files"
  // Default quick access view type
};
var FolderSuggest = class extends import_obsidian14.AbstractInputSuggest {
  constructor(app, inputEl) {
    super(app, inputEl);
    this.inputEl = inputEl;
  }
  getSuggestions(inputStr) {
    const lowerCaseInputStr = inputStr.toLowerCase();
    const allFolders = this.app.vault.getAllFolders();
    const suggestions = allFolders.map((folder) => folder.path).filter((path) => path.toLowerCase().includes(lowerCaseInputStr)).sort((a, b) => a.localeCompare(b));
    if ("/".includes(lowerCaseInputStr)) {
      if (!suggestions.includes("/")) {
        suggestions.unshift("/");
      }
    }
    return suggestions;
  }
  renderSuggestion(suggestion, el) {
    el.setText(suggestion);
  }
  selectSuggestion(suggestion) {
    this.inputEl.value = suggestion;
    this.inputEl.trigger("input");
    this.close();
  }
};
var IgnoredFolderSuggest = class extends import_obsidian14.AbstractInputSuggest {
  constructor(app, inputEl, plugin, settingTab) {
    super(app, inputEl);
    this.plugin = plugin;
    this.settingTab = settingTab;
    this.inputEl = inputEl;
  }
  getSuggestions(inputStr) {
    const lowerCaseInputStr = inputStr.toLowerCase();
    const folders = this.app.vault.getAllFolders();
    return folders.map((folder) => folder.path).filter((path) => {
      if (path === "/")
        return false;
      const isIgnored = this.plugin.settings.ignoredFolders.some(
        (ignoredPath) => path === ignoredPath || path.startsWith(ignoredPath + "/")
      );
      return !isIgnored && path.toLowerCase().includes(lowerCaseInputStr);
    }).sort((a, b) => a.localeCompare(b));
  }
  renderSuggestion(suggestion, el) {
    el.setText(suggestion);
  }
  async selectSuggestion(suggestion) {
    if (suggestion && !this.plugin.settings.ignoredFolders.includes(suggestion)) {
      this.plugin.settings.ignoredFolders.push(suggestion);
      await this.plugin.saveSettings();
      this.inputEl.value = "";
      this.settingTab.display();
    }
    this.close();
  }
};
var GridExplorerSettingTab = class extends import_obsidian14.PluginSettingTab {
  constructor(app, plugin) {
    super(app, plugin);
    this.plugin = plugin;
  }
  display() {
    const { containerEl } = this;
    containerEl.empty();
    containerEl.createEl("h3", { text: t("custom_mode_settings") });
    const customModesContainer = containerEl.createDiv();
    this.plugin.settings.customModes.forEach((mode, index) => {
      const setting = new import_obsidian14.Setting(customModesContainer).setName(`${mode.icon} ${mode.displayName}`).addToggle((toggle) => {
        var _a;
        toggle.setValue((_a = mode.enabled) != null ? _a : true).onChange(async (value) => {
          mode.enabled = value;
          await this.plugin.saveSettings();
        });
      });
      setting.settingEl.setAttr("draggable", "true");
      setting.settingEl.addEventListener("dragstart", (event) => {
        if (event.dataTransfer) {
          event.dataTransfer.setData("text/plain", index.toString());
          event.dataTransfer.effectAllowed = "move";
        }
      });
      setting.settingEl.addEventListener("dragover", (event) => {
        event.preventDefault();
        if (event.dataTransfer) {
          event.dataTransfer.dropEffect = "move";
        }
      });
      setting.settingEl.addEventListener("drop", async (event) => {
        event.preventDefault();
        if (!event.dataTransfer)
          return;
        const fromIndexStr = event.dataTransfer.getData("text/plain");
        if (!fromIndexStr)
          return;
        const fromIndex = parseInt(fromIndexStr);
        const toIndex = index;
        if (fromIndex === toIndex)
          return;
        const modes = this.plugin.settings.customModes;
        const movedMode = modes.splice(fromIndex, 1)[0];
        modes.splice(toIndex, 0, movedMode);
        await this.plugin.saveSettings();
        this.display();
      });
      setting.addButton((button) => {
        button.setButtonText(t("edit")).onClick(() => {
          const modeIndex = this.plugin.settings.customModes.findIndex((m) => m.internalName === mode.internalName);
          if (modeIndex === -1)
            return;
          new CustomModeModal(this.app, this.plugin, this.plugin.settings.customModes[modeIndex], (result) => {
            this.plugin.settings.customModes[modeIndex] = result;
            this.plugin.saveSettings();
            this.display();
          }).open();
        });
      });
      setting.addButton((button) => {
        button.setButtonText(t("remove")).setWarning().onClick(() => {
          const modeIndex = this.plugin.settings.customModes.findIndex((m) => m.internalName === mode.internalName);
          if (modeIndex === -1)
            return;
          this.plugin.settings.customModes.splice(modeIndex, 1);
          this.plugin.saveSettings();
          this.display();
        });
      });
    });
    new import_obsidian14.Setting(containerEl).addButton((button) => {
      button.setButtonText(t("add_custom_mode")).setCta().setTooltip(t("add_custom_mode")).onClick(() => {
        new CustomModeModal(this.app, this.plugin, null, async (result) => {
          this.plugin.settings.customModes.push(result);
          await this.plugin.saveSettings();
          this.display();
        }).open();
      });
    }).addButton((button) => {
      button.setButtonText(t("export")).setTooltip(t("export")).onClick(() => {
        if (this.plugin.settings.customModes.length === 0) {
          new import_obsidian14.Notice(t("no_custom_modes_to_export"));
          return;
        }
        const data = JSON.stringify(this.plugin.settings.customModes, null, 2);
        const blob = new Blob([data], { type: "application/json" });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = "grid-explorer-custom-modes.json";
        a.click();
        URL.revokeObjectURL(url);
      });
    }).addButton((button) => {
      button.setButtonText(t("import")).setTooltip(t("import")).onClick(() => {
        const input2 = document.createElement("input");
        input2.type = "file";
        input2.accept = ".json";
        input2.onchange = async (e) => {
          const files = e.target.files;
          if (!files || files.length === 0) {
            return;
          }
          const file = files[0];
          const reader = new FileReader();
          reader.onload = async (e2) => {
            if (!e2.target || typeof e2.target.result !== "string") {
              new import_obsidian14.Notice(t("import_error"));
              return;
            }
            try {
              const content = e2.target.result;
              const importedModes = JSON.parse(content);
              if (Array.isArray(importedModes)) {
                const validModes = importedModes.filter((m) => m.internalName && m.displayName && m.dataviewCode);
                if (validModes.length > 0) {
                  validModes.forEach((importedMode) => {
                    const existingModeIndex = this.plugin.settings.customModes.findIndex(
                      (m) => m.internalName === importedMode.internalName
                    );
                    if (existingModeIndex !== -1) {
                      this.plugin.settings.customModes[existingModeIndex] = importedMode;
                    } else {
                      this.plugin.settings.customModes.push(importedMode);
                    }
                  });
                  await this.plugin.saveSettings();
                  this.display();
                  new import_obsidian14.Notice(t("import_success"));
                } else {
                  new import_obsidian14.Notice(t("import_error"));
                }
              } else {
                new import_obsidian14.Notice(t("import_error"));
              }
            } catch (error) {
              new import_obsidian14.Notice(t("import_error"));
              console.error("Grid Explorer: Error importing custom modes", error);
            }
          };
          reader.readAsText(file);
        };
        input2.click();
      });
    });
    containerEl.createEl("h3", { text: t("display_mode_settings") });
    new import_obsidian14.Setting(containerEl).setName(`\u{1F4D1} ${t("show_bookmarks_mode")}`).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.showBookmarksMode).onChange(async (value) => {
        this.plugin.settings.showBookmarksMode = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian14.Setting(containerEl).setName(`\u{1F50D} ${t("show_search_mode")}`).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.showSearchMode).onChange(async (value) => {
        this.plugin.settings.showSearchMode = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian14.Setting(containerEl).setName(`\u{1F517} ${t("show_backlinks_mode")}`).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.showBacklinksMode).onChange(async (value) => {
        this.plugin.settings.showBacklinksMode = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian14.Setting(containerEl).setName(`\u{1F517} ${t("show_outgoinglinks_mode")}`).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.showOutgoinglinksMode).onChange(async (value) => {
        this.plugin.settings.showOutgoinglinksMode = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian14.Setting(containerEl).setName(`\u{1F4D4} ${t("show_all_files_mode")}`).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.showAllFilesMode).onChange(async (value) => {
        this.plugin.settings.showAllFilesMode = value;
        await this.plugin.saveSettings();
      });
    });
    const recentFilesSetting = new import_obsidian14.Setting(containerEl).setName(`\u{1F4C5} ${t("show_recent_files_mode")}`);
    recentFilesSetting.addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.showRecentFilesMode).onChange(async (value) => {
        this.plugin.settings.showRecentFilesMode = value;
        await this.plugin.saveSettings();
      });
    });
    const recentDescEl = recentFilesSetting.descEl.createEl("div", { cls: "ge-setting-desc" });
    recentDescEl.createEl("span", { text: t("recent_files_count") });
    const recentInput = recentDescEl.createEl("input", {
      type: "number",
      value: this.plugin.settings.recentFilesCount.toString(),
      cls: "ge-setting-number-input"
    });
    recentInput.addEventListener("change", async (e) => {
      const target = e.target;
      const value = parseInt(target.value);
      if (!isNaN(value) && value > 0) {
        this.plugin.settings.recentFilesCount = value;
        await this.plugin.saveSettings(false);
      } else {
        target.value = this.plugin.settings.recentFilesCount.toString();
      }
    });
    const randomNoteSetting = new import_obsidian14.Setting(containerEl).setName(`\u{1F3B2} ${t("show_random_note_mode")}`);
    randomNoteSetting.addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.showRandomNoteMode).onChange(async (value) => {
        this.plugin.settings.showRandomNoteMode = value;
        await this.plugin.saveSettings();
      });
    });
    const descEl = randomNoteSetting.descEl.createEl("div", { cls: "ge-setting-desc" });
    descEl.createEl("span", { text: t("random_note_count") });
    const input = descEl.createEl("input", {
      type: "number",
      value: this.plugin.settings.randomNoteCount.toString(),
      cls: "ge-setting-number-input"
    });
    input.addEventListener("change", async (e) => {
      const target = e.target;
      const value = parseInt(target.value);
      if (!isNaN(value) && value > 0) {
        this.plugin.settings.randomNoteCount = value;
        await this.plugin.saveSettings(false);
      } else {
        target.value = this.plugin.settings.randomNoteCount.toString();
      }
    });
    new import_obsidian14.Setting(containerEl).setName(`\u2611\uFE0F ${t("show_tasks_mode")}`).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.showTasksMode).onChange(async (value) => {
        this.plugin.settings.showTasksMode = value;
        await this.plugin.saveSettings();
      });
    });
    containerEl.createEl("h3", { text: t("media_files_settings") });
    new import_obsidian14.Setting(containerEl).setName(t("show_media_files")).setDesc(t("show_media_files_desc")).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.showMediaFiles).onChange(async (value) => {
        this.plugin.settings.showMediaFiles = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian14.Setting(containerEl).setName(t("show_video_thumbnails")).setDesc(t("show_video_thumbnails_desc")).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.showVideoThumbnails).onChange(async (value) => {
        this.plugin.settings.showVideoThumbnails = value;
        await this.plugin.saveSettings();
      });
    });
    containerEl.createEl("h3", { text: t("grid_view_settings") });
    new import_obsidian14.Setting(containerEl).setName(t("reuse_existing_leaf")).setDesc(t("reuse_existing_leaf_desc")).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.reuseExistingLeaf).onChange(async (value) => {
        this.plugin.settings.reuseExistingLeaf = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian14.Setting(containerEl).setName(t("default_open_location")).setDesc(t("default_open_location_desc")).addDropdown((dropdown) => {
      dropdown.addOption("tab", t("open_in_new_tab")).addOption("left", t("open_in_left_sidebar")).addOption("right", t("open_in_right_sidebar")).setValue(this.plugin.settings.defaultOpenLocation).onChange(async (value) => {
        this.plugin.settings.defaultOpenLocation = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian14.Setting(containerEl).setName(t("default_sort_type")).setDesc(t("default_sort_type_desc")).addDropdown((dropdown) => {
      dropdown.addOption("name-asc", t("sort_name_asc")).addOption("name-desc", t("sort_name_desc")).addOption("mtime-desc", t("sort_mtime_desc")).addOption("mtime-asc", t("sort_mtime_asc")).addOption("ctime-desc", t("sort_ctime_desc")).addOption("ctime-asc", t("sort_ctime_asc")).addOption("random", t("sort_random")).setValue(this.plugin.settings.defaultSortType).onChange(async (value) => {
        this.plugin.settings.defaultSortType = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian14.Setting(containerEl).setName(t("note_title_field")).setDesc(t("note_title_field_desc")).addText((text) => text.setPlaceholder("title").setValue(this.plugin.settings.noteTitleField).onChange(async (value) => {
      this.plugin.settings.noteTitleField = value;
      await this.plugin.saveSettings(false);
    }));
    new import_obsidian14.Setting(containerEl).setName(t("note_summary_field")).setDesc(t("note_summary_field_desc")).addText((text) => text.setPlaceholder("summary").setValue(this.plugin.settings.noteSummaryField).onChange(async (value) => {
      this.plugin.settings.noteSummaryField = value;
      await this.plugin.saveSettings(false);
    }));
    new import_obsidian14.Setting(containerEl).setName(t("modified_date_field")).setDesc(t("modified_date_field_desc")).addText((text) => text.setPlaceholder("modified_date").setValue(this.plugin.settings.modifiedDateField).onChange(async (value) => {
      this.plugin.settings.modifiedDateField = value;
      await this.plugin.saveSettings(false);
    }));
    new import_obsidian14.Setting(containerEl).setName(t("created_date_field")).setDesc(t("created_date_field_desc")).addText((text) => text.setPlaceholder("created_date").setValue(this.plugin.settings.createdDateField).onChange(async (value) => {
      this.plugin.settings.createdDateField = value;
      await this.plugin.saveSettings(false);
    }));
    new import_obsidian14.Setting(containerEl).setName(t("date_divider_mode")).setDesc(t("date_divider_mode_desc")).addDropdown((dropdown) => {
      dropdown.addOption("none", t("date_divider_mode_none")).addOption("year", t("date_divider_mode_year")).addOption("month", t("date_divider_mode_month")).addOption("day", t("date_divider_mode_day")).setValue(this.plugin.settings.dateDividerMode).onChange(async (value) => {
        this.plugin.settings.dateDividerMode = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian14.Setting(containerEl).setName(t("enable_file_watcher")).setDesc(t("enable_file_watcher_desc")).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.enableFileWatcher).onChange(async (value) => {
        this.plugin.settings.enableFileWatcher = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian14.Setting(containerEl).setName(t("intercept_all_tag_clicks")).setDesc(t("intercept_all_tag_clicks_desc")).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.interceptAllTagClicks).onChange(async (value) => {
        this.plugin.settings.interceptAllTagClicks = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian14.Setting(containerEl).setName(t("custom_document_extensions")).setDesc(t("custom_document_extensions_desc")).addText((text) => {
      text.setPlaceholder(t("custom_document_extensions_placeholder")).setValue(this.plugin.settings.customDocumentExtensions).onChange(async (value) => {
        this.plugin.settings.customDocumentExtensions = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian14.Setting(containerEl).setName(t("custom_folder_icon")).setDesc(t("custom_folder_icon_desc")).addText((text) => {
      text.setValue(this.plugin.settings.customFolderIcon).onChange(async (value) => {
        this.plugin.settings.customFolderIcon = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian14.Setting(containerEl).setName(t("show_note_tags")).setDesc(t("show_note_tags_desc")).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.showNoteTags).onChange(async (value) => {
        this.plugin.settings.showNoteTags = value;
        await this.plugin.saveSettings();
      });
    });
    const gridItemWidthSetting = new import_obsidian14.Setting(containerEl).setName(t("grid_item_width")).setDesc(`${t("grid_item_width_desc")} (now: ${this.plugin.settings.gridItemWidth}px)`).addSlider((slider) => {
      slider.setLimits(200, 600, 10).setValue(this.plugin.settings.gridItemWidth).setDynamicTooltip().onChange(async (value) => {
        gridItemWidthSetting.setDesc(`${t("grid_item_width_desc")} (now: ${value}px)`);
        this.plugin.settings.gridItemWidth = value;
        await this.plugin.saveSettings();
      });
    });
    const gridItemHeightSetting = new import_obsidian14.Setting(containerEl).setName(t("grid_item_height")).setDesc(`${t("grid_item_height_desc")} (now: ${this.plugin.settings.gridItemHeight === 0 ? "auto" : this.plugin.settings.gridItemHeight})`).addSlider((slider) => {
      slider.setLimits(0, 600, 10).setValue(this.plugin.settings.gridItemHeight).setDynamicTooltip().onChange(async (value) => {
        gridItemHeightSetting.setDesc(`${t("grid_item_height_desc")} (now: ${value === 0 ? "auto" : value})`);
        this.plugin.settings.gridItemHeight = value;
        await this.plugin.saveSettings();
      });
    });
    const imageAreaWidthSetting = new import_obsidian14.Setting(containerEl).setName(t("image_area_width")).setDesc(`${t("image_area_width_desc")} (now: ${this.plugin.settings.imageAreaWidth}px)`).addSlider((slider) => {
      slider.setLimits(50, 300, 10).setValue(this.plugin.settings.imageAreaWidth).setDynamicTooltip().onChange(async (value) => {
        imageAreaWidthSetting.setDesc(`${t("image_area_width_desc")} (now: ${value}px)`);
        this.plugin.settings.imageAreaWidth = value;
        await this.plugin.saveSettings();
      });
    });
    const imageAreaHeightSetting = new import_obsidian14.Setting(containerEl).setName(t("image_area_height")).setDesc(`${t("image_area_height_desc")} (now: ${this.plugin.settings.imageAreaHeight}px)`).addSlider((slider) => {
      slider.setLimits(50, 300, 10).setValue(this.plugin.settings.imageAreaHeight).setDynamicTooltip().onChange(async (value) => {
        imageAreaHeightSetting.setDesc(`${t("image_area_height_desc")} (now: ${value}px)`);
        this.plugin.settings.imageAreaHeight = value;
        await this.plugin.saveSettings();
      });
    });
    const titleFontSizeSetting = new import_obsidian14.Setting(containerEl).setName(t("title_font_size")).setDesc(`${t("title_font_size_desc")} (now: ${this.plugin.settings.titleFontSize.toFixed(2)})`).addSlider((slider) => {
      slider.setLimits(0.8, 1.5, 0.05).setValue(this.plugin.settings.titleFontSize).setDynamicTooltip().onChange(async (value) => {
        titleFontSizeSetting.setDesc(`${t("title_font_size_desc")} (now: ${value.toFixed(2)})`);
        this.plugin.settings.titleFontSize = value;
        await this.plugin.saveSettings();
      });
    });
    const summaryLengthSetting = new import_obsidian14.Setting(containerEl).setName(t("summary_length")).setDesc(`${t("summary_length_desc")} (now: ${this.plugin.settings.summaryLength})`).addSlider((slider) => {
      slider.setLimits(50, 600, 25).setValue(this.plugin.settings.summaryLength).setDynamicTooltip().onChange(async (value) => {
        summaryLengthSetting.setDesc(`${t("summary_length_desc")} (now: ${value})`);
        this.plugin.settings.summaryLength = value;
        await this.plugin.saveSettings();
      });
    });
    new import_obsidian14.Setting(containerEl).setName(t("show_code_block_in_summary")).setDesc(t("show_code_block_in_summary_desc")).addToggle((toggle) => toggle.setValue(this.plugin.settings.showCodeBlocksInSummary).onChange(async (value) => {
      this.plugin.settings.showCodeBlocksInSummary = value;
      await this.plugin.saveSettings();
    }));
    containerEl.createEl("h3", { text: t("folder_note_settings") });
    new import_obsidian14.Setting(containerEl).setName(t("foldernote_display_settings")).setDesc(t("foldernote_display_settings_desc")).addDropdown((dropdown) => {
      dropdown.addOption("default", t("default")).addOption("pinned", t("pinned")).addOption("hidden", t("hidden")).setValue(this.plugin.settings.folderNoteDisplaySettings).onChange(async (value) => {
        this.plugin.settings.folderNoteDisplaySettings = value;
        await this.plugin.saveSettings();
      });
    });
    containerEl.createEl("h3", { text: t("quick_access_settings_title") });
    new import_obsidian14.Setting(containerEl).setName(t("quick_access_folder_name")).setDesc(t("quick_access_folder_desc")).addText((text) => {
      new FolderSuggest(this.app, text.inputEl);
      text.setPlaceholder(t("select_folders")).setValue(this.plugin.settings.quickAccessCommandPath).onChange(async (value) => {
        this.plugin.settings.quickAccessCommandPath = value;
        await this.plugin.saveSettings(false);
      });
    });
    new import_obsidian14.Setting(containerEl).setName(t("quick_access_mode_name")).setDesc(t("quick_access_mode_desc")).addDropdown((dropdown) => {
      dropdown.addOption("bookmarks", t("bookmarks_mode")).addOption("search", t("search_results")).addOption("backlinks", t("backlinks_mode")).addOption("outgoinglinks", t("outgoinglinks_mode")).addOption("all-files", t("all_files_mode")).addOption("recent-files", t("recent_files_mode")).addOption("random-note", t("random_note_mode")).addOption("tasks", t("tasks_mode")).setValue(this.plugin.settings.quickAccessModeType).onChange(async (value) => {
        this.plugin.settings.quickAccessModeType = value;
        await this.plugin.saveSettings(false);
      });
    });
    new import_obsidian14.Setting(containerEl).setName(t("use_quick_access_as_new_tab_view")).setDesc(t("use_quick_access_as_new_tab_view_desc")).addDropdown((dropdown) => {
      dropdown.addOption("default", t("default_new_tab")).addOption("folder", t("use_quick_access_folder")).addOption("mode", t("use_quick_access_mode")).setValue(this.plugin.settings.useQuickAccessAsNewTabMode).onChange(async (value) => {
        this.plugin.settings.useQuickAccessAsNewTabMode = value;
        await this.plugin.saveSettings(false);
      });
    });
    containerEl.createEl("h3", { text: t("ignored_folders_settings") });
    const ignoredFoldersContainer = containerEl.createDiv("ignored-folders-container");
    new import_obsidian14.Setting(containerEl).setName(t("ignored_folders")).setDesc(t("ignored_folders_desc")).setHeading();
    new import_obsidian14.Setting(ignoredFoldersContainer).setName(t("add_ignored_folder")).addText((text) => {
      new IgnoredFolderSuggest(this.app, text.inputEl, this.plugin, this);
      text.setPlaceholder(t("select_folders_to_ignore"));
    });
    const ignoredFoldersList = ignoredFoldersContainer.createDiv("ge-ignored-folders-list");
    this.renderIgnoredFoldersList(ignoredFoldersList);
    containerEl.appendChild(ignoredFoldersContainer);
    const ignoredFolderPatternsContainer = containerEl.createDiv("ignored-folder-patterns-container");
    new import_obsidian14.Setting(containerEl).setName(t("ignored_folder_patterns")).setDesc(t("ignored_folder_patterns_desc")).setHeading();
    const patternSetting = new import_obsidian14.Setting(ignoredFolderPatternsContainer).setName(t("add_ignored_folder_pattern")).addText((text) => {
      text.setPlaceholder(t("ignored_folder_pattern_placeholder")).onChange(() => {
      });
      return text;
    });
    patternSetting.addButton((button) => {
      button.setButtonText(t("add")).setCta().onClick(async () => {
        const inputEl = patternSetting.controlEl.querySelector("input");
        const pattern = inputEl.value.trim();
        if (pattern && !this.plugin.settings.ignoredFolderPatterns.includes(pattern)) {
          this.plugin.settings.ignoredFolderPatterns.push(pattern);
          await this.plugin.saveSettings();
          this.renderIgnoredFolderPatternsList(ignoredFolderPatternsList);
          inputEl.value = "";
        }
      });
    });
    const ignoredFolderPatternsList = ignoredFolderPatternsContainer.createDiv("ge-ignored-folder-patterns-list");
    this.renderIgnoredFolderPatternsList(ignoredFolderPatternsList);
    containerEl.appendChild(ignoredFolderPatternsContainer);
    containerEl.createEl("h3", { text: t("reset_to_default") });
    new import_obsidian14.Setting(containerEl).setName(t("reset_to_default")).setDesc(t("reset_to_default_desc")).addButton((button) => button.setButtonText(t("reset")).setWarning().onClick(async () => {
      this.plugin.settings = { ...DEFAULT_SETTINGS };
      await this.plugin.saveSettings();
      this.display();
      new import_obsidian14.Notice(t("settings_reset_notice"));
    }));
  }
  // 渲染已忽略的資料夾列表
  renderIgnoredFoldersList(containerEl) {
    containerEl.empty();
    if (this.plugin.settings.ignoredFolders.length === 0) {
      containerEl.createEl("p", { text: t("no_ignored_folders") });
      return;
    }
    const list = containerEl.createEl("ul", { cls: "ge-ignored-folders-list" });
    this.plugin.settings.ignoredFolders.forEach((folder) => {
      const item = list.createEl("li", { cls: "ge-ignored-folder-item" });
      item.createSpan({ text: folder, cls: "ge-ignored-folder-path" });
      const removeButton = item.createEl("button", {
        cls: "ge-ignored-folder-remove",
        text: t("remove")
      });
      removeButton.addEventListener("click", async () => {
        this.plugin.settings.ignoredFolders = this.plugin.settings.ignoredFolders.filter((f) => f !== folder);
        await this.plugin.saveSettings();
        this.renderIgnoredFoldersList(containerEl);
        this.display();
      });
    });
  }
  // 渲染已忽略的資料夾模式列表
  renderIgnoredFolderPatternsList(containerEl) {
    containerEl.empty();
    if (this.plugin.settings.ignoredFolderPatterns.length === 0) {
      containerEl.createEl("p", { text: t("no_ignored_folder_patterns") });
      return;
    }
    const list = containerEl.createEl("ul", { cls: "ge-ignored-folders-list" });
    this.plugin.settings.ignoredFolderPatterns.forEach((pattern) => {
      const item = list.createEl("li", { cls: "ge-ignored-folder-item" });
      item.createSpan({ text: pattern, cls: "ge-ignored-folder-path" });
      const removeButton = item.createEl("button", {
        cls: "ge-ignored-folder-remove",
        text: t("remove")
      });
      removeButton.addEventListener("click", async () => {
        this.plugin.settings.ignoredFolderPatterns = this.plugin.settings.ignoredFolderPatterns.filter((p) => p !== pattern);
        await this.plugin.saveSettings();
        this.renderIgnoredFolderPatternsList(containerEl);
      });
    });
  }
};

// main.ts
var GridExplorerPlugin = class extends import_obsidian15.Plugin {
  async onload() {
    await this.loadSettings();
    this.registerView(
      "grid-view",
      (leaf) => new GridView(leaf, this)
    );
    this.addSettingTab(new GridExplorerSettingTab(this.app, this));
    this.addCommand({
      id: "open-grid-view",
      name: t("open_grid_view"),
      callback: () => {
        showFolderSelectionModal(this.app, this);
      }
    });
    this.addCommand({
      id: "view-current-note-in-grid-view",
      name: t("open_note_in_grid_view"),
      callback: () => {
        const activeFile = this.app.workspace.getActiveFile();
        if (activeFile) {
          this.openNoteInFolder(activeFile);
        } else {
          this.openNoteInFolder(this.app.vault.getRoot());
        }
      }
    });
    this.addCommand({
      id: "view-backlinks-in-grid-view",
      name: t("open_backlinks_in_grid_view"),
      callback: () => {
        const activeFile = this.app.workspace.getActiveFile();
        if (activeFile) {
          this.activateView("backlinks");
        } else {
          this.openNoteInFolder(this.app.vault.getRoot());
        }
      }
    });
    this.addCommand({
      id: "view-outgoinglinks-in-grid-view",
      name: t("open_outgoinglinks_in_grid_view"),
      callback: () => {
        const activeFile = this.app.workspace.getActiveFile();
        if (activeFile) {
          this.activateView("outgoinglinks");
        } else {
          this.openNoteInFolder(this.app.vault.getRoot());
        }
      }
    });
    this.addCommand({
      id: "view-recent-files-in-grid-view",
      name: t("open_recent_files_in_grid_view"),
      callback: () => {
        const activeFile = this.app.workspace.getActiveFile();
        if (activeFile) {
          this.openNoteInRecentFiles(activeFile);
        } else {
          this.openNoteInFolder(this.app.vault.getRoot());
        }
      }
    });
    this.addCommand({
      id: "open-quick-access-folder",
      name: t("open_quick_access_folder"),
      callback: async () => {
        let targetPath = this.settings.quickAccessCommandPath;
        if (!targetPath) {
          targetPath = this.app.vault.getRoot().path;
        }
        const targetFile = this.app.vault.getAbstractFileByPath(targetPath);
        if (targetFile instanceof import_obsidian15.TFolder) {
          this.openNoteInFolder(targetFile);
        } else {
          this.openNoteInFolder(this.app.vault.getRoot());
        }
      }
    });
    this.addCommand({
      id: "open-quick-access-mode",
      name: t("open_quick_access_mode"),
      callback: async () => {
        this.activateView(this.settings.quickAccessModeType);
      }
    });
    this.addRibbonIcon("grid", t("open_grid_view"), () => {
      showFolderSelectionModal(this.app, this);
    });
    this.statusBarItem = this.addStatusBarItem();
    this.registerEvent(
      this.app.workspace.on("file-menu", (menu, file) => {
        if (file instanceof import_obsidian15.TFolder) {
          menu.addItem((item) => {
            var _a, _b;
            (_b = (_a = item.setTitle(t("open_in_grid_view")).setIcon("grid")).setSection) == null ? void 0 : _b.call(_a, "open").onClick(() => {
              this.openNoteInFolder(file);
            });
          });
        }
        if (file instanceof import_obsidian15.TFile) {
          menu.addItem((item) => {
            var _a;
            item.setTitle(t("open_in_grid_view"));
            item.setIcon("grid");
            (_a = item.setSection) == null ? void 0 : _a.call(item, "open");
            const ogSubmenu = item.setSubmenu();
            ogSubmenu.addItem((item2) => {
              item2.setTitle(t("open_note_in_grid_view")).setIcon("folder").onClick(() => {
                this.openNoteInFolder(file);
              });
            });
            if (this.settings.showBacklinksMode) {
              ogSubmenu.addItem((item2) => {
                item2.setTitle(t("open_backlinks_in_grid_view")).setIcon("links-coming-in").onClick(() => {
                  this.app.workspace.getLeaf().openFile(file);
                  setTimeout(() => {
                    this.activateView("backlinks");
                  }, 100);
                });
              });
            }
            if (this.settings.showOutgoinglinksMode) {
              ogSubmenu.addItem((item2) => {
                item2.setTitle(t("open_outgoinglinks_in_grid_view")).setIcon("links-going-out").onClick(() => {
                  this.app.workspace.getLeaf().openFile(file);
                  setTimeout(() => {
                    this.activateView("outgoinglinks");
                  }, 100);
                });
              });
            }
            if (this.settings.showRecentFilesMode && file instanceof import_obsidian15.TFile) {
              ogSubmenu.addItem((item2) => {
                item2.setTitle(t("open_recent_files_in_grid_view")).setIcon("calendar-days").onClick(() => {
                  this.openNoteInRecentFiles(file);
                });
              });
            }
          });
          menu.addItem((item) => {
            item.setTitle(t("set_note_attribute")).setIcon("palette").onClick(() => {
              showNoteSettingsModal(this.app, this, file);
            });
          });
        }
      })
    );
    this.registerEvent(
      this.app.workspace.on("editor-menu", (menu, editor) => {
        if (editor.somethingSelected()) {
          const selectedText = editor.getSelection();
          const truncatedText = selectedText.length > 20 ? selectedText.substring(0, 20) + "..." : selectedText;
          const menuItemTitle = t("search_selection_in_grid_view").replace("...", `\u300C${truncatedText}\u300D`);
          menu.addItem((item) => {
            var _a, _b;
            (_b = (_a = item.setTitle(menuItemTitle).setIcon("search")).setSection) == null ? void 0 : _b.call(_a, "view").onClick(async () => {
              const selectedText2 = editor.getSelection();
              const view = await this.activateView("", "");
              if (view instanceof GridView) {
                view.searchQuery = selectedText2;
                view.render(true);
              }
            });
          });
        }
      })
    );
    this.registerEvent(
      this.app.workspace.on("tag-wrangler:contextmenu", (menu, tagName) => {
        const truncatedText = tagName.length > 20 ? tagName.substring(0, 20) + "..." : tagName;
        const menuItemTitle = t("search_selection_in_grid_view").replace("...", `\u300C#${truncatedText}\u300D`);
        menu.addItem((item) => {
          var _a, _b;
          (_b = (_a = item.setTitle(menuItemTitle).setIcon("search")).setSection) == null ? void 0 : _b.call(_a, "view").onClick(async () => {
            const view = await this.activateView("", "");
            if (view instanceof GridView) {
              view.searchQuery = `#${tagName}`;
              view.render(true);
            }
          });
        });
      })
    );
    this.registerDomEvent(document, "click", async (evt) => {
      var _a, _b, _c, _d, _e, _f, _g, _h;
      if (!this.settings.interceptAllTagClicks)
        return;
      if (evt.button !== 0)
        return;
      if (evt.target.closest(".multi-select-pill-remove-button"))
        return;
      const el = evt.target.closest(
        'a.tag,        /* \u9810\u89BD\u6A21\u5F0F\u4E2D\u7684 #tag */\n.tag-pane-tag, /* \u6A19\u7C64\u9762\u677F\u4E2D\u7684 tag */\nspan.cm-hashtag, /* \u7DE8\u8F2F\u5668\u4E2D\u7684 tag */\n.metadata-property[data-property-key="tags"] .multi-select-pill /* \u5C6C\u6027\u9762\u677F\u4E2D\u7684 tag */'
      );
      if (!el)
        return;
      let tagName = "";
      if (el.matches("span.cm-hashtag")) {
        const collect = [];
        let curr = el;
        while (curr && curr.matches("span.cm-hashtag") && !curr.classList.contains("cm-formatting")) {
          collect.unshift((_a = curr.textContent) != null ? _a : "");
          curr = curr.previousElementSibling;
          if (curr && (!curr.matches("span.cm-hashtag") || curr.classList.contains("cm-formatting")))
            break;
        }
        curr = el.nextElementSibling;
        while (curr && curr.matches("span.cm-hashtag") && !curr.classList.contains("cm-formatting")) {
          collect.push((_b = curr.textContent) != null ? _b : "");
          curr = curr.nextElementSibling;
        }
        tagName = collect.join("").trim();
      } else if (el.classList.contains("tag-pane-tag")) {
        const inner = el.querySelector(".tag-pane-tag-text, .tree-item-inner-text");
        tagName = (_d = (_c = inner == null ? void 0 : inner.textContent) == null ? void 0 : _c.trim()) != null ? _d : "";
      } else if (el.matches('.metadata-property[data-property-key="tags"] .multi-select-pill')) {
        tagName = (_f = (_e = el.textContent) == null ? void 0 : _e.trim()) != null ? _f : "";
      } else {
        tagName = ((_h = (_g = el.getAttribute("data-tag")) != null ? _g : el.textContent) != null ? _h : "").trim();
      }
      tagName = tagName.replace(/^#/, "");
      if (!tagName)
        return;
      evt.preventDefault();
      evt.stopPropagation();
      const view = await this.activateView("", "");
      if (view instanceof GridView) {
        view.searchQuery = `#${tagName}`;
        view.render(true);
      }
    }, true);
    this.setupCanvasDropHandlers();
    const { workspace } = this.app;
    workspace.onLayoutReady(() => {
      const existingLeaves = /* @__PURE__ */ new WeakSet();
      workspace.iterateAllLeaves((leaf) => {
        existingLeaves.add(leaf);
      });
      this.registerEvent(
        workspace.on("layout-change", () => {
          this.checkForNewTab(existingLeaves);
        })
      );
    });
  }
  setupCanvasDropHandlers() {
    const setup = () => {
      this.app.workspace.getLeavesOfType("canvas").forEach((leaf) => {
        const canvasView = leaf.view;
        if (canvasView.gridExplorerDropHandler) {
          return;
        }
        canvasView.gridExplorerDropHandler = true;
        const canvasEl = canvasView.containerEl;
        const dragoverHandler = (evt) => {
          var _a;
          if ((_a = evt.dataTransfer) == null ? void 0 : _a.types.includes("application/obsidian-grid-explorer-files")) {
            evt.preventDefault();
            evt.dataTransfer.dropEffect = "copy";
          }
        };
        const dropHandler = async (evt) => {
          var _a;
          if (!((_a = evt.dataTransfer) == null ? void 0 : _a.types.includes("application/obsidian-grid-explorer-files"))) {
            return;
          }
          evt.preventDefault();
          evt.stopPropagation();
          let filePath;
          const data = evt.dataTransfer.getData("application/obsidian-grid-explorer-files");
          try {
            const paths = JSON.parse(data);
            if (Array.isArray(paths) && paths.length === 1) {
              filePath = paths[0];
            }
          } catch (e) {
            console.error("Grid Explorer: Failed to parse drop data from Grid Explorer.", e);
          }
          if (!filePath) {
            return;
          }
          const tfile = this.app.vault.getAbstractFileByPath(filePath);
          if (!(tfile instanceof import_obsidian15.TFile)) {
            console.warn("Grid Explorer: Dropped item is not a TFile or could not be found.", filePath);
            return;
          }
          const canvas = canvasView.canvas;
          if (!canvas) {
            return;
          }
          const pos = canvas.posFromEvt(evt);
          const newNode = canvas.createFileNode({
            file: tfile,
            pos,
            size: { width: 400, height: 400 },
            // 預設大小
            focus: true
            // 建立後自動對焦
          });
          canvas.addNode(newNode);
          await canvas.requestSave();
        };
        this.registerDomEvent(canvasEl, "dragover", dragoverHandler);
        this.registerDomEvent(canvasEl, "drop", dropHandler, true);
      });
    };
    this.registerEvent(this.app.workspace.on("layout-change", setup));
    setup();
  }
  async openNoteInRecentFiles(file) {
    const view = await this.activateView("recent-files");
    if (file instanceof import_obsidian15.TFile) {
      setTimeout(() => {
        const gridContainer = view.containerEl.querySelector(".ge-grid-container");
        if (!gridContainer)
          return;
        const gridItem = Array.from(gridContainer.querySelectorAll(".ge-grid-item")).find(
          (item) => item.dataset.filePath === file.path
        );
        if (gridItem) {
          gridItem.scrollIntoView({ behavior: "smooth", block: "center" });
          const itemIndex = view.gridItems.indexOf(gridItem);
          if (itemIndex >= 0) {
            view.selectItem(itemIndex);
          }
        }
      }, 100);
    }
  }
  async openNoteInFolder(file = this.app.vault.getRoot()) {
    var _a;
    const folderPath = file ? file instanceof import_obsidian15.TFile ? (_a = file.parent) == null ? void 0 : _a.path : file.path : "/";
    const view = await this.activateView("folder", folderPath);
    if (file instanceof import_obsidian15.TFile) {
      setTimeout(() => {
        const gridContainer = view.containerEl.querySelector(".ge-grid-container");
        if (!gridContainer)
          return;
        const gridItem = Array.from(gridContainer.querySelectorAll(".ge-grid-item")).find(
          (item) => item.dataset.filePath === file.path
        );
        if (gridItem) {
          gridItem.scrollIntoView({ behavior: "smooth", block: "center" });
          const itemIndex = view.gridItems.indexOf(gridItem);
          if (itemIndex >= 0) {
            view.selectItem(itemIndex);
          }
        }
      }, 100);
    }
  }
  async activateView(mode = "folder", path = "") {
    const { workspace } = this.app;
    let leaf = null;
    const leaves = workspace.getLeavesOfType("grid-view");
    if (this.settings.reuseExistingLeaf && leaves.length > 0) {
      leaf = leaves[0];
    } else {
      switch (this.settings.defaultOpenLocation) {
        case "left":
          leaf = workspace.getLeftLeaf(false);
          break;
        case "right":
          leaf = workspace.getRightLeaf(false);
          break;
        case "tab":
        default:
          leaf = workspace.getLeaf("tab");
          break;
      }
    }
    if (!leaf) {
      leaf = workspace.getLeaf("tab");
    }
    await leaf.setViewState({ type: "grid-view", active: true });
    if (leaf.view instanceof GridView) {
      await leaf.view.setSource(mode, path);
    }
    workspace.revealLeaf(leaf);
    return leaf.view;
  }
  // Function to check for new tabs and convert them to grid-view (for useQuickAccessAsNewTabMode setting)
  checkForNewTab(existingLeaves) {
    if (this.settings.useQuickAccessAsNewTabMode === "default") {
      return;
    }
    if (this.settings.defaultOpenLocation !== "tab") {
      return;
    }
    this.app.workspace.iterateAllLeaves((leaf) => {
      if (existingLeaves.has(leaf))
        return;
      existingLeaves.add(leaf);
      if (!this.tabIsEmpty(leaf))
        return;
      const gridViewLeaves = this.app.workspace.getLeavesOfType("grid-view");
      const openInFolder = this.settings.useQuickAccessAsNewTabMode === "folder";
      const mode = openInFolder ? "folder" : this.settings.quickAccessModeType;
      let path = "";
      if (openInFolder) {
        path = this.settings.quickAccessCommandPath || this.app.vault.getRoot().path;
        const targetFile = this.app.vault.getAbstractFileByPath(path);
        if (!(targetFile instanceof import_obsidian15.TFolder)) {
          path = this.app.vault.getRoot().path;
        }
      }
      if (this.settings.reuseExistingLeaf && gridViewLeaves.length > 0) {
        const leafToReuse = gridViewLeaves[0];
        const isSidebarLeaf = leafToReuse.getRoot() !== this.app.workspace.rootSplit;
        if (!isSidebarLeaf) {
          if (leafToReuse.view instanceof GridView) {
            leafToReuse.view.setSource(mode, path);
          }
          this.app.workspace.revealLeaf(leafToReuse);
          leaf.detach();
          return;
        }
      }
      leaf.setViewState({ type: "grid-view", active: true }).then(() => {
        if (leaf.view instanceof GridView) {
          leaf.view.setSource(mode, path);
        }
      });
    });
  }
  // Checks if a given WorkspaceLeaf is currently empty (is New Tab).
  tabIsEmpty(leaf) {
    var _a;
    return ((_a = leaf.getViewState()) == null ? void 0 : _a.type) === "empty";
  }
  async loadSettings() {
    this.settings = Object.assign({}, DEFAULT_SETTINGS, await this.loadData());
    updateCustomDocumentExtensions(this.settings);
  }
  async saveSettings(update = true) {
    await this.saveData(this.settings);
    updateCustomDocumentExtensions(this.settings);
    if (update) {
      const leaves = this.app.workspace.getLeavesOfType("grid-view");
      leaves.forEach((leaf) => {
        if (leaf.view instanceof GridView) {
          leaf.view.render();
        }
      });
    }
  }
};


/* nosourcemap */