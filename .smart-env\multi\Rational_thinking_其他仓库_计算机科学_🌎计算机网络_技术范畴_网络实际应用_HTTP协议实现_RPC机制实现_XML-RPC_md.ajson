"smart_sources:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/XML-RPC.md": {"path":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/XML-RPC.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09720404,-0.02406788,0.01100131,-0.0150537,-0.02809671,-0.00290609,0.00661716,0.05556794,0.03078628,0.00763657,0.02248603,0.01986441,0.05406771,0.04448802,0.11539489,-0.00136635,0.02739151,0.06350637,-0.03193847,-0.00958688,0.10298559,-0.01948583,-0.04795384,-0.05910622,0.04696164,0.05542079,-0.02730259,-0.02438639,-0.02146262,-0.14410457,-0.02675575,-0.00509756,0.01178883,0.02331472,-0.00136055,-0.01525278,0.03163431,0.02048604,-0.04369937,0.00208236,-0.0350923,-0.02138479,-0.00838388,0.00978853,-0.05390386,-0.07716626,-0.00082318,-0.00833669,0.02231761,0.02625613,-0.06194009,-0.00918394,-0.02605518,-0.02632279,0.00065427,-0.03141133,0.03162079,0.01124259,0.03720701,-0.0506805,-0.01030923,0.0652748,-0.18760858,0.09149314,-0.01754542,-0.02396622,-0.04278271,0.0030775,0.06297298,-0.0081196,-0.03761505,0.02611324,-0.07375205,0.0617781,0.05628394,-0.0200859,-0.02923478,-0.04900317,-0.0210648,-0.02826078,-0.00816017,0.02499613,-0.03476601,-0.00908446,0.00678913,0.05269865,-0.0316013,-0.05375556,0.02923938,0.00805136,-0.00953024,-0.04541359,-0.01664505,0.03758701,-0.0292499,-0.02163122,0.03326777,0.05258223,-0.0638537,0.10226298,-0.03250776,-0.00210155,-0.01141248,-0.05839634,0.01736667,-0.0449617,-0.02701889,-0.03571561,-0.03696159,0.04353791,-0.09384613,-0.06320982,0.02381259,-0.01530827,0.02237777,0.02694611,0.02441901,0.00502068,-0.01342226,-0.06192448,-0.02844065,0.00742388,0.0758728,-0.0205312,0.01202387,-0.05888807,0.09470081,0.0651831,-0.00376242,0.02478875,0.08581411,-0.01818698,-0.05694534,0.01406337,-0.01938006,-0.01329409,0.02728576,-0.02346693,-0.03105817,-0.02847563,-0.00682599,-0.05980812,0.00435743,-0.08459682,-0.03466653,0.08341466,-0.06792296,0.01130224,-0.01046084,-0.03594198,0.03548454,0.06738318,-0.01697079,-0.01844938,-0.02935302,0.0284554,0.0872866,0.10565268,-0.05192925,-0.02628986,0.03099507,-0.05418446,-0.06912085,0.15971874,0.04481256,-0.11719052,-0.02434165,0.01276354,0.02024302,-0.02420295,0.05371628,-0.01743359,0.02471581,-0.05317455,-0.00724412,-0.02020717,-0.02975183,-0.03171173,0.03515647,0.00188238,0.05353797,-0.03015097,-0.00809879,0.01193274,-0.04214947,-0.07403228,-0.01253612,-0.02215578,0.0399977,-0.0355304,-0.109344,-0.00483109,0.00113761,-0.01626238,-0.02282945,-0.04337638,0.03886137,-0.00249514,0.04397263,-0.01909437,0.12410142,0.04497745,-0.02785628,0.00214721,-0.03718435,-0.03842756,0.00429933,0.03146308,-0.00677245,0.06450807,-0.02950846,0.0682628,-0.00654135,0.04082385,-0.04607161,0.03665979,-0.00076511,0.07258315,0.05539904,0.05175027,0.00027213,0.03316156,-0.0339964,-0.19356094,0.02095585,0.00910492,-0.03342305,0.02312245,-0.03753776,0.02355178,0.02065708,0.05387984,0.03031518,0.13159035,0.02484202,-0.07328035,-0.03295342,-0.03611133,0.00625557,0.0390041,-0.05601527,-0.00532567,0.01355705,-0.02624181,0.06928249,-0.00485318,0.04595363,0.07240683,-0.02494784,0.14790848,-0.03316926,0.03833586,0.01308888,0.03316562,0.01940802,-0.02833984,-0.11132743,0.00096466,0.06815525,-0.02805112,-1.4e-7,0.03492017,-0.04873228,-0.01259102,0.0392045,-0.02234821,-0.11893484,0.01721009,-0.01743523,-0.04191849,-0.00292142,0.07960373,0.05678537,0.00317455,0.02315121,0.0122136,0.02967977,-0.01487986,-0.01119843,-0.05014765,-0.04862638,0.00881187,0.01926064,0.03085291,0.00699834,0.0361686,0.00297038,0.07084461,-0.02042523,0.05238181,0.01960694,-0.01732061,-0.02500266,-0.03161535,0.17810342,0.03380092,-0.03615155,0.01605347,0.00946844,-0.02461758,-0.04579306,0.01996767,-0.02995569,0.03909677,0.00033966,0.04161053,-0.01049674,-0.03889198,0.00138752,-0.02086146,-0.05669926,0.03780449,-0.04073664,-0.04182738,-0.03255337,-0.07409584,0.05450178,0.05331844,-0.02358054,-0.30812898,0.02139269,-0.01180078,0.00258008,-0.01995918,0.05727987,0.03857948,-0.01788512,-0.05730662,0.01859569,-0.04025589,0.05814129,0.00372687,-0.02932187,0.00207172,-0.00934194,0.0510578,0.02200223,0.05024755,-0.03524948,-0.01340079,0.02948781,0.22949523,0.00415637,0.0803697,0.0072121,0.01569721,0.01330708,0.04786942,0.04799423,0.02098022,-0.01462804,-0.01547276,-0.08009836,0.05857549,0.00023412,-0.00803096,0.00904616,-0.00932632,-0.02937358,-0.02431803,0.05078218,-0.03910752,-0.02372164,0.08559828,0.05505639,-0.0465618,-0.03673589,0.00846793,0.09060093,-0.03300456,0.02449403,0.01546892,0.02077097,0.00013396,-0.0076987,0.02446361,-0.04768084,-0.06189484,-0.03475944,0.03780307,-0.01978144,0.06716394,0.10699745,0.079093],"last_embed":{"hash":"0ca918697c5f8d4d09874da564cbb5b45db68edc8ffb0667affb28ea6a6b9e01","tokens":450}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.04289204,-0.00025609,-0.02225322,-0.0030611,-0.03339858,0.00777366,0.02585134,-0.00153905,0.03791051,-0.00150829,0.01924471,0.00871228,-0.00744905,-0.0627652,0.0068957,0.03529491,-0.06362824,0.00172532,0.05003026,-0.01708311,-0.03067783,0.01976198,0.03215989,0.00920055,-0.02846259,0.03088541,-0.03516746,-0.10146191,0.00122116,0.03268647,0.06542034,0.02485044,0.02926273,-0.02249131,0.03294196,0.01553708,0.0213211,0.03574651,-0.01784609,-0.03930209,-0.0437331,0.00286507,-0.02741507,0.01415285,-0.09147216,-0.02970956,0.00813479,-0.01470332,-0.01991579,0.04973163,0.01255043,0.01380937,0.00220868,-0.00147896,-0.00562383,0.01580222,-0.0254038,-0.02911862,-0.07314655,-0.05504105,0.04290853,0.01895728,0.03001833,0.03924608,0.01231338,0.00988891,0.00150331,-0.06473779,0.02425427,0.00749639,-0.03082217,0.0392046,0.07980374,0.00088729,0.04908413,0.00088559,-0.05596038,-0.02182209,0.00300995,0.04390599,0.00444432,0.07800423,0.01294079,0.05240778,0.01047298,-0.01024474,0.03385342,-0.038166,-0.0357011,-0.00784341,0.01739665,-0.04263987,-0.09574144,-0.08668395,0.00557105,-0.00935197,-0.01185881,-0.00368054,0.02782714,-0.04046455,-0.0241505,-0.05029668,-0.07683774,-0.00693247,-0.04416228,-0.03186035,0.0211179,-0.00300067,-0.0058021,0.01161942,-0.02700364,0.00973829,-0.09990755,0.02281451,0.00497868,0.00413694,-0.00593336,-0.018846,0.03364514,0.07348709,-0.02218401,0.05021063,-0.03557444,0.02189026,0.02358962,0.01369666,0.02605849,0.00581357,0.01551063,-0.02352268,-0.04904737,0.03895126,-0.0135531,0.00297509,-0.02155871,0.02485063,0.03797469,-0.02981877,-0.03151949,-0.01923523,0.00888122,-0.04002618,-0.05659642,-0.02410221,0.01067246,-0.01983689,-0.02902904,0.02558446,0.05869202,0.02262648,0.00854144,0.02041235,-0.08776747,0.02006143,-0.00816412,-0.00472061,0.04342305,0.00859077,-0.07648902,-0.00217251,-0.02944439,0.03131483,-0.00781111,-0.05699585,0.0645526,0.01757374,-0.03318432,0.01895167,-0.02336773,-0.02138215,0.06511033,0.0223283,0.03945171,-0.05503064,0.01074838,0.01516074,-0.01173715,0.03717111,-0.02100648,0.03719166,0.02818318,0.00688835,0.02914707,0.05150091,-0.00993951,0.0444127,-0.08194011,0.02979624,0.05622423,-0.00142345,0.03080981,0.02228701,0.05043698,0.00208128,0.02601788,0.05661757,-0.02483997,-0.0340908,0.03102462,0.02655918,-0.05562302,0.00820958,-0.04836955,-0.09048468,0.00123654,0.01441611,-0.01214336,-0.02966402,-0.05193104,-0.04610904,-0.02258939,0.03741309,0.04815477,0.0364881,0.00809996,0.04621012,0.02439336,0.06509273,-0.0089711,-0.04511752,-0.0384777,0.01143368,-0.07604026,-0.01167076,0.0368539,-0.05973648,0.01108197,0.04180914,0.02000403,-0.00712583,0.00070085,0.04223414,0.00781119,-0.0288955,-0.07934795,0.05340258,-0.00956508,-0.01705228,-0.01720096,-0.02525115,-0.02557387,-0.02833232,0.03404317,-0.08055191,-0.01090491,0.00600225,-0.00891787,0.07282733,0.02229736,0.04729994,0.02293947,0.07597487,-0.03281687,0.01293111,-0.01704872,-0.08362807,-0.03872021,-0.00040098,-0.04565395,-0.02168719,0.05846068,-0.03461843,0.02150698,0.00401548,-0.04426944,-0.00857921,0.03223781,0.05826503,0.05287158,0.02235426,0.01444263,-0.00637905,0.00707265,0.01646486,-0.04161217,-0.0294426,-0.01083783,0.00257484,0.04945718,-0.03283588,0.01995867,-0.00387637,0.03697926,-0.01325643,-0.0130348,0.03295566,-0.04508265,-0.03418297,0.03767109,0.0190789,-0.03952177,-0.01475463,-0.0412145,0.02398761,-0.01772625,-0.05127405,-0.02175202,0.08076572,0.03240516,-0.06955837,-0.0215243,0.05466624,-0.06074269,-0.01827836,-0.01447399,0.02734643,0.00296358,0.01665627,0.00471398,-0.03515994,-0.01999463,-0.01880953,0.00909451,-0.01679225,-0.00176616,0.01086301,0.06202966,-0.03060322,-0.03328253,-0.01385549,-0.01398225,0.05085553,-0.07413973,0.01465719,-0.02185008,-0.07258198,0.0656675,0.02264875,-0.00591591,0.02658309,-0.04312195,-0.02973222,-0.05004704,-0.02350318,-0.02615973,-0.05914044,0.0002505,-0.01033566,0.00557745,-0.03558931,-0.03533203,-0.00378518,0.02748967,0.02722343,-0.00423989,0.07018478,-0.03994861,0.04023201,-0.0060261,-0.01838607,0.00887747,0.04868227,-0.06336336,0.04911098,0.02875706,0.00162057,-0.01231647,0.00484569,-0.03706579,0.04097043,-0.06885553,-0.06125988,0.00508359,-0.03295617,0.01410812,0.0270611,0.05734268,0.01658469,-0.01618194,-0.01977808,0.03543517,-0.00528631,0.03433888,0.01274439,-0.02420965,-0.00163814,-0.03415991,0.0095972,0.13039771,-0.01905883,-0.0457418,-0.06650066,-0.01290206,-0.0234704,0.01611426,-0.03887826,-0.06703195,0.03338486,-0.0361078,-0.03917517,-0.01921711,-0.00710671,-0.04251992,-0.04402609,0.04302924,-0.03405045,-0.00096688,-0.02249061,0.04182168,-0.08794419,0.03576893,0.00858412,-0.00943675,-0.044214,-0.01589316,0.01224969,-0.04182585,0.03585314,0.00879405,0.00763868,0.01127833,0.022157,-0.04965824,0.0054549,0.01659955,0.02375636,0.03928893,-0.08030956,-0.01990592,-0.00754535,-0.01824702,-0.05512567,-0.01359403,0.01434844,-0.01538226,0.01193702,-0.0682762,-0.01030639,-0.05371261,-0.02294745,-0.04547761,-0.05383556,-0.01088144,-0.01073832,-0.00441738,-0.00704421,0.01788061,-0.01667748,0.04294834,-0.03750079,-0.04015384,0.01812685,-0.00033664,0.02034752,-0.01395963,-0.00307195,0.00484134,-0.01519847,0.04988458,0.04299824,-0.00137278,-0.00954779,-0.06550895,-0.04537294,0.02682407,-0.00940044,0.00570251,0.00100395,0.03357711,0.01326362,0.03873541,0.01127183,-0.00775258,-0.0007444,0.01876557,0.01059321,0.04894456,0.02060805,-0.13378963,0.0230969,-0.04477492,-0.04165436,-0.01842442,-0.03805443,-0.01023604,-0.03253198,0.03546958,-0.01782591,-0.07158076,0.01987726,-0.01584953,-0.01190284,-0.07185517,0.00862878,-0.02082189,-0.01240034,0.01304017,0.06126342,0.01288322,-0.02373656,-0.00444334,0.00065253,-0.02131442,0.02174548,-0.02993344,0.01283991,-0.02630528,0.03873974,-0.04611189,0.05093976,0.00988987,0.01302353,-0.02432327,0.00565154,-0.00184433,0.00558421,0.00346981,0.01137111,-0.01052184,-0.02262182,0.00141365,0.07654759,-0.00099553,0.00604017,-0.04310123,0.00128438,0.06737419,0.04393586,0.00442423,-0.07336333,-0.00641069,0.02748208,-0.03773091,0.0072884,0.05538683,-0.02893497,0.05442761,-0.01690241,0.0040031,0.04872091,0.02655191,-0.00556699,0.04454987,-0.04033335,0.0000233,0.02844146,0.03852553,-0.04043253,-0.00360634,0.03460715,-0.03227546,0.00701886,-0.05122661,0.03159543,0.00982142,0.01847596,0.04654748,-0.04646548,-0.01968008,-0.00750719,-0.01002526,-0.01815917,-0.04712093,-0.00217022,-0.01878433,0.01213843,0.02344544,-0.05311028,-0.04964923,0.0340374,-0.00920309,0.00440328,0.04787412,0.02693554,0.01502736,-0.00007882,0.0101306,-0.03662912,0.03547709,0.05718974,-0.12499277,0.02237767,0.02914111,0.03985704,-0.03366946,-0.05748501,-0.03683244,0.01177848,0.06245064,0.03030366,-0.00231034,-0.01033631,0.02027725,-0.00653078,-0.01859968,-0.00796502,0.02863693,0.04226018,-0.00128965,-0.0035459,0.05301218,-0.05449599,-0.01183812,-0.01431875,0.05856379,0.06541946,-0.05250533,0.02564597,0.01850023,-0.03821271,0.0184524,0.02269116,-0.02256287,0.00666101,0.02866434,0.01146703,0.01253567,-0.03809682,0.02789704,0.04106706,0.05222884,0.08942439,-0.02351288,0.01687393,0.03123947,0.01424228,0.00172658,-0.06870256,0.05200485,0.00735583,-0.05088798,-0.01722354,-0.04894123,-0.00640876,0.02096583,0.00441952,0.04385179,0.05391855,-0.03835597,-0.01544318,-0.00569291,0.02007219,-0.01056373,0.02619376,0.02506112,-0.01345092,-0.03138967,-0.00610901,-0.00639598,-0.01168963,0.02926281,-0.04361079,-0.00617259,0.02139919,-0.01565894,0.02940366,-0.08440926,-0.03704285,-0.04354661,0.05373822,0.02222277,0.0135285,0.00146171,-0.0469755,0.03105957,0.04050721,0.00349318,0.03557169,-0.0273568,0.0264627,-0.02517084,0.01530245,0.02319242,0.03740077,-0.0115348,-0.04543636,0.01028429,-0.02863166,-0.03663408,-0.07392199,-0.02721903,-0.03582847,-0.00114671,0.01335096,-0.00110897,0.04017058,0.01132156,0.02877341,-0.01249007,0.02840834,-0.07784584,-0.02355019,-0.00338685,-0.00121885,-0.00099307,-0.00462868,-0.01428083,-0.0003296,0.0296933,-0.00917736,-0.03271198,0.01054536,0.02997981,-0.00857054,0.02666761,-0.03023237,-0.00237056,0.09355485,-0.01684738,-0.04296476,-0.00374359,0.01490512,-0.01237841,0.0083001,-0.0082867,0.00751323,-0.00251411,0.0306079,0.03954767,0.05155952,-0.04190506,0.07077073,0.01262584,-0.03157571,0.03018083,0.0319172,0.03377234,0.02438315,-0.0115217,-0.01760986,-0.00008633,-0.00326604,0.06289621,0.08301987,-0.06632749,-0.02927844,-0.04166714,0.00527112,0.008469,0.00742898,0.01794062,-0.06855216,0.0055603,0.01276914,0.09192482,-0.01173923,0.03719522,0.00279155,0.08429299,0.0327156,0.02241067,-0.01124754,-0.02006913,-0.017465,-0.03398897,0.05852005,-0.05500103,0.04000447,0.01617184,0.03540312,0.02503296,-0.01543947,0.02220061,0.0694112,-0.0432306,0.01988006,0.03427831,0.00216362,0.0000011,0.03259253,0.02832245,0.00533949,-0.00527587,-0.01769308,-0.05186571,-0.05006422,0.10505534,-0.01616227],"last_embed":{"tokens":348,"hash":"rp1nr0"}}},"last_read":{"hash":"rp1nr0","at":1751079994305},"class_name":"SmartSource","outlinks":[{"title":"RPC协议","target":"RPC协议","line":12},{"title":"HTTP(S)协议","target":"HTTP(S)协议","line":25},{"title":"HTTP(S)协议","target":"HTTP(S)协议","line":26}],"metadata":{"tags":[],"aliases":["eXtensible Markup Language Remote Procedure Call","可扩展标记语言——远程过程调用"],"英文":"eXtensible Markup Language Remote Procedure Call","协议层级":"应用层"},"blocks":{"#---frontmatter---":[1,8],"#简介":[10,15],"#简介#{1}":[12,12],"#简介#{2}":[13,15],"#基本工作原理":[16,35],"#基本工作原理#{1}":[18,35]},"last_import":{"mtime":1731396395515,"size":1413,"at":1749024987637,"hash":"rp1nr0"},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/XML-RPC.md","last_embed":{"hash":"rp1nr0","at":1751079994305}},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/XML-RPC.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/XML-RPC.md#---frontmatter---","lines":[1,8],"size":161,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/XML-RPC.md#简介": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/XML-RPC.md#简介","lines":[10,15],"size":105,"outlinks":[{"title":"RPC协议","target":"RPC协议","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/XML-RPC.md#简介#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/XML-RPC.md#简介#{1}","lines":[12,12],"size":38,"outlinks":[{"title":"RPC协议","target":"RPC协议","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/XML-RPC.md#简介#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/XML-RPC.md#简介#{2}","lines":[13,15],"size":59,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/XML-RPC.md#基本工作原理": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/XML-RPC.md#基本工作原理","lines":[16,35],"size":468,"outlinks":[{"title":"HTTP(S)协议","target":"HTTP(S)协议","line":10},{"title":"HTTP(S)协议","target":"HTTP(S)协议","line":11}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/XML-RPC.md#基本工作原理#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/XML-RPC.md#基本工作原理#{1}","lines":[18,35],"size":458,"outlinks":[{"title":"HTTP(S)协议","target":"HTTP(S)协议","line":8},{"title":"HTTP(S)协议","target":"HTTP(S)协议","line":9}],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/XML-RPC.md": {"path":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/XML-RPC.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09720404,-0.02406788,0.01100131,-0.0150537,-0.02809671,-0.00290609,0.00661716,0.05556794,0.03078628,0.00763657,0.02248603,0.01986441,0.05406771,0.04448802,0.11539489,-0.00136635,0.02739151,0.06350637,-0.03193847,-0.00958688,0.10298559,-0.01948583,-0.04795384,-0.05910622,0.04696164,0.05542079,-0.02730259,-0.02438639,-0.02146262,-0.14410457,-0.02675575,-0.00509756,0.01178883,0.02331472,-0.00136055,-0.01525278,0.03163431,0.02048604,-0.04369937,0.00208236,-0.0350923,-0.02138479,-0.00838388,0.00978853,-0.05390386,-0.07716626,-0.00082318,-0.00833669,0.02231761,0.02625613,-0.06194009,-0.00918394,-0.02605518,-0.02632279,0.00065427,-0.03141133,0.03162079,0.01124259,0.03720701,-0.0506805,-0.01030923,0.0652748,-0.18760858,0.09149314,-0.01754542,-0.02396622,-0.04278271,0.0030775,0.06297298,-0.0081196,-0.03761505,0.02611324,-0.07375205,0.0617781,0.05628394,-0.0200859,-0.02923478,-0.04900317,-0.0210648,-0.02826078,-0.00816017,0.02499613,-0.03476601,-0.00908446,0.00678913,0.05269865,-0.0316013,-0.05375556,0.02923938,0.00805136,-0.00953024,-0.04541359,-0.01664505,0.03758701,-0.0292499,-0.02163122,0.03326777,0.05258223,-0.0638537,0.10226298,-0.03250776,-0.00210155,-0.01141248,-0.05839634,0.01736667,-0.0449617,-0.02701889,-0.03571561,-0.03696159,0.04353791,-0.09384613,-0.06320982,0.02381259,-0.01530827,0.02237777,0.02694611,0.02441901,0.00502068,-0.01342226,-0.06192448,-0.02844065,0.00742388,0.0758728,-0.0205312,0.01202387,-0.05888807,0.09470081,0.0651831,-0.00376242,0.02478875,0.08581411,-0.01818698,-0.05694534,0.01406337,-0.01938006,-0.01329409,0.02728576,-0.02346693,-0.03105817,-0.02847563,-0.00682599,-0.05980812,0.00435743,-0.08459682,-0.03466653,0.08341466,-0.06792296,0.01130224,-0.01046084,-0.03594198,0.03548454,0.06738318,-0.01697079,-0.01844938,-0.02935302,0.0284554,0.0872866,0.10565268,-0.05192925,-0.02628986,0.03099507,-0.05418446,-0.06912085,0.15971874,0.04481256,-0.11719052,-0.02434165,0.01276354,0.02024302,-0.02420295,0.05371628,-0.01743359,0.02471581,-0.05317455,-0.00724412,-0.02020717,-0.02975183,-0.03171173,0.03515647,0.00188238,0.05353797,-0.03015097,-0.00809879,0.01193274,-0.04214947,-0.07403228,-0.01253612,-0.02215578,0.0399977,-0.0355304,-0.109344,-0.00483109,0.00113761,-0.01626238,-0.02282945,-0.04337638,0.03886137,-0.00249514,0.04397263,-0.01909437,0.12410142,0.04497745,-0.02785628,0.00214721,-0.03718435,-0.03842756,0.00429933,0.03146308,-0.00677245,0.06450807,-0.02950846,0.0682628,-0.00654135,0.04082385,-0.04607161,0.03665979,-0.00076511,0.07258315,0.05539904,0.05175027,0.00027213,0.03316156,-0.0339964,-0.19356094,0.02095585,0.00910492,-0.03342305,0.02312245,-0.03753776,0.02355178,0.02065708,0.05387984,0.03031518,0.13159035,0.02484202,-0.07328035,-0.03295342,-0.03611133,0.00625557,0.0390041,-0.05601527,-0.00532567,0.01355705,-0.02624181,0.06928249,-0.00485318,0.04595363,0.07240683,-0.02494784,0.14790848,-0.03316926,0.03833586,0.01308888,0.03316562,0.01940802,-0.02833984,-0.11132743,0.00096466,0.06815525,-0.02805112,-1.4e-7,0.03492017,-0.04873228,-0.01259102,0.0392045,-0.02234821,-0.11893484,0.01721009,-0.01743523,-0.04191849,-0.00292142,0.07960373,0.05678537,0.00317455,0.02315121,0.0122136,0.02967977,-0.01487986,-0.01119843,-0.05014765,-0.04862638,0.00881187,0.01926064,0.03085291,0.00699834,0.0361686,0.00297038,0.07084461,-0.02042523,0.05238181,0.01960694,-0.01732061,-0.02500266,-0.03161535,0.17810342,0.03380092,-0.03615155,0.01605347,0.00946844,-0.02461758,-0.04579306,0.01996767,-0.02995569,0.03909677,0.00033966,0.04161053,-0.01049674,-0.03889198,0.00138752,-0.02086146,-0.05669926,0.03780449,-0.04073664,-0.04182738,-0.03255337,-0.07409584,0.05450178,0.05331844,-0.02358054,-0.30812898,0.02139269,-0.01180078,0.00258008,-0.01995918,0.05727987,0.03857948,-0.01788512,-0.05730662,0.01859569,-0.04025589,0.05814129,0.00372687,-0.02932187,0.00207172,-0.00934194,0.0510578,0.02200223,0.05024755,-0.03524948,-0.01340079,0.02948781,0.22949523,0.00415637,0.0803697,0.0072121,0.01569721,0.01330708,0.04786942,0.04799423,0.02098022,-0.01462804,-0.01547276,-0.08009836,0.05857549,0.00023412,-0.00803096,0.00904616,-0.00932632,-0.02937358,-0.02431803,0.05078218,-0.03910752,-0.02372164,0.08559828,0.05505639,-0.0465618,-0.03673589,0.00846793,0.09060093,-0.03300456,0.02449403,0.01546892,0.02077097,0.00013396,-0.0076987,0.02446361,-0.04768084,-0.06189484,-0.03475944,0.03780307,-0.01978144,0.06716394,0.10699745,0.079093],"last_embed":{"hash":"0ca918697c5f8d4d09874da564cbb5b45db68edc8ffb0667affb28ea6a6b9e01","tokens":450}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.04289204,-0.00025609,-0.02225322,-0.0030611,-0.03339858,0.00777366,0.02585134,-0.00153905,0.03791051,-0.00150829,0.01924471,0.00871228,-0.00744905,-0.0627652,0.0068957,0.03529491,-0.06362824,0.00172532,0.05003026,-0.01708311,-0.03067783,0.01976198,0.03215989,0.00920055,-0.02846259,0.03088541,-0.03516746,-0.10146191,0.00122116,0.03268647,0.06542034,0.02485044,0.02926273,-0.02249131,0.03294196,0.01553708,0.0213211,0.03574651,-0.01784609,-0.03930209,-0.0437331,0.00286507,-0.02741507,0.01415285,-0.09147216,-0.02970956,0.00813479,-0.01470332,-0.01991579,0.04973163,0.01255043,0.01380937,0.00220868,-0.00147896,-0.00562383,0.01580222,-0.0254038,-0.02911862,-0.07314655,-0.05504105,0.04290853,0.01895728,0.03001833,0.03924608,0.01231338,0.00988891,0.00150331,-0.06473779,0.02425427,0.00749639,-0.03082217,0.0392046,0.07980374,0.00088729,0.04908413,0.00088559,-0.05596038,-0.02182209,0.00300995,0.04390599,0.00444432,0.07800423,0.01294079,0.05240778,0.01047298,-0.01024474,0.03385342,-0.038166,-0.0357011,-0.00784341,0.01739665,-0.04263987,-0.09574144,-0.08668395,0.00557105,-0.00935197,-0.01185881,-0.00368054,0.02782714,-0.04046455,-0.0241505,-0.05029668,-0.07683774,-0.00693247,-0.04416228,-0.03186035,0.0211179,-0.00300067,-0.0058021,0.01161942,-0.02700364,0.00973829,-0.09990755,0.02281451,0.00497868,0.00413694,-0.00593336,-0.018846,0.03364514,0.07348709,-0.02218401,0.05021063,-0.03557444,0.02189026,0.02358962,0.01369666,0.02605849,0.00581357,0.01551063,-0.02352268,-0.04904737,0.03895126,-0.0135531,0.00297509,-0.02155871,0.02485063,0.03797469,-0.02981877,-0.03151949,-0.01923523,0.00888122,-0.04002618,-0.05659642,-0.02410221,0.01067246,-0.01983689,-0.02902904,0.02558446,0.05869202,0.02262648,0.00854144,0.02041235,-0.08776747,0.02006143,-0.00816412,-0.00472061,0.04342305,0.00859077,-0.07648902,-0.00217251,-0.02944439,0.03131483,-0.00781111,-0.05699585,0.0645526,0.01757374,-0.03318432,0.01895167,-0.02336773,-0.02138215,0.06511033,0.0223283,0.03945171,-0.05503064,0.01074838,0.01516074,-0.01173715,0.03717111,-0.02100648,0.03719166,0.02818318,0.00688835,0.02914707,0.05150091,-0.00993951,0.0444127,-0.08194011,0.02979624,0.05622423,-0.00142345,0.03080981,0.02228701,0.05043698,0.00208128,0.02601788,0.05661757,-0.02483997,-0.0340908,0.03102462,0.02655918,-0.05562302,0.00820958,-0.04836955,-0.09048468,0.00123654,0.01441611,-0.01214336,-0.02966402,-0.05193104,-0.04610904,-0.02258939,0.03741309,0.04815477,0.0364881,0.00809996,0.04621012,0.02439336,0.06509273,-0.0089711,-0.04511752,-0.0384777,0.01143368,-0.07604026,-0.01167076,0.0368539,-0.05973648,0.01108197,0.04180914,0.02000403,-0.00712583,0.00070085,0.04223414,0.00781119,-0.0288955,-0.07934795,0.05340258,-0.00956508,-0.01705228,-0.01720096,-0.02525115,-0.02557387,-0.02833232,0.03404317,-0.08055191,-0.01090491,0.00600225,-0.00891787,0.07282733,0.02229736,0.04729994,0.02293947,0.07597487,-0.03281687,0.01293111,-0.01704872,-0.08362807,-0.03872021,-0.00040098,-0.04565395,-0.02168719,0.05846068,-0.03461843,0.02150698,0.00401548,-0.04426944,-0.00857921,0.03223781,0.05826503,0.05287158,0.02235426,0.01444263,-0.00637905,0.00707265,0.01646486,-0.04161217,-0.0294426,-0.01083783,0.00257484,0.04945718,-0.03283588,0.01995867,-0.00387637,0.03697926,-0.01325643,-0.0130348,0.03295566,-0.04508265,-0.03418297,0.03767109,0.0190789,-0.03952177,-0.01475463,-0.0412145,0.02398761,-0.01772625,-0.05127405,-0.02175202,0.08076572,0.03240516,-0.06955837,-0.0215243,0.05466624,-0.06074269,-0.01827836,-0.01447399,0.02734643,0.00296358,0.01665627,0.00471398,-0.03515994,-0.01999463,-0.01880953,0.00909451,-0.01679225,-0.00176616,0.01086301,0.06202966,-0.03060322,-0.03328253,-0.01385549,-0.01398225,0.05085553,-0.07413973,0.01465719,-0.02185008,-0.07258198,0.0656675,0.02264875,-0.00591591,0.02658309,-0.04312195,-0.02973222,-0.05004704,-0.02350318,-0.02615973,-0.05914044,0.0002505,-0.01033566,0.00557745,-0.03558931,-0.03533203,-0.00378518,0.02748967,0.02722343,-0.00423989,0.07018478,-0.03994861,0.04023201,-0.0060261,-0.01838607,0.00887747,0.04868227,-0.06336336,0.04911098,0.02875706,0.00162057,-0.01231647,0.00484569,-0.03706579,0.04097043,-0.06885553,-0.06125988,0.00508359,-0.03295617,0.01410812,0.0270611,0.05734268,0.01658469,-0.01618194,-0.01977808,0.03543517,-0.00528631,0.03433888,0.01274439,-0.02420965,-0.00163814,-0.03415991,0.0095972,0.13039771,-0.01905883,-0.0457418,-0.06650066,-0.01290206,-0.0234704,0.01611426,-0.03887826,-0.06703195,0.03338486,-0.0361078,-0.03917517,-0.01921711,-0.00710671,-0.04251992,-0.04402609,0.04302924,-0.03405045,-0.00096688,-0.02249061,0.04182168,-0.08794419,0.03576893,0.00858412,-0.00943675,-0.044214,-0.01589316,0.01224969,-0.04182585,0.03585314,0.00879405,0.00763868,0.01127833,0.022157,-0.04965824,0.0054549,0.01659955,0.02375636,0.03928893,-0.08030956,-0.01990592,-0.00754535,-0.01824702,-0.05512567,-0.01359403,0.01434844,-0.01538226,0.01193702,-0.0682762,-0.01030639,-0.05371261,-0.02294745,-0.04547761,-0.05383556,-0.01088144,-0.01073832,-0.00441738,-0.00704421,0.01788061,-0.01667748,0.04294834,-0.03750079,-0.04015384,0.01812685,-0.00033664,0.02034752,-0.01395963,-0.00307195,0.00484134,-0.01519847,0.04988458,0.04299824,-0.00137278,-0.00954779,-0.06550895,-0.04537294,0.02682407,-0.00940044,0.00570251,0.00100395,0.03357711,0.01326362,0.03873541,0.01127183,-0.00775258,-0.0007444,0.01876557,0.01059321,0.04894456,0.02060805,-0.13378963,0.0230969,-0.04477492,-0.04165436,-0.01842442,-0.03805443,-0.01023604,-0.03253198,0.03546958,-0.01782591,-0.07158076,0.01987726,-0.01584953,-0.01190284,-0.07185517,0.00862878,-0.02082189,-0.01240034,0.01304017,0.06126342,0.01288322,-0.02373656,-0.00444334,0.00065253,-0.02131442,0.02174548,-0.02993344,0.01283991,-0.02630528,0.03873974,-0.04611189,0.05093976,0.00988987,0.01302353,-0.02432327,0.00565154,-0.00184433,0.00558421,0.00346981,0.01137111,-0.01052184,-0.02262182,0.00141365,0.07654759,-0.00099553,0.00604017,-0.04310123,0.00128438,0.06737419,0.04393586,0.00442423,-0.07336333,-0.00641069,0.02748208,-0.03773091,0.0072884,0.05538683,-0.02893497,0.05442761,-0.01690241,0.0040031,0.04872091,0.02655191,-0.00556699,0.04454987,-0.04033335,0.0000233,0.02844146,0.03852553,-0.04043253,-0.00360634,0.03460715,-0.03227546,0.00701886,-0.05122661,0.03159543,0.00982142,0.01847596,0.04654748,-0.04646548,-0.01968008,-0.00750719,-0.01002526,-0.01815917,-0.04712093,-0.00217022,-0.01878433,0.01213843,0.02344544,-0.05311028,-0.04964923,0.0340374,-0.00920309,0.00440328,0.04787412,0.02693554,0.01502736,-0.00007882,0.0101306,-0.03662912,0.03547709,0.05718974,-0.12499277,0.02237767,0.02914111,0.03985704,-0.03366946,-0.05748501,-0.03683244,0.01177848,0.06245064,0.03030366,-0.00231034,-0.01033631,0.02027725,-0.00653078,-0.01859968,-0.00796502,0.02863693,0.04226018,-0.00128965,-0.0035459,0.05301218,-0.05449599,-0.01183812,-0.01431875,0.05856379,0.06541946,-0.05250533,0.02564597,0.01850023,-0.03821271,0.0184524,0.02269116,-0.02256287,0.00666101,0.02866434,0.01146703,0.01253567,-0.03809682,0.02789704,0.04106706,0.05222884,0.08942439,-0.02351288,0.01687393,0.03123947,0.01424228,0.00172658,-0.06870256,0.05200485,0.00735583,-0.05088798,-0.01722354,-0.04894123,-0.00640876,0.02096583,0.00441952,0.04385179,0.05391855,-0.03835597,-0.01544318,-0.00569291,0.02007219,-0.01056373,0.02619376,0.02506112,-0.01345092,-0.03138967,-0.00610901,-0.00639598,-0.01168963,0.02926281,-0.04361079,-0.00617259,0.02139919,-0.01565894,0.02940366,-0.08440926,-0.03704285,-0.04354661,0.05373822,0.02222277,0.0135285,0.00146171,-0.0469755,0.03105957,0.04050721,0.00349318,0.03557169,-0.0273568,0.0264627,-0.02517084,0.01530245,0.02319242,0.03740077,-0.0115348,-0.04543636,0.01028429,-0.02863166,-0.03663408,-0.07392199,-0.02721903,-0.03582847,-0.00114671,0.01335096,-0.00110897,0.04017058,0.01132156,0.02877341,-0.01249007,0.02840834,-0.07784584,-0.02355019,-0.00338685,-0.00121885,-0.00099307,-0.00462868,-0.01428083,-0.0003296,0.0296933,-0.00917736,-0.03271198,0.01054536,0.02997981,-0.00857054,0.02666761,-0.03023237,-0.00237056,0.09355485,-0.01684738,-0.04296476,-0.00374359,0.01490512,-0.01237841,0.0083001,-0.0082867,0.00751323,-0.00251411,0.0306079,0.03954767,0.05155952,-0.04190506,0.07077073,0.01262584,-0.03157571,0.03018083,0.0319172,0.03377234,0.02438315,-0.0115217,-0.01760986,-0.00008633,-0.00326604,0.06289621,0.08301987,-0.06632749,-0.02927844,-0.04166714,0.00527112,0.008469,0.00742898,0.01794062,-0.06855216,0.0055603,0.01276914,0.09192482,-0.01173923,0.03719522,0.00279155,0.08429299,0.0327156,0.02241067,-0.01124754,-0.02006913,-0.017465,-0.03398897,0.05852005,-0.05500103,0.04000447,0.01617184,0.03540312,0.02503296,-0.01543947,0.02220061,0.0694112,-0.0432306,0.01988006,0.03427831,0.00216362,0.0000011,0.03259253,0.02832245,0.00533949,-0.00527587,-0.01769308,-0.05186571,-0.05006422,0.10505534,-0.01616227],"last_embed":{"tokens":348,"hash":"rp1nr0"}}},"last_read":{"hash":"rp1nr0","at":1751251735581},"class_name":"SmartSource","outlinks":[{"title":"RPC协议","target":"RPC协议","line":12},{"title":"HTTP(S)协议","target":"HTTP(S)协议","line":25},{"title":"HTTP(S)协议","target":"HTTP(S)协议","line":26}],"metadata":{"tags":[],"aliases":["eXtensible Markup Language Remote Procedure Call","可扩展标记语言——远程过程调用"],"英文":"eXtensible Markup Language Remote Procedure Call","协议层级":"应用层"},"blocks":{"#---frontmatter---":[1,8],"#简介":[10,15],"#简介#{1}":[12,12],"#简介#{2}":[13,15],"#基本工作原理":[16,35],"#基本工作原理#{1}":[18,35]},"last_import":{"mtime":1731396395515,"size":1413,"at":1749024987637,"hash":"rp1nr0"},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络实际应用/HTTP协议实现/RPC机制实现/XML-RPC.md","last_embed":{"hash":"rp1nr0","at":1751251735581}},