"smart_sources:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络接口相关/项目/DailyHotApi.md": {"path":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络接口相关/项目/DailyHotApi.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09321903,-0.01510941,-0.04319102,-0.0027043,0.00771928,-0.04936912,-0.02117136,-0.02270431,0.00959998,0.00896873,0.03130045,-0.06365745,0.04826317,0.06200438,0.0270522,0.03048115,-0.00718239,-0.00291616,-0.02895824,0.00514433,0.07942651,-0.0687142,-0.03854804,-0.08621299,-0.04920689,0.03216539,-0.00326781,-0.02491527,0.01087181,-0.15255007,0.01470562,0.02602292,0.02676342,0.00748106,0.00833287,0.00811749,0.01193511,0.03665524,-0.00410751,0.02928127,0.04792891,0.02702134,0.02240626,0.01043362,-0.05959798,-0.03409408,-0.08236105,0.02404735,0.01867139,-0.02468473,-0.02602719,-0.02000701,-0.02750022,-0.03046199,-0.02075089,-0.00246156,0.01773413,0.05405753,0.04083636,-0.01392596,0.08756959,0.00882138,-0.22092445,0.0493892,0.04494112,-0.04031332,-0.00303625,0.01606575,0.05285547,0.00812571,-0.05492095,0.03546895,0.00741718,0.08692785,0.06715105,-0.02126122,0.04383149,0.00097922,-0.01114195,-0.03284151,-0.03117329,0.04334934,-0.06242201,0.00488403,0.02429899,0.04764017,-0.04457907,-0.01141697,0.0613882,-0.00416784,-0.01096823,-0.06639662,-0.00846909,0.03108103,0.03937517,0.02550148,0.02109469,0.03847464,-0.12651348,0.12246355,-0.0635528,0.01935934,0.01642149,-0.06686936,0.01138208,-0.03067175,-0.03836408,-0.05104561,-0.02250844,0.0031397,-0.02842192,-0.00061188,0.07825009,-0.00922597,0.03417466,0.01180301,0.02678078,0.01082259,-0.04734768,-0.00795669,-0.01365267,-0.01889928,0.09001886,-0.00330974,-0.02236154,-0.03015761,0.05880494,0.05360379,0.01483949,0.01878979,0.06163552,-0.01515085,-0.0691757,-0.03650529,-0.00483181,0.00596207,-0.01830192,-0.03368203,0.01560354,-0.02344429,-0.02776133,-0.08301417,0.00495804,-0.10271544,-0.05281188,0.07974075,-0.04935167,0.01138196,0.08387947,-0.07571953,-0.01492126,0.07911205,-0.02867418,-0.05076287,-0.00922318,-0.02752893,0.06945497,0.15955929,0.01142394,-0.02296163,-0.02585768,0.01716177,-0.12166774,0.10240293,-0.02689934,-0.06638096,-0.02170605,0.01273566,0.02560723,-0.01561784,0.03232923,0.02078141,0.01098261,-0.00787152,0.09937809,-0.03594171,0.00944392,-0.03494059,0.05122638,0.01833456,0.04209588,-0.07744963,-0.05196159,0.02977221,0.01055266,-0.07466328,-0.00571008,-0.060257,-0.00760743,-0.04500659,-0.05592449,0.01129021,-0.01842964,0.05715566,-0.02706159,-0.07000369,0.0333317,0.00737342,0.01969929,-0.03252616,0.09259479,-0.00136446,-0.01671843,-0.01167641,-0.01568997,-0.05276164,0.0160581,-0.01187821,0.02763887,0.01786376,-0.00879076,0.01227833,0.03284995,0.02640096,-0.03988539,0.00572191,0.01410686,-0.00766935,0.01489279,0.04236464,0.00084631,0.00560087,-0.09790023,-0.2125129,-0.05223906,0.00820725,-0.02459243,0.0111284,-0.04446896,0.02721221,-0.00435076,0.08569964,0.08877561,0.08900034,-0.04120646,-0.03429795,-0.0181153,0.04856242,0.02267605,0.01742494,0.00999942,-0.01829752,-0.0300001,-0.02153437,0.02390613,-0.02425309,-0.02831891,0.05777114,-0.02654827,0.1588289,0.04380318,0.01158041,0.08155232,0.03780227,0.00946695,-0.01091321,-0.13457091,0.00980843,0.01543728,-0.0256363,-0.06522692,-0.0202521,0.00299058,-0.02431803,0.0608523,-0.06214263,-0.09688576,-0.02553394,-0.02867799,0.01805167,-0.0170648,-0.02631423,0.0387624,-0.00920716,0.03479967,0.01148193,-0.02501901,-0.01842273,-0.04871688,-0.02904382,-0.01129291,-0.02279043,0.01819553,-0.02007579,0.03396774,0.00044486,0.02289975,-0.00607606,-0.06230365,0.00399979,-0.01411239,-0.04489118,0.00799203,0.00988976,0.13779958,-0.00580572,-0.03202844,0.0823768,-0.00685487,0.01800813,-0.04172854,0.02627405,0.01677343,0.01125591,0.0302519,0.03721542,0.04409171,0.00994047,0.03868287,0.01668568,-0.01224991,0.05755366,-0.05722905,-0.07257297,0.01976309,-0.03663802,0.02810656,0.07448879,-0.00204711,-0.28818545,0.03516604,-0.03957776,0.00025004,0.0338545,0.05968674,0.06775501,0.01793732,-0.03990512,0.04010027,-0.05060015,0.05137657,0.01130612,-0.04130684,-0.03724062,-0.04391523,0.0644642,-0.01554567,0.05001329,-0.01505011,-0.02343456,0.05323033,0.19697984,-0.00255573,0.03218891,0.01020365,0.00517241,0.05420797,0.02055866,0.03531367,0.02309841,-0.06269001,0.01423667,-0.05447569,0.06240891,0.05786353,-0.01875048,0.01168228,0.00155983,-0.01331181,-0.04044661,0.07951874,-0.07209256,0.05107408,0.13630809,-0.00611694,-0.01199132,-0.07483703,-0.01559532,0.06481073,0.02048312,-0.01992953,-0.01045262,-0.03719772,0.02389039,0.07510363,0.0476363,-0.02941445,-0.03624319,-0.00441855,0.00190611,-0.00729257,0.08718215,0.05622859,0.08390413],"last_embed":{"hash":"a28659b550e48b1bbf665a84473cdd73105f16d0f9450d29c431459a25322606","tokens":225}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[0.00927653,-0.00373516,-0.05317235,0.01463033,-0.06919584,0.03705353,-0.00716606,-0.0202867,-0.00911855,0.00329319,-0.00407193,0.02103234,0.01688584,-0.0052702,0.06332081,0.05194159,-0.07416564,0.07103323,-0.05717979,-0.04642992,-0.00341515,-0.00891438,0.07351145,0.0634091,-0.0102485,-0.01404812,0.07016314,-0.01632295,0.02235912,-0.00077791,0.01157525,-0.07900074,-0.00765513,0.01256301,-0.02207504,-0.03510949,0.05894626,0.0336115,-0.00524265,-0.03422716,-0.04173489,0.00993607,-0.0501501,0.02628437,-0.12475683,-0.03145824,-0.00975926,-0.02786356,-0.0335972,-0.03278069,-0.02535368,-0.00985643,0.02030722,-0.02434244,-0.02620396,-0.03049185,-0.01269356,0.02169885,-0.06634528,-0.02943446,0.01238276,-0.03126049,0.04170865,0.06520762,-0.05438891,0.00456591,0.03625816,-0.02220716,-0.03051137,0.0032369,-0.00844674,-0.05044384,0.03210412,0.01146643,-0.01433461,0.04315694,0.00041996,0.02976932,-0.05349157,0.04786664,-0.03940011,-0.00773227,0.00521381,0.08071895,-0.05769192,0.01667097,0.07745262,-0.06604113,-0.03392769,-0.02290336,0.00143066,-0.05642631,-0.01113301,-0.07569791,-0.00117028,-0.02685769,-0.01449191,0.02503324,0.00883138,-0.01443018,-0.0606829,-0.0163847,0.03334863,-0.02140263,0.00771097,-0.03981094,0.0163617,-0.01191727,-0.01310019,-0.02980179,-0.00556617,0.01073611,-0.04985888,-0.02890895,-0.01701991,0.00259689,-0.00399689,0.02003776,-0.018331,0.05823975,0.01208777,0.02669852,-0.00053992,0.00099182,-0.01851185,0.02458171,-0.0264582,-0.04808034,0.0262617,-0.08186864,-0.00371375,0.0758581,0.01210293,0.00462534,0.01616584,-0.0239312,-0.00266593,0.0229843,-0.00941725,-0.04571975,0.03146137,-0.01728434,0.02526817,-0.00367758,0.00918942,-0.0311173,-0.084979,-0.01481347,0.01194842,0.04534358,0.00164505,-0.02375501,-0.05390718,-0.0303445,-0.02349415,0.04944861,0.04680139,0.01373643,-0.02686925,0.00010459,-0.02710516,-0.05829748,0.05971045,-0.0059599,0.00322865,-0.00517181,0.01318721,0.03228405,0.02654019,0.00810005,-0.02084556,0.02573683,0.03992643,0.01511176,-0.00127441,0.00536844,0.06810384,0.03340272,0.00737849,-0.05830136,0.03279289,0.04252589,0.03544119,0.06832762,-0.0038169,0.05703189,-0.02814566,0.04146082,0.02783508,0.00626164,0.02098577,-0.00561117,-0.00232624,0.0080454,0.02557599,-0.00341851,0.0213378,-0.07695647,0.04915515,0.00101533,0.0432677,0.04381828,-0.0260285,-0.07363016,0.03831445,0.00918404,-0.04582887,-0.0372158,-0.00764575,-0.0215953,0.00198597,-0.0389799,0.06479982,0.05063158,0.01494927,0.06210547,0.00652981,0.03266446,0.06825145,-0.01577663,-0.08633262,0.02351316,-0.00492824,0.01854183,-0.01590995,-0.07179624,0.04213426,0.05459264,0.02471506,0.00767613,-0.00921832,0.03337935,0.03811416,-0.00902252,-0.0189376,0.00158563,0.03204265,-0.0283604,0.0001991,0.02790076,-0.0102715,0.01629685,0.04973826,0.03231456,0.00778058,0.00070927,-0.0422157,0.05249631,0.04839661,0.08140524,-0.00018384,0.02502882,-0.04696618,0.03369085,0.03353172,-0.02869102,-0.01602091,0.03724758,0.01479567,-0.02217637,0.0507035,-0.01561452,0.00880512,-0.0027585,0.01523533,-0.03670456,0.01076895,-0.01497627,0.01495364,-0.0049504,0.01575376,-0.01194864,0.08698823,-0.00917203,-0.00223707,-0.00843611,-0.05781536,-0.00463683,0.04714502,0.01289341,0.01297485,-0.01635775,0.03166444,0.01757797,-0.0092292,0.04013897,-0.00039875,-0.04784856,0.08759655,0.01272963,-0.00746195,-0.01652852,0.01835235,0.05952579,-0.00577022,-0.0550965,-0.10778227,-0.00946173,-0.00406249,0.01469315,0.01385488,0.05684334,-0.0377272,-0.05328364,0.03852574,0.02757361,0.00083656,0.02097461,-0.00228516,-0.03890397,0.01616691,-0.01391923,0.04042697,-0.02694609,0.02675083,0.02054566,0.03446864,0.00133069,-0.02157771,0.03591735,-0.04809046,-0.01695574,-0.03223417,0.01568842,-0.0416455,-0.06255227,0.06143056,0.02238684,0.00978677,-0.04495553,-0.03121623,0.0112048,0.03847813,-0.01817083,0.02676401,0.03971414,-0.04219468,-0.01515073,0.03566354,-0.04744759,-0.04390873,-0.04563426,0.03090568,-0.01444121,0.03784169,0.01670326,-0.03252101,0.06271667,-0.04792061,-0.00337007,0.01937212,0.01804854,0.0106518,0.05832356,0.01358189,-0.01278667,0.01139394,0.00349892,-0.03084875,-0.01098137,0.01001385,0.00540177,-0.05960127,-0.02694001,0.07741424,-0.00451046,0.06555104,0.02741846,-0.02743237,0.00204621,0.01824901,0.02847201,0.00821509,-0.02694806,-0.03195271,-0.00679906,0.03873789,-0.00073337,-0.02113222,-0.03360456,-0.0056001,-0.01264523,-0.02479731,-0.05803656,-0.02026258,-0.0070441,0.02962179,-0.04836618,0.02594843,-0.06606089,0.01521248,-0.02762724,-0.05178118,0.01913381,0.05923881,-0.02938143,-0.02598096,0.00125807,-0.01230569,0.01837742,0.00690825,-0.02037256,0.0920156,-0.02535818,-0.02344546,0.02251873,-0.0762804,0.03081788,-0.05459953,-0.01604984,-0.00927768,0.02059416,-0.04954993,0.04500197,-0.02158304,-0.0260081,-0.02453294,-0.018847,0.0773029,-0.0322095,0.00234312,-0.02971927,0.05665302,0.03761469,-0.06463655,0.00405052,-0.03020436,0.00793418,0.04848704,0.03143379,0.00172772,-0.05150426,-0.0766531,-0.07503635,0.00418823,-0.04410965,0.01646839,0.00803787,0.01950697,-0.04460756,0.01222201,-0.02531302,-0.05160031,0.03871351,0.01050605,-0.00882655,0.01719552,-0.01242845,-0.00251382,0.0567221,-0.00009998,-0.02124581,-0.03017789,-0.03973615,-0.01634319,0.00691389,0.01657496,0.00352333,0.05222308,0.01693693,-0.02998592,0.03022803,-0.0241263,0.00428831,-0.02125994,0.03636828,0.07506105,-0.02204771,0.00085786,0.02820533,0.02047959,-0.02533735,0.00929844,-0.06448698,0.01047225,0.03426287,0.01966674,-0.06716222,-0.06056588,0.0376194,-0.07942914,0.00903232,-0.02370011,0.00799613,-0.02269867,-0.01972573,0.02529714,0.04848278,-0.00614252,-0.02263679,-0.00032646,0.04904067,0.02907841,-0.03871747,-0.04167828,0.02608091,-0.01837724,0.08906538,-0.01728825,0.00241319,0.02781354,-0.01953988,-0.03480518,-0.00408538,0.03212245,-0.03002251,-0.0144793,0.02703942,0.08970247,-0.02563135,-0.00617747,0.01866803,-0.00649123,0.02559304,-0.05297648,-0.01822408,0.0763699,-0.05124637,-0.06147783,-0.10992064,0.04714491,0.06096148,0.00143148,0.00722835,0.02094168,0.04648811,0.04213857,0.01164728,0.02162819,0.0296208,-0.04029921,-0.06903331,0.04590129,-0.03240468,-0.01473408,-0.00895755,-0.01174694,-0.00453313,0.02488478,-0.04100722,-0.09146498,0.01422793,-0.02164275,0.00674819,0.03693408,-0.00942996,-0.01287564,-0.04622279,-0.00224942,0.02994513,0.01542054,0.01578277,-0.05542323,0.02314867,-0.01462406,0.04702301,0.01945986,-0.04993563,0.01828006,0.05404438,0.0010252,0.0327866,0.03642084,0.03625464,-0.05509541,0.0432727,-0.03785269,0.0171922,-0.03439408,-0.02245626,-0.02989638,-0.00399398,0.08831304,-0.01223512,-0.01974905,-0.04408357,-0.04500926,0.03356161,0.05317676,-0.02175368,0.02938466,0.00985822,0.02020598,-0.04687787,-0.02984033,-0.02281085,0.01030821,0.03516749,0.01736711,0.04263991,0.01736868,-0.01207579,-0.03275775,-0.02852684,-0.0172091,0.00481797,-0.07615817,0.00088687,0.07437783,-0.03042782,0.06078041,-0.03841238,0.03277289,0.02429422,-0.07849824,-0.03910245,0.02532844,-0.09588994,-0.00103523,0.08090204,0.01181702,0.09891443,0.01101152,-0.06363501,0.04374249,0.0361743,-0.01547702,-0.04002019,-0.06548761,0.04168548,-0.02953845,-0.03570409,-0.00163517,0.01874864,0.02844482,0.02748075,0.0101002,0.00343033,0.05038278,-0.00490431,0.01711803,-0.03384856,-0.00683842,0.02831544,0.01273395,0.02809625,-0.01553737,0.06257328,-0.05782858,-0.04078825,0.05872235,-0.03938561,0.01154184,-0.01330054,0.02443084,-0.00230818,-0.04209853,-0.00199642,0.004977,0.02588682,-0.00063357,0.00565976,-0.03491996,-0.00700693,0.0072324,-0.01143965,-0.00614353,0.00732467,-0.03419194,0.02258095,-0.05471036,-0.03051162,-0.03719032,-0.02165184,-0.04420958,-0.05154873,0.0584482,0.017221,-0.03990737,-0.03884684,-0.00152566,-0.05361895,-0.01173033,0.03751722,-0.02710925,0.05635455,0.04240347,0.02984456,0.03169418,0.01443701,0.01590716,-0.03122812,0.0000721,-0.0139226,0.02874506,0.03904405,-0.02135876,0.00372521,-0.08808766,0.00869506,-0.0211715,0.00159445,-0.0041877,0.0140216,-0.00208761,0.010519,-0.00888583,0.00538596,-0.01858236,-0.00492667,-0.06332876,0.05665756,0.02508771,-0.01937884,-0.02741437,-0.00505077,0.0211784,0.05687918,0.01952713,-0.03579088,0.0061965,0.03196979,0.00262242,-0.02542052,0.01897802,0.00596386,0.02041193,0.0217368,0.00769778,0.03899889,0.02399282,0.01883668,0.01191146,0.07811685,0.00480116,0.0199463,-0.04706775,-0.00871578,-0.01349331,0.00987928,-0.07246393,-0.03511283,0.02530852,0.06045335,0.01659804,-0.05237213,0.00563502,-0.00480171,0.05907249,-0.02185085,-0.03946082,-0.0209535,-0.02285053,0.01185431,-0.03231428,0.00515315,-0.02721037,0.04311573,0.02008905,0.02753969,0.00242495,0.01016019,-0.01679325,0.04483803,0.01271884,0.0313694,0.00358132,-0.02939493,8e-7,0.0035227,-0.0039156,0.0638413,-0.00475299,-0.05862638,-0.07714394,-0.01115675,0.04976912,-0.01117666],"last_embed":{"tokens":152,"hash":"en8cay"}}},"last_read":{"hash":"en8cay","at":1751079987520},"class_name":"SmartSource","outlinks":[{"title":"docker","target":"docker","line":16}],"metadata":{"官网":["https://github.com/imsyy/DailyHotApi"],"tags":["计算机网络/数据接口"]},"blocks":{"#---frontmatter---":[1,6],"#简介":[8,13],"#简介#{1}":[9,9],"#简介#{2}":[10,11],"#简介#{3}":[12,13],"#部署":[14,27],"#部署#[[docker]]部署":[16,27],"#部署#[[docker]]部署#{1}":[17,19],"#部署#[[docker]]部署#{2}":[20,23],"#部署#[[docker]]部署#{3}":[21,23],"#部署#[[docker]]部署#{4}":[24,24],"#部署#[[docker]]部署#{5}":[25,27]},"last_import":{"mtime":1715691958773,"size":464,"at":1749024987540,"hash":"en8cay"},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络接口相关/项目/DailyHotApi.md","last_embed":{"hash":"en8cay","at":1751079987520}},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络接口相关/项目/DailyHotApi.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络接口相关/项目/DailyHotApi.md#---frontmatter---","lines":[1,6],"size":73,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络接口相关/项目/DailyHotApi.md#简介": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络接口相关/项目/DailyHotApi.md#简介","lines":[8,13],"size":76,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络接口相关/项目/DailyHotApi.md#简介#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络接口相关/项目/DailyHotApi.md#简介#{1}","lines":[9,9],"size":18,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络接口相关/项目/DailyHotApi.md#简介#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络接口相关/项目/DailyHotApi.md#简介#{2}","lines":[10,11],"size":47,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络接口相关/项目/DailyHotApi.md#简介#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络接口相关/项目/DailyHotApi.md#简介#{3}","lines":[12,13],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络接口相关/项目/DailyHotApi.md#部署": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络接口相关/项目/DailyHotApi.md#部署","lines":[14,27],"size":158,"outlinks":[{"title":"docker","target":"docker","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络接口相关/项目/DailyHotApi.md#部署#[[docker]]部署": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络接口相关/项目/DailyHotApi.md#部署#[[docker]]部署","lines":[16,27],"size":152,"outlinks":[{"title":"docker","target":"docker","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络接口相关/项目/DailyHotApi.md#部署#[[docker]]部署#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络接口相关/项目/DailyHotApi.md#部署#[[docker]]部署#{1}","lines":[17,19],"size":53,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络接口相关/项目/DailyHotApi.md#部署#[[docker]]部署#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络接口相关/项目/DailyHotApi.md#部署#[[docker]]部署#{2}","lines":[20,23],"size":70,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络接口相关/项目/DailyHotApi.md#部署#[[docker]]部署#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络接口相关/项目/DailyHotApi.md#部署#[[docker]]部署#{3}","lines":[21,23],"size":65,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络接口相关/项目/DailyHotApi.md#部署#[[docker]]部署#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络接口相关/项目/DailyHotApi.md#部署#[[docker]]部署#{4}","lines":[24,24],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络接口相关/项目/DailyHotApi.md#部署#[[docker]]部署#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络接口相关/项目/DailyHotApi.md#部署#[[docker]]部署#{5}","lines":[25,27],"size":5,"outlinks":[],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络接口相关/项目/DailyHotApi.md": {"path":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络接口相关/项目/DailyHotApi.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09321903,-0.01510941,-0.04319102,-0.0027043,0.00771928,-0.04936912,-0.02117136,-0.02270431,0.00959998,0.00896873,0.03130045,-0.06365745,0.04826317,0.06200438,0.0270522,0.03048115,-0.00718239,-0.00291616,-0.02895824,0.00514433,0.07942651,-0.0687142,-0.03854804,-0.08621299,-0.04920689,0.03216539,-0.00326781,-0.02491527,0.01087181,-0.15255007,0.01470562,0.02602292,0.02676342,0.00748106,0.00833287,0.00811749,0.01193511,0.03665524,-0.00410751,0.02928127,0.04792891,0.02702134,0.02240626,0.01043362,-0.05959798,-0.03409408,-0.08236105,0.02404735,0.01867139,-0.02468473,-0.02602719,-0.02000701,-0.02750022,-0.03046199,-0.02075089,-0.00246156,0.01773413,0.05405753,0.04083636,-0.01392596,0.08756959,0.00882138,-0.22092445,0.0493892,0.04494112,-0.04031332,-0.00303625,0.01606575,0.05285547,0.00812571,-0.05492095,0.03546895,0.00741718,0.08692785,0.06715105,-0.02126122,0.04383149,0.00097922,-0.01114195,-0.03284151,-0.03117329,0.04334934,-0.06242201,0.00488403,0.02429899,0.04764017,-0.04457907,-0.01141697,0.0613882,-0.00416784,-0.01096823,-0.06639662,-0.00846909,0.03108103,0.03937517,0.02550148,0.02109469,0.03847464,-0.12651348,0.12246355,-0.0635528,0.01935934,0.01642149,-0.06686936,0.01138208,-0.03067175,-0.03836408,-0.05104561,-0.02250844,0.0031397,-0.02842192,-0.00061188,0.07825009,-0.00922597,0.03417466,0.01180301,0.02678078,0.01082259,-0.04734768,-0.00795669,-0.01365267,-0.01889928,0.09001886,-0.00330974,-0.02236154,-0.03015761,0.05880494,0.05360379,0.01483949,0.01878979,0.06163552,-0.01515085,-0.0691757,-0.03650529,-0.00483181,0.00596207,-0.01830192,-0.03368203,0.01560354,-0.02344429,-0.02776133,-0.08301417,0.00495804,-0.10271544,-0.05281188,0.07974075,-0.04935167,0.01138196,0.08387947,-0.07571953,-0.01492126,0.07911205,-0.02867418,-0.05076287,-0.00922318,-0.02752893,0.06945497,0.15955929,0.01142394,-0.02296163,-0.02585768,0.01716177,-0.12166774,0.10240293,-0.02689934,-0.06638096,-0.02170605,0.01273566,0.02560723,-0.01561784,0.03232923,0.02078141,0.01098261,-0.00787152,0.09937809,-0.03594171,0.00944392,-0.03494059,0.05122638,0.01833456,0.04209588,-0.07744963,-0.05196159,0.02977221,0.01055266,-0.07466328,-0.00571008,-0.060257,-0.00760743,-0.04500659,-0.05592449,0.01129021,-0.01842964,0.05715566,-0.02706159,-0.07000369,0.0333317,0.00737342,0.01969929,-0.03252616,0.09259479,-0.00136446,-0.01671843,-0.01167641,-0.01568997,-0.05276164,0.0160581,-0.01187821,0.02763887,0.01786376,-0.00879076,0.01227833,0.03284995,0.02640096,-0.03988539,0.00572191,0.01410686,-0.00766935,0.01489279,0.04236464,0.00084631,0.00560087,-0.09790023,-0.2125129,-0.05223906,0.00820725,-0.02459243,0.0111284,-0.04446896,0.02721221,-0.00435076,0.08569964,0.08877561,0.08900034,-0.04120646,-0.03429795,-0.0181153,0.04856242,0.02267605,0.01742494,0.00999942,-0.01829752,-0.0300001,-0.02153437,0.02390613,-0.02425309,-0.02831891,0.05777114,-0.02654827,0.1588289,0.04380318,0.01158041,0.08155232,0.03780227,0.00946695,-0.01091321,-0.13457091,0.00980843,0.01543728,-0.0256363,-0.06522692,-0.0202521,0.00299058,-0.02431803,0.0608523,-0.06214263,-0.09688576,-0.02553394,-0.02867799,0.01805167,-0.0170648,-0.02631423,0.0387624,-0.00920716,0.03479967,0.01148193,-0.02501901,-0.01842273,-0.04871688,-0.02904382,-0.01129291,-0.02279043,0.01819553,-0.02007579,0.03396774,0.00044486,0.02289975,-0.00607606,-0.06230365,0.00399979,-0.01411239,-0.04489118,0.00799203,0.00988976,0.13779958,-0.00580572,-0.03202844,0.0823768,-0.00685487,0.01800813,-0.04172854,0.02627405,0.01677343,0.01125591,0.0302519,0.03721542,0.04409171,0.00994047,0.03868287,0.01668568,-0.01224991,0.05755366,-0.05722905,-0.07257297,0.01976309,-0.03663802,0.02810656,0.07448879,-0.00204711,-0.28818545,0.03516604,-0.03957776,0.00025004,0.0338545,0.05968674,0.06775501,0.01793732,-0.03990512,0.04010027,-0.05060015,0.05137657,0.01130612,-0.04130684,-0.03724062,-0.04391523,0.0644642,-0.01554567,0.05001329,-0.01505011,-0.02343456,0.05323033,0.19697984,-0.00255573,0.03218891,0.01020365,0.00517241,0.05420797,0.02055866,0.03531367,0.02309841,-0.06269001,0.01423667,-0.05447569,0.06240891,0.05786353,-0.01875048,0.01168228,0.00155983,-0.01331181,-0.04044661,0.07951874,-0.07209256,0.05107408,0.13630809,-0.00611694,-0.01199132,-0.07483703,-0.01559532,0.06481073,0.02048312,-0.01992953,-0.01045262,-0.03719772,0.02389039,0.07510363,0.0476363,-0.02941445,-0.03624319,-0.00441855,0.00190611,-0.00729257,0.08718215,0.05622859,0.08390413],"last_embed":{"hash":"a28659b550e48b1bbf665a84473cdd73105f16d0f9450d29c431459a25322606","tokens":225}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[0.00927653,-0.00373516,-0.05317235,0.01463033,-0.06919584,0.03705353,-0.00716606,-0.0202867,-0.00911855,0.00329319,-0.00407193,0.02103234,0.01688584,-0.0052702,0.06332081,0.05194159,-0.07416564,0.07103323,-0.05717979,-0.04642992,-0.00341515,-0.00891438,0.07351145,0.0634091,-0.0102485,-0.01404812,0.07016314,-0.01632295,0.02235912,-0.00077791,0.01157525,-0.07900074,-0.00765513,0.01256301,-0.02207504,-0.03510949,0.05894626,0.0336115,-0.00524265,-0.03422716,-0.04173489,0.00993607,-0.0501501,0.02628437,-0.12475683,-0.03145824,-0.00975926,-0.02786356,-0.0335972,-0.03278069,-0.02535368,-0.00985643,0.02030722,-0.02434244,-0.02620396,-0.03049185,-0.01269356,0.02169885,-0.06634528,-0.02943446,0.01238276,-0.03126049,0.04170865,0.06520762,-0.05438891,0.00456591,0.03625816,-0.02220716,-0.03051137,0.0032369,-0.00844674,-0.05044384,0.03210412,0.01146643,-0.01433461,0.04315694,0.00041996,0.02976932,-0.05349157,0.04786664,-0.03940011,-0.00773227,0.00521381,0.08071895,-0.05769192,0.01667097,0.07745262,-0.06604113,-0.03392769,-0.02290336,0.00143066,-0.05642631,-0.01113301,-0.07569791,-0.00117028,-0.02685769,-0.01449191,0.02503324,0.00883138,-0.01443018,-0.0606829,-0.0163847,0.03334863,-0.02140263,0.00771097,-0.03981094,0.0163617,-0.01191727,-0.01310019,-0.02980179,-0.00556617,0.01073611,-0.04985888,-0.02890895,-0.01701991,0.00259689,-0.00399689,0.02003776,-0.018331,0.05823975,0.01208777,0.02669852,-0.00053992,0.00099182,-0.01851185,0.02458171,-0.0264582,-0.04808034,0.0262617,-0.08186864,-0.00371375,0.0758581,0.01210293,0.00462534,0.01616584,-0.0239312,-0.00266593,0.0229843,-0.00941725,-0.04571975,0.03146137,-0.01728434,0.02526817,-0.00367758,0.00918942,-0.0311173,-0.084979,-0.01481347,0.01194842,0.04534358,0.00164505,-0.02375501,-0.05390718,-0.0303445,-0.02349415,0.04944861,0.04680139,0.01373643,-0.02686925,0.00010459,-0.02710516,-0.05829748,0.05971045,-0.0059599,0.00322865,-0.00517181,0.01318721,0.03228405,0.02654019,0.00810005,-0.02084556,0.02573683,0.03992643,0.01511176,-0.00127441,0.00536844,0.06810384,0.03340272,0.00737849,-0.05830136,0.03279289,0.04252589,0.03544119,0.06832762,-0.0038169,0.05703189,-0.02814566,0.04146082,0.02783508,0.00626164,0.02098577,-0.00561117,-0.00232624,0.0080454,0.02557599,-0.00341851,0.0213378,-0.07695647,0.04915515,0.00101533,0.0432677,0.04381828,-0.0260285,-0.07363016,0.03831445,0.00918404,-0.04582887,-0.0372158,-0.00764575,-0.0215953,0.00198597,-0.0389799,0.06479982,0.05063158,0.01494927,0.06210547,0.00652981,0.03266446,0.06825145,-0.01577663,-0.08633262,0.02351316,-0.00492824,0.01854183,-0.01590995,-0.07179624,0.04213426,0.05459264,0.02471506,0.00767613,-0.00921832,0.03337935,0.03811416,-0.00902252,-0.0189376,0.00158563,0.03204265,-0.0283604,0.0001991,0.02790076,-0.0102715,0.01629685,0.04973826,0.03231456,0.00778058,0.00070927,-0.0422157,0.05249631,0.04839661,0.08140524,-0.00018384,0.02502882,-0.04696618,0.03369085,0.03353172,-0.02869102,-0.01602091,0.03724758,0.01479567,-0.02217637,0.0507035,-0.01561452,0.00880512,-0.0027585,0.01523533,-0.03670456,0.01076895,-0.01497627,0.01495364,-0.0049504,0.01575376,-0.01194864,0.08698823,-0.00917203,-0.00223707,-0.00843611,-0.05781536,-0.00463683,0.04714502,0.01289341,0.01297485,-0.01635775,0.03166444,0.01757797,-0.0092292,0.04013897,-0.00039875,-0.04784856,0.08759655,0.01272963,-0.00746195,-0.01652852,0.01835235,0.05952579,-0.00577022,-0.0550965,-0.10778227,-0.00946173,-0.00406249,0.01469315,0.01385488,0.05684334,-0.0377272,-0.05328364,0.03852574,0.02757361,0.00083656,0.02097461,-0.00228516,-0.03890397,0.01616691,-0.01391923,0.04042697,-0.02694609,0.02675083,0.02054566,0.03446864,0.00133069,-0.02157771,0.03591735,-0.04809046,-0.01695574,-0.03223417,0.01568842,-0.0416455,-0.06255227,0.06143056,0.02238684,0.00978677,-0.04495553,-0.03121623,0.0112048,0.03847813,-0.01817083,0.02676401,0.03971414,-0.04219468,-0.01515073,0.03566354,-0.04744759,-0.04390873,-0.04563426,0.03090568,-0.01444121,0.03784169,0.01670326,-0.03252101,0.06271667,-0.04792061,-0.00337007,0.01937212,0.01804854,0.0106518,0.05832356,0.01358189,-0.01278667,0.01139394,0.00349892,-0.03084875,-0.01098137,0.01001385,0.00540177,-0.05960127,-0.02694001,0.07741424,-0.00451046,0.06555104,0.02741846,-0.02743237,0.00204621,0.01824901,0.02847201,0.00821509,-0.02694806,-0.03195271,-0.00679906,0.03873789,-0.00073337,-0.02113222,-0.03360456,-0.0056001,-0.01264523,-0.02479731,-0.05803656,-0.02026258,-0.0070441,0.02962179,-0.04836618,0.02594843,-0.06606089,0.01521248,-0.02762724,-0.05178118,0.01913381,0.05923881,-0.02938143,-0.02598096,0.00125807,-0.01230569,0.01837742,0.00690825,-0.02037256,0.0920156,-0.02535818,-0.02344546,0.02251873,-0.0762804,0.03081788,-0.05459953,-0.01604984,-0.00927768,0.02059416,-0.04954993,0.04500197,-0.02158304,-0.0260081,-0.02453294,-0.018847,0.0773029,-0.0322095,0.00234312,-0.02971927,0.05665302,0.03761469,-0.06463655,0.00405052,-0.03020436,0.00793418,0.04848704,0.03143379,0.00172772,-0.05150426,-0.0766531,-0.07503635,0.00418823,-0.04410965,0.01646839,0.00803787,0.01950697,-0.04460756,0.01222201,-0.02531302,-0.05160031,0.03871351,0.01050605,-0.00882655,0.01719552,-0.01242845,-0.00251382,0.0567221,-0.00009998,-0.02124581,-0.03017789,-0.03973615,-0.01634319,0.00691389,0.01657496,0.00352333,0.05222308,0.01693693,-0.02998592,0.03022803,-0.0241263,0.00428831,-0.02125994,0.03636828,0.07506105,-0.02204771,0.00085786,0.02820533,0.02047959,-0.02533735,0.00929844,-0.06448698,0.01047225,0.03426287,0.01966674,-0.06716222,-0.06056588,0.0376194,-0.07942914,0.00903232,-0.02370011,0.00799613,-0.02269867,-0.01972573,0.02529714,0.04848278,-0.00614252,-0.02263679,-0.00032646,0.04904067,0.02907841,-0.03871747,-0.04167828,0.02608091,-0.01837724,0.08906538,-0.01728825,0.00241319,0.02781354,-0.01953988,-0.03480518,-0.00408538,0.03212245,-0.03002251,-0.0144793,0.02703942,0.08970247,-0.02563135,-0.00617747,0.01866803,-0.00649123,0.02559304,-0.05297648,-0.01822408,0.0763699,-0.05124637,-0.06147783,-0.10992064,0.04714491,0.06096148,0.00143148,0.00722835,0.02094168,0.04648811,0.04213857,0.01164728,0.02162819,0.0296208,-0.04029921,-0.06903331,0.04590129,-0.03240468,-0.01473408,-0.00895755,-0.01174694,-0.00453313,0.02488478,-0.04100722,-0.09146498,0.01422793,-0.02164275,0.00674819,0.03693408,-0.00942996,-0.01287564,-0.04622279,-0.00224942,0.02994513,0.01542054,0.01578277,-0.05542323,0.02314867,-0.01462406,0.04702301,0.01945986,-0.04993563,0.01828006,0.05404438,0.0010252,0.0327866,0.03642084,0.03625464,-0.05509541,0.0432727,-0.03785269,0.0171922,-0.03439408,-0.02245626,-0.02989638,-0.00399398,0.08831304,-0.01223512,-0.01974905,-0.04408357,-0.04500926,0.03356161,0.05317676,-0.02175368,0.02938466,0.00985822,0.02020598,-0.04687787,-0.02984033,-0.02281085,0.01030821,0.03516749,0.01736711,0.04263991,0.01736868,-0.01207579,-0.03275775,-0.02852684,-0.0172091,0.00481797,-0.07615817,0.00088687,0.07437783,-0.03042782,0.06078041,-0.03841238,0.03277289,0.02429422,-0.07849824,-0.03910245,0.02532844,-0.09588994,-0.00103523,0.08090204,0.01181702,0.09891443,0.01101152,-0.06363501,0.04374249,0.0361743,-0.01547702,-0.04002019,-0.06548761,0.04168548,-0.02953845,-0.03570409,-0.00163517,0.01874864,0.02844482,0.02748075,0.0101002,0.00343033,0.05038278,-0.00490431,0.01711803,-0.03384856,-0.00683842,0.02831544,0.01273395,0.02809625,-0.01553737,0.06257328,-0.05782858,-0.04078825,0.05872235,-0.03938561,0.01154184,-0.01330054,0.02443084,-0.00230818,-0.04209853,-0.00199642,0.004977,0.02588682,-0.00063357,0.00565976,-0.03491996,-0.00700693,0.0072324,-0.01143965,-0.00614353,0.00732467,-0.03419194,0.02258095,-0.05471036,-0.03051162,-0.03719032,-0.02165184,-0.04420958,-0.05154873,0.0584482,0.017221,-0.03990737,-0.03884684,-0.00152566,-0.05361895,-0.01173033,0.03751722,-0.02710925,0.05635455,0.04240347,0.02984456,0.03169418,0.01443701,0.01590716,-0.03122812,0.0000721,-0.0139226,0.02874506,0.03904405,-0.02135876,0.00372521,-0.08808766,0.00869506,-0.0211715,0.00159445,-0.0041877,0.0140216,-0.00208761,0.010519,-0.00888583,0.00538596,-0.01858236,-0.00492667,-0.06332876,0.05665756,0.02508771,-0.01937884,-0.02741437,-0.00505077,0.0211784,0.05687918,0.01952713,-0.03579088,0.0061965,0.03196979,0.00262242,-0.02542052,0.01897802,0.00596386,0.02041193,0.0217368,0.00769778,0.03899889,0.02399282,0.01883668,0.01191146,0.07811685,0.00480116,0.0199463,-0.04706775,-0.00871578,-0.01349331,0.00987928,-0.07246393,-0.03511283,0.02530852,0.06045335,0.01659804,-0.05237213,0.00563502,-0.00480171,0.05907249,-0.02185085,-0.03946082,-0.0209535,-0.02285053,0.01185431,-0.03231428,0.00515315,-0.02721037,0.04311573,0.02008905,0.02753969,0.00242495,0.01016019,-0.01679325,0.04483803,0.01271884,0.0313694,0.00358132,-0.02939493,8e-7,0.0035227,-0.0039156,0.0638413,-0.00475299,-0.05862638,-0.07714394,-0.01115675,0.04976912,-0.01117666],"last_embed":{"tokens":152,"hash":"en8cay"}}},"last_read":{"hash":"en8cay","at":1751251727080},"class_name":"SmartSource","outlinks":[{"title":"docker","target":"docker","line":16}],"metadata":{"官网":["https://github.com/imsyy/DailyHotApi"],"tags":["计算机网络/数据接口"]},"blocks":{"#---frontmatter---":[1,6],"#简介":[8,13],"#简介#{1}":[9,9],"#简介#{2}":[10,11],"#简介#{3}":[12,13],"#部署":[14,27],"#部署#[[docker]]部署":[16,27],"#部署#[[docker]]部署#{1}":[17,19],"#部署#[[docker]]部署#{2}":[20,23],"#部署#[[docker]]部署#{3}":[21,23],"#部署#[[docker]]部署#{4}":[24,24],"#部署#[[docker]]部署#{5}":[25,27]},"last_import":{"mtime":1715691958773,"size":464,"at":1749024987540,"hash":"en8cay"},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络接口相关/项目/DailyHotApi.md","last_embed":{"hash":"en8cay","at":1751251727080}},