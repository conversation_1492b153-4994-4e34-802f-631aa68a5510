"smart_sources:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md": {"path":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08724637,-0.01712776,0.00622108,-0.01810723,-0.01109454,-0.03609508,0.00775222,0.03974966,0.03826688,0.00139955,0.0425317,-0.07425436,0.05522698,0.04360349,0.04418913,0.02499702,-0.03188355,0.02312233,0.02094044,-0.01791669,0.06953008,-0.03395885,-0.00522889,-0.05087426,-0.00960883,0.02261707,0.01038372,-0.01825826,-0.02061152,-0.15642749,0.01673094,0.03682297,-0.01432461,0.01508829,0.00728263,-0.03313903,0.02059768,0.04494405,0.00613581,0.03879894,-0.01399241,0.03249789,-0.02460713,-0.05729464,-0.04096748,-0.04588619,-0.06249367,0.00042499,0.01417592,-0.02310047,-0.03825961,-0.04091617,-0.02704895,-0.00135651,-0.04144034,-0.0207651,0.04834353,0.01510635,0.06305239,-0.06506572,0.04272058,0.01823858,-0.19222954,0.05268878,0.05156079,-0.01103833,-0.02733426,0.01864034,0.02836203,0.03351474,-0.05926237,0.03271533,-0.0306049,0.06183349,0.055256,0.01345738,-0.01073928,-0.00274723,-0.04988107,-0.04931198,-0.04211506,0.08338591,-0.0167302,0.00795015,-0.01769333,0.02078789,-0.03584467,-0.03733831,-0.03020994,-0.01106426,0.02316944,-0.02735364,0.00493256,0.02990892,-0.02693903,0.0355945,0.04251804,0.05904138,-0.05956106,0.0983876,-0.0475369,-0.02593284,-0.01407835,-0.05257883,0.01064117,-0.03080836,0.00731717,-0.03534373,-0.0366266,0.01071291,-0.06091136,-0.0515299,0.07261359,-0.01422463,0.04132902,0.02314451,0.00333976,0.007347,-0.03285677,-0.03100991,-0.00958715,0.03633849,0.11392616,-0.01771647,-0.03467441,-0.03413652,0.05474388,0.07822979,0.03853508,0.06194838,0.09584948,-0.01098349,-0.04295879,-0.00560094,-0.0305009,-0.0232054,-0.020557,-0.01519534,-0.0044806,0.01733623,-0.01525567,-0.07296874,-0.01436148,-0.082944,-0.1082439,0.06416029,-0.05656152,0.00164089,0.06320585,-0.02749099,-0.002518,0.05168429,0.00506798,-0.01018093,-0.01392642,0.0109089,0.0700566,0.19531992,-0.00075543,0.01526161,-0.0370181,0.0448829,-0.10202323,0.17319171,-0.00799922,-0.05603538,-0.0362813,0.00610938,0.00204706,-0.02518248,-0.00575298,-0.01076435,0.00902627,0.01405789,0.08472129,-0.00770686,-0.01896726,-0.04120141,0.01135592,-0.01202392,0.11012056,-0.0113529,-0.05422512,0.04156499,0.01818635,-0.06528001,-0.02330856,-0.05033537,0.00992353,-0.03107966,-0.09479073,0.00849911,-0.03546757,0.00493947,-0.05049594,-0.08627678,0.02376637,0.01720806,0.06813808,-0.0207367,0.06973133,0.01412997,-0.03540853,0.01043813,-0.06961189,-0.03115713,-0.00575445,-0.02506849,0.02798167,0.01628906,0.03198072,0.0287574,-0.01065895,0.00999181,-0.0008515,0.01156455,0.00626323,0.01764594,0.01321569,0.07262078,0.01048846,-0.03693186,-0.0765955,-0.20398916,-0.03368405,-0.00012883,-0.0286303,0.00977312,-0.04697042,0.01551913,0.019769,0.0748897,0.11312833,0.06083174,0.04206733,-0.05700119,-0.04238202,0.00745244,0.01440259,0.00722704,-0.00902755,-0.04287016,-0.00503566,-0.00449329,0.0072456,-0.00801946,-0.01935654,0.07002995,-0.03566155,0.12400948,0.03254048,0.01432632,0.05305092,0.04571632,0.02824083,-0.0007936,-0.1048982,0.01977704,0.03805483,-0.04199379,-0.02841483,-0.00475156,-0.03784272,-0.02275095,0.01249042,-0.06637166,-0.09711421,-0.02796665,0.0008956,-0.05085223,0.0113444,0.02627326,0.03152167,-0.00691729,-0.00225488,-0.0028322,-0.03913874,0.0110963,-0.02812904,-0.0324732,-0.03246793,-0.00284097,0.04869638,0.01903119,0.01832795,0.01235732,0.01682756,-0.02089146,-0.01377412,-0.007327,-0.00825496,-0.04996236,0.00615629,-0.00701055,0.17470868,0.00818109,-0.02663528,0.03293769,-0.02127105,-0.01263733,-0.05197439,0.02915197,0.02215071,0.03901146,0.01487483,0.05933949,-0.01437033,-0.03026688,0.00004028,0.03527082,-0.04049971,0.08749395,-0.04478754,-0.06136525,-0.00360673,-0.051628,0.03628283,0.09834993,-0.00092589,-0.30271584,0.05119798,-0.0253246,-0.02423066,0.08413263,0.06957407,0.05021956,0.0078436,-0.07046361,-0.00857764,-0.02851162,0.06370929,-0.00070709,-0.02342376,-0.00208331,-0.03134793,0.05607459,-0.02521218,0.05519891,-0.0201476,-0.01878301,0.02033625,0.19862093,-0.02989537,0.07641833,0.01265869,-0.006239,0.04180733,0.06381281,0.03156337,-0.0159468,-0.02857518,0.02782427,-0.05543601,0.02998893,0.05187262,-0.04824907,0.0116334,0.03277433,-0.00183964,-0.04841967,0.06211989,-0.08634375,0.00266155,0.07368586,0.03736637,-0.01457455,-0.06003308,-0.00486519,0.07609996,0.02684638,0.01977077,-0.03606332,-0.02834049,0.05060156,0.07118034,0.0228727,-0.00636362,-0.07363832,-0.03085767,-0.00946409,-0.01415071,0.10820097,0.117121,0.0764749],"last_embed":{"hash":"1506fb0deb54876329a4ccb78716405ecee90437fb5dc8063b70703a864cac05","tokens":468}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.02105616,-0.0201768,-0.00411567,-0.04291137,-0.04377816,-0.02582652,0.02843606,-0.03709984,-0.03493004,-0.03462865,0.02500341,0.0297763,0.00778544,0.02423665,0.01720417,0.10264116,-0.0122378,0.03675031,-0.01030821,-0.03710086,-0.05052746,-0.0136772,0.00008924,0.02372085,-0.00117535,-0.0193504,-0.04375771,-0.06541636,-0.01921622,0.04373654,0.0383984,-0.04418598,0.00035178,0.02995978,-0.01799092,0.0024211,-0.01211301,0.01645958,-0.02583336,-0.01992876,-0.04106061,-0.01080191,-0.05211771,0.00645166,-0.05602819,0.00049288,-0.00420785,-0.07462903,-0.01457968,0.014062,-0.01519095,0.0156724,0.00655392,0.01980577,0.00247321,0.02990308,-0.03670131,-0.06810364,-0.01905462,-0.11267372,0.00051731,-0.05913945,0.04896908,0.00446985,-0.00491788,0.04260334,0.02743905,-0.00828967,-0.06034052,-0.00761102,-0.00594923,-0.05463341,0.0402269,0.01406258,-0.01879204,-0.00450989,0.00930711,0.00540139,0.00021231,0.00848601,-0.01770261,0.02020093,0.06848234,0.07766457,-0.05087748,0.00222175,0.0276389,-0.0199744,-0.03487617,0.01979414,0.02979409,-0.04185282,-0.06068958,-0.0418308,0.0272475,-0.01538693,0.02226787,0.00438642,0.03194245,-0.04037462,-0.03631495,-0.00422656,-0.00787511,-0.02670546,0.01307743,-0.00810292,-0.0225222,0.06466981,-0.01300646,-0.03964024,-0.01788426,-0.02236076,-0.0071642,0.00129258,-0.0200881,0.0130377,-0.03360254,-0.02569638,0.06353387,0.07467084,0.01849815,0.01307547,-0.01935711,-0.00950731,-0.04168214,0.0273167,-0.03723314,0.04942643,0.00024749,-0.06318083,-0.02349165,0.02634976,-0.01181366,-0.00141704,0.09420972,-0.01405174,0.05237504,-0.03173099,-0.03015789,-0.00373493,0.01124955,-0.01905752,0.04474526,0.01586917,-0.00917967,-0.02711391,-0.04181734,-0.0400655,-0.05394822,0.04526369,0.0622058,-0.01027711,-0.03641134,-0.04150479,-0.03898057,0.0180934,-0.02597617,-0.05122799,-0.01215574,-0.04860832,-0.04582198,-0.02219461,0.02980259,-0.00318451,-0.00894112,0.01923415,0.0118131,-0.02192854,-0.02129483,-0.03601057,-0.00095762,0.01952245,-0.0074718,-0.0006747,0.03264514,-0.00441032,0.07553811,0.05003816,-0.03206777,-0.03435935,0.06527474,0.00997972,-0.01022817,0.06640385,-0.00962387,0.05344373,0.00710347,0.06448376,0.02654378,0.02453995,0.04432255,0.03488073,-0.01485428,0.00614885,0.03895007,0.00115682,-0.00910341,-0.05756878,0.03943618,0.01390888,-0.02025213,-0.02285914,0.02178113,-0.09273964,0.09020026,0.02880675,-0.07959447,-0.03768869,-0.02521581,0.03093277,-0.04391043,-0.00763749,0.04425224,0.03816525,-0.02101072,0.01979843,0.00461431,0.00905905,-0.01262811,0.00746222,-0.00238016,0.00645567,-0.00538015,-0.02096411,-0.00234294,-0.0037657,0.04019079,0.034671,-0.01036242,0.0148541,0.04987252,0.06844369,0.01464778,-0.04885656,-0.04417194,0.03221456,-0.02699433,0.04192777,0.04815573,0.01148638,-0.03836644,0.02363698,0.00531137,0.04061199,-0.04452608,-0.03245727,0.0039171,0.00117314,0.00342112,0.03912393,0.08372061,0.054335,-0.01388936,-0.03097877,-0.0445213,-0.0854172,-0.03410279,0.03230133,-0.04749995,-0.01886003,0.02111593,-0.00229337,0.00025401,0.00844484,0.0372026,-0.04679154,-0.00039128,0.03958298,-0.02799999,-0.02967836,0.0154924,0.02096883,0.02281681,-0.00349161,-0.05126885,0.00211296,-0.05159273,-0.03653683,0.05464029,-0.0112072,0.04465111,-0.01264284,-0.00809691,-0.00416425,-0.01075095,0.05871848,0.00426384,-0.05045689,0.04642477,0.02596509,-0.00652505,-0.00111497,-0.03330483,0.03902162,-0.01955735,-0.03837187,-0.00695007,0.02354482,-0.00190076,-0.03087462,0.00533956,-0.04073728,-0.00910692,-0.0240758,0.06631002,0.06372546,0.00259104,-0.02003933,-0.058121,-0.01809706,-0.06062837,-0.02677005,0.04475503,-0.0028975,0.04028654,0.01488,0.06169734,-0.00418167,-0.00800368,-0.07963086,-0.04890757,0.02901636,-0.06006689,0.03984212,0.005885,-0.07654929,0.05707422,0.04449177,-0.02067421,0.01527626,-0.04606316,0.02762716,-0.06339672,-0.01989518,-0.01798506,0.02459311,-0.05085233,-0.05104991,0.02027917,-0.02275782,0.0807871,-0.05514918,0.03560971,-0.00882675,-0.01424378,0.06976052,-0.00432705,0.05559274,-0.01271594,0.01634723,-0.01700724,0.01817126,-0.00596716,-0.00489024,0.02517816,0.04528187,-0.01102328,-0.05495109,-0.0483842,-0.01139497,0.01516277,-0.03511048,-0.0253048,-0.03440493,-0.05579771,-0.02858939,0.06320789,0.09059582,-0.00723029,0.01507091,-0.02757959,-0.00951146,0.00763154,0.02161636,-0.05953695,-0.01389041,-0.01102666,0.04710318,0.04397534,0.03247621,-0.0715855,0.00217141,-0.03497795,-0.05453541,-0.02531714,0.02765095,-0.04208731,0.03133913,-0.0106186,-0.00717868,0.03146004,0.039421,-0.0380417,0.01934198,0.08646144,-0.02754675,-0.01081909,0.01613996,0.03207965,0.002597,-0.00129103,0.01704509,-0.00713165,-0.00909554,0.03034872,0.0452806,-0.03782473,0.02728509,-0.03710118,-0.01764739,-0.04259118,0.00444495,-0.01623755,0.05981489,0.02198543,-0.013191,-0.01630974,-0.0414286,-0.04123183,-0.03194197,0.00920164,-0.04931846,0.02575555,-0.01371972,0.00875802,0.01857141,-0.03975237,-0.04590619,0.06855888,0.03941145,0.00510585,0.01964046,-0.03291124,0.00740742,0.02293643,-0.03669425,0.0146457,0.0473955,0.06739892,-0.05232025,-0.01274851,-0.02335774,-0.00443334,-0.02483133,0.02882284,0.02820098,0.00655817,-0.01509408,-0.00560777,0.03594411,-0.00585317,-0.0191534,-0.08865244,-0.00625289,0.00969201,-0.00570884,-0.02333955,-0.0186802,0.03179534,0.02707005,-0.02960036,0.05186527,0.01900807,0.00689974,-0.04774455,0.07415127,0.05552253,0.01381074,-0.02880227,0.01455492,-0.02325823,0.01505208,0.04688599,-0.08974185,0.03649268,-0.00884348,0.01105391,-0.01615527,-0.0246496,0.04850255,-0.04722172,-0.00751846,-0.05174005,0.03821769,-0.04700822,0.03783675,0.02329309,0.00458471,-0.03189323,-0.05014786,0.01004622,0.01684801,0.03318973,-0.00688005,-0.03770561,-0.07269225,0.04262395,0.06362838,-0.00888503,0.03244577,-0.02405321,0.00142682,-0.03658526,0.03510021,-0.00374248,-0.02405888,-0.03495143,0.01285754,0.03130515,-0.04119061,-0.0183727,-0.01190801,0.03205509,0.00005186,-0.05182077,-0.00925055,0.04580288,0.00144311,0.00292092,0.01519268,0.01675117,0.04145873,0.01082158,-0.0060513,-0.01587892,-0.02266998,-0.0033063,0.02579503,0.03460129,-0.00173858,-0.0116572,-0.01771666,0.05286837,-0.01967637,0.02835311,-0.02208203,0.0309522,-0.00553987,-0.05230431,-0.01079584,-0.03954488,0.02504891,-0.0301687,0.07028697,0.01358676,-0.02064241,0.02939551,-0.02597086,-0.00305772,0.04049455,0.01716469,0.00908063,0.02164119,0.05543431,-0.05584452,0.01449294,-0.0461003,-0.08505578,0.00354267,0.04243441,0.00898211,0.0519204,0.05572155,-0.02007939,0.02666924,0.03474266,-0.03867704,-0.03238966,0.01930497,-0.02661276,-0.11337873,0.02568898,0.05145577,0.04644526,-0.06088273,-0.0476399,-0.03205759,0.0179273,0.06134367,-0.00052244,0.02155372,0.00517263,0.0429679,-0.05072567,-0.00542475,0.00415145,0.02994465,0.04553999,0.04821401,0.04133754,0.05013667,-0.04198075,-0.00690074,0.0120374,0.02590544,0.02440125,-0.02113559,0.03473035,0.023876,-0.01609524,0.01153389,-0.02448523,0.01678169,-0.0008559,-0.0186977,-0.08826537,0.01712511,-0.02586957,-0.02165998,0.00193302,0.04269257,0.03276503,-0.01269597,-0.0685701,0.033561,0.00255157,-0.01132115,0.00223379,-0.04713342,-0.03740742,-0.0371086,0.02196758,-0.05117926,-0.01882533,0.02940799,-0.00374655,0.00669168,0.03786004,0.06187287,-0.04945617,-0.00732055,-0.05898188,-0.00484347,0.02760306,0.05940644,0.05811243,-0.04248228,-0.0099413,-0.00212808,0.02837401,0.07129206,-0.01689334,0.02530374,-0.02152507,0.0458871,-0.01226681,-0.06573353,-0.01117654,0.02389085,0.00129988,0.01640487,-0.03538657,-0.0097048,-0.04750358,-0.02575114,0.04293198,0.0095777,0.0137318,-0.01669214,0.09904435,-0.04104656,-0.037481,-0.02244932,0.00215981,-0.02098424,0.02973142,0.05231291,0.05932964,0.02048454,0.01182286,0.04333412,-0.03992294,-0.01434019,0.0553835,0.02693627,-0.00771923,-0.00751026,0.0110534,0.02023356,-0.01770123,-0.03979244,0.03682543,0.00008193,-0.06814938,-0.02592045,-0.03294504,-0.02740622,-0.00962519,-0.03184151,-0.03195197,-0.02515414,0.07378764,0.00013873,0.02463827,0.05628139,-0.00833359,0.00914363,0.03942896,0.01325534,0.00033173,-0.04542384,0.01278059,0.009209,0.007086,-0.02024051,0.02956067,0.03378697,0.06752617,-0.02159305,-0.00259639,0.01017038,0.02737748,-0.01511741,-0.04821753,0.02040587,-0.02694797,0.00738817,-0.00984541,-0.11916048,-0.01659422,0.00969497,0.02616606,0.04691064,0.05711636,-0.11068647,-0.01028641,-0.05978771,0.05011625,-0.01691344,0.01822631,0.00250401,-0.02150603,-0.00661404,0.08366934,0.09631893,0.02304147,0.02624759,-0.00867973,0.03580206,-0.01524437,0.05116365,-0.00955984,-0.01023783,-0.06316079,0.00980196,0.01286728,-0.03006283,0.00831443,0.02916136,0.03048527,-0.01190436,-0.03177845,-0.05265541,-0.00723965,0.02821808,-0.05845476,-0.00301521,-0.00522676,9e-7,0.05620108,-0.05549251,0.04931697,-0.03807802,-0.0346716,-0.06155628,-0.02424405,0.00837167,-0.00056953],"last_embed":{"tokens":1268,"hash":"16yfxun"}}},"last_read":{"hash":"16yfxun","at":1751079987476},"class_name":"SmartSource","outlinks":[{"title":"linux","target":"linux","line":21},{"title":"HTTP(S)协议","target":"HTTP(S)协议","line":25},{"title":"Tshark","target":"Tshark","line":28},{"title":"Tshark","target":"Tshark","line":32},{"title":"WireShark","target":"WireShark","line":33},{"title":"ASCII编码","target":"ASCII编码","line":45},{"title":"Pasted image 20240521211531.png","target":"Pasted image 20240521211531.png","line":58},{"title":"IP协议","target":"IP协议","line":78},{"title":"ASCII编码","target":"ASCII编码","line":149},{"title":"WireShark","target":"WireShark","line":149}],"metadata":{"aliases":null,"tags":["计算机网络/网络分析"],"工具界面":null,"系统平台":null,"发布时间":null,"开发者":null,"邮箱":null,"官网":null,"♥star":null,"文档更新日期":"2024-02-17 13:59","类型":null,"开发栈":null,"编程语言":null,"架构平台":null},"blocks":{"#---frontmatter---":[1,17],"#简介":[19,34],"#简介#{1}":[21,22],"#简介#{2}":[23,23],"#简介#{3}":[24,28],"#简介#{4}":[29,30],"#简介#{5}":[31,32],"#简介#{6}":[33,33],"#简介#{7}":[34,34],"#参数相关":[35,71],"#参数相关#基础参数":[36,55],"#参数相关#基础参数#{1}":[38,55],"#参数相关#参数解析":[56,71],"#参数相关#参数解析#{1}":[58,58],"#参数相关#参数解析#{2}":[59,59],"#参数相关#参数解析#{3}":[60,61],"#参数相关#参数解析#{4}":[62,62],"#参数相关#参数解析#{5}":[63,64],"#参数相关#参数解析#{6}":[65,69],"#参数相关#参数解析#{7}":[70,71],"#常用操作":[72,121],"#常用操作#捕获特定IP数据包到本地":[74,80],"#常用操作#捕获特定IP数据包到本地#{1}":[75,77],"#常用操作#捕获特定IP数据包到本地#{2}":[78,79],"#常用操作#捕获特定IP数据包到本地#{3}":[80,80],"#常用操作#基于网段进行过滤":[81,92],"#常用操作#基于网段进行过滤#{1}":[82,84],"#常用操作#基于网段进行过滤#{2}":[85,85],"#常用操作#基于网段进行过滤#{3}":[86,90],"#常用操作#基于网段进行过滤#{4}":[91,91],"#常用操作#基于网段进行过滤#{5}":[92,92],"#常用操作#基于端口进行过滤":[93,104],"#常用操作#基于端口进行过滤#{1}":[94,96],"#常用操作#基于端口进行过滤#{2}":[97,102],"#常用操作#基于端口进行过滤#{3}":[98,102],"#常用操作#基于端口进行过滤#{4}":[103,103],"#常用操作#基于端口进行过滤#{5}":[104,104],"#常用操作#基于协议进行过滤":[105,111],"#常用操作#基于协议进行过滤#{1}":[107,109],"#常用操作#基于协议进行过滤#{2}":[110,110],"#常用操作#基于协议进行过滤#{3}":[111,111],"#常用操作#基于MAC地址过滤":[112,114],"#常用操作#基于DNS请求过滤":[115,121],"#常用操作#基于DNS请求过滤#{1}":[117,119],"#常用操作#基于DNS请求过滤#{2}":[120,120],"#常用操作#基于DNS请求过滤#{3}":[121,121],"#流量输出分析":[122,150],"#流量输出分析#tcp输出数据包":[124,130],"#流量输出分析#tcp输出数据包#{1}":[126,130],"#流量输出分析#DNS数据分析":[131,150],"#流量输出分析#DNS数据分析#{1}":[132,135],"#流量输出分析#DNS数据分析#{2}":[136,136],"#流量输出分析#DNS数据分析#{3}":[137,137],"#流量输出分析#DNS数据分析#{4}":[138,138],"#流量输出分析#DNS数据分析#{5}":[139,139],"#流量输出分析#DNS数据分析#{6}":[140,144],"#流量输出分析#DNS数据分析#{7}":[145,145],"#流量输出分析#DNS数据分析#{8}":[146,146],"#流量输出分析#DNS数据分析#{9}":[147,150]},"last_import":{"mtime":1731396395969,"size":4561,"at":1748488128956,"hash":"16yfxun"},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md","last_embed":{"hash":"16yfxun","at":1751079987476}},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#参数相关": {"path":null,"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.0147576,-0.02907139,-0.03040393,-0.02362172,-0.04154125,-0.01588151,0.00818342,-0.0253714,-0.00515722,-0.04943095,0.01672905,0.05979232,0.00982552,0.01445962,-0.01694854,0.07480362,0.0101766,0.05249127,0.01059852,-0.03033864,-0.02482902,-0.00048077,0.00589448,0.01287079,-0.02057353,-0.02232336,-0.03854452,-0.07192615,-0.02862793,0.03873959,0.02576608,-0.05855355,0.03639041,0.0387081,-0.01433348,0.03398607,-0.01178433,0.01091806,0.01228097,-0.03855668,-0.03192621,-0.02624905,-0.07048011,-0.00248992,-0.08084021,0.01452655,-0.01931317,-0.04280385,0.02598377,0.01286989,-0.01681004,0.01890788,0.00333275,0.03977138,-0.00006284,0.02141877,-0.05372953,-0.050115,-0.01686121,-0.10520022,0.01501544,-0.0686828,0.02076283,-0.00778079,0.01323238,0.03291957,0.07620179,-0.02802397,-0.05464776,0.00097844,-0.00894561,-0.06826628,0.05688853,-0.00722806,-0.01725189,0.00903957,-0.02865994,0.0091137,-0.03021,0.00914518,0.00187008,0.05108758,0.05612806,-0.01546193,-0.023149,0.01146132,0.04157611,0.00217482,-0.04432746,0.00854348,0.01963887,-0.06232065,-0.05001516,-0.01646001,0.02511563,-0.01495268,0.03236335,-0.01454717,0.03856339,-0.0341149,-0.05286041,-0.02246319,-0.03983103,-0.02977868,0.01026864,-0.00505668,-0.01647215,0.05163567,-0.01006445,-0.0050104,-0.00389647,-0.0343036,-0.01897087,-0.03520252,-0.05866601,-0.02386662,-0.01007686,0.02842168,0.02519254,0.07657637,0.03174534,-0.01206639,-0.01809381,-0.00040024,-0.03730638,0.00293395,0.00691663,-0.02475472,0.04676315,-0.06302814,-0.00569358,-0.00281416,-0.05146001,-0.02886357,0.08453542,0.00232646,0.05580544,-0.03464796,-0.06854898,-0.01863908,0.03541638,-0.0247355,0.02955165,0.0217385,0.0005261,0.0207333,-0.05633564,-0.02458878,-0.03797601,0.03489895,0.09665219,-0.00653543,-0.04828955,-0.02969035,-0.04772333,0.02203823,-0.01539283,-0.03992612,-0.02009565,-0.03491108,-0.06132462,0.00795067,0.0372906,-0.01461468,-0.0027968,0.03521495,0.01388755,0.00697879,-0.0404544,-0.04058822,-0.04535607,0.01239944,-0.03343442,0.00390914,0.00752338,-0.01858139,0.07245757,0.04881379,-0.02560723,-0.02423336,0.05228538,-0.00752316,-0.01144697,0.0661175,-0.00031784,0.07735138,-0.01461274,0.02992663,-0.00168853,0.01728968,0.02753143,0.01420042,0.00234332,0.00795762,0.04891406,0.04237578,-0.01315309,-0.04350854,0.0300023,-0.00164755,-0.00904199,-0.01039042,0.01300265,-0.12524906,0.05507468,0.04530042,-0.05211935,-0.0632087,-0.02197636,0.02027434,-0.00532702,0.0013649,0.08567443,0.03082082,-0.01678461,0.08795293,-0.00492084,0.00763189,0.00580985,-0.0202311,-0.00700901,0.01891032,0.01789768,-0.01581361,0.01406799,0.00878303,0.05725125,0.0231859,-0.02132199,0.0149193,0.0200715,0.10173562,0.01858686,-0.05506476,-0.05140856,0.0423995,0.02343229,0.03531457,0.01916545,0.03610992,-0.00312129,-0.02128683,0.01076737,-0.00828237,-0.02581191,-0.01957663,-0.01248096,0.00015026,0.02342687,0.06361457,0.10444264,-0.00142545,-0.0137395,-0.00902893,-0.02050894,-0.05180015,0.00323811,0.02030046,-0.0626085,-0.03387998,0.04327064,-0.01146346,0.02255659,0.00036804,0.04292755,-0.01692221,0.03534766,0.03913404,-0.01287844,0.02322724,0.0048324,0.0004196,0.02510026,-0.00357069,-0.04976355,-0.00096543,-0.02249497,-0.01992458,0.05138267,0.00923374,0.01904087,0.01370925,-0.01225937,-0.01522004,-0.03205628,0.04409066,-0.00956987,-0.045287,0.01931409,0.05841199,0.01614176,-0.02177019,-0.03347702,0.02332165,-0.03572083,0.02223464,-0.03641854,0.010297,0.02296839,-0.05081917,0.00616367,-0.01598426,-0.01318467,-0.03235877,0.04459636,0.07437977,0.00158834,-0.01100759,-0.05957723,-0.02996969,-0.05695961,-0.02414746,0.00065325,-0.00252057,0.02340014,0.02246511,0.04039913,0.02786228,-0.02004257,-0.06426147,-0.05129908,0.0305182,-0.08217072,0.05496116,-0.03016788,-0.08888676,0.05638625,0.03829569,-0.01902705,0.03364018,-0.0324733,0.01594197,-0.06161416,-0.01139461,-0.01054711,0.01318557,-0.02636343,-0.05155031,0.03647646,-0.0078065,0.07050161,-0.04563176,0.04905372,-0.00511143,0.00598901,0.00715532,-0.00436909,0.02318054,0.01602244,-0.00999555,-0.0035417,0.03145185,-0.01595593,0.01830494,0.03014869,0.02566519,-0.02221991,-0.01105612,-0.06033929,-0.03159943,0.03410889,-0.06212607,-0.02526375,-0.02098512,-0.01216856,-0.00982273,0.0367259,0.07895152,-0.04387666,-0.00385282,-0.04783553,-0.00885647,-0.02351264,-0.02802451,-0.01116738,-0.01202187,-0.05404582,0.04269007,0.0414454,0.03343392,-0.06980561,0.03302883,-0.04567563,-0.02151826,-0.02460658,0.00096368,-0.04033199,0.03404864,0.01938583,-0.01801662,0.0234158,0.04444849,-0.03279793,0.0428165,0.08339436,-0.02345056,-0.02261097,0.02810486,0.04802015,-0.0141021,0.00798147,0.01774521,0.00108324,-0.00057787,-0.01118877,0.05179853,-0.04868979,0.0256539,-0.05050004,-0.0079063,-0.03224318,0.04829686,-0.01042568,0.03033539,0.01009754,-0.01678782,-0.02790254,-0.03919371,-0.06510887,-0.03679815,0.01452651,-0.04729827,0.03863095,-0.03684197,-0.02277906,-0.01909517,-0.00455165,-0.03180809,0.03531748,0.04639443,0.00222992,0.00522329,-0.03936264,-0.01884213,0.02751681,-0.01565752,-0.00488066,0.05315683,0.02953709,-0.0279278,0.01719375,-0.0154831,-0.01358766,-0.04946196,0.00562935,0.04038392,0.01172673,0.01086454,0.00804671,0.0096068,0.02833974,0.00904877,-0.08765996,0.0136132,0.02199201,-0.00604027,-0.04242275,-0.0188265,0.05311428,0.03351539,0.01192305,0.07283845,0.01000434,0.0305793,-0.04303956,0.01795852,0.04889052,0.01213292,-0.04955283,-0.0165522,-0.03908663,-0.00704003,0.03888099,-0.04819652,0.04711264,-0.00308052,0.01043225,0.00870755,-0.01508321,0.04642928,-0.03417116,0.00666048,-0.05970234,0.0451595,-0.04430894,0.04190244,0.01998259,0.04882914,-0.009669,-0.03175336,0.0230406,0.00194561,0.0140839,-0.03464561,-0.06887814,-0.03535927,0.03151775,0.03775933,0.00016952,0.01405191,-0.04652069,0.01379676,-0.06473533,0.03985997,-0.0086506,-0.00478865,-0.00800667,0.00496881,0.00858494,-0.039235,-0.03695993,-0.01100651,0.03830726,-0.00222197,-0.05265796,-0.01806543,0.00208115,0.03087718,-0.01271479,0.01526068,0.03706297,0.03616512,-0.00259949,0.00155876,-0.0089003,-0.01793965,-0.0194911,-0.00984033,0.0363586,0.03746995,-0.00090643,-0.04696178,0.02166608,0.02328821,-0.00205024,0.00760524,0.03579844,-0.03226088,-0.03166984,0.03584386,-0.01971122,0.00695468,-0.04994867,0.02544835,0.03060979,-0.02097914,0.03156383,-0.02700424,-0.02621513,0.01724206,0.02127471,0.02225443,0.03783472,0.05527261,-0.04320262,0.01403174,-0.04616262,-0.05941102,0.03730395,0.03652618,0.02582804,0.0475074,0.0665071,-0.01359331,0.04440006,0.07384989,-0.0517558,-0.00428139,-0.02327241,-0.06409028,-0.10691719,0.00389249,0.05548175,0.04155336,-0.04378781,-0.02603521,-0.04777508,0.04989761,0.03568022,-0.00030391,0.02306006,0.01671555,0.03954452,-0.0524647,-0.02126526,0.00289817,0.03213648,0.07191318,0.05457424,0.02551943,0.01786534,-0.01406151,-0.03606494,-0.03455289,0.01674754,0.04533075,-0.02183243,0.01005305,0.01458577,-0.01789601,0.02307223,-0.01368853,-0.03835002,-0.02688819,0.01580526,-0.04410375,-0.01085937,-0.03405238,-0.04379763,0.02641121,0.03602393,0.00371761,-0.00691097,-0.08754643,0.018507,0.01808905,0.00753285,-0.00633836,-0.04215337,-0.00530221,-0.04441511,-0.00831497,-0.08569923,-0.06499746,-0.00850075,-0.0084082,0.0119113,0.06961359,0.04585446,-0.06419986,0.0153087,-0.06850976,-0.01618816,0.03062243,0.0730758,0.03128291,-0.01725592,-0.00067318,-0.0184442,0.03313602,0.06664927,-0.00282522,0.04261221,0.00689009,0.06795999,-0.00602906,-0.04249576,-0.02260352,-0.01284191,-0.03211159,0.00624246,-0.02623909,-0.04147645,0.0012073,-0.00887209,0.05992856,0.03425545,0.00147694,-0.01490999,0.12328473,-0.05272125,-0.0324699,-0.01163144,0.02183044,0.00292911,0.0152191,0.08591144,0.05054059,0.01830475,0.02124871,0.08156357,0.00250232,-0.01088873,0.04777364,0.02855692,-0.00949933,0.00247201,0.0440346,0.02253482,-0.02402003,0.00998833,0.01412413,0.00601751,-0.06257569,-0.02848952,-0.0182229,0.01657477,-0.00434875,-0.02641191,-0.01901884,-0.03509979,0.09271532,0.00184811,0.03101003,0.02246638,-0.00677217,0.00500582,0.02048452,-0.00039976,-0.00942103,-0.03721149,-0.01204782,-0.00371677,-0.01622096,-0.02721098,0.02176266,0.0202918,0.05599044,0.01359783,-0.00880825,-0.00061394,0.01179166,-0.02025429,-0.05724718,0.03197677,-0.05847884,0.01913504,0.00064842,-0.11306526,0.01523288,0.01024314,0.05419267,0.03730645,0.02223374,-0.10055792,-0.0335555,-0.05245556,0.05936329,-0.02150959,0.01284117,-0.00835992,-0.0562409,0.01752847,0.05714751,0.06570619,0.01407506,0.0693896,-0.00656256,0.02211541,0.00867968,0.02757639,0.00627772,0.0030106,-0.04616543,0.01717409,0.03051856,-0.01098119,0.0089446,-0.00528541,0.02724128,0.01456263,-0.02103033,-0.02818265,-0.04440084,0.00917816,-0.05644205,0.00476587,-0.0019544,9.2e-7,0.01920472,-0.05099937,0.05150166,-0.06389546,-0.00889576,-0.04773431,-0.02817263,0.02694738,0.03704603],"last_embed":{"hash":"irdwdq","tokens":379}}},"text":null,"length":0,"last_read":{"hash":"irdwdq","at":1749002760449},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#参数相关","lines":[35,71],"size":1028,"outlinks":[{"title":"ASCII编码","target":"ASCII编码","line":11},{"title":"Pasted image 20240521211531.png","target":"Pasted image 20240521211531.png","line":24}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#---frontmatter---","lines":[1,17],"size":132,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#简介": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#简介","lines":[19,34],"size":342,"outlinks":[{"title":"linux","target":"linux","line":3},{"title":"HTTP(S)协议","target":"HTTP(S)协议","line":7},{"title":"Tshark","target":"Tshark","line":10},{"title":"Tshark","target":"Tshark","line":14},{"title":"WireShark","target":"WireShark","line":15}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#简介#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#简介#{1}","lines":[21,22],"size":46,"outlinks":[{"title":"linux","target":"linux","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#简介#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#简介#{2}","lines":[23,23],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#简介#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#简介#{3}","lines":[24,28],"size":136,"outlinks":[{"title":"HTTP(S)协议","target":"HTTP(S)协议","line":2},{"title":"Tshark","target":"Tshark","line":5}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#简介#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#简介#{4}","lines":[29,30],"size":27,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#简介#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#简介#{5}","lines":[31,32],"size":67,"outlinks":[{"title":"Tshark","target":"Tshark","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#简介#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#简介#{6}","lines":[33,33],"size":47,"outlinks":[{"title":"WireShark","target":"WireShark","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#简介#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#简介#{7}","lines":[34,34],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#参数相关#基础参数": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#参数相关#基础参数","lines":[36,55],"size":764,"outlinks":[{"title":"ASCII编码","target":"ASCII编码","line":10}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#参数相关#基础参数#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#参数相关#基础参数#{1}","lines":[38,55],"size":755,"outlinks":[{"title":"ASCII编码","target":"ASCII编码","line":8}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#参数相关#参数解析": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#参数相关#参数解析","lines":[56,71],"size":255,"outlinks":[{"title":"Pasted image 20240521211531.png","target":"Pasted image 20240521211531.png","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#参数相关#参数解析#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#参数相关#参数解析#{1}","lines":[58,58],"size":36,"outlinks":[{"title":"Pasted image 20240521211531.png","target":"Pasted image 20240521211531.png","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#参数相关#参数解析#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#参数相关#参数解析#{2}","lines":[59,59],"size":14,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#参数相关#参数解析#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#参数相关#参数解析#{3}","lines":[60,61],"size":31,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#参数相关#参数解析#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#参数相关#参数解析#{4}","lines":[62,62],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#参数相关#参数解析#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#参数相关#参数解析#{5}","lines":[63,64],"size":57,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#参数相关#参数解析#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#参数相关#参数解析#{6}","lines":[65,69],"size":95,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#参数相关#参数解析#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#参数相关#参数解析#{7}","lines":[70,71],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作","lines":[72,121],"size":714,"outlinks":[{"title":"IP协议","target":"IP协议","line":7}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#捕获特定IP数据包到本地": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#捕获特定IP数据包到本地","lines":[74,80],"size":144,"outlinks":[{"title":"IP协议","target":"IP协议","line":5}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#捕获特定IP数据包到本地#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#捕获特定IP数据包到本地#{1}","lines":[75,77],"size":61,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#捕获特定IP数据包到本地#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#捕获特定IP数据包到本地#{2}","lines":[78,79],"size":61,"outlinks":[{"title":"IP协议","target":"IP协议","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#捕获特定IP数据包到本地#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#捕获特定IP数据包到本地#{3}","lines":[80,80],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于网段进行过滤": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于网段进行过滤","lines":[81,92],"size":160,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于网段进行过滤#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于网段进行过滤#{1}","lines":[82,84],"size":39,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于网段进行过滤#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于网段进行过滤#{2}","lines":[85,85],"size":10,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于网段进行过滤#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于网段进行过滤#{3}","lines":[86,90],"size":79,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于网段进行过滤#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于网段进行过滤#{4}","lines":[91,91],"size":13,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于网段进行过滤#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于网段进行过滤#{5}","lines":[92,92],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于端口进行过滤": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于端口进行过滤","lines":[93,104],"size":188,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于端口进行过滤#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于端口进行过滤#{1}","lines":[94,96],"size":40,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于端口进行过滤#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于端口进行过滤#{2}","lines":[97,102],"size":117,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于端口进行过滤#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于端口进行过滤#{3}","lines":[98,102],"size":103,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于端口进行过滤#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于端口进行过滤#{4}","lines":[103,103],"size":13,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于端口进行过滤#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于端口进行过滤#{5}","lines":[104,104],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于协议进行过滤": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于协议进行过滤","lines":[105,111],"size":90,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于协议进行过滤#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于协议进行过滤#{1}","lines":[107,109],"size":33,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于协议进行过滤#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于协议进行过滤#{2}","lines":[110,110],"size":39,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于协议进行过滤#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于协议进行过滤#{3}","lines":[111,111],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于MAC地址过滤": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于MAC地址过滤","lines":[112,114],"size":14,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于DNS请求过滤": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于DNS请求过滤","lines":[115,121],"size":104,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于DNS请求过滤#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于DNS请求过滤#{1}","lines":[117,119],"size":69,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于DNS请求过滤#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于DNS请求过滤#{2}","lines":[120,120],"size":16,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于DNS请求过滤#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#常用操作#基于DNS请求过滤#{3}","lines":[121,121],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#流量输出分析": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#流量输出分析","lines":[122,150],"size":656,"outlinks":[{"title":"ASCII编码","target":"ASCII编码","line":28},{"title":"WireShark","target":"WireShark","line":28}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#流量输出分析#tcp输出数据包": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#流量输出分析#tcp输出数据包","lines":[124,130],"size":141,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#流量输出分析#tcp输出数据包#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#流量输出分析#tcp输出数据包#{1}","lines":[126,130],"size":128,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#流量输出分析#DNS数据分析": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#流量输出分析#DNS数据分析","lines":[131,150],"size":504,"outlinks":[{"title":"ASCII编码","target":"ASCII编码","line":19},{"title":"WireShark","target":"WireShark","line":19}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#流量输出分析#DNS数据分析#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#流量输出分析#DNS数据分析#{1}","lines":[132,135],"size":106,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#流量输出分析#DNS数据分析#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#流量输出分析#DNS数据分析#{2}","lines":[136,136],"size":22,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#流量输出分析#DNS数据分析#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#流量输出分析#DNS数据分析#{3}","lines":[137,137],"size":16,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#流量输出分析#DNS数据分析#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#流量输出分析#DNS数据分析#{4}","lines":[138,138],"size":19,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#流量输出分析#DNS数据分析#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#流量输出分析#DNS数据分析#{5}","lines":[139,139],"size":28,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#流量输出分析#DNS数据分析#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#流量输出分析#DNS数据分析#{6}","lines":[140,144],"size":119,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#流量输出分析#DNS数据分析#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#流量输出分析#DNS数据分析#{7}","lines":[145,145],"size":42,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#流量输出分析#DNS数据分析#{8}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#流量输出分析#DNS数据分析#{8}","lines":[146,146],"size":25,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#流量输出分析#DNS数据分析#{9}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md#流量输出分析#DNS数据分析#{9}","lines":[147,150],"size":108,"outlinks":[{"title":"ASCII编码","target":"ASCII编码","line":3},{"title":"WireShark","target":"WireShark","line":3}],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md": {"path":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08724637,-0.01712776,0.00622108,-0.01810723,-0.01109454,-0.03609508,0.00775222,0.03974966,0.03826688,0.00139955,0.0425317,-0.07425436,0.05522698,0.04360349,0.04418913,0.02499702,-0.03188355,0.02312233,0.02094044,-0.01791669,0.06953008,-0.03395885,-0.00522889,-0.05087426,-0.00960883,0.02261707,0.01038372,-0.01825826,-0.02061152,-0.15642749,0.01673094,0.03682297,-0.01432461,0.01508829,0.00728263,-0.03313903,0.02059768,0.04494405,0.00613581,0.03879894,-0.01399241,0.03249789,-0.02460713,-0.05729464,-0.04096748,-0.04588619,-0.06249367,0.00042499,0.01417592,-0.02310047,-0.03825961,-0.04091617,-0.02704895,-0.00135651,-0.04144034,-0.0207651,0.04834353,0.01510635,0.06305239,-0.06506572,0.04272058,0.01823858,-0.19222954,0.05268878,0.05156079,-0.01103833,-0.02733426,0.01864034,0.02836203,0.03351474,-0.05926237,0.03271533,-0.0306049,0.06183349,0.055256,0.01345738,-0.01073928,-0.00274723,-0.04988107,-0.04931198,-0.04211506,0.08338591,-0.0167302,0.00795015,-0.01769333,0.02078789,-0.03584467,-0.03733831,-0.03020994,-0.01106426,0.02316944,-0.02735364,0.00493256,0.02990892,-0.02693903,0.0355945,0.04251804,0.05904138,-0.05956106,0.0983876,-0.0475369,-0.02593284,-0.01407835,-0.05257883,0.01064117,-0.03080836,0.00731717,-0.03534373,-0.0366266,0.01071291,-0.06091136,-0.0515299,0.07261359,-0.01422463,0.04132902,0.02314451,0.00333976,0.007347,-0.03285677,-0.03100991,-0.00958715,0.03633849,0.11392616,-0.01771647,-0.03467441,-0.03413652,0.05474388,0.07822979,0.03853508,0.06194838,0.09584948,-0.01098349,-0.04295879,-0.00560094,-0.0305009,-0.0232054,-0.020557,-0.01519534,-0.0044806,0.01733623,-0.01525567,-0.07296874,-0.01436148,-0.082944,-0.1082439,0.06416029,-0.05656152,0.00164089,0.06320585,-0.02749099,-0.002518,0.05168429,0.00506798,-0.01018093,-0.01392642,0.0109089,0.0700566,0.19531992,-0.00075543,0.01526161,-0.0370181,0.0448829,-0.10202323,0.17319171,-0.00799922,-0.05603538,-0.0362813,0.00610938,0.00204706,-0.02518248,-0.00575298,-0.01076435,0.00902627,0.01405789,0.08472129,-0.00770686,-0.01896726,-0.04120141,0.01135592,-0.01202392,0.11012056,-0.0113529,-0.05422512,0.04156499,0.01818635,-0.06528001,-0.02330856,-0.05033537,0.00992353,-0.03107966,-0.09479073,0.00849911,-0.03546757,0.00493947,-0.05049594,-0.08627678,0.02376637,0.01720806,0.06813808,-0.0207367,0.06973133,0.01412997,-0.03540853,0.01043813,-0.06961189,-0.03115713,-0.00575445,-0.02506849,0.02798167,0.01628906,0.03198072,0.0287574,-0.01065895,0.00999181,-0.0008515,0.01156455,0.00626323,0.01764594,0.01321569,0.07262078,0.01048846,-0.03693186,-0.0765955,-0.20398916,-0.03368405,-0.00012883,-0.0286303,0.00977312,-0.04697042,0.01551913,0.019769,0.0748897,0.11312833,0.06083174,0.04206733,-0.05700119,-0.04238202,0.00745244,0.01440259,0.00722704,-0.00902755,-0.04287016,-0.00503566,-0.00449329,0.0072456,-0.00801946,-0.01935654,0.07002995,-0.03566155,0.12400948,0.03254048,0.01432632,0.05305092,0.04571632,0.02824083,-0.0007936,-0.1048982,0.01977704,0.03805483,-0.04199379,-0.02841483,-0.00475156,-0.03784272,-0.02275095,0.01249042,-0.06637166,-0.09711421,-0.02796665,0.0008956,-0.05085223,0.0113444,0.02627326,0.03152167,-0.00691729,-0.00225488,-0.0028322,-0.03913874,0.0110963,-0.02812904,-0.0324732,-0.03246793,-0.00284097,0.04869638,0.01903119,0.01832795,0.01235732,0.01682756,-0.02089146,-0.01377412,-0.007327,-0.00825496,-0.04996236,0.00615629,-0.00701055,0.17470868,0.00818109,-0.02663528,0.03293769,-0.02127105,-0.01263733,-0.05197439,0.02915197,0.02215071,0.03901146,0.01487483,0.05933949,-0.01437033,-0.03026688,0.00004028,0.03527082,-0.04049971,0.08749395,-0.04478754,-0.06136525,-0.00360673,-0.051628,0.03628283,0.09834993,-0.00092589,-0.30271584,0.05119798,-0.0253246,-0.02423066,0.08413263,0.06957407,0.05021956,0.0078436,-0.07046361,-0.00857764,-0.02851162,0.06370929,-0.00070709,-0.02342376,-0.00208331,-0.03134793,0.05607459,-0.02521218,0.05519891,-0.0201476,-0.01878301,0.02033625,0.19862093,-0.02989537,0.07641833,0.01265869,-0.006239,0.04180733,0.06381281,0.03156337,-0.0159468,-0.02857518,0.02782427,-0.05543601,0.02998893,0.05187262,-0.04824907,0.0116334,0.03277433,-0.00183964,-0.04841967,0.06211989,-0.08634375,0.00266155,0.07368586,0.03736637,-0.01457455,-0.06003308,-0.00486519,0.07609996,0.02684638,0.01977077,-0.03606332,-0.02834049,0.05060156,0.07118034,0.0228727,-0.00636362,-0.07363832,-0.03085767,-0.00946409,-0.01415071,0.10820097,0.117121,0.0764749],"last_embed":{"hash":"1506fb0deb54876329a4ccb78716405ecee90437fb5dc8063b70703a864cac05","tokens":468}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.02105616,-0.0201768,-0.00411567,-0.04291137,-0.04377816,-0.02582652,0.02843606,-0.03709984,-0.03493004,-0.03462865,0.02500341,0.0297763,0.00778544,0.02423665,0.01720417,0.10264116,-0.0122378,0.03675031,-0.01030821,-0.03710086,-0.05052746,-0.0136772,0.00008924,0.02372085,-0.00117535,-0.0193504,-0.04375771,-0.06541636,-0.01921622,0.04373654,0.0383984,-0.04418598,0.00035178,0.02995978,-0.01799092,0.0024211,-0.01211301,0.01645958,-0.02583336,-0.01992876,-0.04106061,-0.01080191,-0.05211771,0.00645166,-0.05602819,0.00049288,-0.00420785,-0.07462903,-0.01457968,0.014062,-0.01519095,0.0156724,0.00655392,0.01980577,0.00247321,0.02990308,-0.03670131,-0.06810364,-0.01905462,-0.11267372,0.00051731,-0.05913945,0.04896908,0.00446985,-0.00491788,0.04260334,0.02743905,-0.00828967,-0.06034052,-0.00761102,-0.00594923,-0.05463341,0.0402269,0.01406258,-0.01879204,-0.00450989,0.00930711,0.00540139,0.00021231,0.00848601,-0.01770261,0.02020093,0.06848234,0.07766457,-0.05087748,0.00222175,0.0276389,-0.0199744,-0.03487617,0.01979414,0.02979409,-0.04185282,-0.06068958,-0.0418308,0.0272475,-0.01538693,0.02226787,0.00438642,0.03194245,-0.04037462,-0.03631495,-0.00422656,-0.00787511,-0.02670546,0.01307743,-0.00810292,-0.0225222,0.06466981,-0.01300646,-0.03964024,-0.01788426,-0.02236076,-0.0071642,0.00129258,-0.0200881,0.0130377,-0.03360254,-0.02569638,0.06353387,0.07467084,0.01849815,0.01307547,-0.01935711,-0.00950731,-0.04168214,0.0273167,-0.03723314,0.04942643,0.00024749,-0.06318083,-0.02349165,0.02634976,-0.01181366,-0.00141704,0.09420972,-0.01405174,0.05237504,-0.03173099,-0.03015789,-0.00373493,0.01124955,-0.01905752,0.04474526,0.01586917,-0.00917967,-0.02711391,-0.04181734,-0.0400655,-0.05394822,0.04526369,0.0622058,-0.01027711,-0.03641134,-0.04150479,-0.03898057,0.0180934,-0.02597617,-0.05122799,-0.01215574,-0.04860832,-0.04582198,-0.02219461,0.02980259,-0.00318451,-0.00894112,0.01923415,0.0118131,-0.02192854,-0.02129483,-0.03601057,-0.00095762,0.01952245,-0.0074718,-0.0006747,0.03264514,-0.00441032,0.07553811,0.05003816,-0.03206777,-0.03435935,0.06527474,0.00997972,-0.01022817,0.06640385,-0.00962387,0.05344373,0.00710347,0.06448376,0.02654378,0.02453995,0.04432255,0.03488073,-0.01485428,0.00614885,0.03895007,0.00115682,-0.00910341,-0.05756878,0.03943618,0.01390888,-0.02025213,-0.02285914,0.02178113,-0.09273964,0.09020026,0.02880675,-0.07959447,-0.03768869,-0.02521581,0.03093277,-0.04391043,-0.00763749,0.04425224,0.03816525,-0.02101072,0.01979843,0.00461431,0.00905905,-0.01262811,0.00746222,-0.00238016,0.00645567,-0.00538015,-0.02096411,-0.00234294,-0.0037657,0.04019079,0.034671,-0.01036242,0.0148541,0.04987252,0.06844369,0.01464778,-0.04885656,-0.04417194,0.03221456,-0.02699433,0.04192777,0.04815573,0.01148638,-0.03836644,0.02363698,0.00531137,0.04061199,-0.04452608,-0.03245727,0.0039171,0.00117314,0.00342112,0.03912393,0.08372061,0.054335,-0.01388936,-0.03097877,-0.0445213,-0.0854172,-0.03410279,0.03230133,-0.04749995,-0.01886003,0.02111593,-0.00229337,0.00025401,0.00844484,0.0372026,-0.04679154,-0.00039128,0.03958298,-0.02799999,-0.02967836,0.0154924,0.02096883,0.02281681,-0.00349161,-0.05126885,0.00211296,-0.05159273,-0.03653683,0.05464029,-0.0112072,0.04465111,-0.01264284,-0.00809691,-0.00416425,-0.01075095,0.05871848,0.00426384,-0.05045689,0.04642477,0.02596509,-0.00652505,-0.00111497,-0.03330483,0.03902162,-0.01955735,-0.03837187,-0.00695007,0.02354482,-0.00190076,-0.03087462,0.00533956,-0.04073728,-0.00910692,-0.0240758,0.06631002,0.06372546,0.00259104,-0.02003933,-0.058121,-0.01809706,-0.06062837,-0.02677005,0.04475503,-0.0028975,0.04028654,0.01488,0.06169734,-0.00418167,-0.00800368,-0.07963086,-0.04890757,0.02901636,-0.06006689,0.03984212,0.005885,-0.07654929,0.05707422,0.04449177,-0.02067421,0.01527626,-0.04606316,0.02762716,-0.06339672,-0.01989518,-0.01798506,0.02459311,-0.05085233,-0.05104991,0.02027917,-0.02275782,0.0807871,-0.05514918,0.03560971,-0.00882675,-0.01424378,0.06976052,-0.00432705,0.05559274,-0.01271594,0.01634723,-0.01700724,0.01817126,-0.00596716,-0.00489024,0.02517816,0.04528187,-0.01102328,-0.05495109,-0.0483842,-0.01139497,0.01516277,-0.03511048,-0.0253048,-0.03440493,-0.05579771,-0.02858939,0.06320789,0.09059582,-0.00723029,0.01507091,-0.02757959,-0.00951146,0.00763154,0.02161636,-0.05953695,-0.01389041,-0.01102666,0.04710318,0.04397534,0.03247621,-0.0715855,0.00217141,-0.03497795,-0.05453541,-0.02531714,0.02765095,-0.04208731,0.03133913,-0.0106186,-0.00717868,0.03146004,0.039421,-0.0380417,0.01934198,0.08646144,-0.02754675,-0.01081909,0.01613996,0.03207965,0.002597,-0.00129103,0.01704509,-0.00713165,-0.00909554,0.03034872,0.0452806,-0.03782473,0.02728509,-0.03710118,-0.01764739,-0.04259118,0.00444495,-0.01623755,0.05981489,0.02198543,-0.013191,-0.01630974,-0.0414286,-0.04123183,-0.03194197,0.00920164,-0.04931846,0.02575555,-0.01371972,0.00875802,0.01857141,-0.03975237,-0.04590619,0.06855888,0.03941145,0.00510585,0.01964046,-0.03291124,0.00740742,0.02293643,-0.03669425,0.0146457,0.0473955,0.06739892,-0.05232025,-0.01274851,-0.02335774,-0.00443334,-0.02483133,0.02882284,0.02820098,0.00655817,-0.01509408,-0.00560777,0.03594411,-0.00585317,-0.0191534,-0.08865244,-0.00625289,0.00969201,-0.00570884,-0.02333955,-0.0186802,0.03179534,0.02707005,-0.02960036,0.05186527,0.01900807,0.00689974,-0.04774455,0.07415127,0.05552253,0.01381074,-0.02880227,0.01455492,-0.02325823,0.01505208,0.04688599,-0.08974185,0.03649268,-0.00884348,0.01105391,-0.01615527,-0.0246496,0.04850255,-0.04722172,-0.00751846,-0.05174005,0.03821769,-0.04700822,0.03783675,0.02329309,0.00458471,-0.03189323,-0.05014786,0.01004622,0.01684801,0.03318973,-0.00688005,-0.03770561,-0.07269225,0.04262395,0.06362838,-0.00888503,0.03244577,-0.02405321,0.00142682,-0.03658526,0.03510021,-0.00374248,-0.02405888,-0.03495143,0.01285754,0.03130515,-0.04119061,-0.0183727,-0.01190801,0.03205509,0.00005186,-0.05182077,-0.00925055,0.04580288,0.00144311,0.00292092,0.01519268,0.01675117,0.04145873,0.01082158,-0.0060513,-0.01587892,-0.02266998,-0.0033063,0.02579503,0.03460129,-0.00173858,-0.0116572,-0.01771666,0.05286837,-0.01967637,0.02835311,-0.02208203,0.0309522,-0.00553987,-0.05230431,-0.01079584,-0.03954488,0.02504891,-0.0301687,0.07028697,0.01358676,-0.02064241,0.02939551,-0.02597086,-0.00305772,0.04049455,0.01716469,0.00908063,0.02164119,0.05543431,-0.05584452,0.01449294,-0.0461003,-0.08505578,0.00354267,0.04243441,0.00898211,0.0519204,0.05572155,-0.02007939,0.02666924,0.03474266,-0.03867704,-0.03238966,0.01930497,-0.02661276,-0.11337873,0.02568898,0.05145577,0.04644526,-0.06088273,-0.0476399,-0.03205759,0.0179273,0.06134367,-0.00052244,0.02155372,0.00517263,0.0429679,-0.05072567,-0.00542475,0.00415145,0.02994465,0.04553999,0.04821401,0.04133754,0.05013667,-0.04198075,-0.00690074,0.0120374,0.02590544,0.02440125,-0.02113559,0.03473035,0.023876,-0.01609524,0.01153389,-0.02448523,0.01678169,-0.0008559,-0.0186977,-0.08826537,0.01712511,-0.02586957,-0.02165998,0.00193302,0.04269257,0.03276503,-0.01269597,-0.0685701,0.033561,0.00255157,-0.01132115,0.00223379,-0.04713342,-0.03740742,-0.0371086,0.02196758,-0.05117926,-0.01882533,0.02940799,-0.00374655,0.00669168,0.03786004,0.06187287,-0.04945617,-0.00732055,-0.05898188,-0.00484347,0.02760306,0.05940644,0.05811243,-0.04248228,-0.0099413,-0.00212808,0.02837401,0.07129206,-0.01689334,0.02530374,-0.02152507,0.0458871,-0.01226681,-0.06573353,-0.01117654,0.02389085,0.00129988,0.01640487,-0.03538657,-0.0097048,-0.04750358,-0.02575114,0.04293198,0.0095777,0.0137318,-0.01669214,0.09904435,-0.04104656,-0.037481,-0.02244932,0.00215981,-0.02098424,0.02973142,0.05231291,0.05932964,0.02048454,0.01182286,0.04333412,-0.03992294,-0.01434019,0.0553835,0.02693627,-0.00771923,-0.00751026,0.0110534,0.02023356,-0.01770123,-0.03979244,0.03682543,0.00008193,-0.06814938,-0.02592045,-0.03294504,-0.02740622,-0.00962519,-0.03184151,-0.03195197,-0.02515414,0.07378764,0.00013873,0.02463827,0.05628139,-0.00833359,0.00914363,0.03942896,0.01325534,0.00033173,-0.04542384,0.01278059,0.009209,0.007086,-0.02024051,0.02956067,0.03378697,0.06752617,-0.02159305,-0.00259639,0.01017038,0.02737748,-0.01511741,-0.04821753,0.02040587,-0.02694797,0.00738817,-0.00984541,-0.11916048,-0.01659422,0.00969497,0.02616606,0.04691064,0.05711636,-0.11068647,-0.01028641,-0.05978771,0.05011625,-0.01691344,0.01822631,0.00250401,-0.02150603,-0.00661404,0.08366934,0.09631893,0.02304147,0.02624759,-0.00867973,0.03580206,-0.01524437,0.05116365,-0.00955984,-0.01023783,-0.06316079,0.00980196,0.01286728,-0.03006283,0.00831443,0.02916136,0.03048527,-0.01190436,-0.03177845,-0.05265541,-0.00723965,0.02821808,-0.05845476,-0.00301521,-0.00522676,9e-7,0.05620108,-0.05549251,0.04931697,-0.03807802,-0.0346716,-0.06155628,-0.02424405,0.00837167,-0.00056953],"last_embed":{"tokens":1268,"hash":"16yfxun"}}},"last_read":{"hash":"16yfxun","at":1751251727187},"class_name":"SmartSource","outlinks":[{"title":"linux","target":"linux","line":21},{"title":"HTTP(S)协议","target":"HTTP(S)协议","line":25},{"title":"Tshark","target":"Tshark","line":28},{"title":"Tshark","target":"Tshark","line":32},{"title":"WireShark","target":"WireShark","line":33},{"title":"ASCII编码","target":"ASCII编码","line":45},{"title":"Pasted image 20240521211531.png","target":"Pasted image 20240521211531.png","line":58},{"title":"IP协议","target":"IP协议","line":78},{"title":"ASCII编码","target":"ASCII编码","line":149},{"title":"WireShark","target":"WireShark","line":149}],"metadata":{"aliases":null,"tags":["计算机网络/网络分析"],"工具界面":null,"系统平台":null,"发布时间":null,"开发者":null,"邮箱":null,"官网":null,"♥star":null,"文档更新日期":"2024-02-17 13:59","类型":null,"开发栈":null,"编程语言":null,"架构平台":null},"blocks":{"#---frontmatter---":[1,17],"#简介":[19,34],"#简介#{1}":[21,22],"#简介#{2}":[23,23],"#简介#{3}":[24,28],"#简介#{4}":[29,30],"#简介#{5}":[31,32],"#简介#{6}":[33,33],"#简介#{7}":[34,34],"#参数相关":[35,71],"#参数相关#基础参数":[36,55],"#参数相关#基础参数#{1}":[38,55],"#参数相关#参数解析":[56,71],"#参数相关#参数解析#{1}":[58,58],"#参数相关#参数解析#{2}":[59,59],"#参数相关#参数解析#{3}":[60,61],"#参数相关#参数解析#{4}":[62,62],"#参数相关#参数解析#{5}":[63,64],"#参数相关#参数解析#{6}":[65,69],"#参数相关#参数解析#{7}":[70,71],"#常用操作":[72,121],"#常用操作#捕获特定IP数据包到本地":[74,80],"#常用操作#捕获特定IP数据包到本地#{1}":[75,77],"#常用操作#捕获特定IP数据包到本地#{2}":[78,79],"#常用操作#捕获特定IP数据包到本地#{3}":[80,80],"#常用操作#基于网段进行过滤":[81,92],"#常用操作#基于网段进行过滤#{1}":[82,84],"#常用操作#基于网段进行过滤#{2}":[85,85],"#常用操作#基于网段进行过滤#{3}":[86,90],"#常用操作#基于网段进行过滤#{4}":[91,91],"#常用操作#基于网段进行过滤#{5}":[92,92],"#常用操作#基于端口进行过滤":[93,104],"#常用操作#基于端口进行过滤#{1}":[94,96],"#常用操作#基于端口进行过滤#{2}":[97,102],"#常用操作#基于端口进行过滤#{3}":[98,102],"#常用操作#基于端口进行过滤#{4}":[103,103],"#常用操作#基于端口进行过滤#{5}":[104,104],"#常用操作#基于协议进行过滤":[105,111],"#常用操作#基于协议进行过滤#{1}":[107,109],"#常用操作#基于协议进行过滤#{2}":[110,110],"#常用操作#基于协议进行过滤#{3}":[111,111],"#常用操作#基于MAC地址过滤":[112,114],"#常用操作#基于DNS请求过滤":[115,121],"#常用操作#基于DNS请求过滤#{1}":[117,119],"#常用操作#基于DNS请求过滤#{2}":[120,120],"#常用操作#基于DNS请求过滤#{3}":[121,121],"#流量输出分析":[122,150],"#流量输出分析#tcp输出数据包":[124,130],"#流量输出分析#tcp输出数据包#{1}":[126,130],"#流量输出分析#DNS数据分析":[131,150],"#流量输出分析#DNS数据分析#{1}":[132,135],"#流量输出分析#DNS数据分析#{2}":[136,136],"#流量输出分析#DNS数据分析#{3}":[137,137],"#流量输出分析#DNS数据分析#{4}":[138,138],"#流量输出分析#DNS数据分析#{5}":[139,139],"#流量输出分析#DNS数据分析#{6}":[140,144],"#流量输出分析#DNS数据分析#{7}":[145,145],"#流量输出分析#DNS数据分析#{8}":[146,146],"#流量输出分析#DNS数据分析#{9}":[147,150]},"last_import":{"mtime":1731396395969,"size":4561,"at":1748488128956,"hash":"16yfxun"},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/技术范畴/网络监听分析/工具/tcpdump.md","last_embed":{"hash":"16yfxun","at":1751251727187}},