"smart_sources:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md": {"path":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07512741,-0.01241893,-0.02464014,-0.08194825,-0.01086793,-0.00470211,0.00775258,0.02693584,0.04407761,-0.00824355,-0.00245932,-0.06001249,0.07070758,0.04640101,0.05745487,0.0374518,0.05892456,0.00961305,-0.00685717,-0.00633822,0.06425914,-0.07193429,-0.02271962,-0.05113329,0.00713011,-0.01120522,0.00626754,-0.03696421,-0.01998075,-0.15782644,-0.02707082,-0.00422852,-0.03997706,0.04226858,-0.029076,-0.01358798,0.02795483,0.04809997,0.02747239,0.01468003,0.01983138,0.0207794,-0.00072996,-0.03756719,-0.03800495,-0.06620678,-0.0292836,-0.00316967,-0.03347204,-0.04774076,-0.05720865,-0.01832374,-0.02016958,0.02809825,-0.05964166,-0.01070178,0.00783605,0.04482763,0.06587037,-0.0051214,0.04253965,0.01815882,-0.22802436,0.09579319,0.0424269,0.00138323,-0.01442493,0.0204065,0.00914053,-0.0019447,-0.04699206,0.02849527,-0.02076674,0.0290326,0.06347708,0.00278923,0.00496028,-0.0142589,-0.03723364,-0.05923196,-0.02448019,-0.01910042,0.0220217,0.00855515,-0.0528697,-0.01972558,-0.00439131,-0.01869582,0.00908783,0.00959403,-0.00021515,-0.08309188,0.04045965,0.02172821,0.0007021,-0.00866715,0.01989246,0.0549433,-0.11054762,0.10763492,-0.05870714,-0.02003447,-0.01899391,-0.03740962,0.05414626,-0.03817413,0.00275938,-0.0565781,-0.0511129,-0.008896,-0.0787117,-0.05738183,-0.00294438,-0.01234764,0.00902344,0.07265195,-0.00803995,0.01880947,-0.01497222,0.00393925,-0.03716311,-0.00808383,0.06338137,-0.00123685,-0.03705358,-0.01431341,0.08104147,0.07421395,0.01577847,0.05477134,0.05801526,-0.00853982,-0.08601075,0.00659707,-0.00066624,0.00575209,-0.0642978,-0.01944269,-0.05532237,-0.06897014,-0.0006035,-0.04179059,0.05199831,-0.09168571,-0.02185246,0.07604077,-0.02363751,0.0154769,0.00752237,-0.05746599,0.00107816,0.06266869,-0.02036721,-0.04353649,-0.03179316,0.01223163,0.06198072,0.14219767,-0.04560928,-0.01960571,0.03742439,-0.01835598,-0.06203867,0.18800765,0.05964135,-0.12183636,-0.00149254,0.00161218,0.02995816,-0.06540678,-0.0229402,-0.01085613,0.03646402,0.01580908,0.01932431,-0.01559152,-0.0245842,-0.03814994,-0.01941379,0.02446697,0.03265538,-0.04423305,-0.0952477,0.02695595,0.03192161,-0.03298825,-0.01975452,-0.0567536,-0.00910852,-0.04932443,-0.10314599,0.02288319,0.02287771,-0.00152082,-0.008151,-0.09219439,0.03144956,-0.00180096,0.04274304,-0.02566262,0.09638624,0.01885891,-0.04497246,-0.02201186,-0.06464469,-0.00384924,-0.01288464,-0.01965159,0.02687767,0.06270298,0.01630867,0.00138881,0.01551693,0.0322845,-0.01898714,0.02933395,0.02694021,0.00661333,0.02710581,0.02818485,0.00751981,0.03883752,-0.11341263,-0.21056512,-0.0374429,0.0350747,-0.04711122,-0.01305619,-0.02570218,0.00988971,0.00108737,0.05557065,0.04285194,0.12964168,0.07933167,-0.0728061,-0.03053247,0.00778645,0.00165097,0.00548003,-0.01482046,-0.01006564,0.02189653,0.00739961,0.07606923,-0.03735292,0.01410931,0.05255836,-0.0163266,0.11759833,-0.01139807,0.05016252,-0.00099103,0.0114365,0.03056606,0.0346247,-0.09181752,0.04421831,0.04436633,-0.00177543,-0.00786915,0.00366182,-0.00945608,-0.03906716,0.03378309,-0.03177112,-0.08887833,-0.02058633,-0.08747424,-0.01422468,-0.02332859,-0.03403406,0.01085754,-0.00238813,0.00000851,0.03703987,0.017704,0.04972185,-0.03315502,-0.04274234,-0.10218325,0.00325583,0.0366413,-0.01035661,0.01366065,0.02440566,0.01679006,-0.00615664,-0.00330279,-0.02530462,0.01253292,-0.0177726,-0.02840361,-0.07755062,0.16532165,-0.00093599,-0.00904383,0.03618552,0.03199502,-0.02246392,-0.0240397,0.04034518,0.01013841,0.07428595,0.02047782,0.04126088,0.01071698,0.02255622,0.0520796,0.05583666,0.01862102,0.05562165,-0.04876726,-0.02783865,-0.01202705,-0.02468061,0.02055918,0.09805149,0.00196386,-0.28879821,0.02349556,-0.00910377,0.00820666,0.02082215,0.01178601,0.06950571,0.01195627,-0.0368139,0.00001564,-0.02728375,0.05254489,0.01806543,-0.00371527,-0.00195363,-0.0397393,0.08927601,-0.03834694,0.04674829,0.00552212,-0.05690398,0.06643287,0.21991327,0.04108594,0.0574442,0.03271834,-0.00411404,0.06946852,0.01922215,0.05716197,0.03749966,-0.05648591,-0.00282669,0.01407156,0.00558541,0.0270254,0.0107066,-0.01519013,-0.01438345,0.01448169,-0.02048761,0.03579588,-0.07451219,0.04174762,0.09082277,0.00690835,-0.00039638,-0.03881931,0.02611483,0.02505057,0.02791942,0.01994305,-0.01180927,-0.0210661,0.07591708,0.04674799,0.0295406,-0.00615252,-0.06278749,0.01632958,0.00921919,-0.02532419,0.06972771,0.08803521,0.05985518],"last_embed":{"hash":"51e74174c245ba67403e29c25c73e1d6366aa8d367a51ac58e62b8fcf9947dcf","tokens":492}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.08014689,-0.04147562,0.03500064,-0.01128544,0.0144727,0.00872456,0.03992859,-0.03607862,-0.00556777,-0.03216424,-0.02668733,-0.00608583,-0.00720022,-0.10756302,0.10120344,0.08803206,0.01219503,0.00491639,0.05027135,-0.04111293,0.00838005,-0.04259186,-0.04180275,0.00520021,0.00322192,-0.01584884,-0.04904326,-0.00970864,0.02760174,0.04471378,0.04649296,-0.01092592,0.04785689,-0.01882946,-0.00315592,0.03514886,0.01508904,0.0710916,0.02785713,-0.03217205,-0.02829048,0.0441024,-0.00160519,0.01328674,-0.02073246,0.01917424,-0.02255541,-0.04395876,0.05353205,0.06215701,0.02840415,-0.00382911,0.00492596,-0.00660809,-0.03202424,0.07325082,-0.02314448,-0.05326503,0.03213634,-0.00523666,0.02275247,0.00105529,0.02806245,-0.02332449,0.05218521,-0.00549224,0.01199341,-0.00291662,0.01545827,-0.02790013,-0.09249269,-0.06757541,0.0575798,0.0226936,0.00549951,0.0074992,0.03710078,-0.01278078,-0.00449678,0.04786444,-0.0114733,0.00742585,0.0377855,0.01620026,0.02837441,-0.04757548,0.0428546,-0.01170223,-0.01455245,0.02050152,0.02862774,-0.01807325,0.02191691,-0.05768709,0.06283541,0.02091803,-0.0268391,-0.01025064,0.0315054,-0.02093556,0.05110221,-0.01365145,0.00740896,0.00362766,0.02031709,-0.01018356,-0.02233055,0.03850415,-0.03974698,0.00991774,-0.00602728,-0.01176851,-0.04265671,-0.00848122,-0.0245535,0.02628214,0.00145433,-0.06375597,0.01940472,0.07106905,0.07293613,0.0483034,0.00447577,0.02643922,-0.09645244,-0.05680794,0.00702131,0.00912939,0.03503934,-0.02675768,0.06467775,0.04007041,0.01135304,-0.02975474,0.05615199,0.04689775,0.06611555,-0.02132134,-0.01402683,-0.0352464,-0.04302781,-0.01343314,0.01409096,0.04022893,-0.00940023,0.00193234,-0.04502902,-0.01189341,0.02660858,0.02015163,0.07608193,0.00897715,-0.07120373,-0.04992766,-0.02464909,-0.03643132,-0.02698291,0.01154518,0.00935927,0.01899378,-0.02234565,-0.01272232,-0.05295961,0.00564879,-0.02167992,-0.02203937,-0.02108796,0.04735168,0.00184771,0.02553103,-0.00141388,0.03792357,0.03275548,-0.03476477,0.00336289,-0.00490259,0.01756647,0.02039688,-0.05225172,0.05914744,0.01416187,-0.05872569,0.02179311,0.00470528,0.01200619,-0.03721573,-0.00387951,-0.00283784,-0.01819697,-0.00061375,0.04036855,0.02161875,0.02830029,0.03502655,0.0201858,0.00630553,0.02893486,-0.06023515,0.01259831,0.00795949,-0.0220623,-0.02014577,0.00725442,-0.07884201,0.06783468,0.00808172,0.00890738,-0.03852161,-0.04377229,0.03079564,-0.08387316,0.016538,0.01705447,0.00144135,0.02142576,-0.00189952,0.00734491,0.02983867,0.02945978,-0.06789765,-0.03467253,0.03698219,-0.06730141,0.00924936,-0.05184284,-0.01874212,-0.0143326,-0.01209625,0.02361999,0.02325261,0.01014606,0.06468906,0.00511258,0.00783698,-0.0238375,-0.00466865,-0.01449606,-0.00747782,0.00306754,-0.04184938,-0.02593678,-0.00092618,0.03395941,-0.01830357,-0.04353294,-0.02002848,-0.03757293,0.01273691,0.02388883,0.04878403,0.01817464,0.0754351,-0.01727095,0.04136829,-0.05416614,-0.0687514,-0.06393819,-0.00771781,0.08870275,-0.04930104,0.05453454,0.04214375,0.03367712,0.00629332,-0.02699081,0.00349801,-0.03516921,-0.02373323,0.00729247,-0.00234196,0.01897143,-0.02124215,0.00154809,0.00232962,-0.06858968,-0.02044829,-0.01082328,-0.03705288,0.01714049,-0.03334174,0.04424388,-0.01641465,0.00671755,-0.02581995,0.02378444,0.02940299,-0.02236043,0.03878722,0.05192382,-0.00255904,0.01985733,-0.03673519,-0.0616978,-0.01614994,-0.00095551,-0.03072533,0.0550073,0.02374186,0.01603725,-0.08568979,-0.00611062,0.00506384,0.03063451,-0.03749162,0.01697995,-0.01544748,0.00222627,-0.00348109,-0.01327706,-0.00974556,-0.01308488,-0.04075616,-0.01787078,-0.04543636,0.00139754,0.01019913,0.0174534,0.00609169,-0.05051492,-0.0163843,0.00578061,0.04612228,-0.07507339,-0.01324691,-0.0239362,-0.03931187,-0.03727888,0.02088782,0.09232369,0.012591,-0.01793322,0.01911806,-0.03356775,-0.0105631,0.00185447,0.04887802,0.01179356,-0.07132068,0.00875656,-0.01817567,-0.01608582,0.01027092,0.00439121,-0.01945619,-0.0075483,0.04103969,0.01990186,0.02941062,-0.03144077,-0.08130896,-0.0156516,0.03556971,-0.06120015,0.01685327,-0.02702235,0.01349032,-0.04463579,-0.033427,-0.0634656,-0.018834,0.02846869,-0.00406291,-0.01517769,0.04140531,0.01735033,0.03376449,0.06947131,0.00165972,-0.00886502,0.01194979,0.01537848,-0.01072961,0.01076047,0.04616946,-0.04213484,-0.00684799,-0.06278794,0.03723389,-0.00015142,-0.00913714,0.00668195,-0.09214246,0.01771917,-0.00325944,-0.02611184,0.00137082,-0.0397397,0.03042328,0.0332319,-0.11566865,0.00430526,-0.01062586,-0.07060339,-0.00716907,0.02671275,0.01436068,-0.00466049,0.00842054,-0.02141185,-0.01227011,0.0336914,-0.00869633,-0.02625033,0.02822937,-0.04260713,0.02862324,-0.03823936,0.03398212,-0.00613317,0.00143553,0.0109527,0.04351343,-0.04951335,0.03753269,0.02634486,-0.02693453,0.07511206,-0.08806961,-0.04618441,-0.02426024,0.02196947,-0.02630547,0.03560736,0.00474036,-0.02400006,0.00740212,-0.02434458,-0.00789974,-0.0436761,-0.0051632,-0.03854988,0.00077815,-0.02704061,-0.04171671,0.03269207,-0.03719791,0.00591891,-0.03368912,0.02729607,-0.03512232,-0.02805808,-0.03988543,0.01784632,0.00796866,-0.03997582,-0.02180879,0.02638914,-0.04533687,0.01921154,0.02886885,0.00751293,-0.02779791,-0.05203898,0.01884731,0.04170284,0.013944,0.01338164,-0.00265903,0.02286014,0.01692605,-0.06651939,0.02454606,-0.00511079,-0.03592975,0.0722128,0.05314937,0.04556262,0.0153691,-0.00241946,0.00903153,-0.02877657,-0.06682514,0.00504293,-0.02106632,0.0511759,-0.07702306,0.00338941,0.01699193,-0.0221003,-0.01061515,0.00301061,0.03365047,-0.06721902,-0.06706639,-0.02956918,0.02629892,-0.00863644,0.02565961,0.0124257,-0.05440546,0.03270742,0.00664237,-0.00535712,0.01123813,-0.02171608,0.01786718,0.0505023,0.03792293,0.01669675,0.03125912,0.01863004,0.04938445,-0.02142895,0.0022341,0.03868087,0.04615235,-0.04081708,0.00459624,-0.00634308,-0.03059321,-0.00647141,-0.0008782,-0.00236676,-0.00662493,0.00395597,0.00412061,-0.03105036,0.09217443,0.03692768,-0.03353153,0.03109051,0.0080566,-0.00954142,-0.01798333,0.02580041,-0.01603426,-0.01451613,0.0246581,0.05369789,0.03875807,0.02322228,-0.00769895,0.04107919,-0.00825599,0.00573226,0.01112269,0.08650768,-0.01114616,-0.06326374,0.0484441,0.01703659,0.00393776,-0.01516687,0.02776248,0.01012402,-0.04768074,0.01691117,-0.01740078,0.01732033,-0.00517027,0.01738554,0.00751628,0.03169468,-0.0035293,-0.00979568,0.03930148,-0.00387467,-0.03169348,0.01030277,0.07852548,0.00903086,0.00174361,0.0222197,0.01785285,-0.00761329,0.01007832,-0.07703492,0.00277227,0.02203407,0.02814447,-0.15715076,-0.02334831,0.04792852,-0.02068603,-0.06628451,-0.04135782,0.0000935,-0.04536201,0.03012388,-0.00024351,0.03564178,-0.00470643,0.04924837,-0.02206297,-0.01643436,-0.0186441,-0.010983,-0.02569436,0.0490881,-0.01116459,0.08048522,-0.07449501,0.01174045,-0.03583945,-0.02396616,-0.00107134,-0.05364481,0.0692326,0.00781653,-0.03379107,0.00398924,0.02136783,-0.03574084,-0.05948011,0.04886698,-0.02579086,-0.02981667,-0.07369298,-0.02137142,0.07249459,-0.02504987,0.0673102,-0.03218824,-0.027947,0.02243569,-0.01027871,0.05159805,-0.01494494,0.02963127,-0.00215668,-0.08451729,-0.05086086,-0.03525945,-0.05608295,0.03875497,0.02037929,0.01961171,0.05389925,-0.00368243,0.00671727,-0.03728859,-0.00740059,0.01665745,0.01536004,0.02660825,0.00719537,-0.03627004,-0.0386441,0.02086622,-0.00208448,0.06680515,-0.03303148,0.02608551,-0.01487824,-0.05339959,-0.01634888,-0.04945728,-0.04401569,-0.04846234,0.00421567,0.05761611,0.00426872,0.03201902,-0.00283679,0.01139017,-0.02361319,0.00419222,0.01796745,-0.00592236,0.07892176,-0.01737832,-0.01399882,-0.01472485,0.04071793,-0.0419062,-0.01183605,0.02348409,-0.00436235,0.00719184,-0.01194215,0.04140921,-0.05015456,0.02503019,0.00678264,0.01443817,0.01506402,-0.01964099,0.0086943,-0.01406966,-0.01791674,0.022216,-0.02546889,0.04306569,-0.00723915,0.03086261,-0.02070949,0.0165799,-0.05891799,-0.03559234,-0.00382842,-0.03040247,0.0374278,0.04961615,0.01410733,-0.00489275,-0.03730416,-0.01152202,0.04034546,-0.01120421,0.02145113,0.00443082,0.01023755,-0.0537339,0.01707208,-0.04102706,0.00748023,-0.02327976,0.02127378,-0.02897862,0.0391879,0.00498125,-0.05080906,-0.02042054,-0.03792091,0.03468941,-0.00783782,-0.00170228,0.0388461,-0.03951829,0.00550204,0.00607255,0.07487029,0.08723815,0.0418806,-0.08614739,0.00772465,-0.00640015,0.01368332,0.0244625,0.04428898,-0.05548547,-0.04432261,-0.03634519,0.03816789,0.02185211,-0.06604615,-0.01269408,-0.00694724,0.03340492,-0.01569615,0.10969057,-0.02892202,0.01608693,-0.04431554,0.06966168,0.04422122,0.00829585,0.01825846,0.04262215,-0.00855271,-0.01238395,0.06680775,-0.04011923,0.08021802,0.08415008,-0.00407753,-0.00853873,-0.0073734,9e-7,-0.06771477,-0.03742681,-0.04520343,-0.04141627,0.01066223,-0.04612757,-0.06483554,0.03796402,-0.02529904],"last_embed":{"tokens":788,"hash":"1fk7js9"}}},"last_read":{"hash":"1fk7js9","at":1751079987677},"class_name":"SmartSource","outlinks":[{"title":"哈希算法","target":"哈希算法","line":29},{"title":"数字证书","target":"数字证书","line":65},{"title":"X.509证书","target":"X.509证书","line":65}],"metadata":{"aliases":["Transport Layer Security","传输层安全"],"英文":"Transport Layer Security","tags":["计算机网络/OSI模型/传输层"],"cssclasses":["editor-full"]},"blocks":{"#---frontmatter---":[1,10],"#简介":[11,22],"#简介#{1}":[12,13],"#简介#{2}":[14,14],"#简介#{3}":[15,16],"#简介#{4}":[17,17],"#简介#{5}":[18,20],"#简介#{6}":[21,22],"#版本发展":[23,33],"#版本发展#{1}":[24,25],"#版本发展#{2}":[26,27],"#版本发展#{3}":[28,29],"#版本发展#{4}":[30,32],"#版本发展#{5}":[33,33],"#运行原理":[34,94],"#运行原理#. 握手过程（Handshake）":[36,74],"#运行原理#. 握手过程（Handshake）#{1}":[38,57],"#运行原理#. 握手过程（Handshake）#{2}":[58,60],"#运行原理#. 握手过程（Handshake）#{3}":[61,63],"#运行原理#. 握手过程（Handshake）#{4}":[64,66],"#运行原理#. 握手过程（Handshake）#{5}":[67,69],"#运行原理#. 握手过程（Handshake）#{6}":[70,73],"#运行原理#. 握手过程（Handshake）#{7}":[74,74],"#运行原理#数据传输过程":[75,94],"#运行原理#数据传输过程#{1}":[77,90],"#运行原理#数据传输过程#{2}":[91,91],"#运行原理#数据传输过程#{3}":[92,92],"#运行原理#数据传输过程#{4}":[93,93],"#运行原理#数据传输过程#{5}":[94,94]},"last_import":{"mtime":1737625442688,"size":3287,"at":1748488128956,"hash":"1fk7js9"},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md","last_embed":{"hash":"1fk7js9","at":1751079987677}},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#运行原理": {"path":null,"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.09097586,-0.04948002,0.01725791,-0.02677047,-0.02382223,0.01259879,0.03767203,-0.03660544,0.00728361,-0.05141647,-0.00906159,-0.0024389,0.00832398,-0.07560937,0.08693384,0.10605592,-0.01503302,0.00238686,0.01736873,-0.05105971,0.02044692,-0.00740154,-0.05668904,-0.00746788,0.02635336,0.01628235,-0.03097655,0.01538591,0.01963722,0.05641029,0.04088984,-0.04014716,0.03672984,-0.00543675,-0.02295441,0.03606987,-0.00799858,0.07869247,0.03847069,-0.05305585,-0.03736538,0.00919059,0.01288634,0.01637576,-0.05837889,0.03841632,-0.00094811,-0.01614852,0.02145408,0.04343058,-0.00963162,-0.01872706,0.01147713,-0.00066702,-0.04061114,0.05682125,-0.02027354,-0.03864435,0.00509608,0.0020975,0.02202987,0.00966424,0.04381754,0.00036199,0.01326739,-0.00882505,-0.00106625,-0.02209108,0.00769059,-0.02549301,-0.05978295,-0.06399396,0.06186492,-0.02511872,-0.00333454,-0.00674666,-0.01622012,0.00547066,-0.01232623,0.06593596,0.00528018,0.00831092,-0.00251311,0.03536976,0.03088901,-0.03362348,0.00583297,-0.0361582,-0.04725797,0.03379236,0.05471208,-0.03233222,0.01782764,-0.03533342,0.07026708,0.02349121,-0.02513763,-0.00913961,0.04306387,-0.05682539,0.05087114,-0.01406369,-0.01798504,0.00405702,0.0112048,-0.00855908,0.0050989,0.01878612,-0.03423794,-0.01618833,0.00693575,-0.02212313,-0.00478063,-0.02968462,-0.03648976,0.02920404,0.04432218,-0.01085553,0.01843067,0.07339095,0.06386227,0.03817577,0.00763955,0.02850441,-0.08923955,-0.03665015,0.02268632,-0.02246506,0.00460046,-0.01800312,0.04237849,0.03411877,0.00478665,0.00977461,0.04384068,0.00934779,0.08443395,0.01020767,-0.03524357,-0.02690943,-0.0337945,-0.00409767,-0.01218128,0.03017753,0.01777979,0.00425515,-0.05391114,-0.01812179,0.02404039,0.0221458,0.08112611,-0.03460316,-0.09938768,-0.0542005,-0.05208788,0.00393083,-0.0074679,0.01769603,-0.01800358,0.0120426,-0.02811157,-0.02212001,-0.07263877,0.00576771,-0.0138494,-0.00530398,-0.00728737,0.02539595,-0.02406728,-0.01224351,-0.01027653,0.03833914,0.02175181,-0.03982525,0.01061139,0.01837538,0.04010848,0.02045804,-0.06348229,0.04222313,0.00547543,-0.01927595,0.01442484,0.00742172,0.03836849,-0.01370986,-0.01223237,0.02523378,0.03978405,0.0016706,0.03281927,-0.00523449,0.032488,0.02538606,0.0458682,0.01945326,0.00922839,-0.02524973,0.01373135,-0.00152273,-0.04802487,-0.03048808,0.01239128,-0.07906905,0.05936058,0.02271077,0.02909959,-0.04844667,-0.0530387,0.03828524,-0.05544147,0.01221433,0.05740096,0.02684779,0.03023145,-0.00691048,0.01943548,0.04090269,0.05123886,-0.04686607,-0.03276811,0.0452103,-0.0647821,0.00764498,-0.04762344,-0.02300115,0.01702232,-0.00455668,0.05014133,0.04287251,0.00397531,0.08346455,0.00820836,-0.02668221,-0.05598772,0.00357622,-0.00417065,0.00223033,-0.02296341,-0.02797824,-0.02804581,-0.025759,-0.00295739,-0.04402059,-0.02917908,-0.0306617,-0.01252653,0.00850815,0.0182801,0.04954919,0.01911401,0.06622785,-0.04168458,0.04629502,-0.01094395,-0.08189124,-0.0420881,0.0053177,0.06488732,-0.03643408,0.04387089,0.02746416,0.0342934,0.02397,0.02124689,-0.00394187,-0.01003182,-0.02764105,0.02007401,0.00440652,0.00742485,0.01588562,-0.0047618,-0.00261408,-0.07011517,-0.01776642,-0.0060928,-0.04247865,-0.00700751,-0.02147812,0.06238891,0.01975016,0.00914128,0.00355258,0.02150427,0.03254071,-0.01308659,0.01802036,0.03478319,0.00523494,-0.00423196,-0.01186803,-0.04022816,0.00147819,-0.00269653,-0.01118575,0.05062116,0.01883712,0.0031811,-0.06410357,-0.02247619,-0.01537408,-0.00744397,0.00100776,0.03955821,0.0061941,0.00143551,-0.0294215,-0.01352599,-0.02591503,-0.03542699,-0.05467281,-0.01618623,-0.02884241,-0.01179046,0.01059894,0.05138452,0.03441425,-0.02644615,-0.02657174,0.00891066,0.05946948,-0.10523368,0.00764968,-0.03770754,-0.05495653,-0.0018854,0.05553505,0.06564654,0.02961783,-0.02452935,0.02215017,-0.03519761,-0.00788276,0.03032604,0.05311223,-0.00220716,-0.0729203,-0.00351916,-0.04393858,-0.01862245,0.02910056,0.02567422,0.00592018,0.02580639,0.05419168,0.00618285,0.04617443,-0.03642545,-0.09179181,-0.01525819,0.0217753,-0.06131014,0.0197328,-0.00777908,0.04594838,-0.03530187,-0.0333981,-0.05938267,-0.01735668,0.02512032,-0.03066452,-0.06231206,0.0397411,0.03875655,-0.01929178,0.05905589,0.04566452,0.00413763,0.0204484,-0.00891384,-0.02397305,0.0053841,-0.01971285,-0.03759848,-0.00815336,-0.0817678,0.04075457,0.0083863,0.00653225,-0.02657964,-0.05553098,0.01308561,0.00072767,-0.00878938,0.02689441,-0.05971494,0.03427712,0.02033113,-0.08048549,0.00449198,-0.00626921,-0.08939566,0.00723726,0.02730686,0.02351426,0.00408207,0.00124348,-0.02109806,0.01046987,0.03263976,-0.00327652,-0.01601058,0.03419868,-0.05806824,0.01571221,-0.03673052,0.02648389,-0.01229658,-0.01278741,0.01215914,0.0353717,-0.05575107,0.03667025,-0.00502194,-0.01142576,0.04868884,-0.08292922,-0.02949773,-0.02997195,0.00016836,-0.02652467,0.02676042,-0.02552638,-0.02465071,-0.00485853,-0.03713441,-0.01116357,-0.03206011,-0.03049834,-0.02072472,0.00030609,-0.02855506,-0.02954827,0.05701225,-0.0162685,-0.01421281,-0.012879,0.03171262,-0.05807386,-0.0328684,-0.02771849,-0.00049531,-0.0077021,-0.0346051,-0.01255459,0.04452737,0.00026618,-0.00085371,0.05006214,0.00071098,-0.00917406,-0.0410056,-0.01516166,0.0489867,-0.01348654,0.01433681,0.00779785,0.03659776,0.01524356,-0.10531253,0.04135906,0.04986604,-0.00971102,0.04829089,0.03705431,0.02248354,-0.00105662,0.02722412,-0.01799009,-0.02965743,-0.03891937,0.01658381,-0.01855645,0.0719816,-0.05352611,0.01165175,-0.01283448,-0.02471103,-0.01834392,-0.02584,0.03276673,-0.05035723,-0.05138573,-0.01387936,0.04324964,-0.02063614,0.03201163,-0.00417417,-0.06401852,0.04992083,-0.00535431,-0.01226967,-0.0040012,-0.02675698,0.04706633,0.03575235,0.03897145,0.00632759,0.03279387,0.0034971,0.08053058,-0.04334298,0.04001109,0.0250557,0.02543581,-0.01448769,-0.01366114,0.00269214,-0.04148179,-0.0158509,0.01231092,0.02165689,-0.01145706,-0.0003406,0.02897699,-0.0004563,0.08208461,0.03973096,-0.01378918,0.01642727,0.01869494,-0.02780168,-0.02055585,0.05080948,-0.01185177,-0.01050455,-0.0000332,0.06111855,0.04637367,0.04013325,-0.03547935,0.07890901,0.00117185,-0.00213406,0.01452331,0.04083223,-0.03079598,-0.03186415,0.05867762,0.02151353,-0.00350476,0.00664269,-0.00049969,0.01822872,-0.05416391,0.01519655,-0.04972825,-0.00963541,0.00125083,0.02715869,-0.00437358,0.02516365,-0.00228521,0.00885924,0.03315434,0.0380305,-0.05570336,-0.01167658,0.08590195,0.02280441,-0.02675945,0.04089596,-0.00663783,-0.0122101,0.04455223,-0.06105402,0.00320418,0.00360067,0.01052174,-0.14250267,-0.0431741,0.07155903,0.00310303,-0.05888462,-0.00842597,-0.01943191,-0.05596297,0.01277248,0.0284837,0.06436522,-0.00189263,0.02738814,-0.00535735,-0.04631459,-0.02630736,-0.007301,0.01647826,0.02393369,0.00357087,0.10909902,-0.05816155,0.02319169,-0.03110307,-0.00926825,0.01551596,-0.04591598,0.05596978,-0.00676105,-0.01168692,-0.00957898,0.03483864,-0.03643214,-0.047094,0.03212721,-0.02566709,-0.01327061,-0.10224904,-0.03621898,0.05792369,-0.00725434,0.08478974,-0.04565672,-0.02038316,0.02174365,-0.0353867,0.02738873,-0.01306377,0.04358126,0.00005573,-0.07698143,-0.01996163,-0.02581479,-0.0122941,0.02358831,-0.02337311,0.02524709,0.04267708,0.00678445,0.00349209,0.00125834,-0.00670082,-0.0114757,0.01074472,0.06399865,0.01234411,-0.03834926,-0.04820907,0.02102316,0.00839682,0.05918324,-0.03057407,0.01675897,0.00128936,-0.05226858,-0.01634052,-0.04098327,-0.04559113,-0.03350284,0.00312249,0.05388661,0.02113452,0.02559686,-0.00943371,0.02800466,-0.02283309,-0.00134579,0.02013495,-0.00310715,0.08751601,-0.04519052,-0.02719679,-0.01418713,0.05823846,-0.03471004,-0.03054094,0.03566127,0.00764366,0.00470712,-0.01744762,0.0412141,-0.05389629,0.03755273,0.00145633,0.0180333,0.00758879,-0.01666017,0.04120998,0.02519749,-0.02167296,0.0131553,-0.00950748,0.01545992,-0.02119156,0.0439887,-0.02102741,0.01155623,-0.04898041,-0.05449126,-0.02383454,-0.02031094,0.03201209,0.02359309,0.03768962,0.02806893,-0.02462392,-0.02731081,0.03953275,-0.02121473,0.03307603,0.01322223,-0.00495062,-0.0335967,0.00835504,-0.04516605,0.0111983,-0.04492184,0.03039662,-0.01508317,0.00264205,0.02742425,-0.05791297,-0.02355488,-0.02546746,0.01205289,0.00309987,0.00445065,0.02159164,-0.02615355,-0.01091643,0.02085468,0.08112995,0.09971251,0.05566328,-0.06605945,0.01349421,0.01147033,0.02237194,0.02418699,0.02879661,-0.03385776,-0.07079851,-0.03412035,0.01529977,0.02143905,-0.03360909,0.00723634,-0.01529571,0.03708225,-0.01558087,0.09493244,-0.03674271,0.00127038,-0.08267664,0.08290379,0.03028307,-0.0111846,0.02990485,0.01767778,-0.02671163,-0.02841428,0.01832145,-0.05744281,0.0271346,0.05634373,-0.03030596,-0.02345579,-0.03469301,0.000001,-0.05865973,-0.06857671,-0.04359193,-0.02704385,0.01472688,-0.0600428,-0.0808636,0.00190727,-0.02645445],"last_embed":{"hash":"1iu99n7","tokens":518}}},"text":null,"length":0,"last_read":{"hash":"1iu99n7","at":1749002760728},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#运行原理","lines":[34,94],"size":1356,"outlinks":[{"title":"数字证书","target":"数字证书","line":32},{"title":"X.509证书","target":"X.509证书","line":32}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#---frontmatter---","lines":[1,10],"size":138,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#简介": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#简介","lines":[11,22],"size":239,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#简介#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#简介#{1}","lines":[12,13],"size":39,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#简介#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#简介#{2}","lines":[14,14],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#简介#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#简介#{3}","lines":[15,16],"size":66,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#简介#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#简介#{4}","lines":[17,17],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#简介#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#简介#{5}","lines":[18,20],"size":114,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#简介#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#简介#{6}","lines":[21,22],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#版本发展": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#版本发展","lines":[23,33],"size":237,"outlinks":[{"title":"哈希算法","target":"哈希算法","line":7}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#版本发展#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#版本发展#{1}","lines":[24,25],"size":50,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#版本发展#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#版本发展#{2}","lines":[26,27],"size":48,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#版本发展#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#版本发展#{3}","lines":[28,29],"size":56,"outlinks":[{"title":"哈希算法","target":"哈希算法","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#版本发展#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#版本发展#{4}","lines":[30,32],"size":69,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#版本发展#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#版本发展#{5}","lines":[33,33],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#运行原理#. 握手过程（Handshake）": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#运行原理#. 握手过程（Handshake）","lines":[36,74],"size":912,"outlinks":[{"title":"数字证书","target":"数字证书","line":30},{"title":"X.509证书","target":"X.509证书","line":30}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#运行原理#. 握手过程（Handshake）#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#运行原理#. 握手过程（Handshake）#{1}","lines":[38,57],"size":526,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#运行原理#. 握手过程（Handshake）#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#运行原理#. 握手过程（Handshake）#{2}","lines":[58,60],"size":90,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#运行原理#. 握手过程（Handshake）#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#运行原理#. 握手过程（Handshake）#{3}","lines":[61,63],"size":69,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#运行原理#. 握手过程（Handshake）#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#运行原理#. 握手过程（Handshake）#{4}","lines":[64,66],"size":70,"outlinks":[{"title":"数字证书","target":"数字证书","line":2},{"title":"X.509证书","target":"X.509证书","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#运行原理#. 握手过程（Handshake）#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#运行原理#. 握手过程（Handshake）#{5}","lines":[67,69],"size":73,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#运行原理#. 握手过程（Handshake）#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#运行原理#. 握手过程（Handshake）#{6}","lines":[70,73],"size":51,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#运行原理#. 握手过程（Handshake）#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#运行原理#. 握手过程（Handshake）#{7}","lines":[74,74],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#运行原理#数据传输过程": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#运行原理#数据传输过程","lines":[75,94],"size":434,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#运行原理#数据传输过程#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#运行原理#数据传输过程#{1}","lines":[77,90],"size":289,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#运行原理#数据传输过程#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#运行原理#数据传输过程#{2}","lines":[91,91],"size":33,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#运行原理#数据传输过程#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#运行原理#数据传输过程#{3}","lines":[92,92],"size":33,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#运行原理#数据传输过程#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#运行原理#数据传输过程#{4}","lines":[93,93],"size":32,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#运行原理#数据传输过程#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md#运行原理#数据传输过程#{5}","lines":[94,94],"size":32,"outlinks":[],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md": {"path":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07512741,-0.01241893,-0.02464014,-0.08194825,-0.01086793,-0.00470211,0.00775258,0.02693584,0.04407761,-0.00824355,-0.00245932,-0.06001249,0.07070758,0.04640101,0.05745487,0.0374518,0.05892456,0.00961305,-0.00685717,-0.00633822,0.06425914,-0.07193429,-0.02271962,-0.05113329,0.00713011,-0.01120522,0.00626754,-0.03696421,-0.01998075,-0.15782644,-0.02707082,-0.00422852,-0.03997706,0.04226858,-0.029076,-0.01358798,0.02795483,0.04809997,0.02747239,0.01468003,0.01983138,0.0207794,-0.00072996,-0.03756719,-0.03800495,-0.06620678,-0.0292836,-0.00316967,-0.03347204,-0.04774076,-0.05720865,-0.01832374,-0.02016958,0.02809825,-0.05964166,-0.01070178,0.00783605,0.04482763,0.06587037,-0.0051214,0.04253965,0.01815882,-0.22802436,0.09579319,0.0424269,0.00138323,-0.01442493,0.0204065,0.00914053,-0.0019447,-0.04699206,0.02849527,-0.02076674,0.0290326,0.06347708,0.00278923,0.00496028,-0.0142589,-0.03723364,-0.05923196,-0.02448019,-0.01910042,0.0220217,0.00855515,-0.0528697,-0.01972558,-0.00439131,-0.01869582,0.00908783,0.00959403,-0.00021515,-0.08309188,0.04045965,0.02172821,0.0007021,-0.00866715,0.01989246,0.0549433,-0.11054762,0.10763492,-0.05870714,-0.02003447,-0.01899391,-0.03740962,0.05414626,-0.03817413,0.00275938,-0.0565781,-0.0511129,-0.008896,-0.0787117,-0.05738183,-0.00294438,-0.01234764,0.00902344,0.07265195,-0.00803995,0.01880947,-0.01497222,0.00393925,-0.03716311,-0.00808383,0.06338137,-0.00123685,-0.03705358,-0.01431341,0.08104147,0.07421395,0.01577847,0.05477134,0.05801526,-0.00853982,-0.08601075,0.00659707,-0.00066624,0.00575209,-0.0642978,-0.01944269,-0.05532237,-0.06897014,-0.0006035,-0.04179059,0.05199831,-0.09168571,-0.02185246,0.07604077,-0.02363751,0.0154769,0.00752237,-0.05746599,0.00107816,0.06266869,-0.02036721,-0.04353649,-0.03179316,0.01223163,0.06198072,0.14219767,-0.04560928,-0.01960571,0.03742439,-0.01835598,-0.06203867,0.18800765,0.05964135,-0.12183636,-0.00149254,0.00161218,0.02995816,-0.06540678,-0.0229402,-0.01085613,0.03646402,0.01580908,0.01932431,-0.01559152,-0.0245842,-0.03814994,-0.01941379,0.02446697,0.03265538,-0.04423305,-0.0952477,0.02695595,0.03192161,-0.03298825,-0.01975452,-0.0567536,-0.00910852,-0.04932443,-0.10314599,0.02288319,0.02287771,-0.00152082,-0.008151,-0.09219439,0.03144956,-0.00180096,0.04274304,-0.02566262,0.09638624,0.01885891,-0.04497246,-0.02201186,-0.06464469,-0.00384924,-0.01288464,-0.01965159,0.02687767,0.06270298,0.01630867,0.00138881,0.01551693,0.0322845,-0.01898714,0.02933395,0.02694021,0.00661333,0.02710581,0.02818485,0.00751981,0.03883752,-0.11341263,-0.21056512,-0.0374429,0.0350747,-0.04711122,-0.01305619,-0.02570218,0.00988971,0.00108737,0.05557065,0.04285194,0.12964168,0.07933167,-0.0728061,-0.03053247,0.00778645,0.00165097,0.00548003,-0.01482046,-0.01006564,0.02189653,0.00739961,0.07606923,-0.03735292,0.01410931,0.05255836,-0.0163266,0.11759833,-0.01139807,0.05016252,-0.00099103,0.0114365,0.03056606,0.0346247,-0.09181752,0.04421831,0.04436633,-0.00177543,-0.00786915,0.00366182,-0.00945608,-0.03906716,0.03378309,-0.03177112,-0.08887833,-0.02058633,-0.08747424,-0.01422468,-0.02332859,-0.03403406,0.01085754,-0.00238813,0.00000851,0.03703987,0.017704,0.04972185,-0.03315502,-0.04274234,-0.10218325,0.00325583,0.0366413,-0.01035661,0.01366065,0.02440566,0.01679006,-0.00615664,-0.00330279,-0.02530462,0.01253292,-0.0177726,-0.02840361,-0.07755062,0.16532165,-0.00093599,-0.00904383,0.03618552,0.03199502,-0.02246392,-0.0240397,0.04034518,0.01013841,0.07428595,0.02047782,0.04126088,0.01071698,0.02255622,0.0520796,0.05583666,0.01862102,0.05562165,-0.04876726,-0.02783865,-0.01202705,-0.02468061,0.02055918,0.09805149,0.00196386,-0.28879821,0.02349556,-0.00910377,0.00820666,0.02082215,0.01178601,0.06950571,0.01195627,-0.0368139,0.00001564,-0.02728375,0.05254489,0.01806543,-0.00371527,-0.00195363,-0.0397393,0.08927601,-0.03834694,0.04674829,0.00552212,-0.05690398,0.06643287,0.21991327,0.04108594,0.0574442,0.03271834,-0.00411404,0.06946852,0.01922215,0.05716197,0.03749966,-0.05648591,-0.00282669,0.01407156,0.00558541,0.0270254,0.0107066,-0.01519013,-0.01438345,0.01448169,-0.02048761,0.03579588,-0.07451219,0.04174762,0.09082277,0.00690835,-0.00039638,-0.03881931,0.02611483,0.02505057,0.02791942,0.01994305,-0.01180927,-0.0210661,0.07591708,0.04674799,0.0295406,-0.00615252,-0.06278749,0.01632958,0.00921919,-0.02532419,0.06972771,0.08803521,0.05985518],"last_embed":{"hash":"51e74174c245ba67403e29c25c73e1d6366aa8d367a51ac58e62b8fcf9947dcf","tokens":492}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.08014689,-0.04147562,0.03500064,-0.01128544,0.0144727,0.00872456,0.03992859,-0.03607862,-0.00556777,-0.03216424,-0.02668733,-0.00608583,-0.00720022,-0.10756302,0.10120344,0.08803206,0.01219503,0.00491639,0.05027135,-0.04111293,0.00838005,-0.04259186,-0.04180275,0.00520021,0.00322192,-0.01584884,-0.04904326,-0.00970864,0.02760174,0.04471378,0.04649296,-0.01092592,0.04785689,-0.01882946,-0.00315592,0.03514886,0.01508904,0.0710916,0.02785713,-0.03217205,-0.02829048,0.0441024,-0.00160519,0.01328674,-0.02073246,0.01917424,-0.02255541,-0.04395876,0.05353205,0.06215701,0.02840415,-0.00382911,0.00492596,-0.00660809,-0.03202424,0.07325082,-0.02314448,-0.05326503,0.03213634,-0.00523666,0.02275247,0.00105529,0.02806245,-0.02332449,0.05218521,-0.00549224,0.01199341,-0.00291662,0.01545827,-0.02790013,-0.09249269,-0.06757541,0.0575798,0.0226936,0.00549951,0.0074992,0.03710078,-0.01278078,-0.00449678,0.04786444,-0.0114733,0.00742585,0.0377855,0.01620026,0.02837441,-0.04757548,0.0428546,-0.01170223,-0.01455245,0.02050152,0.02862774,-0.01807325,0.02191691,-0.05768709,0.06283541,0.02091803,-0.0268391,-0.01025064,0.0315054,-0.02093556,0.05110221,-0.01365145,0.00740896,0.00362766,0.02031709,-0.01018356,-0.02233055,0.03850415,-0.03974698,0.00991774,-0.00602728,-0.01176851,-0.04265671,-0.00848122,-0.0245535,0.02628214,0.00145433,-0.06375597,0.01940472,0.07106905,0.07293613,0.0483034,0.00447577,0.02643922,-0.09645244,-0.05680794,0.00702131,0.00912939,0.03503934,-0.02675768,0.06467775,0.04007041,0.01135304,-0.02975474,0.05615199,0.04689775,0.06611555,-0.02132134,-0.01402683,-0.0352464,-0.04302781,-0.01343314,0.01409096,0.04022893,-0.00940023,0.00193234,-0.04502902,-0.01189341,0.02660858,0.02015163,0.07608193,0.00897715,-0.07120373,-0.04992766,-0.02464909,-0.03643132,-0.02698291,0.01154518,0.00935927,0.01899378,-0.02234565,-0.01272232,-0.05295961,0.00564879,-0.02167992,-0.02203937,-0.02108796,0.04735168,0.00184771,0.02553103,-0.00141388,0.03792357,0.03275548,-0.03476477,0.00336289,-0.00490259,0.01756647,0.02039688,-0.05225172,0.05914744,0.01416187,-0.05872569,0.02179311,0.00470528,0.01200619,-0.03721573,-0.00387951,-0.00283784,-0.01819697,-0.00061375,0.04036855,0.02161875,0.02830029,0.03502655,0.0201858,0.00630553,0.02893486,-0.06023515,0.01259831,0.00795949,-0.0220623,-0.02014577,0.00725442,-0.07884201,0.06783468,0.00808172,0.00890738,-0.03852161,-0.04377229,0.03079564,-0.08387316,0.016538,0.01705447,0.00144135,0.02142576,-0.00189952,0.00734491,0.02983867,0.02945978,-0.06789765,-0.03467253,0.03698219,-0.06730141,0.00924936,-0.05184284,-0.01874212,-0.0143326,-0.01209625,0.02361999,0.02325261,0.01014606,0.06468906,0.00511258,0.00783698,-0.0238375,-0.00466865,-0.01449606,-0.00747782,0.00306754,-0.04184938,-0.02593678,-0.00092618,0.03395941,-0.01830357,-0.04353294,-0.02002848,-0.03757293,0.01273691,0.02388883,0.04878403,0.01817464,0.0754351,-0.01727095,0.04136829,-0.05416614,-0.0687514,-0.06393819,-0.00771781,0.08870275,-0.04930104,0.05453454,0.04214375,0.03367712,0.00629332,-0.02699081,0.00349801,-0.03516921,-0.02373323,0.00729247,-0.00234196,0.01897143,-0.02124215,0.00154809,0.00232962,-0.06858968,-0.02044829,-0.01082328,-0.03705288,0.01714049,-0.03334174,0.04424388,-0.01641465,0.00671755,-0.02581995,0.02378444,0.02940299,-0.02236043,0.03878722,0.05192382,-0.00255904,0.01985733,-0.03673519,-0.0616978,-0.01614994,-0.00095551,-0.03072533,0.0550073,0.02374186,0.01603725,-0.08568979,-0.00611062,0.00506384,0.03063451,-0.03749162,0.01697995,-0.01544748,0.00222627,-0.00348109,-0.01327706,-0.00974556,-0.01308488,-0.04075616,-0.01787078,-0.04543636,0.00139754,0.01019913,0.0174534,0.00609169,-0.05051492,-0.0163843,0.00578061,0.04612228,-0.07507339,-0.01324691,-0.0239362,-0.03931187,-0.03727888,0.02088782,0.09232369,0.012591,-0.01793322,0.01911806,-0.03356775,-0.0105631,0.00185447,0.04887802,0.01179356,-0.07132068,0.00875656,-0.01817567,-0.01608582,0.01027092,0.00439121,-0.01945619,-0.0075483,0.04103969,0.01990186,0.02941062,-0.03144077,-0.08130896,-0.0156516,0.03556971,-0.06120015,0.01685327,-0.02702235,0.01349032,-0.04463579,-0.033427,-0.0634656,-0.018834,0.02846869,-0.00406291,-0.01517769,0.04140531,0.01735033,0.03376449,0.06947131,0.00165972,-0.00886502,0.01194979,0.01537848,-0.01072961,0.01076047,0.04616946,-0.04213484,-0.00684799,-0.06278794,0.03723389,-0.00015142,-0.00913714,0.00668195,-0.09214246,0.01771917,-0.00325944,-0.02611184,0.00137082,-0.0397397,0.03042328,0.0332319,-0.11566865,0.00430526,-0.01062586,-0.07060339,-0.00716907,0.02671275,0.01436068,-0.00466049,0.00842054,-0.02141185,-0.01227011,0.0336914,-0.00869633,-0.02625033,0.02822937,-0.04260713,0.02862324,-0.03823936,0.03398212,-0.00613317,0.00143553,0.0109527,0.04351343,-0.04951335,0.03753269,0.02634486,-0.02693453,0.07511206,-0.08806961,-0.04618441,-0.02426024,0.02196947,-0.02630547,0.03560736,0.00474036,-0.02400006,0.00740212,-0.02434458,-0.00789974,-0.0436761,-0.0051632,-0.03854988,0.00077815,-0.02704061,-0.04171671,0.03269207,-0.03719791,0.00591891,-0.03368912,0.02729607,-0.03512232,-0.02805808,-0.03988543,0.01784632,0.00796866,-0.03997582,-0.02180879,0.02638914,-0.04533687,0.01921154,0.02886885,0.00751293,-0.02779791,-0.05203898,0.01884731,0.04170284,0.013944,0.01338164,-0.00265903,0.02286014,0.01692605,-0.06651939,0.02454606,-0.00511079,-0.03592975,0.0722128,0.05314937,0.04556262,0.0153691,-0.00241946,0.00903153,-0.02877657,-0.06682514,0.00504293,-0.02106632,0.0511759,-0.07702306,0.00338941,0.01699193,-0.0221003,-0.01061515,0.00301061,0.03365047,-0.06721902,-0.06706639,-0.02956918,0.02629892,-0.00863644,0.02565961,0.0124257,-0.05440546,0.03270742,0.00664237,-0.00535712,0.01123813,-0.02171608,0.01786718,0.0505023,0.03792293,0.01669675,0.03125912,0.01863004,0.04938445,-0.02142895,0.0022341,0.03868087,0.04615235,-0.04081708,0.00459624,-0.00634308,-0.03059321,-0.00647141,-0.0008782,-0.00236676,-0.00662493,0.00395597,0.00412061,-0.03105036,0.09217443,0.03692768,-0.03353153,0.03109051,0.0080566,-0.00954142,-0.01798333,0.02580041,-0.01603426,-0.01451613,0.0246581,0.05369789,0.03875807,0.02322228,-0.00769895,0.04107919,-0.00825599,0.00573226,0.01112269,0.08650768,-0.01114616,-0.06326374,0.0484441,0.01703659,0.00393776,-0.01516687,0.02776248,0.01012402,-0.04768074,0.01691117,-0.01740078,0.01732033,-0.00517027,0.01738554,0.00751628,0.03169468,-0.0035293,-0.00979568,0.03930148,-0.00387467,-0.03169348,0.01030277,0.07852548,0.00903086,0.00174361,0.0222197,0.01785285,-0.00761329,0.01007832,-0.07703492,0.00277227,0.02203407,0.02814447,-0.15715076,-0.02334831,0.04792852,-0.02068603,-0.06628451,-0.04135782,0.0000935,-0.04536201,0.03012388,-0.00024351,0.03564178,-0.00470643,0.04924837,-0.02206297,-0.01643436,-0.0186441,-0.010983,-0.02569436,0.0490881,-0.01116459,0.08048522,-0.07449501,0.01174045,-0.03583945,-0.02396616,-0.00107134,-0.05364481,0.0692326,0.00781653,-0.03379107,0.00398924,0.02136783,-0.03574084,-0.05948011,0.04886698,-0.02579086,-0.02981667,-0.07369298,-0.02137142,0.07249459,-0.02504987,0.0673102,-0.03218824,-0.027947,0.02243569,-0.01027871,0.05159805,-0.01494494,0.02963127,-0.00215668,-0.08451729,-0.05086086,-0.03525945,-0.05608295,0.03875497,0.02037929,0.01961171,0.05389925,-0.00368243,0.00671727,-0.03728859,-0.00740059,0.01665745,0.01536004,0.02660825,0.00719537,-0.03627004,-0.0386441,0.02086622,-0.00208448,0.06680515,-0.03303148,0.02608551,-0.01487824,-0.05339959,-0.01634888,-0.04945728,-0.04401569,-0.04846234,0.00421567,0.05761611,0.00426872,0.03201902,-0.00283679,0.01139017,-0.02361319,0.00419222,0.01796745,-0.00592236,0.07892176,-0.01737832,-0.01399882,-0.01472485,0.04071793,-0.0419062,-0.01183605,0.02348409,-0.00436235,0.00719184,-0.01194215,0.04140921,-0.05015456,0.02503019,0.00678264,0.01443817,0.01506402,-0.01964099,0.0086943,-0.01406966,-0.01791674,0.022216,-0.02546889,0.04306569,-0.00723915,0.03086261,-0.02070949,0.0165799,-0.05891799,-0.03559234,-0.00382842,-0.03040247,0.0374278,0.04961615,0.01410733,-0.00489275,-0.03730416,-0.01152202,0.04034546,-0.01120421,0.02145113,0.00443082,0.01023755,-0.0537339,0.01707208,-0.04102706,0.00748023,-0.02327976,0.02127378,-0.02897862,0.0391879,0.00498125,-0.05080906,-0.02042054,-0.03792091,0.03468941,-0.00783782,-0.00170228,0.0388461,-0.03951829,0.00550204,0.00607255,0.07487029,0.08723815,0.0418806,-0.08614739,0.00772465,-0.00640015,0.01368332,0.0244625,0.04428898,-0.05548547,-0.04432261,-0.03634519,0.03816789,0.02185211,-0.06604615,-0.01269408,-0.00694724,0.03340492,-0.01569615,0.10969057,-0.02892202,0.01608693,-0.04431554,0.06966168,0.04422122,0.00829585,0.01825846,0.04262215,-0.00855271,-0.01238395,0.06680775,-0.04011923,0.08021802,0.08415008,-0.00407753,-0.00853873,-0.0073734,9e-7,-0.06771477,-0.03742681,-0.04520343,-0.04141627,0.01066223,-0.04612757,-0.06483554,0.03796402,-0.02529904],"last_embed":{"tokens":788,"hash":"1fk7js9"}}},"last_read":{"hash":"1fk7js9","at":1751251725540},"class_name":"SmartSource","outlinks":[{"title":"哈希算法","target":"哈希算法","line":29},{"title":"数字证书","target":"数字证书","line":65},{"title":"X.509证书","target":"X.509证书","line":65}],"metadata":{"aliases":["Transport Layer Security","传输层安全"],"英文":"Transport Layer Security","tags":["计算机网络/OSI模型/传输层"],"cssclasses":["editor-full"]},"blocks":{"#---frontmatter---":[1,10],"#简介":[11,22],"#简介#{1}":[12,13],"#简介#{2}":[14,14],"#简介#{3}":[15,16],"#简介#{4}":[17,17],"#简介#{5}":[18,20],"#简介#{6}":[21,22],"#版本发展":[23,33],"#版本发展#{1}":[24,25],"#版本发展#{2}":[26,27],"#版本发展#{3}":[28,29],"#版本发展#{4}":[30,32],"#版本发展#{5}":[33,33],"#运行原理":[34,94],"#运行原理#. 握手过程（Handshake）":[36,74],"#运行原理#. 握手过程（Handshake）#{1}":[38,57],"#运行原理#. 握手过程（Handshake）#{2}":[58,60],"#运行原理#. 握手过程（Handshake）#{3}":[61,63],"#运行原理#. 握手过程（Handshake）#{4}":[64,66],"#运行原理#. 握手过程（Handshake）#{5}":[67,69],"#运行原理#. 握手过程（Handshake）#{6}":[70,73],"#运行原理#. 握手过程（Handshake）#{7}":[74,74],"#运行原理#数据传输过程":[75,94],"#运行原理#数据传输过程#{1}":[77,90],"#运行原理#数据传输过程#{2}":[91,91],"#运行原理#数据传输过程#{3}":[92,92],"#运行原理#数据传输过程#{4}":[93,93],"#运行原理#数据传输过程#{5}":[94,94]},"last_import":{"mtime":1737625442688,"size":3287,"at":1748488128956,"hash":"1fk7js9"},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS.md","last_embed":{"hash":"1fk7js9","at":1751251725540}},