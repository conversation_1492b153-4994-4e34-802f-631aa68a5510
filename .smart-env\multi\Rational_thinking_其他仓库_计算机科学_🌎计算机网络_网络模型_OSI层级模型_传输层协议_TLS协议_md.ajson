"smart_sources:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md": {"path":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08540659,-0.00046375,-0.01507335,-0.07364066,-0.00714499,-0.00647255,0.00321407,0.02029561,0.035677,-0.01319273,-0.0024835,-0.05426528,0.06545851,0.045006,0.06057224,0.04347195,0.05019912,-0.00462421,-0.0115068,0.01294371,0.07348473,-0.06470037,-0.01885284,-0.04731334,0.01124593,-0.01594077,-0.00239452,-0.03003815,0.00106114,-0.15297122,-0.03084683,-0.01531859,-0.03349829,0.03042394,-0.01805021,-0.01955041,0.0360923,0.0444047,0.0242017,0.02295053,0.03101605,0.03684675,0.00461339,-0.02864261,-0.05706513,-0.0463988,-0.03254037,-0.00161524,-0.02503913,-0.04579925,-0.03238568,-0.01079379,-0.02471824,0.02734996,-0.06423585,-0.01843669,0.01434459,0.04515197,0.07345204,-0.00817002,0.03796137,0.0105244,-0.24359931,0.09260968,0.05323492,0.00014311,-0.02099111,0.02603666,0.01866943,0.00225047,-0.04243284,0.01965303,-0.03657839,0.02752189,0.06111428,0.00797056,0.00298686,-0.00677272,-0.04305148,-0.06178217,-0.01266669,0.01115424,0.01039482,0.01506253,-0.05565659,-0.02870709,-0.01215303,-0.0247162,0.02927125,0.01042207,0.02116509,-0.07970528,0.0449396,0.02714876,0.01201988,-0.0191777,0.02212577,0.05825073,-0.11136358,0.09671837,-0.05665396,-0.02438807,-0.01199981,-0.04042111,0.04672823,-0.05074838,0.01270161,-0.05887093,-0.0474486,-0.02074388,-0.06668707,-0.03860913,0.00276321,-0.01191882,0.03717187,0.04719494,-0.00758547,0.01457631,-0.01842495,-0.00345533,-0.02161363,-0.00988946,0.06955247,0.00700272,-0.03400138,-0.02502742,0.08736593,0.08069331,0.0028187,0.06216842,0.06026968,-0.01562317,-0.0690873,-0.01844711,-0.01682807,0.01257466,-0.05653744,0.00131634,-0.07380376,-0.06720135,0.00789428,-0.0359561,0.04718262,-0.09661504,-0.02725244,0.07599149,-0.01502204,0.00413003,0.03010058,-0.05482132,0.00331593,0.04381028,-0.02371083,-0.03358334,-0.04380625,0.0020884,0.06393159,0.14720502,-0.02754441,-0.01738396,0.02556772,-0.0327527,-0.09035756,0.19202276,0.06358802,-0.12366574,-0.00047858,-0.00407423,0.0372068,-0.05898385,-0.01557743,-0.01057453,0.04092176,0.01081975,0.01578534,-0.02045317,-0.04138591,-0.04032566,-0.01307199,0.02073059,0.04198185,-0.06042841,-0.09590562,0.01788256,0.03101917,-0.02735855,-0.02393912,-0.061307,-0.01568491,-0.03496057,-0.10166442,0.02219479,0.02527488,-0.00469532,-0.02100582,-0.08226747,0.03727328,-0.00243598,0.03563147,-0.02856318,0.08256152,0.00365877,-0.03621907,-0.037248,-0.06773984,-0.01144344,-0.01456,-0.01911874,0.03598944,0.06836414,0.01428672,-0.00128135,0.02353956,0.02844552,-0.02109819,0.00776136,0.0211218,0.00157173,0.03231501,0.03510163,0.01019624,0.02604463,-0.11815393,-0.21057792,-0.04332902,0.01485853,-0.04043223,-0.01167238,-0.03597861,0.02197662,-0.00691102,0.05143686,0.05594242,0.11804024,0.0802598,-0.06938527,-0.03071013,0.03479483,-0.00901817,0.00242628,-0.01156446,-0.0080522,0.00988136,0.0020269,0.06823933,-0.03842889,0.00723502,0.06091584,-0.02998392,0.11955283,-0.01613303,0.06688256,-0.00037391,0.00156467,0.03083239,0.05350965,-0.08472618,0.0396791,0.02834682,0.00982588,-0.01203489,0.00536274,-0.01267193,-0.03201945,0.03199778,-0.02011433,-0.06026295,-0.00061731,-0.07055878,-0.00397531,-0.02273523,-0.02767085,0.02446456,-0.00733502,0.00293555,0.0514368,0.02794469,0.04846336,-0.04746221,-0.03721257,-0.09088675,0.01599466,0.04158383,-0.01615249,0.00917688,0.02643127,-0.00333659,-0.02083228,-0.00061897,-0.02354151,-0.0113985,-0.03938882,-0.00901244,-0.06739493,0.16556494,-0.001479,-0.0296139,0.03176852,0.03247757,-0.00500701,-0.01110187,0.04968435,0.00615755,0.05795438,0.02313408,0.02805324,-0.00305087,0.02734053,0.05325811,0.05635218,-0.00112208,0.06797736,-0.0600552,-0.03964672,-0.02392462,-0.01015084,0.02291267,0.10933679,-0.01405184,-0.28639621,0.01588921,-0.00247399,0.00942518,0.00602849,0.01295472,0.05376765,0.01075602,-0.04573234,0.00156983,-0.0297527,0.06064222,0.02749362,-0.00671212,-0.00711957,-0.02609385,0.0838002,-0.04473012,0.04953606,0.01568426,-0.05242304,0.07537037,0.20918278,0.04332116,0.06008996,0.03848871,-0.01866402,0.06933875,0.01694172,0.0543214,0.05323424,-0.07038019,-0.01077879,0.01164716,0.02443564,0.02922536,0.00822835,-0.00798481,-0.01066794,0.01428645,-0.03379267,0.04791085,-0.08681269,0.03896609,0.07868792,0.00525655,0.00132357,-0.03862898,0.02544015,0.0193903,0.02852563,0.01587687,-0.00486987,-0.03510966,0.08190336,0.04091972,0.03410713,-0.00520371,-0.06292622,0.00648061,0.00148943,-0.02909096,0.07796276,0.07892825,0.05564238],"last_embed":{"hash":"e827c903340603a72ed3afec460347c1a2d41776bff5f0e2ff6661f6767d5a7b","tokens":451}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.07395373,-0.03969176,0.03009508,-0.01157291,0.01172039,0.00924406,0.01652598,-0.04093066,0.03168817,-0.02941839,-0.03000719,0.01556539,-0.02059256,-0.09477031,0.10555999,0.08320098,0.01424676,-0.00485393,0.05494294,-0.02835904,0.0087823,-0.04196167,-0.04598017,0.00974881,0.00016925,-0.01615703,-0.04896349,-0.00778435,0.02932475,0.04528576,0.06146375,-0.02444256,0.04920815,-0.00674764,0.00185978,0.04620859,0.0046815,0.07304271,0.02121655,-0.03283999,-0.01694841,0.05606272,0.00673855,0.01389078,-0.02926614,0.02181643,-0.03145906,-0.04034756,0.05675646,0.05484843,0.02407586,-0.01538656,0.00110817,-0.00122854,-0.03815389,0.0648293,-0.01900143,-0.05935287,0.04000411,-0.00170767,0.01680135,0.00101726,0.03111485,-0.03196314,0.05162788,-0.01383667,0.00740296,0.00941584,0.01647108,-0.02474762,-0.09465446,-0.07667626,0.05034409,0.00919844,0.01813824,0.00415931,0.02910676,-0.01374893,0.00267773,0.03796954,-0.00177859,0.00236495,0.03757983,0.00484641,0.03257659,-0.05964816,0.04150624,-0.00520687,-0.0305991,0.01497266,0.03367618,-0.00645084,0.02578352,-0.05711747,0.0532264,0.04671194,-0.02861872,-0.00955865,0.03607455,-0.01658141,0.04270654,-0.01286331,0.01086163,0.00811152,0.02894999,-0.00456173,-0.0183091,0.03171306,-0.01970987,0.02522745,0.00689692,-0.01352932,-0.03494922,-0.01446989,-0.01331112,0.03297925,0.01135836,-0.04581519,0.0077871,0.05962595,0.06352065,0.04300737,0.00333561,0.03557079,-0.09957559,-0.06497097,0.01491488,-0.02129751,0.04028203,-0.0181241,0.04879819,0.04935364,0.0079658,-0.0345931,0.05656628,0.03662328,0.06772121,-0.02277668,-0.01982708,-0.02825127,-0.03880975,-0.01305219,0.01127088,0.04674484,0.0003756,0.01483966,-0.04834938,-0.00334777,0.04141008,0.01698207,0.07551762,0.002764,-0.05067353,-0.04780448,-0.01806385,-0.0350222,-0.02861292,0.01621856,0.01256619,0.02026835,-0.00940465,-0.00895041,-0.06380323,0.01794458,-0.0196645,-0.04039087,-0.01853319,0.0479754,-0.00080533,0.02835855,0.00173263,0.04420342,0.02834775,-0.03329068,-0.01064058,-0.01296437,0.01608207,0.0126749,-0.05843306,0.05785947,0.00828373,-0.05771665,0.01624341,-0.00774968,0.01746296,-0.03819788,0.00450067,0.0014269,-0.03138474,0.00127817,0.04907957,0.01708785,0.02641687,0.03744328,0.0110174,0.00436462,0.02391594,-0.05532946,0.01317593,0.0081142,-0.00417031,-0.01277786,0.01676211,-0.07387995,0.06324018,0.0252683,0.00708371,-0.04898548,-0.041735,0.02946953,-0.09690287,0.03289877,0.02268895,-0.00853849,0.01218376,0.0111459,-0.00144533,0.0209548,0.02636165,-0.07731382,-0.03232958,0.04143272,-0.0430629,0.01221049,-0.04166451,-0.0319213,-0.02553594,-0.01823152,0.01511896,0.02041224,-0.00007755,0.05909742,0.01862516,0.00689193,-0.02215933,-0.00976435,-0.016052,-0.01903153,0.00773624,-0.04705115,-0.02064979,-0.0063167,0.05248334,-0.03065272,-0.04353321,-0.03159656,-0.02965725,-0.00046941,0.01919876,0.03650704,0.02306543,0.07227673,-0.01213337,0.04662603,-0.03918987,-0.06863726,-0.08060534,-0.01459856,0.10580407,-0.03327681,0.04978046,0.03980432,0.04653836,0.0011437,-0.03431898,0.01171613,-0.03939741,-0.00538239,0.00274472,0.00658798,0.0244042,0.00010832,0.01147572,0.00495443,-0.07499721,-0.00659411,-0.01985363,-0.02400438,0.01085442,-0.02091202,0.03452839,-0.01384525,0.00615591,-0.03017945,0.02575488,0.03535808,-0.00568751,0.03311456,0.03850721,0.00312115,0.04134134,-0.03857304,-0.0635976,-0.02641665,-0.00158544,-0.02101163,0.03533529,0.03308793,0.02089102,-0.0825628,-0.00586035,0.01333866,0.02865293,-0.04028883,0.01360695,-0.01600764,0.00184904,0.01064643,-0.01328979,-0.00532264,-0.01777527,-0.02788219,-0.03155115,-0.06006102,0.00936306,0.00953433,0.01049229,0.01910019,-0.05139576,-0.0102433,0.0093374,0.03720314,-0.07272796,-0.00251897,-0.02644973,-0.03091217,-0.05488621,0.00600104,0.08622433,0.03282515,-0.01514319,0.02813006,-0.04075474,-0.00389247,0.00481124,0.05579351,0.02931465,-0.07740819,0.01205339,-0.01999897,-0.03924011,0.01549322,-0.01022248,-0.02271789,-0.00810141,0.03619221,0.02171055,0.03092723,-0.02893065,-0.0808832,-0.0228377,0.04100763,-0.06478681,0.0142699,-0.0249061,0.01174092,-0.05034295,-0.02498103,-0.06651949,-0.02454848,0.02903231,-0.00369843,-0.01311246,0.04675953,0.01549718,0.02909178,0.06097528,-0.00334304,-0.00378087,0.00412791,0.0364313,-0.01441101,0.00638631,0.04550314,-0.03386423,-0.00618024,-0.06963161,0.02150459,0.00890397,-0.00104893,0.0175008,-0.0792207,0.03063361,0.00062035,-0.0250573,-0.0008805,-0.04165234,0.03255665,0.04033997,-0.11097845,0.01231368,-0.01829055,-0.06799547,-0.00031134,0.04157837,0.01836207,-0.00963003,0.00773404,-0.01993114,-0.01966914,0.02120043,0.00594165,-0.04518843,0.03276835,-0.03688574,0.02308651,-0.04335595,0.03253133,-0.00497753,0.01168622,0.01441446,0.02530826,-0.04393019,0.02811717,0.02398144,-0.02821831,0.08925193,-0.09710889,-0.05227758,-0.02672775,0.00841361,-0.01422557,0.03065097,-0.00972045,-0.03593676,0.01297825,-0.02216215,-0.00230438,-0.05937577,0.02529125,-0.0598398,0.00406289,-0.03147451,-0.0442259,0.03970009,-0.03451935,0.00289703,-0.03087993,0.01162024,-0.03611908,-0.01968359,-0.04093389,0.01029753,-0.01296357,-0.05332313,-0.0285666,0.03264711,-0.04371886,0.01972534,0.00822107,-0.00182553,-0.01520995,-0.03149192,0.02467131,0.04383149,0.03494523,0.01028881,-0.01360672,0.01345892,0.02292191,-0.06171132,0.01845289,-0.01186491,-0.03361911,0.07010748,0.03098625,0.04701643,0.00883699,-0.00559126,-0.00275488,-0.04151676,-0.06622268,0.01716019,-0.02589388,0.05423076,-0.0632076,-0.00336696,0.01494641,-0.02070564,-0.01177276,0.00584257,0.01667888,-0.07313246,-0.06227612,-0.04107522,0.02487101,-0.01627483,0.02231937,0.02960391,-0.04912048,0.02742162,-0.01170609,-0.00728328,0.01958109,-0.01867299,0.01980312,0.06122296,0.01947459,0.0139262,0.02268707,0.03068835,0.05589929,-0.0169636,-0.00001464,0.03255325,0.04597279,-0.03104699,0.00455598,-0.01794061,-0.02642693,-0.01529131,-0.0047602,0.00364612,-0.01887553,0.0044455,0.01455888,-0.03624263,0.10349876,0.02100248,-0.03644485,0.03124829,0.01545561,-0.00724067,-0.01362772,0.0190432,-0.02734364,-0.00859774,0.0155915,0.0389139,0.05559836,0.03538743,-0.00658495,0.05137016,-0.00267595,-0.00265147,0.00753527,0.1002556,-0.0047188,-0.06013308,0.03999593,0.01497586,-0.00855081,-0.02348909,0.0310212,0.01849513,-0.03362261,0.02349788,-0.01665473,0.02349988,-0.00704362,0.01985409,0.03098073,0.05056501,-0.00446469,0.00923836,0.03840932,-0.00606047,-0.03776766,0.02190933,0.06808112,-0.00306143,-0.01034548,0.01956498,0.01272028,0.00483884,0.0198243,-0.07621524,-0.00924954,0.01877642,0.0424437,-0.1626969,-0.03039446,0.05144159,-0.00624052,-0.04825663,-0.02806857,-0.00594424,-0.03781125,0.03382978,0.00430744,0.03959411,-0.01956795,0.05088298,-0.03467654,-0.02500557,-0.01303993,-0.01222319,-0.01822401,0.05292715,-0.0061944,0.08265062,-0.07129151,-0.00189327,-0.03957965,-0.01913926,-0.00066692,-0.02997438,0.0691727,0.01054462,-0.02624553,-0.01032285,0.02846627,-0.0377559,-0.05731295,0.04366022,-0.02276169,-0.01747143,-0.08000331,-0.01770817,0.06957997,-0.02967575,0.056132,-0.02753165,-0.01300361,0.01288311,-0.01246761,0.04417615,-0.02348136,0.0227841,-0.0069002,-0.0666438,-0.05363835,-0.02716798,-0.06294331,0.03498069,0.00893125,0.02191754,0.04599676,0.00242927,0.00329141,-0.03168065,-0.01683032,0.02668868,0.0182762,0.01893825,0.00614306,-0.03372972,-0.03575243,0.02475368,0.00702689,0.06117892,-0.03472466,0.02226191,-0.02554626,-0.05585357,-0.01259712,-0.0536308,-0.04949431,-0.06296535,0.02026313,0.05332252,0.00592255,0.0310793,-0.00448429,0.01112168,-0.03316894,-0.00468437,0.00417584,0.00466569,0.0627652,-0.0094037,-0.00841628,-0.02167254,0.0378731,-0.04214592,-0.01281537,0.02620021,-0.00734157,0.01908109,-0.029519,0.04908447,-0.04240664,0.02769403,0.00074973,0.01840152,0.01905018,-0.0103886,-0.00177482,-0.00998356,-0.01171608,0.01810737,-0.01986655,0.05212311,-0.00393573,0.01756976,-0.01986025,0.01175244,-0.06037147,-0.03074441,-0.00229864,-0.031797,0.04513425,0.0278293,0.00522241,0.0111429,-0.01947981,-0.01135115,0.04149554,-0.01887304,0.01559761,0.01743627,0.00676586,-0.06864408,0.00884831,-0.02943741,0.01234468,-0.02255632,0.02939057,-0.01073001,0.04521355,-0.00523694,-0.05589326,-0.02408423,-0.05739704,0.03550717,-0.00093046,0.00449689,0.01825947,-0.0174308,-0.00454585,0.01209672,0.07071754,0.07397369,0.02904028,-0.0763133,-0.00640235,-0.01042974,0.02381829,0.01790534,0.04772893,-0.06932881,-0.05159776,-0.03062778,0.02813973,0.02121611,-0.0700173,-0.00992942,-0.00411855,0.02203128,-0.01145906,0.09706083,-0.02627606,0.00941268,-0.04581865,0.08160309,0.04068933,-0.00043853,0.01479212,0.03064586,0.00285093,-0.00863944,0.0584396,-0.02928652,0.08410116,0.08258732,-0.00042725,-0.00568948,-0.00187874,8.8e-7,-0.06718833,-0.03562899,-0.04985156,-0.04585588,0.01460261,-0.04130469,-0.05501203,0.03130036,-0.03661865],"last_embed":{"tokens":839,"hash":"ffjphh"}}},"last_read":{"hash":"ffjphh","at":1751079987725},"class_name":"SmartSource","outlinks":[{"title":"哈希算法","target":"哈希算法","line":36},{"title":"数字证书","target":"数字证书","line":72},{"title":"X.509证书","target":"X.509证书","line":72}],"metadata":{"aliases":["Transport Layer Security","传输层安全"],"英文":"Transport Layer Security","tags":["计算机网络/OSI模型/传输层"],"协议层级":["表示层"],"相关知识":null,"发布时间":"1999-01-01","cssclasses":["editor-full"],"类型":["加密协议"]},"blocks":{"#---frontmatter---":[1,16],"#简介":[17,29],"#简介#{1}":[18,18],"#简介#{2}":[19,20],"#简介#{3}":[21,21],"#简介#{4}":[22,23],"#简介#{5}":[24,29],"#版本发展":[30,40],"#版本发展#{1}":[31,32],"#版本发展#{2}":[33,34],"#版本发展#{3}":[35,36],"#版本发展#{4}":[37,39],"#版本发展#{5}":[40,40],"#运行原理":[41,101],"#运行原理#. 握手过程（Handshake）":[43,81],"#运行原理#. 握手过程（Handshake）#{1}":[45,64],"#运行原理#. 握手过程（Handshake）#{2}":[65,67],"#运行原理#. 握手过程（Handshake）#{3}":[68,70],"#运行原理#. 握手过程（Handshake）#{4}":[71,73],"#运行原理#. 握手过程（Handshake）#{5}":[74,76],"#运行原理#. 握手过程（Handshake）#{6}":[77,80],"#运行原理#. 握手过程（Handshake）#{7}":[81,81],"#运行原理#数据传输过程":[82,101],"#运行原理#数据传输过程#{1}":[84,97],"#运行原理#数据传输过程#{2}":[98,98],"#运行原理#数据传输过程#{3}":[99,99],"#运行原理#数据传输过程#{4}":[100,100],"#运行原理#数据传输过程#{5}":[101,101]},"last_import":{"mtime":1737625442813,"size":3514,"at":1748488128974,"hash":"ffjphh"},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md","last_embed":{"hash":"ffjphh","at":1751079987725}},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#运行原理": {"path":null,"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.09024111,-0.04279779,0.01742203,-0.029175,-0.02146734,0.00936656,0.03303724,-0.03695538,0.00618524,-0.04620269,-0.00674693,-0.00290142,0.00229561,-0.07637222,0.0906813,0.10543355,-0.0136841,-0.00043552,0.01904999,-0.04454986,0.01776029,-0.01076015,-0.06268067,-0.00850867,0.02546535,0.01465306,-0.03072328,0.0140843,0.01946262,0.05598018,0.03726288,-0.03788479,0.03503299,-0.00796922,-0.0202104,0.03035899,-0.00876595,0.08120254,0.03777626,-0.0550404,-0.03674232,0.01332208,0.01010159,0.01818149,-0.05178141,0.03396004,-0.00694595,-0.01740328,0.0281929,0.04314897,-0.01284423,-0.01083244,0.01215412,-0.00449282,-0.04383916,0.05644081,-0.01816165,-0.03601091,0.00775356,0.00112714,0.02615163,0.01459389,0.04307105,-0.00140232,0.02376617,-0.01210421,0.00171261,-0.01780319,0.01092037,-0.02442485,-0.06059847,-0.06440748,0.06201172,-0.02325899,-0.00508753,-0.00511964,-0.01753776,0.00738325,-0.01060186,0.06529891,0.00861302,0.0083212,0.0080296,0.03432178,0.02609223,-0.02984715,0.01018502,-0.02925309,-0.04731039,0.03286432,0.05651277,-0.03047331,0.02699148,-0.03918704,0.06539252,0.01552601,-0.02903739,-0.00903365,0.04549492,-0.05348608,0.05258101,-0.01400476,-0.01251032,0.0039568,0.00693688,-0.01125809,-0.00039195,0.01561212,-0.03123071,-0.01841144,0.00717655,-0.02719562,-0.00822755,-0.0289487,-0.03728694,0.02761839,0.04532117,-0.0192041,0.01691552,0.07693275,0.06365425,0.04183688,0.00535996,0.02496781,-0.09011811,-0.03827975,0.01842265,-0.01888843,0.00706112,-0.01900502,0.0447948,0.03513498,0.00979227,0.00354517,0.05027109,0.01202809,0.08551533,0.00874839,-0.03513329,-0.02689954,-0.0319779,-0.00518459,-0.0149381,0.02966213,0.01187259,0.00563834,-0.05273812,-0.01224161,0.02584555,0.02109069,0.08496941,-0.0255477,-0.10086773,-0.0519672,-0.04634656,-0.00215404,-0.01167947,0.01413431,-0.0169166,0.00988272,-0.02815183,-0.02420334,-0.07497967,0.00296567,-0.01786725,-0.00681349,-0.00989957,0.03177154,-0.01648778,-0.00944391,-0.00961467,0.03851746,0.0200594,-0.03837309,0.00664487,0.01284582,0.04655093,0.02063358,-0.06809404,0.04417792,0.00734148,-0.02324871,0.01300776,0.00314019,0.03694552,-0.01722253,-0.01472738,0.02429369,0.03636977,0.00342698,0.03261409,-0.00140914,0.0286749,0.02288039,0.04415892,0.01795756,0.01028688,-0.02494263,0.01175791,-0.0050091,-0.05063642,-0.02775358,0.00923543,-0.08195493,0.06315102,0.02069251,0.03105804,-0.04294467,-0.05512364,0.0409411,-0.06135871,0.0105241,0.05383286,0.02580116,0.02898978,-0.00706287,0.01990212,0.03605902,0.05263855,-0.04727634,-0.02909328,0.04868301,-0.06144664,0.01580764,-0.04755323,-0.01860888,0.01903334,-0.00993994,0.04590547,0.0419446,0.00141892,0.0807388,0.00808463,-0.02427265,-0.0562029,-0.00050029,-0.00264671,0.00260019,-0.02053345,-0.02426055,-0.03164604,-0.02356061,-0.00391119,-0.03721972,-0.02852329,-0.03285404,-0.01597938,0.0104054,0.01779833,0.05181564,0.01702944,0.06223423,-0.04105627,0.03716708,-0.01747701,-0.08257853,-0.04123618,0.00375093,0.06805725,-0.03654836,0.04387265,0.02678372,0.03854823,0.02158537,0.02597007,-0.00516271,-0.01336846,-0.02681599,0.01980565,0.00487424,0.00721491,0.01635955,-0.00349921,-0.00252208,-0.06951753,-0.01862845,-0.0068873,-0.03991648,-0.00567487,-0.02111287,0.06334738,0.01608632,0.01121331,-0.00191156,0.02460983,0.03984865,-0.01396208,0.02093579,0.03435105,0.00726632,-0.00836898,-0.01105147,-0.0401789,0.00281864,-0.00549369,-0.0101331,0.05430236,0.02274103,0.00316068,-0.05716338,-0.022495,-0.00846666,-0.00510749,-0.0024501,0.03872627,0.00049174,0.00137162,-0.02410416,-0.01745905,-0.02607434,-0.03538129,-0.05363137,-0.01960835,-0.03295926,-0.01012072,0.00881075,0.05034382,0.03551385,-0.02892257,-0.02543996,0.0079438,0.06092103,-0.1006689,0.00276341,-0.03394044,-0.05754341,-0.00470846,0.05364904,0.06779625,0.03129677,-0.02958952,0.02068391,-0.03457831,-0.00777122,0.02519995,0.05078112,-0.00076322,-0.06914208,-0.00521446,-0.0381209,-0.01898453,0.02651493,0.02998324,0.01063582,0.02697546,0.05007187,0.01051066,0.04668598,-0.0353747,-0.09179308,-0.01670649,0.02108127,-0.06191204,0.02132042,-0.01058032,0.03946752,-0.03926562,-0.03195076,-0.06054623,-0.01538702,0.02684386,-0.02846023,-0.06020985,0.03901831,0.03987335,-0.01951222,0.05722465,0.04509234,0.00385448,0.02151962,-0.01354959,-0.01902001,0.00529245,-0.01846708,-0.03955897,-0.00786736,-0.08163285,0.04045665,0.00970403,0.00677335,-0.02514514,-0.06323472,0.01178529,0.00005686,-0.00689675,0.02362277,-0.058899,0.03189518,0.02452123,-0.08174544,0.00344042,-0.00565605,-0.09135012,0.00479349,0.02871467,0.0190906,-0.00024567,0.00379497,-0.02023528,0.00503242,0.02864437,-0.00507242,-0.01168388,0.03173603,-0.05571873,0.01976477,-0.03724776,0.03060997,-0.01076948,-0.01127449,0.01294789,0.03824111,-0.05827529,0.03235651,-0.00198553,-0.01487448,0.04902712,-0.08914253,-0.03028048,-0.02954006,-0.00025308,-0.02487283,0.02930918,-0.02781183,-0.02194647,-0.0070477,-0.0350814,-0.01293888,-0.03540395,-0.0293065,-0.02442764,-0.00224856,-0.02778553,-0.02593068,0.05763703,-0.01934795,-0.0152808,-0.0130739,0.03192218,-0.0625196,-0.03495068,-0.03013116,-0.00445694,-0.00324399,-0.04161381,-0.01312301,0.04662478,-0.00492117,0.00431233,0.04626802,0.00057982,-0.01050565,-0.04251614,-0.01422755,0.05017339,-0.01282812,0.01461826,0.00368552,0.04061671,0.01508513,-0.09952848,0.04006781,0.04280537,-0.00700538,0.04954912,0.04007855,0.02661644,0.00048411,0.03305965,-0.01351749,-0.03557538,-0.03577112,0.01752387,-0.01687008,0.0650361,-0.04820899,0.0155746,-0.00784342,-0.01930431,-0.01669926,-0.02473585,0.03120791,-0.05224173,-0.05184748,-0.01559602,0.0418766,-0.02116339,0.03569314,-0.00323741,-0.06246845,0.04617218,-0.005443,-0.0054812,0.00020228,-0.0317479,0.04448493,0.04041534,0.04005447,0.00283468,0.03199148,0.00294196,0.08036929,-0.04352926,0.0398389,0.02022069,0.02539526,-0.01495614,-0.01196593,-0.00052749,-0.04272117,-0.01693487,0.01482986,0.01524486,-0.00662218,0.00065488,0.03072821,0.00206592,0.07842147,0.04205599,-0.01862228,0.017397,0.01808161,-0.02707258,-0.01892436,0.04687763,-0.00719217,-0.00907149,0.00760937,0.05859627,0.05126028,0.04083109,-0.03724076,0.07589879,0.0034462,-0.00563333,0.0137048,0.04250734,-0.03300141,-0.03561977,0.06092243,0.01670413,-0.00403043,0.00495706,-0.00236923,0.01333593,-0.05446558,0.01692354,-0.04758482,-0.01075472,0.00206923,0.02703512,-0.00152449,0.02830609,-0.00097092,0.00335974,0.03241244,0.03684241,-0.05168137,-0.00797063,0.09253836,0.02536155,-0.02166106,0.03541556,-0.00748734,-0.01119207,0.04321329,-0.06090005,-0.00340969,0.00198143,0.00646932,-0.14416364,-0.04052629,0.07167747,-0.00003368,-0.06416574,-0.00978106,-0.01618292,-0.05548905,0.01929076,0.02716986,0.06458452,-0.00462862,0.03299407,-0.00925551,-0.04524421,-0.02454141,-0.0052286,0.01480077,0.02395896,0.00192863,0.11013846,-0.06054374,0.02714377,-0.03238316,-0.01267569,0.01107343,-0.05265744,0.05143889,-0.0099464,-0.01330553,-0.00498343,0.03733028,-0.03420772,-0.0453074,0.03053691,-0.02407756,-0.01327828,-0.09961365,-0.04021092,0.05828692,-0.00351312,0.08565924,-0.04643741,-0.02734168,0.02254368,-0.02902118,0.02319223,-0.00951926,0.04238179,-0.00225231,-0.07736056,-0.02695499,-0.0276417,-0.01439536,0.0265714,-0.02556051,0.02184964,0.04118778,0.00389027,0.00495042,-0.00094973,-0.00187312,-0.00712053,0.01205611,0.06213592,0.01489515,-0.03425337,-0.0470249,0.02103686,0.00833663,0.06501906,-0.02018385,0.01952723,0.00386193,-0.04744752,-0.01482636,-0.04325261,-0.04769849,-0.03254341,0.00762616,0.0541239,0.01915679,0.03037401,-0.00308282,0.02383622,-0.01740732,0.00012781,0.02160536,-0.00387154,0.09114243,-0.04389225,-0.02611188,-0.01517249,0.05905724,-0.03389451,-0.02512286,0.0310852,0.00314432,0.0106254,-0.01903448,0.04391986,-0.05319054,0.03804961,0.00160898,0.01589941,0.00620329,-0.0157821,0.04207064,0.0205265,-0.02029432,0.01897907,-0.0107244,0.02002758,-0.02474956,0.04281869,-0.0209968,0.01091242,-0.05199774,-0.05784548,-0.01570101,-0.02073884,0.03081603,0.03036722,0.04016672,0.02217706,-0.02748407,-0.02807656,0.04830542,-0.02177452,0.03321146,0.0117015,-0.00142205,-0.03413639,0.01126722,-0.05078075,0.00817058,-0.0434685,0.02971624,-0.02202564,0.00998397,0.02175988,-0.05725371,-0.02292956,-0.02643956,0.0150489,0.00513898,0.0045844,0.02304602,-0.03361733,-0.00933001,0.0207277,0.08543191,0.09611136,0.04976813,-0.06470571,0.01283333,0.01120723,0.02169011,0.02524419,0.02835765,-0.03987747,-0.06404796,-0.03301486,0.01870471,0.01696906,-0.03989817,0.0091089,-0.01510441,0.03760349,-0.01625022,0.09667513,-0.03948865,0.0033897,-0.08565073,0.08368392,0.03269889,-0.00806123,0.03088875,0.02319504,-0.02949785,-0.02773758,0.02270736,-0.06134062,0.0320338,0.06217933,-0.03192478,-0.01915219,-0.03696384,9.8e-7,-0.05238039,-0.06111607,-0.04434096,-0.03155166,0.01261177,-0.06188193,-0.07791633,0.00271494,-0.02865873],"last_embed":{"hash":"1iu99n7","tokens":519}}},"text":null,"length":0,"last_read":{"hash":"1iu99n7","at":1749002760836},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#运行原理","lines":[41,101],"size":1356,"outlinks":[{"title":"数字证书","target":"数字证书","line":32},{"title":"X.509证书","target":"X.509证书","line":32}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#---frontmatter---","lines":[1,16],"size":189,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#简介": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#简介","lines":[17,29],"size":297,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#简介#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#简介#{1}","lines":[18,18],"size":39,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#简介#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#简介#{2}","lines":[19,20],"size":58,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#简介#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#简介#{3}","lines":[21,21],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#简介#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#简介#{4}","lines":[22,23],"size":66,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#简介#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#简介#{5}","lines":[24,29],"size":122,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#版本发展": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#版本发展","lines":[30,40],"size":237,"outlinks":[{"title":"哈希算法","target":"哈希算法","line":7}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#版本发展#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#版本发展#{1}","lines":[31,32],"size":50,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#版本发展#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#版本发展#{2}","lines":[33,34],"size":48,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#版本发展#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#版本发展#{3}","lines":[35,36],"size":56,"outlinks":[{"title":"哈希算法","target":"哈希算法","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#版本发展#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#版本发展#{4}","lines":[37,39],"size":69,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#版本发展#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#版本发展#{5}","lines":[40,40],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#运行原理#. 握手过程（Handshake）": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#运行原理#. 握手过程（Handshake）","lines":[43,81],"size":912,"outlinks":[{"title":"数字证书","target":"数字证书","line":30},{"title":"X.509证书","target":"X.509证书","line":30}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#运行原理#. 握手过程（Handshake）#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#运行原理#. 握手过程（Handshake）#{1}","lines":[45,64],"size":526,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#运行原理#. 握手过程（Handshake）#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#运行原理#. 握手过程（Handshake）#{2}","lines":[65,67],"size":90,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#运行原理#. 握手过程（Handshake）#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#运行原理#. 握手过程（Handshake）#{3}","lines":[68,70],"size":69,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#运行原理#. 握手过程（Handshake）#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#运行原理#. 握手过程（Handshake）#{4}","lines":[71,73],"size":70,"outlinks":[{"title":"数字证书","target":"数字证书","line":2},{"title":"X.509证书","target":"X.509证书","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#运行原理#. 握手过程（Handshake）#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#运行原理#. 握手过程（Handshake）#{5}","lines":[74,76],"size":73,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#运行原理#. 握手过程（Handshake）#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#运行原理#. 握手过程（Handshake）#{6}","lines":[77,80],"size":51,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#运行原理#. 握手过程（Handshake）#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#运行原理#. 握手过程（Handshake）#{7}","lines":[81,81],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#运行原理#数据传输过程": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#运行原理#数据传输过程","lines":[82,101],"size":434,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#运行原理#数据传输过程#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#运行原理#数据传输过程#{1}","lines":[84,97],"size":289,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#运行原理#数据传输过程#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#运行原理#数据传输过程#{2}","lines":[98,98],"size":33,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#运行原理#数据传输过程#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#运行原理#数据传输过程#{3}","lines":[99,99],"size":33,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#运行原理#数据传输过程#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#运行原理#数据传输过程#{4}","lines":[100,100],"size":32,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#运行原理#数据传输过程#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md#运行原理#数据传输过程#{5}","lines":[101,101],"size":32,"outlinks":[],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md": {"path":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08540659,-0.00046375,-0.01507335,-0.07364066,-0.00714499,-0.00647255,0.00321407,0.02029561,0.035677,-0.01319273,-0.0024835,-0.05426528,0.06545851,0.045006,0.06057224,0.04347195,0.05019912,-0.00462421,-0.0115068,0.01294371,0.07348473,-0.06470037,-0.01885284,-0.04731334,0.01124593,-0.01594077,-0.00239452,-0.03003815,0.00106114,-0.15297122,-0.03084683,-0.01531859,-0.03349829,0.03042394,-0.01805021,-0.01955041,0.0360923,0.0444047,0.0242017,0.02295053,0.03101605,0.03684675,0.00461339,-0.02864261,-0.05706513,-0.0463988,-0.03254037,-0.00161524,-0.02503913,-0.04579925,-0.03238568,-0.01079379,-0.02471824,0.02734996,-0.06423585,-0.01843669,0.01434459,0.04515197,0.07345204,-0.00817002,0.03796137,0.0105244,-0.24359931,0.09260968,0.05323492,0.00014311,-0.02099111,0.02603666,0.01866943,0.00225047,-0.04243284,0.01965303,-0.03657839,0.02752189,0.06111428,0.00797056,0.00298686,-0.00677272,-0.04305148,-0.06178217,-0.01266669,0.01115424,0.01039482,0.01506253,-0.05565659,-0.02870709,-0.01215303,-0.0247162,0.02927125,0.01042207,0.02116509,-0.07970528,0.0449396,0.02714876,0.01201988,-0.0191777,0.02212577,0.05825073,-0.11136358,0.09671837,-0.05665396,-0.02438807,-0.01199981,-0.04042111,0.04672823,-0.05074838,0.01270161,-0.05887093,-0.0474486,-0.02074388,-0.06668707,-0.03860913,0.00276321,-0.01191882,0.03717187,0.04719494,-0.00758547,0.01457631,-0.01842495,-0.00345533,-0.02161363,-0.00988946,0.06955247,0.00700272,-0.03400138,-0.02502742,0.08736593,0.08069331,0.0028187,0.06216842,0.06026968,-0.01562317,-0.0690873,-0.01844711,-0.01682807,0.01257466,-0.05653744,0.00131634,-0.07380376,-0.06720135,0.00789428,-0.0359561,0.04718262,-0.09661504,-0.02725244,0.07599149,-0.01502204,0.00413003,0.03010058,-0.05482132,0.00331593,0.04381028,-0.02371083,-0.03358334,-0.04380625,0.0020884,0.06393159,0.14720502,-0.02754441,-0.01738396,0.02556772,-0.0327527,-0.09035756,0.19202276,0.06358802,-0.12366574,-0.00047858,-0.00407423,0.0372068,-0.05898385,-0.01557743,-0.01057453,0.04092176,0.01081975,0.01578534,-0.02045317,-0.04138591,-0.04032566,-0.01307199,0.02073059,0.04198185,-0.06042841,-0.09590562,0.01788256,0.03101917,-0.02735855,-0.02393912,-0.061307,-0.01568491,-0.03496057,-0.10166442,0.02219479,0.02527488,-0.00469532,-0.02100582,-0.08226747,0.03727328,-0.00243598,0.03563147,-0.02856318,0.08256152,0.00365877,-0.03621907,-0.037248,-0.06773984,-0.01144344,-0.01456,-0.01911874,0.03598944,0.06836414,0.01428672,-0.00128135,0.02353956,0.02844552,-0.02109819,0.00776136,0.0211218,0.00157173,0.03231501,0.03510163,0.01019624,0.02604463,-0.11815393,-0.21057792,-0.04332902,0.01485853,-0.04043223,-0.01167238,-0.03597861,0.02197662,-0.00691102,0.05143686,0.05594242,0.11804024,0.0802598,-0.06938527,-0.03071013,0.03479483,-0.00901817,0.00242628,-0.01156446,-0.0080522,0.00988136,0.0020269,0.06823933,-0.03842889,0.00723502,0.06091584,-0.02998392,0.11955283,-0.01613303,0.06688256,-0.00037391,0.00156467,0.03083239,0.05350965,-0.08472618,0.0396791,0.02834682,0.00982588,-0.01203489,0.00536274,-0.01267193,-0.03201945,0.03199778,-0.02011433,-0.06026295,-0.00061731,-0.07055878,-0.00397531,-0.02273523,-0.02767085,0.02446456,-0.00733502,0.00293555,0.0514368,0.02794469,0.04846336,-0.04746221,-0.03721257,-0.09088675,0.01599466,0.04158383,-0.01615249,0.00917688,0.02643127,-0.00333659,-0.02083228,-0.00061897,-0.02354151,-0.0113985,-0.03938882,-0.00901244,-0.06739493,0.16556494,-0.001479,-0.0296139,0.03176852,0.03247757,-0.00500701,-0.01110187,0.04968435,0.00615755,0.05795438,0.02313408,0.02805324,-0.00305087,0.02734053,0.05325811,0.05635218,-0.00112208,0.06797736,-0.0600552,-0.03964672,-0.02392462,-0.01015084,0.02291267,0.10933679,-0.01405184,-0.28639621,0.01588921,-0.00247399,0.00942518,0.00602849,0.01295472,0.05376765,0.01075602,-0.04573234,0.00156983,-0.0297527,0.06064222,0.02749362,-0.00671212,-0.00711957,-0.02609385,0.0838002,-0.04473012,0.04953606,0.01568426,-0.05242304,0.07537037,0.20918278,0.04332116,0.06008996,0.03848871,-0.01866402,0.06933875,0.01694172,0.0543214,0.05323424,-0.07038019,-0.01077879,0.01164716,0.02443564,0.02922536,0.00822835,-0.00798481,-0.01066794,0.01428645,-0.03379267,0.04791085,-0.08681269,0.03896609,0.07868792,0.00525655,0.00132357,-0.03862898,0.02544015,0.0193903,0.02852563,0.01587687,-0.00486987,-0.03510966,0.08190336,0.04091972,0.03410713,-0.00520371,-0.06292622,0.00648061,0.00148943,-0.02909096,0.07796276,0.07892825,0.05564238],"last_embed":{"hash":"e827c903340603a72ed3afec460347c1a2d41776bff5f0e2ff6661f6767d5a7b","tokens":451}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.07395373,-0.03969176,0.03009508,-0.01157291,0.01172039,0.00924406,0.01652598,-0.04093066,0.03168817,-0.02941839,-0.03000719,0.01556539,-0.02059256,-0.09477031,0.10555999,0.08320098,0.01424676,-0.00485393,0.05494294,-0.02835904,0.0087823,-0.04196167,-0.04598017,0.00974881,0.00016925,-0.01615703,-0.04896349,-0.00778435,0.02932475,0.04528576,0.06146375,-0.02444256,0.04920815,-0.00674764,0.00185978,0.04620859,0.0046815,0.07304271,0.02121655,-0.03283999,-0.01694841,0.05606272,0.00673855,0.01389078,-0.02926614,0.02181643,-0.03145906,-0.04034756,0.05675646,0.05484843,0.02407586,-0.01538656,0.00110817,-0.00122854,-0.03815389,0.0648293,-0.01900143,-0.05935287,0.04000411,-0.00170767,0.01680135,0.00101726,0.03111485,-0.03196314,0.05162788,-0.01383667,0.00740296,0.00941584,0.01647108,-0.02474762,-0.09465446,-0.07667626,0.05034409,0.00919844,0.01813824,0.00415931,0.02910676,-0.01374893,0.00267773,0.03796954,-0.00177859,0.00236495,0.03757983,0.00484641,0.03257659,-0.05964816,0.04150624,-0.00520687,-0.0305991,0.01497266,0.03367618,-0.00645084,0.02578352,-0.05711747,0.0532264,0.04671194,-0.02861872,-0.00955865,0.03607455,-0.01658141,0.04270654,-0.01286331,0.01086163,0.00811152,0.02894999,-0.00456173,-0.0183091,0.03171306,-0.01970987,0.02522745,0.00689692,-0.01352932,-0.03494922,-0.01446989,-0.01331112,0.03297925,0.01135836,-0.04581519,0.0077871,0.05962595,0.06352065,0.04300737,0.00333561,0.03557079,-0.09957559,-0.06497097,0.01491488,-0.02129751,0.04028203,-0.0181241,0.04879819,0.04935364,0.0079658,-0.0345931,0.05656628,0.03662328,0.06772121,-0.02277668,-0.01982708,-0.02825127,-0.03880975,-0.01305219,0.01127088,0.04674484,0.0003756,0.01483966,-0.04834938,-0.00334777,0.04141008,0.01698207,0.07551762,0.002764,-0.05067353,-0.04780448,-0.01806385,-0.0350222,-0.02861292,0.01621856,0.01256619,0.02026835,-0.00940465,-0.00895041,-0.06380323,0.01794458,-0.0196645,-0.04039087,-0.01853319,0.0479754,-0.00080533,0.02835855,0.00173263,0.04420342,0.02834775,-0.03329068,-0.01064058,-0.01296437,0.01608207,0.0126749,-0.05843306,0.05785947,0.00828373,-0.05771665,0.01624341,-0.00774968,0.01746296,-0.03819788,0.00450067,0.0014269,-0.03138474,0.00127817,0.04907957,0.01708785,0.02641687,0.03744328,0.0110174,0.00436462,0.02391594,-0.05532946,0.01317593,0.0081142,-0.00417031,-0.01277786,0.01676211,-0.07387995,0.06324018,0.0252683,0.00708371,-0.04898548,-0.041735,0.02946953,-0.09690287,0.03289877,0.02268895,-0.00853849,0.01218376,0.0111459,-0.00144533,0.0209548,0.02636165,-0.07731382,-0.03232958,0.04143272,-0.0430629,0.01221049,-0.04166451,-0.0319213,-0.02553594,-0.01823152,0.01511896,0.02041224,-0.00007755,0.05909742,0.01862516,0.00689193,-0.02215933,-0.00976435,-0.016052,-0.01903153,0.00773624,-0.04705115,-0.02064979,-0.0063167,0.05248334,-0.03065272,-0.04353321,-0.03159656,-0.02965725,-0.00046941,0.01919876,0.03650704,0.02306543,0.07227673,-0.01213337,0.04662603,-0.03918987,-0.06863726,-0.08060534,-0.01459856,0.10580407,-0.03327681,0.04978046,0.03980432,0.04653836,0.0011437,-0.03431898,0.01171613,-0.03939741,-0.00538239,0.00274472,0.00658798,0.0244042,0.00010832,0.01147572,0.00495443,-0.07499721,-0.00659411,-0.01985363,-0.02400438,0.01085442,-0.02091202,0.03452839,-0.01384525,0.00615591,-0.03017945,0.02575488,0.03535808,-0.00568751,0.03311456,0.03850721,0.00312115,0.04134134,-0.03857304,-0.0635976,-0.02641665,-0.00158544,-0.02101163,0.03533529,0.03308793,0.02089102,-0.0825628,-0.00586035,0.01333866,0.02865293,-0.04028883,0.01360695,-0.01600764,0.00184904,0.01064643,-0.01328979,-0.00532264,-0.01777527,-0.02788219,-0.03155115,-0.06006102,0.00936306,0.00953433,0.01049229,0.01910019,-0.05139576,-0.0102433,0.0093374,0.03720314,-0.07272796,-0.00251897,-0.02644973,-0.03091217,-0.05488621,0.00600104,0.08622433,0.03282515,-0.01514319,0.02813006,-0.04075474,-0.00389247,0.00481124,0.05579351,0.02931465,-0.07740819,0.01205339,-0.01999897,-0.03924011,0.01549322,-0.01022248,-0.02271789,-0.00810141,0.03619221,0.02171055,0.03092723,-0.02893065,-0.0808832,-0.0228377,0.04100763,-0.06478681,0.0142699,-0.0249061,0.01174092,-0.05034295,-0.02498103,-0.06651949,-0.02454848,0.02903231,-0.00369843,-0.01311246,0.04675953,0.01549718,0.02909178,0.06097528,-0.00334304,-0.00378087,0.00412791,0.0364313,-0.01441101,0.00638631,0.04550314,-0.03386423,-0.00618024,-0.06963161,0.02150459,0.00890397,-0.00104893,0.0175008,-0.0792207,0.03063361,0.00062035,-0.0250573,-0.0008805,-0.04165234,0.03255665,0.04033997,-0.11097845,0.01231368,-0.01829055,-0.06799547,-0.00031134,0.04157837,0.01836207,-0.00963003,0.00773404,-0.01993114,-0.01966914,0.02120043,0.00594165,-0.04518843,0.03276835,-0.03688574,0.02308651,-0.04335595,0.03253133,-0.00497753,0.01168622,0.01441446,0.02530826,-0.04393019,0.02811717,0.02398144,-0.02821831,0.08925193,-0.09710889,-0.05227758,-0.02672775,0.00841361,-0.01422557,0.03065097,-0.00972045,-0.03593676,0.01297825,-0.02216215,-0.00230438,-0.05937577,0.02529125,-0.0598398,0.00406289,-0.03147451,-0.0442259,0.03970009,-0.03451935,0.00289703,-0.03087993,0.01162024,-0.03611908,-0.01968359,-0.04093389,0.01029753,-0.01296357,-0.05332313,-0.0285666,0.03264711,-0.04371886,0.01972534,0.00822107,-0.00182553,-0.01520995,-0.03149192,0.02467131,0.04383149,0.03494523,0.01028881,-0.01360672,0.01345892,0.02292191,-0.06171132,0.01845289,-0.01186491,-0.03361911,0.07010748,0.03098625,0.04701643,0.00883699,-0.00559126,-0.00275488,-0.04151676,-0.06622268,0.01716019,-0.02589388,0.05423076,-0.0632076,-0.00336696,0.01494641,-0.02070564,-0.01177276,0.00584257,0.01667888,-0.07313246,-0.06227612,-0.04107522,0.02487101,-0.01627483,0.02231937,0.02960391,-0.04912048,0.02742162,-0.01170609,-0.00728328,0.01958109,-0.01867299,0.01980312,0.06122296,0.01947459,0.0139262,0.02268707,0.03068835,0.05589929,-0.0169636,-0.00001464,0.03255325,0.04597279,-0.03104699,0.00455598,-0.01794061,-0.02642693,-0.01529131,-0.0047602,0.00364612,-0.01887553,0.0044455,0.01455888,-0.03624263,0.10349876,0.02100248,-0.03644485,0.03124829,0.01545561,-0.00724067,-0.01362772,0.0190432,-0.02734364,-0.00859774,0.0155915,0.0389139,0.05559836,0.03538743,-0.00658495,0.05137016,-0.00267595,-0.00265147,0.00753527,0.1002556,-0.0047188,-0.06013308,0.03999593,0.01497586,-0.00855081,-0.02348909,0.0310212,0.01849513,-0.03362261,0.02349788,-0.01665473,0.02349988,-0.00704362,0.01985409,0.03098073,0.05056501,-0.00446469,0.00923836,0.03840932,-0.00606047,-0.03776766,0.02190933,0.06808112,-0.00306143,-0.01034548,0.01956498,0.01272028,0.00483884,0.0198243,-0.07621524,-0.00924954,0.01877642,0.0424437,-0.1626969,-0.03039446,0.05144159,-0.00624052,-0.04825663,-0.02806857,-0.00594424,-0.03781125,0.03382978,0.00430744,0.03959411,-0.01956795,0.05088298,-0.03467654,-0.02500557,-0.01303993,-0.01222319,-0.01822401,0.05292715,-0.0061944,0.08265062,-0.07129151,-0.00189327,-0.03957965,-0.01913926,-0.00066692,-0.02997438,0.0691727,0.01054462,-0.02624553,-0.01032285,0.02846627,-0.0377559,-0.05731295,0.04366022,-0.02276169,-0.01747143,-0.08000331,-0.01770817,0.06957997,-0.02967575,0.056132,-0.02753165,-0.01300361,0.01288311,-0.01246761,0.04417615,-0.02348136,0.0227841,-0.0069002,-0.0666438,-0.05363835,-0.02716798,-0.06294331,0.03498069,0.00893125,0.02191754,0.04599676,0.00242927,0.00329141,-0.03168065,-0.01683032,0.02668868,0.0182762,0.01893825,0.00614306,-0.03372972,-0.03575243,0.02475368,0.00702689,0.06117892,-0.03472466,0.02226191,-0.02554626,-0.05585357,-0.01259712,-0.0536308,-0.04949431,-0.06296535,0.02026313,0.05332252,0.00592255,0.0310793,-0.00448429,0.01112168,-0.03316894,-0.00468437,0.00417584,0.00466569,0.0627652,-0.0094037,-0.00841628,-0.02167254,0.0378731,-0.04214592,-0.01281537,0.02620021,-0.00734157,0.01908109,-0.029519,0.04908447,-0.04240664,0.02769403,0.00074973,0.01840152,0.01905018,-0.0103886,-0.00177482,-0.00998356,-0.01171608,0.01810737,-0.01986655,0.05212311,-0.00393573,0.01756976,-0.01986025,0.01175244,-0.06037147,-0.03074441,-0.00229864,-0.031797,0.04513425,0.0278293,0.00522241,0.0111429,-0.01947981,-0.01135115,0.04149554,-0.01887304,0.01559761,0.01743627,0.00676586,-0.06864408,0.00884831,-0.02943741,0.01234468,-0.02255632,0.02939057,-0.01073001,0.04521355,-0.00523694,-0.05589326,-0.02408423,-0.05739704,0.03550717,-0.00093046,0.00449689,0.01825947,-0.0174308,-0.00454585,0.01209672,0.07071754,0.07397369,0.02904028,-0.0763133,-0.00640235,-0.01042974,0.02381829,0.01790534,0.04772893,-0.06932881,-0.05159776,-0.03062778,0.02813973,0.02121611,-0.0700173,-0.00992942,-0.00411855,0.02203128,-0.01145906,0.09706083,-0.02627606,0.00941268,-0.04581865,0.08160309,0.04068933,-0.00043853,0.01479212,0.03064586,0.00285093,-0.00863944,0.0584396,-0.02928652,0.08410116,0.08258732,-0.00042725,-0.00568948,-0.00187874,8.8e-7,-0.06718833,-0.03562899,-0.04985156,-0.04585588,0.01460261,-0.04130469,-0.05501203,0.03130036,-0.03661865],"last_embed":{"tokens":839,"hash":"ffjphh"}}},"last_read":{"hash":"ffjphh","at":1751251725492},"class_name":"SmartSource","outlinks":[{"title":"哈希算法","target":"哈希算法","line":36},{"title":"数字证书","target":"数字证书","line":72},{"title":"X.509证书","target":"X.509证书","line":72}],"metadata":{"aliases":["Transport Layer Security","传输层安全"],"英文":"Transport Layer Security","tags":["计算机网络/OSI模型/传输层"],"协议层级":["表示层"],"相关知识":null,"发布时间":"1999-01-01","cssclasses":["editor-full"],"类型":["加密协议"]},"blocks":{"#---frontmatter---":[1,16],"#简介":[17,29],"#简介#{1}":[18,18],"#简介#{2}":[19,20],"#简介#{3}":[21,21],"#简介#{4}":[22,23],"#简介#{5}":[24,29],"#版本发展":[30,40],"#版本发展#{1}":[31,32],"#版本发展#{2}":[33,34],"#版本发展#{3}":[35,36],"#版本发展#{4}":[37,39],"#版本发展#{5}":[40,40],"#运行原理":[41,101],"#运行原理#. 握手过程（Handshake）":[43,81],"#运行原理#. 握手过程（Handshake）#{1}":[45,64],"#运行原理#. 握手过程（Handshake）#{2}":[65,67],"#运行原理#. 握手过程（Handshake）#{3}":[68,70],"#运行原理#. 握手过程（Handshake）#{4}":[71,73],"#运行原理#. 握手过程（Handshake）#{5}":[74,76],"#运行原理#. 握手过程（Handshake）#{6}":[77,80],"#运行原理#. 握手过程（Handshake）#{7}":[81,81],"#运行原理#数据传输过程":[82,101],"#运行原理#数据传输过程#{1}":[84,97],"#运行原理#数据传输过程#{2}":[98,98],"#运行原理#数据传输过程#{3}":[99,99],"#运行原理#数据传输过程#{4}":[100,100],"#运行原理#数据传输过程#{5}":[101,101]},"last_import":{"mtime":1737625442813,"size":3514,"at":1748488128974,"hash":"ffjphh"},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/传输层协议/TLS协议.md","last_embed":{"hash":"ffjphh","at":1751251725492}},