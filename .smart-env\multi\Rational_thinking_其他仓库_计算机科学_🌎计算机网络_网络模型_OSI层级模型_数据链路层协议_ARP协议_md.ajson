"smart_sources:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md": {"path":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0947605,-0.01632762,-0.00074894,-0.03601433,0.01232208,-0.03820064,-0.00276448,-0.00101169,0.05734233,-0.00481325,-0.00155026,-0.09310421,0.10270628,0.05882711,0.08206017,0.06887684,-0.02188416,0.00167534,0.03407364,0.02125677,0.08877764,-0.06185871,-0.00213098,-0.06596966,-0.02527583,0.06112798,0.02529052,-0.03902635,0.01973162,-0.17025654,0.00382233,0.04319053,0.03975514,0.01718957,-0.02590443,0.00323948,0.03310585,0.03700723,-0.03543961,0.06535723,0.05719591,-0.01186982,0.0636555,-0.01806206,0.00609898,-0.05909887,-0.00740565,-0.02121065,-0.02577334,-0.05538674,-0.05697555,-0.00184827,-0.07339676,-0.00865354,-0.06577331,0.01447001,-0.01088525,0.01561789,0.06008664,-0.01847854,0.04976179,0.06192403,-0.24193832,0.07689326,0.03317802,-0.03711062,-0.02662462,0.01355482,-0.03409871,-0.0023338,-0.04896154,0.0141317,-0.01140431,0.05079214,0.02255518,-0.0400553,0.01053726,0.01098914,0.0282823,-0.05261643,-0.04142559,0.04115446,-0.0015399,-0.04125118,0.01131121,-0.02703791,0.00966645,-0.03276599,0.03311954,-0.02303199,-0.01519851,-0.10533477,0.01892481,0.01893018,0.00484635,0.00136859,0.02519439,0.0461316,-0.09340829,0.13213532,-0.05772574,-0.01269215,-0.01521576,-0.03700297,0.02838302,-0.05814529,0.02664193,-0.00899638,-0.04261576,0.02377198,-0.08264621,-0.03532634,0.04480844,0.0076796,0.02722806,0.05044517,-0.01005469,0.04237708,-0.00540768,-0.0324471,0.01789906,0.01340144,0.02817359,0.0345343,-0.01037631,-0.07473436,0.03672548,0.08330563,0.01206533,0.03269211,0.05066205,-0.06568423,-0.05941526,-0.00893371,-0.00218894,0.02502391,-0.0359249,0.002355,-0.04031964,-0.04380889,0.00027921,-0.03888621,-0.01069984,-0.03650792,-0.0491352,0.11907197,-0.04693837,0.05390226,0.03141652,-0.05741146,-0.00036548,0.01177035,-0.03254728,-0.00242825,-0.05292534,0.05125415,0.07608262,0.10119905,-0.03758597,-0.01047075,-0.02602881,-0.02092047,-0.09770589,0.14593677,0.03316829,-0.08476374,-0.04428874,-0.0060073,-0.01784414,-0.04104165,-0.01534365,0.00670369,0.04476503,0.02431343,0.03900515,-0.01699429,-0.0249236,0.00466077,0.01572198,-0.01706164,-0.02005911,-0.01916272,-0.0615938,0.02337399,0.04299721,-0.07172181,-0.04437866,-0.0368754,0.04645473,-0.11286903,-0.09132858,0.0280249,-0.05472763,0.00431339,-0.03386484,-0.09419218,0.03991353,0.02060694,0.09057778,-0.00469959,0.06562228,0.05925571,-0.07273191,-0.01679231,-0.03734874,-0.00527766,-0.03415712,0.05612464,-0.00760765,0.03579821,-0.04862963,0.01208564,0.03038359,0.007273,-0.01961588,0.0055157,0.02796848,-0.01090624,0.05182373,0.04636373,-0.00133255,-0.00626116,-0.06506535,-0.20481431,-0.02673046,0.01661732,-0.00987165,0.0076839,-0.03824067,-0.00093807,0.01009784,0.05882195,0.05964296,0.05674179,0.00270525,-0.05071801,0.03488397,0.03495081,0.0261536,0.04366767,-0.00249577,-0.04413886,0.01389096,0.01679313,0.06652676,-0.07694887,0.0435264,0.03107447,-0.02333717,0.0944546,0.04798907,0.05007111,0.03594849,0.03406061,0.02517176,-0.00853768,-0.10541318,-0.01565528,0.08843995,-0.0133743,-0.02230706,-0.01602838,0.00792827,0.0034516,0.0720956,-0.04537554,-0.07432915,-0.04042559,-0.04630629,-0.01905096,0.00309651,0.0054721,0.02809603,0.00486212,0.0205154,-0.006494,-0.02016797,0.03034484,-0.00981401,-0.08159804,-0.09460684,0.00654807,-0.02002628,0.04806171,-0.04298961,0.01466688,0.01987996,0.04165873,0.00975616,-0.01549237,-0.0084655,-0.04194067,0.01701842,0.011589,0.10742709,-0.01531676,-0.04219717,0.04975079,0.00298729,-0.02738167,-0.02183888,0.01913846,-0.00498232,0.05091166,0.02692809,0.04293549,-0.00595551,0.02106388,0.04385392,0.02594056,0.01087634,0.04576699,-0.05204492,-0.01115506,-0.00732063,-0.06695699,0.0244426,0.10124706,-0.00635311,-0.29072207,0.00645776,0.00323889,0.00156938,0.01746952,0.03852626,0.06289645,0.02384185,-0.04141573,0.05003823,-0.06790861,0.06083801,0.0286648,-0.01564442,-0.019627,-0.01413004,0.04748604,-0.01753857,0.02065373,-0.06512503,-0.01960441,0.08373797,0.18992063,-0.0271973,0.00969314,0.00331887,0.05633489,0.0984749,0.00556424,-0.00299372,0.04579699,-0.09830575,-0.01172779,-0.00366463,0.01049285,0.07967812,-0.03745012,0.01042742,0.01237258,0.01266151,-0.04669649,0.01950763,-0.09673088,0.03117955,0.09316554,0.05613804,-0.02224188,-0.03178952,0.0327474,0.01997969,-0.00055415,0.00164666,0.02476932,-0.00518727,0.03887434,0.03237741,0.00186346,0.02342723,-0.04994161,0.04332981,0.00035809,0.04269334,0.05634402,0.11038793,0.0437844],"last_embed":{"hash":"1dc3395c0c7d48c4ac8e2ed2be801fc17109673d40f608c91d07eb97ffc0090f","tokens":426}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.02107048,0.01702482,-0.02346422,0.00155926,-0.00852759,0.00357519,0.02864282,-0.03344386,-0.00252149,-0.01826059,0.04251646,0.01995409,-0.01681799,-0.06313957,-0.00577986,0.10614268,0.01472138,-0.03010838,0.08900751,-0.02226241,-0.06176699,0.01823438,-0.02930406,0.03631883,-0.01553869,0.00043863,-0.06549528,-0.00959674,0.06033808,0.02705516,0.10956531,-0.05755083,0.04927679,0.00731522,0.0106427,0.04267091,0.01839147,0.02828752,0.00879586,-0.00868393,-0.03988362,0.01794806,-0.0421875,0.01799965,-0.08378047,-0.02580467,-0.02095179,-0.02523677,0.0286406,-0.01486717,-0.0160678,0.02489994,0.02453391,-0.0246385,-0.00869086,-0.00817495,-0.08104956,-0.05918943,-0.0098392,-0.02296916,0.02895225,-0.02142651,0.02160569,0.0019691,0.02234725,0.00069726,0.04979045,-0.04543953,-0.04386954,0.00904131,-0.059046,-0.03629708,0.09107216,-0.00090599,0.07709415,0.0480876,-0.01696627,0.00894654,-0.01405171,0.00934142,-0.01843811,0.03167658,0.02270862,-0.02269622,-0.02336429,-0.04640844,0.05291507,-0.00368429,-0.02294811,-0.00397224,0.00418248,-0.0248233,-0.01429367,-0.05911627,0.01335498,-0.0238271,0.04214525,-0.02458964,0.02103195,-0.00628269,-0.04495345,-0.05567749,-0.00905788,-0.00999359,0.00595137,-0.01287535,0.05585632,0.00521331,0.00538533,0.02363353,-0.0187906,-0.05131797,-0.0205243,-0.01605251,-0.04045721,0.00560128,0.02550088,0.02810053,0.07096142,0.0898582,0.02829087,-0.00318381,-0.04100429,0.00763737,-0.0601756,-0.05169393,0.03129626,-0.01759964,0.05460762,-0.05106719,0.02396809,0.02940265,-0.01007228,-0.04135194,0.03954998,0.00903973,0.02884576,-0.06433662,-0.08956699,-0.03482706,0.02095634,-0.04603625,-0.04642913,0.00422385,-0.04754915,0.04165086,-0.03813,0.043174,0.03661707,0.03265297,0.04535489,0.00148891,-0.04574047,-0.02964501,0.02902907,-0.01519436,-0.0173755,-0.02320545,0.0106544,-0.02880656,0.00805193,-0.01735571,-0.04216746,0.01989453,0.0206474,-0.04669267,-0.01538094,0.04876063,0.00375749,-0.00176197,-0.01397743,0.03092936,-0.00365462,-0.03547141,-0.00969754,0.01159734,0.05170094,-0.03367833,-0.04175011,0.02959109,0.00983742,-0.05619569,0.01632917,0.00360594,0.00123485,0.00899743,-0.02923049,0.00308075,0.01914428,-0.01478639,0.03875588,-0.00914032,0.00525421,0.01375286,0.05679748,0.05680947,-0.00410856,-0.04527267,0.01808717,-0.01866462,-0.037654,0.0329037,0.00463739,-0.10982742,0.05865851,0.05357121,0.00546108,-0.03910044,-0.05003005,-0.05602628,-0.08916655,0.03410481,0.03867033,-0.00777119,0.00692675,0.05924494,0.00745892,0.03331257,0.00327826,-0.08180468,-0.06493635,0.04797981,0.04577048,0.04263414,0.0302074,-0.05886001,0.01989659,-0.02510387,0.03340666,-0.01566337,-0.00833735,0.08173513,0.10742496,-0.02570939,-0.06026841,-0.00517878,-0.00526794,-0.0181785,0.04684509,-0.019557,-0.03923776,-0.0146747,0.04234125,0.00126138,-0.01636106,-0.03461085,-0.03839606,0.01813814,0.02404826,0.07138225,0.01048368,0.05607216,-0.05085187,0.03981568,-0.02587296,-0.05437613,-0.04950328,-0.04400861,0.03133191,-0.02861306,0.02738423,0.05049325,0.02691778,-0.00033915,-0.04689944,0.00097478,-0.0105508,0.0323537,0.0060319,0.00727869,0.06574912,-0.00877903,0.03338109,-0.00240018,-0.04065407,-0.01088105,-0.0263773,0.06477041,0.02294093,-0.03936096,-0.02825882,-0.00651332,-0.00011999,-0.00071009,0.02829782,0.01803853,-0.02985151,0.0054555,0.04344809,0.0268504,-0.00964492,-0.00846474,-0.00771829,0.025808,-0.02006848,-0.0272667,-0.06457089,0.04605557,0.00384565,-0.05261928,0.02823756,0.07513607,-0.00536247,-0.03652262,0.04321792,0.01027056,0.0018665,0.00229785,-0.00046521,-0.00191341,-0.00322769,-0.04278839,-0.04042407,-0.03061685,-0.02193486,-0.00152215,0.06257966,0.04368399,-0.01562628,-0.02683353,-0.06548385,0.00315563,-0.05628209,0.02965203,-0.02077861,-0.00001938,-0.01025599,0.0014424,0.01056226,0.06641904,-0.0210522,0.00151206,-0.06744079,-0.0620502,-0.00086392,0.07338166,-0.01244773,-0.01847798,0.02166085,-0.00176155,-0.02727836,-0.03685964,0.0478216,-0.00961219,-0.0140166,-0.00456075,-0.00364246,0.03921598,-0.05417619,-0.02442258,-0.00708492,0.02199842,-0.04980085,0.04522841,0.02962589,0.0238749,-0.04705409,0.00169995,-0.02984391,0.01678266,-0.0255808,0.00573212,-0.03137365,-0.0155768,-0.00895822,0.02826556,0.05672096,0.02312534,-0.00195608,-0.00043266,0.00667609,0.02919309,0.01245613,0.01779157,-0.01251532,-0.00567291,-0.05756343,-0.00392997,0.05820104,-0.0280077,-0.00088772,-0.06228923,0.05009846,0.00221955,-0.01413405,-0.03230258,0.02405382,0.02559368,0.0328107,-0.06285716,-0.02247052,0.0006431,-0.05318876,0.03333309,0.09242656,0.01448889,-0.02686153,0.01231849,0.02178679,-0.01427118,0.0108236,0.03857967,-0.01280456,-0.02751294,-0.00152432,0.04114274,-0.04505681,0.03029765,-0.01094538,0.00271297,0.10236657,-0.01092114,0.0016285,0.03706579,0.01912089,-0.04922381,0.03573813,-0.04572739,0.00206803,-0.00114265,-0.00293672,-0.01783275,0.02789887,-0.00433602,-0.02932299,0.04265438,-0.05933117,-0.00117178,-0.03648532,0.04066272,-0.03641224,-0.02676979,-0.02211124,-0.05662772,-0.0029545,-0.00500941,0.02552419,0.03308087,-0.03105319,-0.03557028,-0.03024274,-0.01610322,-0.00619277,-0.03181137,-0.06580963,0.00575065,0.00939303,-0.02018891,0.01223787,0.02506744,-0.00139458,0.03136084,-0.08879778,-0.02746903,0.00384015,0.03830735,0.0077932,0.00884869,0.02733248,0.01737808,0.04503646,0.08824366,-0.02971714,-0.04095419,-0.00537206,0.01919976,0.07185158,-0.02201798,-0.08598816,0.01427417,-0.04712551,-0.02065036,0.01117624,-0.02688451,0.00573165,-0.02888427,0.03136863,-0.02905451,-0.04251997,0.00857048,0.01838627,0.01237714,-0.04273656,-0.00201653,-0.03939628,0.04169842,0.00518615,0.04637089,0.05821474,-0.05885264,0.01193546,0.0255353,0.01865757,0.02216466,-0.03016546,0.01798238,0.01590825,0.01937872,0.02084436,0.00273965,0.03742054,0.01290064,-0.01903217,-0.012767,0.01990965,0.00387082,-0.03682709,0.01497501,0.03318436,-0.0201101,-0.03748403,-0.0102557,0.0144006,0.01003804,-0.02385969,-0.0445809,0.07384293,0.07984287,-0.01468043,-0.02270452,0.04842363,0.01789585,-0.03034351,-0.01704629,0.00703209,-0.02876309,-0.00154106,0.02190238,0.04168608,0.00131302,0.01612923,-0.02181735,0.04561399,-0.0004912,0.01515964,0.00523911,0.08143644,-0.02731819,0.00400286,0.01826583,-0.00575107,0.04196448,-0.06288038,-0.02296986,0.02452734,0.06326904,0.0506256,-0.03040607,0.023121,0.03165735,0.00462991,0.1238415,0.00408604,0.02384681,0.00177032,-0.0166326,0.01773365,-0.0150114,-0.00891945,0.02043089,0.03003072,0.04888211,-0.01390448,0.0531368,-0.00660861,0.04443039,-0.07373679,-0.01162115,-0.02958524,0.03571986,-0.13107646,-0.01978557,0.03291066,0.03929246,-0.03030345,-0.07479992,-0.01518881,0.02076963,0.0252659,-0.0091118,0.04983675,-0.02002168,0.01152895,-0.03677424,-0.01305801,0.03940641,-0.00362338,0.04549676,0.02731873,-0.04596594,0.06838482,-0.0324185,0.01768341,-0.06238353,0.01110845,0.0360243,-0.03054395,0.01991647,0.00446429,-0.02928018,-0.00098122,-0.01635146,-0.00886812,-0.02427456,-0.02550584,0.00103771,0.01551905,-0.00845159,0.00691916,0.10666849,-0.02730498,0.10131465,-0.02241009,-0.01922445,-0.00301974,0.02123341,0.00583709,-0.0399516,0.02062822,-0.00402564,-0.05527806,-0.08787443,-0.03075072,-0.05104366,0.00130092,-0.00490069,0.04088157,0.06393879,0.02690742,-0.02572891,-0.01853545,0.00726419,0.00058946,0.03327729,0.01646001,0.01486788,-0.05898571,-0.00167605,-0.00886834,-0.01520076,0.08407136,-0.00933574,0.03113573,-0.00033203,0.00935784,0.01127392,-0.06104525,-0.02994942,-0.01516425,0.0458778,0.0042085,0.01660073,-0.03565969,-0.02791259,0.02007877,0.00923779,-0.0187801,-0.01368196,0.00539909,0.01842684,-0.03114202,-0.00714667,-0.01534647,0.02435382,-0.04129946,-0.04667413,0.04946913,-0.00190659,-0.00071128,-0.11213183,0.06681038,0.00546985,-0.03227812,-0.00494979,-0.00132588,-0.01658124,0.0281026,0.02880953,-0.00990027,0.01426107,-0.07646164,0.00901922,0.00024148,-0.06666941,-0.01482923,-0.01129421,0.02376298,-0.0557851,-0.05818329,-0.00872269,0.01115029,0.04300471,0.00810385,-0.0118728,-0.01256953,-0.02033122,0.01904812,0.05573061,-0.00947089,-0.00165688,-0.02993751,0.01262607,-0.00972732,0.0263977,-0.01244708,0.01069713,-0.04313013,0.02464519,-0.00407592,0.016195,-0.01466389,0.01524237,-0.03661074,-0.05434879,0.04587145,-0.02332021,0.03124214,-0.00907563,-0.03071448,0.00627021,0.03002051,0.00984655,0.03340174,0.03972095,-0.09382162,-0.03479157,-0.0288381,0.00575543,-0.00024001,0.02797863,-0.08735783,-0.06213064,-0.02245683,-0.00709507,0.05317,-0.05021756,0.04964652,-0.00816108,0.03685414,-0.01539247,0.0236417,-0.01048324,0.02861276,-0.00566473,-0.02785002,0.03798267,-0.0237476,0.03548763,-0.03396329,0.01803317,0.02087753,0.01969014,-0.05127205,0.08855127,-0.00322217,-0.00693396,-0.00006496,0.02430647,8.9e-7,0.01894756,-0.03746637,-0.03518821,-0.03479321,0.00122099,-0.00935003,0.01163129,0.05020418,0.01163007],"last_embed":{"tokens":1041,"hash":"1j23ii9"}}},"last_read":{"hash":"1j23ii9","at":1750993401986},"class_name":"SmartSource","outlinks":[{"title":"IP协议","target":"IP协议","line":13},{"title":"IP协议","target":"IP协议","line":21},{"title":"IP协议","target":"IP协议","line":33},{"title":"TCP协议","target":"TCP协议","line":33},{"title":"IPv4协议","target":"IPv4协议","line":43},{"title":"IPv6协议","target":"IPv6协议","line":47},{"title":"DNS协议","target":"DNS协议","line":50},{"title":"以太网","target":"以太网","line":62},{"title":"操作码","target":"操作码","line":72},{"title":"Opcode","target":"操作码","line":72}],"metadata":{"tags":["计算机网络/OSI模型/数据链路层","计算机网络/OSI模型/网络层"],"英文":"Address Resolution Protocol","aliases":["Address Resolution Protocol","地址解析协议","ARP协议"],"协议层级":["网络层"],"基础知识":["[[IP协议]]"],"cssclasses":["editor-full"]},"blocks":{"#---frontmatter---":[1,16],"#简介":[17,36],"#简介#IP地址 => MAC地址":[18,36],"#简介#IP地址 => MAC地址#{1}":[19,23],"#简介#IP地址 => MAC地址#{2}":[24,31],"#简介#IP地址 => MAC地址#{3}":[32,32],"#简介#IP地址 => MAC地址#{4}":[33,34],"#简介#IP地址 => MAC地址#{5}":[35,36],"#协议特性":[37,54],"#协议特性#无状态协议":[38,54],"#协议特性#无状态协议#{1}":[39,39],"#协议特性#无状态协议#{2}":[40,54],"#数据包结构":[55,95],"#数据包结构#结构分析":[57,95],"#数据包结构#结构分析#{1}":[59,83],"#数据包结构#结构分析#{2}":[84,90],"#数据包结构#结构分析#{3}":[91,95]},"last_import":{"mtime":1741422415503,"size":3970,"at":1749024987540,"hash":"1j23ii9"},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md","last_embed":{"hash":"1j23ii9","at":1750993401986}},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md#---frontmatter---","lines":[1,16],"size":212,"outlinks":[{"title":"IP协议","target":"IP协议","line":13}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md#简介": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md#简介","lines":[17,36],"size":483,"outlinks":[{"title":"IP协议","target":"IP协议","line":5},{"title":"IP协议","target":"IP协议","line":17},{"title":"TCP协议","target":"TCP协议","line":17}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md#简介#IP地址 => MAC地址": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md#简介#IP地址 => MAC地址","lines":[18,36],"size":477,"outlinks":[{"title":"IP协议","target":"IP协议","line":4},{"title":"IP协议","target":"IP协议","line":16},{"title":"TCP协议","target":"TCP协议","line":16}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md#简介#IP地址 => MAC地址#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md#简介#IP地址 => MAC地址#{1}","lines":[19,23],"size":156,"outlinks":[{"title":"IP协议","target":"IP协议","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md#简介#IP地址 => MAC地址#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md#简介#IP地址 => MAC地址#{2}","lines":[24,31],"size":222,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md#简介#IP地址 => MAC地址#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md#简介#IP地址 => MAC地址#{3}","lines":[32,32],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md#简介#IP地址 => MAC地址#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md#简介#IP地址 => MAC地址#{4}","lines":[33,34],"size":70,"outlinks":[{"title":"IP协议","target":"IP协议","line":1},{"title":"TCP协议","target":"TCP协议","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md#简介#IP地址 => MAC地址#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md#简介#IP地址 => MAC地址#{5}","lines":[35,36],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md#协议特性": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md#协议特性","lines":[37,54],"size":456,"outlinks":[{"title":"IPv4协议","target":"IPv4协议","line":7},{"title":"IPv6协议","target":"IPv6协议","line":11},{"title":"DNS协议","target":"DNS协议","line":14}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md#协议特性#无状态协议": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md#协议特性#无状态协议","lines":[38,54],"size":449,"outlinks":[{"title":"IPv4协议","target":"IPv4协议","line":6},{"title":"IPv6协议","target":"IPv6协议","line":10},{"title":"DNS协议","target":"DNS协议","line":13}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md#协议特性#无状态协议#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md#协议特性#无状态协议#{1}","lines":[39,39],"size":38,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md#协议特性#无状态协议#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md#协议特性#无状态协议#{2}","lines":[40,54],"size":400,"outlinks":[{"title":"IPv4协议","target":"IPv4协议","line":4},{"title":"IPv6协议","target":"IPv6协议","line":8},{"title":"DNS协议","target":"DNS协议","line":11}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md#数据包结构": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md#数据包结构","lines":[55,95],"size":928,"outlinks":[{"title":"以太网","target":"以太网","line":8},{"title":"操作码","target":"操作码","line":18},{"title":"Opcode","target":"操作码","line":18}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md#数据包结构#结构分析": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md#数据包结构#结构分析","lines":[57,95],"size":919,"outlinks":[{"title":"以太网","target":"以太网","line":6},{"title":"操作码","target":"操作码","line":16},{"title":"Opcode","target":"操作码","line":16}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md#数据包结构#结构分析#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md#数据包结构#结构分析#{1}","lines":[59,83],"size":734,"outlinks":[{"title":"以太网","target":"以太网","line":4},{"title":"操作码","target":"操作码","line":14},{"title":"Opcode","target":"操作码","line":14}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md#数据包结构#结构分析#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md#数据包结构#结构分析#{2}","lines":[84,90],"size":71,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md#数据包结构#结构分析#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md#数据包结构#结构分析#{3}","lines":[91,95],"size":102,"outlinks":[],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md": {"path":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0947605,-0.01632762,-0.00074894,-0.03601433,0.01232208,-0.03820064,-0.00276448,-0.00101169,0.05734233,-0.00481325,-0.00155026,-0.09310421,0.10270628,0.05882711,0.08206017,0.06887684,-0.02188416,0.00167534,0.03407364,0.02125677,0.08877764,-0.06185871,-0.00213098,-0.06596966,-0.02527583,0.06112798,0.02529052,-0.03902635,0.01973162,-0.17025654,0.00382233,0.04319053,0.03975514,0.01718957,-0.02590443,0.00323948,0.03310585,0.03700723,-0.03543961,0.06535723,0.05719591,-0.01186982,0.0636555,-0.01806206,0.00609898,-0.05909887,-0.00740565,-0.02121065,-0.02577334,-0.05538674,-0.05697555,-0.00184827,-0.07339676,-0.00865354,-0.06577331,0.01447001,-0.01088525,0.01561789,0.06008664,-0.01847854,0.04976179,0.06192403,-0.24193832,0.07689326,0.03317802,-0.03711062,-0.02662462,0.01355482,-0.03409871,-0.0023338,-0.04896154,0.0141317,-0.01140431,0.05079214,0.02255518,-0.0400553,0.01053726,0.01098914,0.0282823,-0.05261643,-0.04142559,0.04115446,-0.0015399,-0.04125118,0.01131121,-0.02703791,0.00966645,-0.03276599,0.03311954,-0.02303199,-0.01519851,-0.10533477,0.01892481,0.01893018,0.00484635,0.00136859,0.02519439,0.0461316,-0.09340829,0.13213532,-0.05772574,-0.01269215,-0.01521576,-0.03700297,0.02838302,-0.05814529,0.02664193,-0.00899638,-0.04261576,0.02377198,-0.08264621,-0.03532634,0.04480844,0.0076796,0.02722806,0.05044517,-0.01005469,0.04237708,-0.00540768,-0.0324471,0.01789906,0.01340144,0.02817359,0.0345343,-0.01037631,-0.07473436,0.03672548,0.08330563,0.01206533,0.03269211,0.05066205,-0.06568423,-0.05941526,-0.00893371,-0.00218894,0.02502391,-0.0359249,0.002355,-0.04031964,-0.04380889,0.00027921,-0.03888621,-0.01069984,-0.03650792,-0.0491352,0.11907197,-0.04693837,0.05390226,0.03141652,-0.05741146,-0.00036548,0.01177035,-0.03254728,-0.00242825,-0.05292534,0.05125415,0.07608262,0.10119905,-0.03758597,-0.01047075,-0.02602881,-0.02092047,-0.09770589,0.14593677,0.03316829,-0.08476374,-0.04428874,-0.0060073,-0.01784414,-0.04104165,-0.01534365,0.00670369,0.04476503,0.02431343,0.03900515,-0.01699429,-0.0249236,0.00466077,0.01572198,-0.01706164,-0.02005911,-0.01916272,-0.0615938,0.02337399,0.04299721,-0.07172181,-0.04437866,-0.0368754,0.04645473,-0.11286903,-0.09132858,0.0280249,-0.05472763,0.00431339,-0.03386484,-0.09419218,0.03991353,0.02060694,0.09057778,-0.00469959,0.06562228,0.05925571,-0.07273191,-0.01679231,-0.03734874,-0.00527766,-0.03415712,0.05612464,-0.00760765,0.03579821,-0.04862963,0.01208564,0.03038359,0.007273,-0.01961588,0.0055157,0.02796848,-0.01090624,0.05182373,0.04636373,-0.00133255,-0.00626116,-0.06506535,-0.20481431,-0.02673046,0.01661732,-0.00987165,0.0076839,-0.03824067,-0.00093807,0.01009784,0.05882195,0.05964296,0.05674179,0.00270525,-0.05071801,0.03488397,0.03495081,0.0261536,0.04366767,-0.00249577,-0.04413886,0.01389096,0.01679313,0.06652676,-0.07694887,0.0435264,0.03107447,-0.02333717,0.0944546,0.04798907,0.05007111,0.03594849,0.03406061,0.02517176,-0.00853768,-0.10541318,-0.01565528,0.08843995,-0.0133743,-0.02230706,-0.01602838,0.00792827,0.0034516,0.0720956,-0.04537554,-0.07432915,-0.04042559,-0.04630629,-0.01905096,0.00309651,0.0054721,0.02809603,0.00486212,0.0205154,-0.006494,-0.02016797,0.03034484,-0.00981401,-0.08159804,-0.09460684,0.00654807,-0.02002628,0.04806171,-0.04298961,0.01466688,0.01987996,0.04165873,0.00975616,-0.01549237,-0.0084655,-0.04194067,0.01701842,0.011589,0.10742709,-0.01531676,-0.04219717,0.04975079,0.00298729,-0.02738167,-0.02183888,0.01913846,-0.00498232,0.05091166,0.02692809,0.04293549,-0.00595551,0.02106388,0.04385392,0.02594056,0.01087634,0.04576699,-0.05204492,-0.01115506,-0.00732063,-0.06695699,0.0244426,0.10124706,-0.00635311,-0.29072207,0.00645776,0.00323889,0.00156938,0.01746952,0.03852626,0.06289645,0.02384185,-0.04141573,0.05003823,-0.06790861,0.06083801,0.0286648,-0.01564442,-0.019627,-0.01413004,0.04748604,-0.01753857,0.02065373,-0.06512503,-0.01960441,0.08373797,0.18992063,-0.0271973,0.00969314,0.00331887,0.05633489,0.0984749,0.00556424,-0.00299372,0.04579699,-0.09830575,-0.01172779,-0.00366463,0.01049285,0.07967812,-0.03745012,0.01042742,0.01237258,0.01266151,-0.04669649,0.01950763,-0.09673088,0.03117955,0.09316554,0.05613804,-0.02224188,-0.03178952,0.0327474,0.01997969,-0.00055415,0.00164666,0.02476932,-0.00518727,0.03887434,0.03237741,0.00186346,0.02342723,-0.04994161,0.04332981,0.00035809,0.04269334,0.05634402,0.11038793,0.0437844],"last_embed":{"hash":"1dc3395c0c7d48c4ac8e2ed2be801fc17109673d40f608c91d07eb97ffc0090f","tokens":426}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.02107048,0.01702482,-0.02346422,0.00155926,-0.00852759,0.00357519,0.02864282,-0.03344386,-0.00252149,-0.01826059,0.04251646,0.01995409,-0.01681799,-0.06313957,-0.00577986,0.10614268,0.01472138,-0.03010838,0.08900751,-0.02226241,-0.06176699,0.01823438,-0.02930406,0.03631883,-0.01553869,0.00043863,-0.06549528,-0.00959674,0.06033808,0.02705516,0.10956531,-0.05755083,0.04927679,0.00731522,0.0106427,0.04267091,0.01839147,0.02828752,0.00879586,-0.00868393,-0.03988362,0.01794806,-0.0421875,0.01799965,-0.08378047,-0.02580467,-0.02095179,-0.02523677,0.0286406,-0.01486717,-0.0160678,0.02489994,0.02453391,-0.0246385,-0.00869086,-0.00817495,-0.08104956,-0.05918943,-0.0098392,-0.02296916,0.02895225,-0.02142651,0.02160569,0.0019691,0.02234725,0.00069726,0.04979045,-0.04543953,-0.04386954,0.00904131,-0.059046,-0.03629708,0.09107216,-0.00090599,0.07709415,0.0480876,-0.01696627,0.00894654,-0.01405171,0.00934142,-0.01843811,0.03167658,0.02270862,-0.02269622,-0.02336429,-0.04640844,0.05291507,-0.00368429,-0.02294811,-0.00397224,0.00418248,-0.0248233,-0.01429367,-0.05911627,0.01335498,-0.0238271,0.04214525,-0.02458964,0.02103195,-0.00628269,-0.04495345,-0.05567749,-0.00905788,-0.00999359,0.00595137,-0.01287535,0.05585632,0.00521331,0.00538533,0.02363353,-0.0187906,-0.05131797,-0.0205243,-0.01605251,-0.04045721,0.00560128,0.02550088,0.02810053,0.07096142,0.0898582,0.02829087,-0.00318381,-0.04100429,0.00763737,-0.0601756,-0.05169393,0.03129626,-0.01759964,0.05460762,-0.05106719,0.02396809,0.02940265,-0.01007228,-0.04135194,0.03954998,0.00903973,0.02884576,-0.06433662,-0.08956699,-0.03482706,0.02095634,-0.04603625,-0.04642913,0.00422385,-0.04754915,0.04165086,-0.03813,0.043174,0.03661707,0.03265297,0.04535489,0.00148891,-0.04574047,-0.02964501,0.02902907,-0.01519436,-0.0173755,-0.02320545,0.0106544,-0.02880656,0.00805193,-0.01735571,-0.04216746,0.01989453,0.0206474,-0.04669267,-0.01538094,0.04876063,0.00375749,-0.00176197,-0.01397743,0.03092936,-0.00365462,-0.03547141,-0.00969754,0.01159734,0.05170094,-0.03367833,-0.04175011,0.02959109,0.00983742,-0.05619569,0.01632917,0.00360594,0.00123485,0.00899743,-0.02923049,0.00308075,0.01914428,-0.01478639,0.03875588,-0.00914032,0.00525421,0.01375286,0.05679748,0.05680947,-0.00410856,-0.04527267,0.01808717,-0.01866462,-0.037654,0.0329037,0.00463739,-0.10982742,0.05865851,0.05357121,0.00546108,-0.03910044,-0.05003005,-0.05602628,-0.08916655,0.03410481,0.03867033,-0.00777119,0.00692675,0.05924494,0.00745892,0.03331257,0.00327826,-0.08180468,-0.06493635,0.04797981,0.04577048,0.04263414,0.0302074,-0.05886001,0.01989659,-0.02510387,0.03340666,-0.01566337,-0.00833735,0.08173513,0.10742496,-0.02570939,-0.06026841,-0.00517878,-0.00526794,-0.0181785,0.04684509,-0.019557,-0.03923776,-0.0146747,0.04234125,0.00126138,-0.01636106,-0.03461085,-0.03839606,0.01813814,0.02404826,0.07138225,0.01048368,0.05607216,-0.05085187,0.03981568,-0.02587296,-0.05437613,-0.04950328,-0.04400861,0.03133191,-0.02861306,0.02738423,0.05049325,0.02691778,-0.00033915,-0.04689944,0.00097478,-0.0105508,0.0323537,0.0060319,0.00727869,0.06574912,-0.00877903,0.03338109,-0.00240018,-0.04065407,-0.01088105,-0.0263773,0.06477041,0.02294093,-0.03936096,-0.02825882,-0.00651332,-0.00011999,-0.00071009,0.02829782,0.01803853,-0.02985151,0.0054555,0.04344809,0.0268504,-0.00964492,-0.00846474,-0.00771829,0.025808,-0.02006848,-0.0272667,-0.06457089,0.04605557,0.00384565,-0.05261928,0.02823756,0.07513607,-0.00536247,-0.03652262,0.04321792,0.01027056,0.0018665,0.00229785,-0.00046521,-0.00191341,-0.00322769,-0.04278839,-0.04042407,-0.03061685,-0.02193486,-0.00152215,0.06257966,0.04368399,-0.01562628,-0.02683353,-0.06548385,0.00315563,-0.05628209,0.02965203,-0.02077861,-0.00001938,-0.01025599,0.0014424,0.01056226,0.06641904,-0.0210522,0.00151206,-0.06744079,-0.0620502,-0.00086392,0.07338166,-0.01244773,-0.01847798,0.02166085,-0.00176155,-0.02727836,-0.03685964,0.0478216,-0.00961219,-0.0140166,-0.00456075,-0.00364246,0.03921598,-0.05417619,-0.02442258,-0.00708492,0.02199842,-0.04980085,0.04522841,0.02962589,0.0238749,-0.04705409,0.00169995,-0.02984391,0.01678266,-0.0255808,0.00573212,-0.03137365,-0.0155768,-0.00895822,0.02826556,0.05672096,0.02312534,-0.00195608,-0.00043266,0.00667609,0.02919309,0.01245613,0.01779157,-0.01251532,-0.00567291,-0.05756343,-0.00392997,0.05820104,-0.0280077,-0.00088772,-0.06228923,0.05009846,0.00221955,-0.01413405,-0.03230258,0.02405382,0.02559368,0.0328107,-0.06285716,-0.02247052,0.0006431,-0.05318876,0.03333309,0.09242656,0.01448889,-0.02686153,0.01231849,0.02178679,-0.01427118,0.0108236,0.03857967,-0.01280456,-0.02751294,-0.00152432,0.04114274,-0.04505681,0.03029765,-0.01094538,0.00271297,0.10236657,-0.01092114,0.0016285,0.03706579,0.01912089,-0.04922381,0.03573813,-0.04572739,0.00206803,-0.00114265,-0.00293672,-0.01783275,0.02789887,-0.00433602,-0.02932299,0.04265438,-0.05933117,-0.00117178,-0.03648532,0.04066272,-0.03641224,-0.02676979,-0.02211124,-0.05662772,-0.0029545,-0.00500941,0.02552419,0.03308087,-0.03105319,-0.03557028,-0.03024274,-0.01610322,-0.00619277,-0.03181137,-0.06580963,0.00575065,0.00939303,-0.02018891,0.01223787,0.02506744,-0.00139458,0.03136084,-0.08879778,-0.02746903,0.00384015,0.03830735,0.0077932,0.00884869,0.02733248,0.01737808,0.04503646,0.08824366,-0.02971714,-0.04095419,-0.00537206,0.01919976,0.07185158,-0.02201798,-0.08598816,0.01427417,-0.04712551,-0.02065036,0.01117624,-0.02688451,0.00573165,-0.02888427,0.03136863,-0.02905451,-0.04251997,0.00857048,0.01838627,0.01237714,-0.04273656,-0.00201653,-0.03939628,0.04169842,0.00518615,0.04637089,0.05821474,-0.05885264,0.01193546,0.0255353,0.01865757,0.02216466,-0.03016546,0.01798238,0.01590825,0.01937872,0.02084436,0.00273965,0.03742054,0.01290064,-0.01903217,-0.012767,0.01990965,0.00387082,-0.03682709,0.01497501,0.03318436,-0.0201101,-0.03748403,-0.0102557,0.0144006,0.01003804,-0.02385969,-0.0445809,0.07384293,0.07984287,-0.01468043,-0.02270452,0.04842363,0.01789585,-0.03034351,-0.01704629,0.00703209,-0.02876309,-0.00154106,0.02190238,0.04168608,0.00131302,0.01612923,-0.02181735,0.04561399,-0.0004912,0.01515964,0.00523911,0.08143644,-0.02731819,0.00400286,0.01826583,-0.00575107,0.04196448,-0.06288038,-0.02296986,0.02452734,0.06326904,0.0506256,-0.03040607,0.023121,0.03165735,0.00462991,0.1238415,0.00408604,0.02384681,0.00177032,-0.0166326,0.01773365,-0.0150114,-0.00891945,0.02043089,0.03003072,0.04888211,-0.01390448,0.0531368,-0.00660861,0.04443039,-0.07373679,-0.01162115,-0.02958524,0.03571986,-0.13107646,-0.01978557,0.03291066,0.03929246,-0.03030345,-0.07479992,-0.01518881,0.02076963,0.0252659,-0.0091118,0.04983675,-0.02002168,0.01152895,-0.03677424,-0.01305801,0.03940641,-0.00362338,0.04549676,0.02731873,-0.04596594,0.06838482,-0.0324185,0.01768341,-0.06238353,0.01110845,0.0360243,-0.03054395,0.01991647,0.00446429,-0.02928018,-0.00098122,-0.01635146,-0.00886812,-0.02427456,-0.02550584,0.00103771,0.01551905,-0.00845159,0.00691916,0.10666849,-0.02730498,0.10131465,-0.02241009,-0.01922445,-0.00301974,0.02123341,0.00583709,-0.0399516,0.02062822,-0.00402564,-0.05527806,-0.08787443,-0.03075072,-0.05104366,0.00130092,-0.00490069,0.04088157,0.06393879,0.02690742,-0.02572891,-0.01853545,0.00726419,0.00058946,0.03327729,0.01646001,0.01486788,-0.05898571,-0.00167605,-0.00886834,-0.01520076,0.08407136,-0.00933574,0.03113573,-0.00033203,0.00935784,0.01127392,-0.06104525,-0.02994942,-0.01516425,0.0458778,0.0042085,0.01660073,-0.03565969,-0.02791259,0.02007877,0.00923779,-0.0187801,-0.01368196,0.00539909,0.01842684,-0.03114202,-0.00714667,-0.01534647,0.02435382,-0.04129946,-0.04667413,0.04946913,-0.00190659,-0.00071128,-0.11213183,0.06681038,0.00546985,-0.03227812,-0.00494979,-0.00132588,-0.01658124,0.0281026,0.02880953,-0.00990027,0.01426107,-0.07646164,0.00901922,0.00024148,-0.06666941,-0.01482923,-0.01129421,0.02376298,-0.0557851,-0.05818329,-0.00872269,0.01115029,0.04300471,0.00810385,-0.0118728,-0.01256953,-0.02033122,0.01904812,0.05573061,-0.00947089,-0.00165688,-0.02993751,0.01262607,-0.00972732,0.0263977,-0.01244708,0.01069713,-0.04313013,0.02464519,-0.00407592,0.016195,-0.01466389,0.01524237,-0.03661074,-0.05434879,0.04587145,-0.02332021,0.03124214,-0.00907563,-0.03071448,0.00627021,0.03002051,0.00984655,0.03340174,0.03972095,-0.09382162,-0.03479157,-0.0288381,0.00575543,-0.00024001,0.02797863,-0.08735783,-0.06213064,-0.02245683,-0.00709507,0.05317,-0.05021756,0.04964652,-0.00816108,0.03685414,-0.01539247,0.0236417,-0.01048324,0.02861276,-0.00566473,-0.02785002,0.03798267,-0.0237476,0.03548763,-0.03396329,0.01803317,0.02087753,0.01969014,-0.05127205,0.08855127,-0.00322217,-0.00693396,-0.00006496,0.02430647,8.9e-7,0.01894756,-0.03746637,-0.03518821,-0.03479321,0.00122099,-0.00935003,0.01163129,0.05020418,0.01163007],"last_embed":{"tokens":1041,"hash":"1j23ii9"}}},"last_read":{"hash":"1j23ii9","at":1751079987875},"class_name":"SmartSource","outlinks":[{"title":"IP协议","target":"IP协议","line":13},{"title":"IP协议","target":"IP协议","line":21},{"title":"IP协议","target":"IP协议","line":33},{"title":"TCP协议","target":"TCP协议","line":33},{"title":"IPv4协议","target":"IPv4协议","line":43},{"title":"IPv6协议","target":"IPv6协议","line":47},{"title":"DNS协议","target":"DNS协议","line":50},{"title":"以太网","target":"以太网","line":62},{"title":"操作码","target":"操作码","line":72},{"title":"Opcode","target":"操作码","line":72}],"metadata":{"tags":["计算机网络/OSI模型/数据链路层","计算机网络/OSI模型/网络层"],"英文":"Address Resolution Protocol","aliases":["Address Resolution Protocol","地址解析协议","ARP协议"],"协议层级":["网络层"],"基础知识":["[[IP协议]]"],"cssclasses":["editor-full"]},"blocks":{"#---frontmatter---":[1,16],"#简介":[17,36],"#简介#IP地址 => MAC地址":[18,36],"#简介#IP地址 => MAC地址#{1}":[19,23],"#简介#IP地址 => MAC地址#{2}":[24,31],"#简介#IP地址 => MAC地址#{3}":[32,32],"#简介#IP地址 => MAC地址#{4}":[33,34],"#简介#IP地址 => MAC地址#{5}":[35,36],"#协议特性":[37,54],"#协议特性#无状态协议":[38,54],"#协议特性#无状态协议#{1}":[39,39],"#协议特性#无状态协议#{2}":[40,54],"#数据包结构":[55,95],"#数据包结构#结构分析":[57,95],"#数据包结构#结构分析#{1}":[59,83],"#数据包结构#结构分析#{2}":[84,90],"#数据包结构#结构分析#{3}":[91,95]},"last_import":{"mtime":1741422415503,"size":3970,"at":1749024987540,"hash":"1j23ii9"},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/ARP协议.md","last_embed":{"hash":"1j23ii9","at":1751079987875}},