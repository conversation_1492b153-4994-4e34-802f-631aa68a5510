"smart_sources:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/PPPOE协议.md": {"path":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/PPPOE协议.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.13707267,-0.00029072,-0.04170333,-0.06356119,-0.03220762,-0.00086494,0.0302136,0.00813983,0.06779308,-0.01654938,-0.00350084,-0.07480792,0.06655511,0.06508446,0.03692516,0.03286572,0.01205975,0.00807996,0.00958103,0.0277578,0.11492284,-0.07005051,-0.04469262,-0.06134192,-0.00930976,0.05880255,0.02978114,0.00174784,0.01005867,-0.15773994,-0.01635049,0.03871529,0.04014168,0.00582997,-0.05494819,0.03487016,0.01319834,0.02827813,-0.01433086,0.05198096,0.02627775,-0.028871,0.03317596,-0.03865356,0.0279496,-0.07484055,-0.03731714,-0.02315653,-0.01461321,-0.05309372,-0.02053936,-0.00449429,-0.03679463,-0.01951984,-0.04013783,0.02180619,0.00245,0.0346011,0.03898932,-0.03692091,0.03099126,0.0606671,-0.23243424,0.05598085,0.02113158,0.00011836,-0.02961756,0.03097867,-0.01881806,0.00637789,-0.07681148,0.05500289,0.00422886,0.02273002,0.03888801,0.02295453,0.02899402,-0.01599048,-0.029147,-0.03045193,-0.01770184,0.04090067,-0.02277274,-0.04294081,-0.01369811,-0.00611155,-0.00142072,-0.02780579,0.01000654,-0.0167186,-0.01654001,-0.06047486,0.00281239,0.02170949,0.01414014,0.05088164,0.03521007,0.02547647,-0.06326471,0.10070524,-0.10507393,0.01410105,-0.01797221,-0.05547176,0.05379735,-0.0923558,0.02386656,-0.08113848,0.00810253,0.00563442,-0.09337617,-0.02566244,-0.00584705,-0.03316559,0.04063393,0.04709041,-0.0025241,0.05102129,-0.01991918,-0.02421992,-0.00416267,-0.00721671,0.0463715,0.00749087,-0.01735313,-0.03942024,0.07065214,0.06283351,0.02755525,0.0384401,0.10377536,-0.02739933,-0.09903873,-0.01103305,0.0107638,-0.02234505,0.00581456,-0.02326618,0.00982262,-0.03875069,-0.01656689,-0.07103857,0.02962885,-0.03951184,-0.06009666,0.10481181,-0.05864706,0.06731232,0.04370642,-0.02032826,-0.0115727,0.05542697,-0.0327093,0.00215251,-0.03778957,0.01357344,0.06657614,0.11935391,-0.00681782,0.01003371,0.00676289,-0.0093988,-0.05653317,0.15304266,0.02345425,-0.10424188,-0.01669036,0.01336423,0.01864279,-0.05169451,-0.02365631,0.01054254,0.06931827,0.01077937,0.07349485,-0.0179265,-0.01899461,-0.00732555,0.01655906,0.00592193,0.00942177,-0.05266253,-0.02638346,-0.01423894,0.0314602,-0.10456012,-0.06066906,-0.06442887,0.04343494,-0.05655279,-0.06870472,0.02617442,-0.02305112,0.01560898,-0.01653627,-0.08623505,0.03792882,0.03124143,0.03009432,-0.03615523,0.05303779,0.04042797,-0.04741313,-0.01418014,-0.02412382,-0.0034438,-0.0062401,0.0285529,0.03854416,0.036784,-0.02106792,0.02548357,-0.01227616,0.0602915,-0.07880805,0.02990281,-0.00093928,0.00360644,0.02635781,0.05845057,-0.02966688,0.01432654,-0.04913976,-0.20177177,-0.02709465,0.02548614,-0.0409083,-0.00361228,0.01381823,-0.05498926,0.01358356,0.04502663,0.06719219,0.08753007,0.00176765,-0.05896179,0.03390157,0.01023042,0.01032271,0.02499312,-0.02029034,-0.02679666,-0.02113775,0.00322092,0.03023511,-0.07721956,0.00186624,0.04158287,-0.01926251,0.1223699,0.05974517,0.01420299,0.04980642,0.04052608,0.00473279,-0.02223173,-0.11918576,0.01268378,0.044693,-0.03646852,-0.03663933,0.00197951,-0.00515644,-0.00552995,0.05713147,-0.03879863,-0.09282864,-0.04736524,-0.03190159,-0.01733267,-0.01676054,0.00227301,0.04585465,0.015288,0.02651491,0.02378544,-0.01057646,0.0190984,-0.07425058,-0.02973376,-0.07003878,-0.03822255,0.00013315,0.00636813,0.01765717,0.02403293,0.00228782,-0.01451963,-0.0182928,-0.00797386,0.010704,-0.00167776,0.03468307,-0.01710425,0.1646824,0.01516685,-0.0434458,0.08293262,0.00587281,-0.02488798,-0.01590605,0.01827245,-0.01681099,0.04868615,0.04018358,0.00625629,0.02277329,0.00767198,0.03070784,0.07688201,-0.03260826,0.04017821,-0.04796078,-0.04234184,0.01967354,-0.03819647,0.05121124,0.08624794,0.03890023,-0.31746477,0.0188215,-0.02437329,0.04272384,0.01474176,0.07408264,0.05721697,-0.01112485,-0.04188399,-0.00531287,-0.04452182,0.08579897,0.00495072,-0.04056028,-0.00828341,-0.01440141,0.03177085,-0.01115105,0.04787866,-0.05864493,-0.02763568,0.05064437,0.19184184,-0.00332166,0.02200855,-0.00453173,0.03017807,0.05963188,0.01296301,0.0546186,0.03019323,-0.0504892,0.00335167,-0.04956547,0.03558285,0.0743826,-0.00537991,0.00557289,-0.00177441,-0.01022354,-0.02105775,0.01833521,-0.0694012,0.03301332,0.07843811,0.07022838,-0.03708349,-0.03959877,-0.00765153,0.0971392,-0.02162683,-0.0060159,0.00875323,-0.00921183,0.01596591,0.02590167,0.03000771,0.00485764,-0.02485314,-0.01977666,0.04172817,0.00609757,0.06994162,0.11396351,0.03069556],"last_embed":{"hash":"ebc833ac6dc982ac7bebd6da848054da67892e0e8e2a7d4ecf13630b4b313eba","tokens":450}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[0.02068406,-0.00788088,-0.04046812,-0.02090889,-0.03142709,0.01180486,0.02333991,-0.03404389,-0.00144603,0.00492077,0.01209682,0.00651113,0.01303383,-0.07686663,-0.01612939,0.07806538,0.03859064,0.03402886,0.05493464,-0.00843989,-0.04325972,-0.02125999,0.0096498,0.0291237,-0.00360399,0.00086145,-0.06854352,-0.00084459,0.03917871,0.04122603,0.0632645,-0.07307663,0.00672175,-0.01887851,0.01409287,0.03170639,0.02348429,-0.001589,0.02173283,-0.06379132,-0.05415978,-0.00195627,-0.03907155,0.02394783,-0.11713862,-0.01333053,-0.00844425,-0.00312746,-0.00738967,-0.01056811,-0.03529685,0.03780729,0.01497182,-0.03425686,-0.04157653,0.00766445,-0.05942471,-0.03777682,-0.01744813,-0.06986862,-0.02387006,-0.03001845,0.01569154,-0.00234754,0.07419995,-0.00357923,0.01112971,-0.02579816,-0.01422601,0.02578393,-0.06554512,-0.04715949,0.10216223,0.01490884,0.01725486,-0.00571784,-0.0044209,0.03697052,-0.03263849,0.07196166,-0.02241984,0.03994528,0.04128518,0.02090256,-0.01555947,-0.01105413,0.07252728,-0.0099169,-0.02334749,0.02164348,0.01009117,-0.01042694,0.00225668,-0.07412644,0.03456988,-0.03581218,-0.02299216,0.00893256,0.0415668,-0.01127839,-0.055746,-0.00715092,-0.02141292,-0.01425743,-0.02941358,-0.00924858,0.03801823,0.03733986,-0.02208099,0.04005377,-0.00169891,-0.05770235,-0.03641513,-0.00699641,-0.00077364,-0.00437972,0.03369714,-0.00617196,0.00299773,0.03215742,0.09263818,0.02477616,-0.00041338,0.01553679,-0.0003328,0.02624055,0.02045296,-0.00412252,0.06924388,-0.03348677,0.00184706,0.03214383,-0.00169896,0.00325904,0.02024321,-0.00977712,0.01664216,-0.05768389,-0.05800889,-0.04396847,0.02672384,-0.01211213,-0.0391819,-0.00226465,0.00505046,0.01436992,-0.08127829,0.07243311,-0.02251933,0.02571299,-0.00060258,0.00569191,-0.00930157,-0.03189626,0.00893614,-0.00693641,-0.01345447,-0.02130334,-0.03202823,-0.01349205,-0.01338841,-0.02307494,-0.01225273,0.04593403,-0.04432157,0.03570852,-0.00481099,0.04109802,0.00732516,0.01768264,0.05547486,0.03234981,-0.00823278,0.00153373,-0.01060091,-0.01477854,-0.00355756,-0.00615974,-0.05623248,0.01266748,0.01742036,-0.05914434,0.00336592,0.00897471,0.01742051,0.02437664,-0.03983324,0.05412517,0.06165873,0.01829697,0.0398267,-0.02016717,0.01047663,0.0151974,0.03112871,0.09948511,0.01670515,-0.02692228,0.03785072,-0.00283872,0.01430027,-0.02232228,0.02781538,-0.12509096,-0.02156809,0.01921467,-0.00761847,-0.03816536,-0.03224886,-0.01515128,-0.06431077,0.03503512,0.00892778,-0.00540924,0.00109799,0.02895548,0.01992093,0.04685534,0.0484199,-0.0380871,0.00145922,-0.03988918,0.01692029,0.03428446,0.02009527,-0.06112021,0.01366489,0.00853605,-0.0234732,0.00296696,-0.03661773,0.04341013,0.05851459,-0.03219232,-0.0684416,0.00007875,0.03627518,-0.03625724,-0.01799223,-0.04152882,-0.05360655,0.00662648,0.06752922,-0.04599589,-0.0066437,-0.04696782,-0.06434158,0.04470533,0.02905723,0.08342378,0.0233763,0.07760315,-0.0648322,0.04598842,-0.02643914,-0.02118023,-0.03081194,-0.03491724,0.07433168,-0.01768651,0.06161159,0.00689214,0.05115526,0.01303359,0.0013591,0.01104828,0.01330853,0.05098372,-0.00748771,-0.01542755,0.00519412,-0.00411982,0.00265945,-0.0154137,-0.05416917,0.02340594,-0.00478664,0.04280787,0.05340641,-0.0428423,0.01040009,0.04102548,-0.01407803,-0.00492775,0.0542007,0.04317943,-0.03482502,0.02662586,0.05255789,0.00674481,-0.00996164,-0.01140131,-0.01525835,0.00773756,-0.01136547,-0.05290118,-0.04138993,0.02656347,-0.0250834,-0.02527233,0.00569753,0.03290566,-0.03662844,-0.00657034,0.05274099,0.02778034,0.00271079,0.02319913,0.01662902,0.02376934,0.01801057,-0.04891659,-0.00499026,-0.04763921,0.02428471,0.00281771,0.04930685,0.01660607,-0.01859432,-0.04652166,-0.0300805,0.05649987,-0.09742679,0.02192939,-0.01565814,-0.05250077,0.03275802,0.00851091,0.02554765,0.07093373,-0.01991566,-0.02684467,-0.02713658,-0.04932668,-0.01893917,0.0193038,0.0079495,-0.0359005,-0.02159305,-0.02127415,0.02323143,-0.03687459,0.02812747,-0.05580508,-0.0252984,0.09622383,-0.03651788,0.05137153,-0.05432433,-0.07027543,-0.02161698,0.0251989,-0.08854071,0.01297427,0.02170863,0.0111826,-0.0389049,0.00805095,-0.02359755,0.01131709,0.00589563,-0.06574937,-0.04753914,-0.02717053,0.03148665,0.01302025,0.07479591,0.02988197,-0.00916799,0.00032805,0.01781424,0.00295573,0.01327627,-0.01098254,-0.04082922,-0.0086679,-0.03725973,0.03226407,0.08821611,0.01355953,-0.02625272,0.00759751,0.05120413,-0.00234469,0.00322889,0.01286983,0.00595972,0.00510418,0.0014509,-0.09221204,0.00270346,-0.00249845,-0.01939187,0.05641292,0.05174791,-0.02361567,-0.03850755,0.01836518,0.02002423,-0.02515889,-0.00754024,0.0605937,-0.00696844,-0.01370987,-0.00801375,0.0579339,-0.04153653,0.05679033,-0.00746652,-0.00251125,0.0787909,-0.02393147,-0.00981472,0.04703537,-0.01862506,-0.05547191,0.00312795,-0.06018056,-0.02553475,0.02128815,-0.01544831,-0.05355655,0.03197277,-0.02176677,-0.04210028,-0.01060223,-0.08111468,0.02512822,-0.05772688,0.03302601,-0.02298234,-0.03995389,-0.03561026,-0.07036647,0.0046616,0.00386635,0.0390431,-0.00330397,0.02208058,-0.01907492,-0.03406392,-0.01396876,-0.02583637,0.01449321,-0.03610635,0.0091611,0.01200524,-0.0202503,-0.01101564,0.04198547,0.03277075,0.00182879,-0.07098344,-0.05692521,0.05033883,0.01710281,0.02248447,-0.00972962,0.05445299,-0.00765797,0.01331155,0.05501533,0.00600675,-0.01405275,0.01444139,0.03464267,0.10507698,-0.0198539,-0.07143527,0.01466635,-0.03013771,-0.0113715,0.03393315,-0.0130093,-0.0078703,-0.02120388,0.01566476,0.00714037,-0.07346147,0.00748033,-0.02397051,-0.03057154,-0.03435082,-0.02431335,-0.01218075,-0.01113315,-0.00334751,0.02813663,0.04845954,-0.02935301,0.04110952,0.0365145,-0.01125845,-0.02468794,-0.04998102,0.01531719,0.02044416,0.01423306,0.01791779,-0.00141437,0.00310602,0.02777446,-0.0416214,0.00368287,0.03587567,0.0002846,-0.03470414,-0.0312732,0.0319448,-0.05191707,-0.00817822,0.01752158,0.01541705,0.02675585,-0.06418262,-0.01341732,0.05270382,0.04768179,-0.03915796,-0.01451534,0.05289013,0.00518411,-0.03039597,-0.00611775,0.03902346,-0.00765914,0.00020568,-0.03387135,0.05138426,0.01539147,0.02968054,0.0229745,0.03069182,-0.01652237,-0.00703545,0.00604678,0.06083729,-0.0450807,-0.03325399,-0.01681937,0.03552131,0.04265527,-0.05071994,-0.00842597,0.0462396,-0.02496758,0.05810161,-0.04684542,-0.01770288,0.0078409,0.00171956,0.0681835,-0.0222868,0.02267176,0.00421047,-0.0228454,0.03283315,-0.00875503,-0.03357341,0.01166114,0.01659713,0.0270754,0.01185073,0.02911814,0.02153185,0.07660745,-0.08154517,0.02815368,0.06010502,0.02757794,-0.12981783,-0.01314459,0.03666341,0.01494782,-0.03536306,-0.019612,-0.07063837,0.01017429,0.0169213,-0.0159044,0.04854257,-0.00649858,0.01333386,-0.0039361,-0.05054886,0.00100193,0.0303043,0.02171965,0.00507409,-0.04214282,0.0266593,-0.08236939,0.02051397,-0.02480953,-0.02720629,0.04808217,-0.07529142,-0.00383101,0.02690047,-0.03569198,0.00098002,0.01377819,-0.00056797,-0.02761744,0.03921483,-0.04088715,0.02777507,-0.02341268,-0.02643433,0.04180636,-0.0183447,0.049906,-0.05570409,-0.01040236,-0.01143394,-0.01556741,0.01190625,-0.02002073,-0.01264387,0.01029175,-0.05796653,-0.05696826,-0.04513603,-0.00402727,0.01389541,0.01338967,0.06456694,0.06586895,0.02060287,-0.01348114,0.03991095,-0.01035572,0.04151129,0.04683539,0.08411982,0.03710392,-0.05049584,0.00492791,0.01049154,-0.00380164,0.06894903,0.03139994,0.03879746,-0.0116634,0.01259582,-0.01214146,-0.07926425,-0.02567505,-0.03839138,0.02975692,0.01538375,-0.02420577,0.00008602,-0.01092694,-0.00881648,0.0051066,0.01062247,-0.02700835,0.00265098,0.06859782,0.02732655,-0.02242058,-0.0165936,0.05683721,-0.02724952,-0.03257215,0.02224003,0.01566352,-0.00433051,-0.06612248,0.03960723,-0.0502486,-0.01063567,-0.00996365,0.0035234,-0.00006175,0.01009195,0.0301097,0.02223192,-0.00105384,-0.10571466,-0.01081398,0.02263295,-0.01609254,0.03561119,-0.02459408,0.04175228,-0.02780273,-0.06250692,0.01789576,-0.01411568,0.05371271,0.01280361,-0.06091629,-0.03116359,-0.03261263,0.03259209,0.01075521,-0.00546202,-0.02477279,-0.0523506,0.01277721,-0.00784656,-0.00059897,-0.04852373,0.01588503,-0.04051116,0.0259413,-0.01934542,0.01661073,-0.02182036,0.00134944,-0.03357548,-0.10577852,0.03647233,-0.01131572,0.04728043,0.0141529,-0.03364077,0.02426301,0.00335445,-0.00292216,0.01913016,0.00908919,-0.06601252,-0.02987296,0.00099643,0.01554406,-0.00912047,0.00590311,-0.07529508,-0.06192254,-0.0252354,0.04128238,0.04426864,-0.02464975,0.04171306,-0.00330337,0.04331232,-0.00855571,0.07123633,-0.04303262,0.00515838,-0.03551115,0.03049373,0.0366619,-0.02952135,0.02606626,0.02732387,0.03219547,0.01002747,0.0244147,-0.00444725,0.03865101,0.00276838,-0.00232659,-0.02177795,0.00359264,0.00000103,-0.03393513,-0.03077509,-0.00200409,-0.01604319,-0.0274647,-0.06663829,-0.05162806,0.04589744,0.02047709],"last_embed":{"tokens":427,"hash":"gg0qn5"}}},"last_read":{"hash":"gg0qn5","at":1751079987925},"class_name":"SmartSource","outlinks":[{"title":"以太网","target":"以太网","line":11},{"title":"DSL","target":"DSL","line":12},{"title":"数字用户线路","target":"DSL","line":12}],"metadata":{"aliases":["Point-to-Point Protocol"],"英文":"Point-to-Point Protocol","tags":["计算机网络/OSI模型/数据链路层"],"协议层级":"数据链路层"},"blocks":{"#---frontmatter---":[1,8],"#简介":[9,20],"#简介#{1}":[10,14],"#简介#{2}":[15,17],"#简介#{3}":[18,20],"#工作流程":[21,48],"#工作流程#{1}":[24,38],"#工作流程#{2}":[39,40],"#工作流程#{3}":[41,42],"#工作流程#{4}":[43,44],"#工作流程#{5}":[45,47],"#工作流程#{6}":[48,48],"#捕获PPPoe数据包":[49,57],"#捕获PPPoe数据包#{1}":[51,53],"#捕获PPPoe数据包#{2}":[54,57]},"last_import":{"mtime":1731036190412,"size":1617,"at":1749024987540,"hash":"gg0qn5"},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/PPPOE协议.md","last_embed":{"hash":"gg0qn5","at":1751079987925}},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/PPPOE协议.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/PPPOE协议.md#---frontmatter---","lines":[1,8],"size":112,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/PPPOE协议.md#简介": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/PPPOE协议.md#简介","lines":[9,20],"size":221,"outlinks":[{"title":"以太网","target":"以太网","line":3},{"title":"DSL","target":"DSL","line":4},{"title":"数字用户线路","target":"DSL","line":4}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/PPPOE协议.md#简介#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/PPPOE协议.md#简介#{1}","lines":[10,14],"size":136,"outlinks":[{"title":"以太网","target":"以太网","line":2},{"title":"DSL","target":"DSL","line":3},{"title":"数字用户线路","target":"DSL","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/PPPOE协议.md#简介#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/PPPOE协议.md#简介#{2}","lines":[15,17],"size":73,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/PPPOE协议.md#简介#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/PPPOE协议.md#简介#{3}","lines":[18,20],"size":5,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/PPPOE协议.md#工作流程": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/PPPOE协议.md#工作流程","lines":[21,48],"size":465,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/PPPOE协议.md#工作流程#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/PPPOE协议.md#工作流程#{1}","lines":[24,38],"size":273,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/PPPOE协议.md#工作流程#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/PPPOE协议.md#工作流程#{2}","lines":[39,40],"size":42,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/PPPOE协议.md#工作流程#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/PPPOE协议.md#工作流程#{3}","lines":[41,42],"size":44,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/PPPOE协议.md#工作流程#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/PPPOE协议.md#工作流程#{4}","lines":[43,44],"size":43,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/PPPOE协议.md#工作流程#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/PPPOE协议.md#工作流程#{5}","lines":[45,47],"size":46,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/PPPOE协议.md#工作流程#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/PPPOE协议.md#工作流程#{6}","lines":[48,48],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/PPPOE协议.md#捕获PPPoe数据包": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/PPPOE协议.md#捕获PPPoe数据包","lines":[49,57],"size":110,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/PPPOE协议.md#捕获PPPoe数据包#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/PPPOE协议.md#捕获PPPoe数据包#{1}","lines":[51,53],"size":74,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/PPPOE协议.md#捕获PPPoe数据包#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/PPPOE协议.md#捕获PPPoe数据包#{2}","lines":[54,57],"size":21,"outlinks":[],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/PPPOE协议.md": {"path":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/PPPOE协议.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.13707267,-0.00029072,-0.04170333,-0.06356119,-0.03220762,-0.00086494,0.0302136,0.00813983,0.06779308,-0.01654938,-0.00350084,-0.07480792,0.06655511,0.06508446,0.03692516,0.03286572,0.01205975,0.00807996,0.00958103,0.0277578,0.11492284,-0.07005051,-0.04469262,-0.06134192,-0.00930976,0.05880255,0.02978114,0.00174784,0.01005867,-0.15773994,-0.01635049,0.03871529,0.04014168,0.00582997,-0.05494819,0.03487016,0.01319834,0.02827813,-0.01433086,0.05198096,0.02627775,-0.028871,0.03317596,-0.03865356,0.0279496,-0.07484055,-0.03731714,-0.02315653,-0.01461321,-0.05309372,-0.02053936,-0.00449429,-0.03679463,-0.01951984,-0.04013783,0.02180619,0.00245,0.0346011,0.03898932,-0.03692091,0.03099126,0.0606671,-0.23243424,0.05598085,0.02113158,0.00011836,-0.02961756,0.03097867,-0.01881806,0.00637789,-0.07681148,0.05500289,0.00422886,0.02273002,0.03888801,0.02295453,0.02899402,-0.01599048,-0.029147,-0.03045193,-0.01770184,0.04090067,-0.02277274,-0.04294081,-0.01369811,-0.00611155,-0.00142072,-0.02780579,0.01000654,-0.0167186,-0.01654001,-0.06047486,0.00281239,0.02170949,0.01414014,0.05088164,0.03521007,0.02547647,-0.06326471,0.10070524,-0.10507393,0.01410105,-0.01797221,-0.05547176,0.05379735,-0.0923558,0.02386656,-0.08113848,0.00810253,0.00563442,-0.09337617,-0.02566244,-0.00584705,-0.03316559,0.04063393,0.04709041,-0.0025241,0.05102129,-0.01991918,-0.02421992,-0.00416267,-0.00721671,0.0463715,0.00749087,-0.01735313,-0.03942024,0.07065214,0.06283351,0.02755525,0.0384401,0.10377536,-0.02739933,-0.09903873,-0.01103305,0.0107638,-0.02234505,0.00581456,-0.02326618,0.00982262,-0.03875069,-0.01656689,-0.07103857,0.02962885,-0.03951184,-0.06009666,0.10481181,-0.05864706,0.06731232,0.04370642,-0.02032826,-0.0115727,0.05542697,-0.0327093,0.00215251,-0.03778957,0.01357344,0.06657614,0.11935391,-0.00681782,0.01003371,0.00676289,-0.0093988,-0.05653317,0.15304266,0.02345425,-0.10424188,-0.01669036,0.01336423,0.01864279,-0.05169451,-0.02365631,0.01054254,0.06931827,0.01077937,0.07349485,-0.0179265,-0.01899461,-0.00732555,0.01655906,0.00592193,0.00942177,-0.05266253,-0.02638346,-0.01423894,0.0314602,-0.10456012,-0.06066906,-0.06442887,0.04343494,-0.05655279,-0.06870472,0.02617442,-0.02305112,0.01560898,-0.01653627,-0.08623505,0.03792882,0.03124143,0.03009432,-0.03615523,0.05303779,0.04042797,-0.04741313,-0.01418014,-0.02412382,-0.0034438,-0.0062401,0.0285529,0.03854416,0.036784,-0.02106792,0.02548357,-0.01227616,0.0602915,-0.07880805,0.02990281,-0.00093928,0.00360644,0.02635781,0.05845057,-0.02966688,0.01432654,-0.04913976,-0.20177177,-0.02709465,0.02548614,-0.0409083,-0.00361228,0.01381823,-0.05498926,0.01358356,0.04502663,0.06719219,0.08753007,0.00176765,-0.05896179,0.03390157,0.01023042,0.01032271,0.02499312,-0.02029034,-0.02679666,-0.02113775,0.00322092,0.03023511,-0.07721956,0.00186624,0.04158287,-0.01926251,0.1223699,0.05974517,0.01420299,0.04980642,0.04052608,0.00473279,-0.02223173,-0.11918576,0.01268378,0.044693,-0.03646852,-0.03663933,0.00197951,-0.00515644,-0.00552995,0.05713147,-0.03879863,-0.09282864,-0.04736524,-0.03190159,-0.01733267,-0.01676054,0.00227301,0.04585465,0.015288,0.02651491,0.02378544,-0.01057646,0.0190984,-0.07425058,-0.02973376,-0.07003878,-0.03822255,0.00013315,0.00636813,0.01765717,0.02403293,0.00228782,-0.01451963,-0.0182928,-0.00797386,0.010704,-0.00167776,0.03468307,-0.01710425,0.1646824,0.01516685,-0.0434458,0.08293262,0.00587281,-0.02488798,-0.01590605,0.01827245,-0.01681099,0.04868615,0.04018358,0.00625629,0.02277329,0.00767198,0.03070784,0.07688201,-0.03260826,0.04017821,-0.04796078,-0.04234184,0.01967354,-0.03819647,0.05121124,0.08624794,0.03890023,-0.31746477,0.0188215,-0.02437329,0.04272384,0.01474176,0.07408264,0.05721697,-0.01112485,-0.04188399,-0.00531287,-0.04452182,0.08579897,0.00495072,-0.04056028,-0.00828341,-0.01440141,0.03177085,-0.01115105,0.04787866,-0.05864493,-0.02763568,0.05064437,0.19184184,-0.00332166,0.02200855,-0.00453173,0.03017807,0.05963188,0.01296301,0.0546186,0.03019323,-0.0504892,0.00335167,-0.04956547,0.03558285,0.0743826,-0.00537991,0.00557289,-0.00177441,-0.01022354,-0.02105775,0.01833521,-0.0694012,0.03301332,0.07843811,0.07022838,-0.03708349,-0.03959877,-0.00765153,0.0971392,-0.02162683,-0.0060159,0.00875323,-0.00921183,0.01596591,0.02590167,0.03000771,0.00485764,-0.02485314,-0.01977666,0.04172817,0.00609757,0.06994162,0.11396351,0.03069556],"last_embed":{"hash":"ebc833ac6dc982ac7bebd6da848054da67892e0e8e2a7d4ecf13630b4b313eba","tokens":450}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[0.02068406,-0.00788088,-0.04046812,-0.02090889,-0.03142709,0.01180486,0.02333991,-0.03404389,-0.00144603,0.00492077,0.01209682,0.00651113,0.01303383,-0.07686663,-0.01612939,0.07806538,0.03859064,0.03402886,0.05493464,-0.00843989,-0.04325972,-0.02125999,0.0096498,0.0291237,-0.00360399,0.00086145,-0.06854352,-0.00084459,0.03917871,0.04122603,0.0632645,-0.07307663,0.00672175,-0.01887851,0.01409287,0.03170639,0.02348429,-0.001589,0.02173283,-0.06379132,-0.05415978,-0.00195627,-0.03907155,0.02394783,-0.11713862,-0.01333053,-0.00844425,-0.00312746,-0.00738967,-0.01056811,-0.03529685,0.03780729,0.01497182,-0.03425686,-0.04157653,0.00766445,-0.05942471,-0.03777682,-0.01744813,-0.06986862,-0.02387006,-0.03001845,0.01569154,-0.00234754,0.07419995,-0.00357923,0.01112971,-0.02579816,-0.01422601,0.02578393,-0.06554512,-0.04715949,0.10216223,0.01490884,0.01725486,-0.00571784,-0.0044209,0.03697052,-0.03263849,0.07196166,-0.02241984,0.03994528,0.04128518,0.02090256,-0.01555947,-0.01105413,0.07252728,-0.0099169,-0.02334749,0.02164348,0.01009117,-0.01042694,0.00225668,-0.07412644,0.03456988,-0.03581218,-0.02299216,0.00893256,0.0415668,-0.01127839,-0.055746,-0.00715092,-0.02141292,-0.01425743,-0.02941358,-0.00924858,0.03801823,0.03733986,-0.02208099,0.04005377,-0.00169891,-0.05770235,-0.03641513,-0.00699641,-0.00077364,-0.00437972,0.03369714,-0.00617196,0.00299773,0.03215742,0.09263818,0.02477616,-0.00041338,0.01553679,-0.0003328,0.02624055,0.02045296,-0.00412252,0.06924388,-0.03348677,0.00184706,0.03214383,-0.00169896,0.00325904,0.02024321,-0.00977712,0.01664216,-0.05768389,-0.05800889,-0.04396847,0.02672384,-0.01211213,-0.0391819,-0.00226465,0.00505046,0.01436992,-0.08127829,0.07243311,-0.02251933,0.02571299,-0.00060258,0.00569191,-0.00930157,-0.03189626,0.00893614,-0.00693641,-0.01345447,-0.02130334,-0.03202823,-0.01349205,-0.01338841,-0.02307494,-0.01225273,0.04593403,-0.04432157,0.03570852,-0.00481099,0.04109802,0.00732516,0.01768264,0.05547486,0.03234981,-0.00823278,0.00153373,-0.01060091,-0.01477854,-0.00355756,-0.00615974,-0.05623248,0.01266748,0.01742036,-0.05914434,0.00336592,0.00897471,0.01742051,0.02437664,-0.03983324,0.05412517,0.06165873,0.01829697,0.0398267,-0.02016717,0.01047663,0.0151974,0.03112871,0.09948511,0.01670515,-0.02692228,0.03785072,-0.00283872,0.01430027,-0.02232228,0.02781538,-0.12509096,-0.02156809,0.01921467,-0.00761847,-0.03816536,-0.03224886,-0.01515128,-0.06431077,0.03503512,0.00892778,-0.00540924,0.00109799,0.02895548,0.01992093,0.04685534,0.0484199,-0.0380871,0.00145922,-0.03988918,0.01692029,0.03428446,0.02009527,-0.06112021,0.01366489,0.00853605,-0.0234732,0.00296696,-0.03661773,0.04341013,0.05851459,-0.03219232,-0.0684416,0.00007875,0.03627518,-0.03625724,-0.01799223,-0.04152882,-0.05360655,0.00662648,0.06752922,-0.04599589,-0.0066437,-0.04696782,-0.06434158,0.04470533,0.02905723,0.08342378,0.0233763,0.07760315,-0.0648322,0.04598842,-0.02643914,-0.02118023,-0.03081194,-0.03491724,0.07433168,-0.01768651,0.06161159,0.00689214,0.05115526,0.01303359,0.0013591,0.01104828,0.01330853,0.05098372,-0.00748771,-0.01542755,0.00519412,-0.00411982,0.00265945,-0.0154137,-0.05416917,0.02340594,-0.00478664,0.04280787,0.05340641,-0.0428423,0.01040009,0.04102548,-0.01407803,-0.00492775,0.0542007,0.04317943,-0.03482502,0.02662586,0.05255789,0.00674481,-0.00996164,-0.01140131,-0.01525835,0.00773756,-0.01136547,-0.05290118,-0.04138993,0.02656347,-0.0250834,-0.02527233,0.00569753,0.03290566,-0.03662844,-0.00657034,0.05274099,0.02778034,0.00271079,0.02319913,0.01662902,0.02376934,0.01801057,-0.04891659,-0.00499026,-0.04763921,0.02428471,0.00281771,0.04930685,0.01660607,-0.01859432,-0.04652166,-0.0300805,0.05649987,-0.09742679,0.02192939,-0.01565814,-0.05250077,0.03275802,0.00851091,0.02554765,0.07093373,-0.01991566,-0.02684467,-0.02713658,-0.04932668,-0.01893917,0.0193038,0.0079495,-0.0359005,-0.02159305,-0.02127415,0.02323143,-0.03687459,0.02812747,-0.05580508,-0.0252984,0.09622383,-0.03651788,0.05137153,-0.05432433,-0.07027543,-0.02161698,0.0251989,-0.08854071,0.01297427,0.02170863,0.0111826,-0.0389049,0.00805095,-0.02359755,0.01131709,0.00589563,-0.06574937,-0.04753914,-0.02717053,0.03148665,0.01302025,0.07479591,0.02988197,-0.00916799,0.00032805,0.01781424,0.00295573,0.01327627,-0.01098254,-0.04082922,-0.0086679,-0.03725973,0.03226407,0.08821611,0.01355953,-0.02625272,0.00759751,0.05120413,-0.00234469,0.00322889,0.01286983,0.00595972,0.00510418,0.0014509,-0.09221204,0.00270346,-0.00249845,-0.01939187,0.05641292,0.05174791,-0.02361567,-0.03850755,0.01836518,0.02002423,-0.02515889,-0.00754024,0.0605937,-0.00696844,-0.01370987,-0.00801375,0.0579339,-0.04153653,0.05679033,-0.00746652,-0.00251125,0.0787909,-0.02393147,-0.00981472,0.04703537,-0.01862506,-0.05547191,0.00312795,-0.06018056,-0.02553475,0.02128815,-0.01544831,-0.05355655,0.03197277,-0.02176677,-0.04210028,-0.01060223,-0.08111468,0.02512822,-0.05772688,0.03302601,-0.02298234,-0.03995389,-0.03561026,-0.07036647,0.0046616,0.00386635,0.0390431,-0.00330397,0.02208058,-0.01907492,-0.03406392,-0.01396876,-0.02583637,0.01449321,-0.03610635,0.0091611,0.01200524,-0.0202503,-0.01101564,0.04198547,0.03277075,0.00182879,-0.07098344,-0.05692521,0.05033883,0.01710281,0.02248447,-0.00972962,0.05445299,-0.00765797,0.01331155,0.05501533,0.00600675,-0.01405275,0.01444139,0.03464267,0.10507698,-0.0198539,-0.07143527,0.01466635,-0.03013771,-0.0113715,0.03393315,-0.0130093,-0.0078703,-0.02120388,0.01566476,0.00714037,-0.07346147,0.00748033,-0.02397051,-0.03057154,-0.03435082,-0.02431335,-0.01218075,-0.01113315,-0.00334751,0.02813663,0.04845954,-0.02935301,0.04110952,0.0365145,-0.01125845,-0.02468794,-0.04998102,0.01531719,0.02044416,0.01423306,0.01791779,-0.00141437,0.00310602,0.02777446,-0.0416214,0.00368287,0.03587567,0.0002846,-0.03470414,-0.0312732,0.0319448,-0.05191707,-0.00817822,0.01752158,0.01541705,0.02675585,-0.06418262,-0.01341732,0.05270382,0.04768179,-0.03915796,-0.01451534,0.05289013,0.00518411,-0.03039597,-0.00611775,0.03902346,-0.00765914,0.00020568,-0.03387135,0.05138426,0.01539147,0.02968054,0.0229745,0.03069182,-0.01652237,-0.00703545,0.00604678,0.06083729,-0.0450807,-0.03325399,-0.01681937,0.03552131,0.04265527,-0.05071994,-0.00842597,0.0462396,-0.02496758,0.05810161,-0.04684542,-0.01770288,0.0078409,0.00171956,0.0681835,-0.0222868,0.02267176,0.00421047,-0.0228454,0.03283315,-0.00875503,-0.03357341,0.01166114,0.01659713,0.0270754,0.01185073,0.02911814,0.02153185,0.07660745,-0.08154517,0.02815368,0.06010502,0.02757794,-0.12981783,-0.01314459,0.03666341,0.01494782,-0.03536306,-0.019612,-0.07063837,0.01017429,0.0169213,-0.0159044,0.04854257,-0.00649858,0.01333386,-0.0039361,-0.05054886,0.00100193,0.0303043,0.02171965,0.00507409,-0.04214282,0.0266593,-0.08236939,0.02051397,-0.02480953,-0.02720629,0.04808217,-0.07529142,-0.00383101,0.02690047,-0.03569198,0.00098002,0.01377819,-0.00056797,-0.02761744,0.03921483,-0.04088715,0.02777507,-0.02341268,-0.02643433,0.04180636,-0.0183447,0.049906,-0.05570409,-0.01040236,-0.01143394,-0.01556741,0.01190625,-0.02002073,-0.01264387,0.01029175,-0.05796653,-0.05696826,-0.04513603,-0.00402727,0.01389541,0.01338967,0.06456694,0.06586895,0.02060287,-0.01348114,0.03991095,-0.01035572,0.04151129,0.04683539,0.08411982,0.03710392,-0.05049584,0.00492791,0.01049154,-0.00380164,0.06894903,0.03139994,0.03879746,-0.0116634,0.01259582,-0.01214146,-0.07926425,-0.02567505,-0.03839138,0.02975692,0.01538375,-0.02420577,0.00008602,-0.01092694,-0.00881648,0.0051066,0.01062247,-0.02700835,0.00265098,0.06859782,0.02732655,-0.02242058,-0.0165936,0.05683721,-0.02724952,-0.03257215,0.02224003,0.01566352,-0.00433051,-0.06612248,0.03960723,-0.0502486,-0.01063567,-0.00996365,0.0035234,-0.00006175,0.01009195,0.0301097,0.02223192,-0.00105384,-0.10571466,-0.01081398,0.02263295,-0.01609254,0.03561119,-0.02459408,0.04175228,-0.02780273,-0.06250692,0.01789576,-0.01411568,0.05371271,0.01280361,-0.06091629,-0.03116359,-0.03261263,0.03259209,0.01075521,-0.00546202,-0.02477279,-0.0523506,0.01277721,-0.00784656,-0.00059897,-0.04852373,0.01588503,-0.04051116,0.0259413,-0.01934542,0.01661073,-0.02182036,0.00134944,-0.03357548,-0.10577852,0.03647233,-0.01131572,0.04728043,0.0141529,-0.03364077,0.02426301,0.00335445,-0.00292216,0.01913016,0.00908919,-0.06601252,-0.02987296,0.00099643,0.01554406,-0.00912047,0.00590311,-0.07529508,-0.06192254,-0.0252354,0.04128238,0.04426864,-0.02464975,0.04171306,-0.00330337,0.04331232,-0.00855571,0.07123633,-0.04303262,0.00515838,-0.03551115,0.03049373,0.0366619,-0.02952135,0.02606626,0.02732387,0.03219547,0.01002747,0.0244147,-0.00444725,0.03865101,0.00276838,-0.00232659,-0.02177795,0.00359264,0.00000103,-0.03393513,-0.03077509,-0.00200409,-0.01604319,-0.0274647,-0.06663829,-0.05162806,0.04589744,0.02047709],"last_embed":{"tokens":427,"hash":"gg0qn5"}}},"last_read":{"hash":"gg0qn5","at":1751251725649},"class_name":"SmartSource","outlinks":[{"title":"以太网","target":"以太网","line":11},{"title":"DSL","target":"DSL","line":12},{"title":"数字用户线路","target":"DSL","line":12}],"metadata":{"aliases":["Point-to-Point Protocol"],"英文":"Point-to-Point Protocol","tags":["计算机网络/OSI模型/数据链路层"],"协议层级":"数据链路层"},"blocks":{"#---frontmatter---":[1,8],"#简介":[9,20],"#简介#{1}":[10,14],"#简介#{2}":[15,17],"#简介#{3}":[18,20],"#工作流程":[21,48],"#工作流程#{1}":[24,38],"#工作流程#{2}":[39,40],"#工作流程#{3}":[41,42],"#工作流程#{4}":[43,44],"#工作流程#{5}":[45,47],"#工作流程#{6}":[48,48],"#捕获PPPoe数据包":[49,57],"#捕获PPPoe数据包#{1}":[51,53],"#捕获PPPoe数据包#{2}":[54,57]},"last_import":{"mtime":1731036190412,"size":1617,"at":1749024987540,"hash":"gg0qn5"},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/数据链路层协议/PPPOE协议.md","last_embed":{"hash":"gg0qn5","at":1751251725649}},