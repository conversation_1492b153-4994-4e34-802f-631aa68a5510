"smart_sources:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md": {"path":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08277602,-0.01275569,-0.01705368,-0.03697751,0.0372382,-0.01907072,0.00721633,0.02133267,0.06342746,0.02765083,0.00949298,-0.05698371,0.07774017,0.04444084,0.04277769,0.02758712,-0.02110198,-0.00318952,0.04268868,0.03941898,0.09095055,-0.04276929,0.0316082,-0.06757627,0.01549781,-0.0132919,0.04619891,0.00808262,0.01996295,-0.15067358,-0.00065842,0.05360877,0.03380952,0.02040587,-0.01877999,-0.03489039,0.03730118,-0.00694837,-0.02331696,0.05942218,0.03350349,0.03157579,0.05135152,-0.02960827,-0.02360383,-0.03644821,0.00614421,0.00339486,0.02549476,-0.05391269,-0.02315529,-0.00757145,-0.03012405,-0.00692007,-0.03975873,0.00976578,0.01598789,0.03253971,0.04403614,-0.02653261,0.06927421,0.05404215,-0.22708289,0.06050067,0.04761624,-0.01865499,-0.05146128,0.00162025,-0.00628093,0.01607828,-0.05548847,0.03706388,-0.03589946,0.04971741,0.04216123,0.02759401,-0.03793337,0.01622298,-0.00760222,-0.07193474,-0.03927318,0.0339491,-0.00472765,-0.0086238,-0.01274492,0.02459781,-0.00812044,-0.02830505,0.0223395,-0.0022068,-0.01146681,-0.04298811,0.00079768,0.03062224,0.0045353,0.02745687,0.05492225,0.04412738,-0.10460024,0.11247803,-0.03952381,-0.0396737,-0.0507115,-0.06487149,0.04330277,-0.04965829,-0.01084824,-0.01023943,-0.02001869,0.04195949,-0.07493016,-0.03172328,0.03624971,-0.01181745,0.0334512,0.05572046,0.01801894,0.03939341,-0.02711805,-0.05149738,-0.03172654,0.00989906,0.04257065,0.00534389,0.00134521,-0.06712551,0.038352,0.06882439,0.01381857,0.08872435,0.0878934,0.00200497,-0.05400689,-0.01819233,-0.00085834,-0.00999576,-0.01551892,0.02121374,-0.06272151,-0.01558553,0.01650271,-0.06439275,-0.02048228,-0.0173091,-0.04828748,0.06910111,-0.04740285,0.00906428,0.06946436,-0.06131471,0.016905,0.03846129,-0.01772948,-0.00932712,-0.02554319,0.01564584,0.10836622,0.14414459,-0.05471034,-0.01128573,-0.03194017,-0.02000241,-0.09224921,0.15348743,0.04294859,-0.06534316,-0.03719494,0.01072668,0.01502697,-0.06239046,-0.02037063,-0.03618179,0.01806528,0.05142887,0.05946565,-0.03582185,-0.00463752,-0.02527931,0.00876493,0.003626,0.01382682,-0.02067083,-0.04725928,0.05889454,-0.01385263,-0.0761207,-0.07029817,-0.03798999,0.01149153,-0.09699578,-0.12289447,0.06035462,-0.07519367,0.04641481,-0.07443253,-0.07911773,0.03351537,0.00850247,0.08292351,-0.02679,0.10262607,0.01898682,-0.03368741,-0.06946079,-0.09846294,-0.04338163,0.02527324,0.00662697,0.01710729,0.06031972,-0.01730736,0.04678126,0.01804643,-0.01697245,-0.01274733,0.01272632,0.0316862,0.03667514,0.0653351,0.06408048,-0.01725233,-0.01481853,-0.09957352,-0.21559648,-0.0233989,0.02835676,-0.06258723,0.03139098,-0.00364023,0.00533989,0.03361709,0.10707135,0.07474007,0.07610279,0.01569756,-0.05158796,0.01548791,0.06735344,0.01216584,0.04709827,0.0001971,-0.04772811,-0.03104237,0.02829918,0.03042756,-0.04427463,0.01880914,0.04620361,-0.02514643,0.0845601,-0.00358127,0.03868239,0.02419777,0.01845766,0.00622953,0.01855971,-0.12499847,-0.01870325,0.03370011,-0.00380297,-0.00152721,-0.01836072,0.00466431,-0.01623851,0.0221602,-0.03333202,-0.0460286,0.01652055,-0.02210071,-0.05344246,0.00287222,0.00798908,0.02598431,-0.00423846,0.01217751,-0.00687201,0.00982694,0.01638966,-0.03860206,-0.04565946,-0.06481742,0.0016996,0.03426761,0.01370348,-0.02708983,0.03052798,-0.01203168,0.00819371,-0.00319399,-0.01142764,-0.00967888,-0.05921265,0.01054619,-0.05592097,0.1258717,-0.0128307,-0.02555485,0.03510666,0.012769,-0.00440688,-0.03102156,0.01513532,0.01336804,0.03504146,-0.03073297,0.02947698,-0.00951798,0.01888447,0.03923256,0.08091322,-0.00084824,0.07189765,-0.0456474,-0.03488687,-0.01988419,-0.05029854,0.00257577,0.07756267,-0.00959324,-0.30766404,0.01260942,-0.00323887,0.02224667,0.03492544,0.03346691,0.04594046,0.02049066,-0.05650286,0.04653764,-0.0462664,0.01725224,0.01138148,-0.03013306,-0.02135245,0.01379571,0.045222,-0.02172084,0.02963386,-0.03809976,-0.00000483,0.06723972,0.19706859,-0.00251683,0.05305356,0.0462023,-0.0364526,0.0933762,0.00766191,0.0200895,0.02167593,-0.0848149,0.01635132,-0.01328463,0.05200113,0.03392346,-0.038167,-0.01365852,0.0131975,-0.01730505,-0.07806052,0.04744502,-0.10191309,0.03863527,0.06231426,0.02457201,-0.05547686,-0.01043769,0.02388161,0.04675709,0.00857484,0.01017461,0.03032026,-0.02154237,0.02217444,0.01393459,-0.02511885,-0.04765376,-0.10324728,0.00356529,-0.00707808,-0.00723356,0.07524765,0.0982416,0.02495211],"last_embed":{"hash":"a4163930e6986808c9dccd374934ff874d38c7d443614494e09b74a1315b5c4c","tokens":441}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.04528758,-0.03691911,0.00799135,0.0079423,-0.05151377,0.00225661,-0.00348104,-0.03547679,0.01810321,-0.00898369,0.05230373,0.00655269,-0.03713113,-0.03669661,0.02690708,0.01910747,0.02083964,-0.01394181,0.10044434,-0.01370968,-0.00545201,0.02201026,-0.04889261,0.0338433,0.01269663,-0.02565757,-0.06268982,-0.04810217,0.02065704,0.0368234,0.16558452,-0.0306586,0.10354263,0.01294831,0.027684,0.019864,0.03523457,0.03895417,0.04244682,-0.03239996,-0.01187968,0.03335008,-0.05770251,0.01729894,-0.10435081,-0.00320033,0.0105578,-0.04579997,-0.00821489,0.00487733,-0.02607355,-0.05882438,0.02727256,0.02291624,-0.00392061,-0.00831093,-0.08226136,0.00519645,-0.03867118,-0.01110988,0.00218027,-0.01933698,0.00232315,-0.01064826,0.05149348,-0.00863214,0.01349843,-0.07758106,-0.0237759,0.01207924,-0.02575456,-0.01067188,0.0563907,0.00644104,0.02330863,0.05288419,0.01279481,-0.03753993,-0.04442445,0.01682024,-0.02547508,0.02544223,-0.00509672,0.00985574,-0.01599413,-0.03382846,0.1003276,-0.04653851,-0.0377296,-0.03253676,0.0110195,0.00542225,0.03842612,-0.04745319,0.00646702,-0.00342468,0.00751416,-0.00372348,0.02459943,-0.0096734,-0.08854048,-0.00235176,-0.00167661,-0.02227771,0.00469931,-0.01636088,0.03620645,0.02824344,-0.03685127,0.05652164,0.0232999,-0.01490077,-0.04824334,-0.02539533,-0.00878999,-0.04161787,0.01804144,0.01924038,0.01192164,0.05505095,-0.00392153,0.01397777,-0.00697868,0.00655891,-0.03309063,-0.03475155,0.01571367,-0.01130351,0.0083032,-0.10258129,-0.0234613,0.02247041,-0.02949091,-0.03136377,0.01687197,0.00017066,0.03979231,-0.05688662,-0.03714162,-0.04700823,0.01004563,0.0177817,-0.00109421,-0.00592211,-0.02635428,0.01656996,-0.00569488,0.02127277,-0.0175404,0.00936318,0.00622371,0.0142218,-0.06779549,-0.0194004,-0.02460834,0.02185153,0.04617437,-0.03356227,-0.00243737,-0.06586292,-0.02988695,0.01358848,-0.01923191,-0.00570337,0.00426959,-0.05080473,0.02725585,0.05383645,0.02386262,-0.0100983,-0.0141072,-0.00277508,0.01716155,-0.0288175,-0.02959901,0.01445447,-0.00594452,-0.01641326,-0.06185051,0.04472363,0.02853943,-0.04092006,0.04210315,0.06162541,0.00918222,0.04386206,-0.03719448,-0.03871128,0.03371699,0.01441221,0.0495724,-0.03717667,0.01714319,0.03555314,0.02909931,0.04286359,-0.02575221,-0.0894312,0.09218052,-0.02383398,0.00659277,0.0594244,-0.05598415,-0.10114025,0.06634062,0.02335544,-0.07129092,-0.02916696,-0.00741336,-0.05345789,-0.00298697,0.00434292,0.0303077,0.00512698,-0.00847941,0.0628882,0.02486093,0.03158133,0.00765318,-0.05467483,-0.01504161,0.03853351,0.02059576,-0.02330842,-0.00122142,-0.01878842,0.01856169,-0.00191866,0.04525852,-0.00299109,-0.01652678,0.06626142,0.07892489,-0.02239139,-0.04957508,0.00934771,0.02659678,-0.02857229,0.04600202,0.00594959,-0.02399464,-0.02276524,0.04102086,-0.01918297,-0.01788677,-0.0097481,-0.04216184,0.0069568,0.03374029,0.01631891,0.02795029,0.05118408,-0.05049092,0.04302172,0.01158978,-0.03681089,0.01013173,-0.00254025,0.04500742,-0.01665927,0.02729285,-0.01636149,0.03816629,0.03288189,-0.04948023,-0.02934834,-0.00378273,0.03045771,0.01756205,0.05229811,0.07079291,-0.02307277,0.05368466,0.01818676,-0.02387923,0.02352683,-0.0065558,0.03620718,0.02795552,-0.03506703,0.0378264,-0.00433912,0.03549477,-0.02807515,0.05997014,-0.00624845,0.01213886,-0.05645041,0.03317216,0.01294096,0.03134774,0.01859724,0.0161237,-0.01756668,-0.0407572,-0.06294882,-0.07413958,0.0232061,-0.01969707,-0.02213865,0.0480663,0.04895869,-0.00748462,-0.03875171,0.02884034,0.0230643,0.00206881,-0.01091496,0.01874235,-0.00351643,-0.0005328,-0.04659914,-0.01423134,-0.03214553,0.00398367,-0.00357678,0.07123488,0.01312049,0.00091099,-0.01335468,-0.00031969,0.01206697,-0.12125497,-0.02454402,-0.03625546,-0.04860285,-0.02174401,0.04365217,-0.00881369,0.04546952,-0.03157897,-0.03770502,-0.03754754,-0.04387698,0.0440365,0.04986197,-0.02932327,0.0047183,0.04606872,0.0035561,0.00911948,-0.06941565,0.00563211,-0.02239428,0.01032302,0.04243082,-0.02002977,0.01369057,0.03553203,-0.02377434,0.0082286,0.03447406,-0.05162009,0.00964279,0.00391746,0.04911668,-0.05595119,0.03765941,-0.0646535,0.05890684,-0.03494482,-0.01052611,-0.03482535,-0.0138136,-0.00901391,0.00596448,0.04012635,0.05245233,-0.010297,-0.00897975,-0.055678,0.01624426,0.05322744,0.00760981,-0.03044279,-0.00521718,-0.01038465,-0.00953307,0.07332326,-0.017133,-0.03218532,-0.04059527,0.00721764,-0.05494315,-0.01185051,-0.04026867,-0.0024981,0.0141572,0.04684331,-0.06994894,0.01642045,0.00356192,-0.00429781,0.02847299,0.05540738,0.00804414,-0.03063122,-0.03029938,0.00982913,-0.04966333,0.01273417,0.05387942,-0.06018916,0.00408746,0.00778497,0.00521777,-0.03048337,-0.02966426,-0.03987404,-0.00227947,0.05530723,0.01575537,0.03213391,-0.00936401,0.00208644,-0.04071023,0.01860633,-0.05716439,0.00992288,0.00253121,-0.00980143,-0.02938043,-0.01766999,-0.00239774,-0.04048242,0.07435735,-0.04282417,-0.01497375,-0.04549181,0.03854091,-0.03946573,0.00585551,-0.03193233,-0.08776034,-0.03167883,0.00261827,0.05524595,0.0553502,-0.00774604,0.00258439,-0.0282872,-0.00722699,-0.02609255,-0.05366774,-0.05216054,-0.00745287,0.03606853,-0.0598469,0.01437669,0.02495095,0.04024811,0.02637267,-0.02792723,-0.0301527,0.03431259,0.0262325,-0.01325134,0.01598126,-0.00105232,0.00873109,-0.01972302,0.05850582,0.01058593,-0.04914628,0.00679146,0.01029585,0.06623842,0.00750281,-0.07349361,0.01467766,0.00218756,-0.04218158,0.01146485,-0.02679374,0.03959381,0.02252877,0.05022732,-0.01419309,-0.05343347,0.02003305,-0.0212078,-0.07006805,-0.09862041,0.0233579,-0.05134485,0.05791261,-0.00087514,-0.00832738,0.04151041,-0.08709367,0.01162989,0.03444139,0.02379206,0.01616273,-0.04055114,0.01797874,0.00318023,0.03024941,0.00161768,0.00541963,0.00966985,0.01518637,-0.03820905,-0.00776997,0.02460007,0.02098016,-0.04776055,-0.01939133,0.03023447,-0.00281057,0.00133704,0.01710007,0.00005593,-0.00261748,-0.0498004,-0.05538912,0.02422447,0.06359958,0.0008681,-0.03635067,-0.01628097,0.0431592,-0.03827482,0.01837522,-0.01946834,0.01801433,-0.02136384,0.01698137,0.03132393,0.07316767,0.00353069,-0.0381709,0.05268063,-0.01035318,0.03921878,-0.02331367,0.06774817,-0.00838346,-0.06511699,0.02597655,0.01862397,0.03785554,-0.04423927,-0.00163526,0.06572533,0.03165033,0.04224816,-0.0446057,0.01021606,0.04943206,-0.0024298,0.08207322,0.01423152,0.00837899,0.02104085,-0.0149766,0.04373685,0.00252779,-0.02247317,0.03224279,-0.01541606,0.00038412,-0.02473837,0.02071504,-0.00012072,0.03404638,-0.08434288,0.01139982,0.0001319,0.00925804,-0.06574795,-0.03052228,0.0279365,0.00020598,-0.03282018,0.00500739,-0.0343865,0.01791778,0.04628722,-0.0378424,0.06504804,-0.01300007,-0.0001203,-0.04104402,-0.0320675,0.03467792,-0.00896428,-0.04658321,0.01266325,-0.00324936,0.05520148,-0.00054997,0.00228199,-0.02869093,-0.0255511,0.0657106,-0.01730255,0.05026844,0.0151941,-0.0264575,-0.00693388,-0.01788984,-0.02448813,0.0334482,0.00457999,-0.03095919,-0.00911098,-0.01350792,0.02836482,0.02568693,0.05192581,0.10024686,-0.007152,-0.00333502,0.02359359,-0.00376478,0.03669211,-0.04933458,-0.03261818,0.03485448,-0.05550155,-0.08231065,-0.02065056,-0.04911121,-0.01207124,0.03846758,0.02681546,0.01602294,0.04931911,-0.04160839,0.01447753,-0.02830474,0.01902145,0.02429107,0.03153483,0.01018932,-0.04978905,-0.03558332,-0.01681577,-0.03071562,0.07330557,-0.03933209,0.00313365,-0.01715822,-0.00325427,-0.02077882,-0.04124346,0.01080587,-0.01926438,-0.03606718,0.0317779,-0.02805568,-0.01622234,-0.03396513,-0.05047617,-0.02169102,-0.00273712,-0.04103681,0.00224026,0.00589361,-0.0575189,0.02192972,-0.03656387,-0.01311199,-0.02864497,-0.04558675,0.04770086,0.00033692,-0.08216996,-0.03350565,0.05680388,0.00154329,-0.0318363,0.01356111,-0.01875463,0.00889197,-0.00256963,0.05631375,-0.04397929,0.02184827,-0.04470494,-0.02721269,0.027232,0.0280752,-0.02944714,-0.03377277,0.03006116,0.00286692,-0.06735291,-0.01907695,0.00384097,0.04330481,-0.02415112,-0.0155303,-0.00639875,0.01591197,0.02721946,0.01614287,0.03042552,-0.00667511,-0.03020519,0.0525552,-0.01877763,0.05312742,-0.0574939,0.01271929,0.0146368,-0.01159666,-0.02180836,0.024376,0.02441368,-0.01807952,-0.00186923,-0.03967737,-0.01130268,0.03258774,0.04655973,0.01450997,-0.06574814,0.0313421,0.05365689,0.01126429,0.0296998,0.05741539,-0.10706085,-0.04511776,-0.00956847,-0.0119306,0.00980004,0.05699573,-0.02424486,-0.04215459,0.00576643,-0.02541559,0.04641245,-0.0598969,0.06160795,-0.03090824,0.03744741,0.03558581,0.01031842,-0.01962377,0.00611124,0.0326724,-0.02754988,-0.01338418,0.02644166,0.05096094,-0.00073539,0.03991658,-0.01218388,-0.00360802,0.00889763,0.04212105,0.03734382,0.0612052,0.00130813,-0.03478597,9e-7,0.04237253,0.02893769,0.01708989,-0.04523512,-0.01560865,0.00297321,0.0001184,0.0696889,0.02259886],"last_embed":{"tokens":814,"hash":"11anfyd"}}},"last_read":{"hash":"11anfyd","at":1751079988091},"class_name":"SmartSource","outlinks":[{"title":"IPv4协议","target":"IPv4协议","line":12},{"title":"ISP","target":"ISP","line":51},{"title":"VPN","target":"VPN","line":63}],"metadata":{"aliases":["网络地址转换","Network Address Translation"],"英文":"Network Address Translation","cssclasses":["editor-full"]},"blocks":{"#---frontmatter---":[1,8],"#简介":[9,19],"#简介#{1}":[10,10],"#简介#{2}":[11,13],"#简介#{3}":[14,19],"#静态NAT（Static NAT）":[20,32],"#静态NAT（Static NAT）#{1}":[21,21],"#静态NAT（Static NAT）#{2}":[22,22],"#静态NAT（Static NAT）#{3}":[23,23],"#静态NAT（Static NAT）#{4}":[24,26],"#静态NAT（Static NAT）#{5}":[27,28],"#静态NAT（Static NAT）#{6}":[29,30],"#静态NAT（Static NAT）#{7}":[31,32],"#动态NAT（Dynamic NAT）":[33,45],"#动态NAT（Dynamic NAT）#{1}":[34,34],"#动态NAT（Dynamic NAT）#{2}":[35,35],"#动态NAT（Dynamic NAT）#{3}":[36,36],"#动态NAT（Dynamic NAT）#{4}":[37,39],"#动态NAT（Dynamic NAT）#{5}":[40,41],"#动态NAT（Dynamic NAT）#{6}":[42,43],"#动态NAT（Dynamic NAT）#{7}":[44,45],"#PAT（Port Address Translation，端口地址转换）":[46,60],"#PAT（Port Address Translation，端口地址转换）#{1}":[47,47],"#PAT（Port Address Translation，端口地址转换）#{2}":[48,48],"#PAT（Port Address Translation，端口地址转换）#{3}":[49,50],"#PAT（Port Address Translation，端口地址转换）#{4}":[51,53],"#PAT（Port Address Translation，端口地址转换）#{5}":[54,56],"#PAT（Port Address Translation，端口地址转换）#{6}":[57,58],"#PAT（Port Address Translation，端口地址转换）#{7}":[59,60],"#双向NAT（Bidirectional NAT）":[61,78],"#双向NAT（Bidirectional NAT）#{1}":[62,62],"#双向NAT（Bidirectional NAT）#{2}":[63,63],"#双向NAT（Bidirectional NAT）#{3}":[64,65],"#双向NAT（Bidirectional NAT）#{4}":[66,66],"#双向NAT（Bidirectional NAT）#{5}":[67,67],"#双向NAT（Bidirectional NAT）#{6}":[68,70],"#双向NAT（Bidirectional NAT）#{7}":[71,73],"#双向NAT（Bidirectional NAT）#{8}":[74,74],"#双向NAT（Bidirectional NAT）#{9}":[75,76],"#双向NAT（Bidirectional NAT）#{10}":[77,78]},"last_import":{"mtime":1740737712341,"size":3222,"at":1749024987540,"hash":"11anfyd"},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md","last_embed":{"hash":"11anfyd","at":1751079988091}},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#---frontmatter---","lines":[1,8],"size":119,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#简介": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#简介","lines":[9,19],"size":246,"outlinks":[{"title":"IPv4协议","target":"IPv4协议","line":4}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#简介#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#简介#{1}","lines":[10,10],"size":27,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#简介#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#简介#{2}","lines":[11,13],"size":89,"outlinks":[{"title":"IPv4协议","target":"IPv4协议","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#简介#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#简介#{3}","lines":[14,19],"size":123,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#静态NAT（Static NAT）": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#静态NAT（Static NAT）","lines":[20,32],"size":227,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#静态NAT（Static NAT）#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#静态NAT（Static NAT）#{1}","lines":[21,21],"size":20,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#静态NAT（Static NAT）#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#静态NAT（Static NAT）#{2}","lines":[22,22],"size":30,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#静态NAT（Static NAT）#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#静态NAT（Static NAT）#{3}","lines":[23,23],"size":30,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#静态NAT（Static NAT）#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#静态NAT（Static NAT）#{4}","lines":[24,26],"size":43,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#静态NAT（Static NAT）#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#静态NAT（Static NAT）#{5}","lines":[27,28],"size":31,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#静态NAT（Static NAT）#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#静态NAT（Static NAT）#{6}","lines":[29,30],"size":43,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#静态NAT（Static NAT）#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#静态NAT（Static NAT）#{7}","lines":[31,32],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#动态NAT（Dynamic NAT）": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#动态NAT（Dynamic NAT）","lines":[33,45],"size":230,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#动态NAT（Dynamic NAT）#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#动态NAT（Dynamic NAT）#{1}","lines":[34,34],"size":20,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#动态NAT（Dynamic NAT）#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#动态NAT（Dynamic NAT）#{2}","lines":[35,35],"size":31,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#动态NAT（Dynamic NAT）#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#动态NAT（Dynamic NAT）#{3}","lines":[36,36],"size":22,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#动态NAT（Dynamic NAT）#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#动态NAT（Dynamic NAT）#{4}","lines":[37,39],"size":69,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#动态NAT（Dynamic NAT）#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#动态NAT（Dynamic NAT）#{5}","lines":[40,41],"size":31,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#动态NAT（Dynamic NAT）#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#动态NAT（Dynamic NAT）#{6}","lines":[42,43],"size":26,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#动态NAT（Dynamic NAT）#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#动态NAT（Dynamic NAT）#{7}","lines":[44,45],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#PAT（Port Address Translation，端口地址转换）": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#PAT（Port Address Translation，端口地址转换）","lines":[46,60],"size":376,"outlinks":[{"title":"ISP","target":"ISP","line":6}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#PAT（Port Address Translation，端口地址转换）#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#PAT（Port Address Translation，端口地址转换）#{1}","lines":[47,47],"size":20,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#PAT（Port Address Translation，端口地址转换）#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#PAT（Port Address Translation，端口地址转换）#{2}","lines":[48,48],"size":56,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#PAT（Port Address Translation，端口地址转换）#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#PAT（Port Address Translation，端口地址转换）#{3}","lines":[49,50],"size":52,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#PAT（Port Address Translation，端口地址转换）#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#PAT（Port Address Translation，端口地址转换）#{4}","lines":[51,53],"size":94,"outlinks":[{"title":"ISP","target":"ISP","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#PAT（Port Address Translation，端口地址转换）#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#PAT（Port Address Translation，端口地址转换）#{5}","lines":[54,56],"size":65,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#PAT（Port Address Translation，端口地址转换）#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#PAT（Port Address Translation，端口地址转换）#{6}","lines":[57,58],"size":40,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#PAT（Port Address Translation，端口地址转换）#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#PAT（Port Address Translation，端口地址转换）#{7}","lines":[59,60],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#双向NAT（Bidirectional NAT）": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#双向NAT（Bidirectional NAT）","lines":[61,78],"size":461,"outlinks":[{"title":"VPN","target":"VPN","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#双向NAT（Bidirectional NAT）#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#双向NAT（Bidirectional NAT）#{1}","lines":[62,62],"size":18,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#双向NAT（Bidirectional NAT）#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#双向NAT（Bidirectional NAT）#{2}","lines":[63,63],"size":44,"outlinks":[{"title":"VPN","target":"VPN","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#双向NAT（Bidirectional NAT）#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#双向NAT（Bidirectional NAT）#{3}","lines":[64,65],"size":131,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#双向NAT（Bidirectional NAT）#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#双向NAT（Bidirectional NAT）#{4}","lines":[66,66],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#双向NAT（Bidirectional NAT）#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#双向NAT（Bidirectional NAT）#{5}","lines":[67,67],"size":6,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#双向NAT（Bidirectional NAT）#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#双向NAT（Bidirectional NAT）#{6}","lines":[68,70],"size":84,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#双向NAT（Bidirectional NAT）#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#双向NAT（Bidirectional NAT）#{7}","lines":[71,73],"size":84,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#双向NAT（Bidirectional NAT）#{8}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#双向NAT（Bidirectional NAT）#{8}","lines":[74,74],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#双向NAT（Bidirectional NAT）#{9}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#双向NAT（Bidirectional NAT）#{9}","lines":[75,76],"size":28,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#双向NAT（Bidirectional NAT）#{10}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md#双向NAT（Bidirectional NAT）#{10}","lines":[77,78],"size":24,"outlinks":[],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md": {"path":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08277602,-0.01275569,-0.01705368,-0.03697751,0.0372382,-0.01907072,0.00721633,0.02133267,0.06342746,0.02765083,0.00949298,-0.05698371,0.07774017,0.04444084,0.04277769,0.02758712,-0.02110198,-0.00318952,0.04268868,0.03941898,0.09095055,-0.04276929,0.0316082,-0.06757627,0.01549781,-0.0132919,0.04619891,0.00808262,0.01996295,-0.15067358,-0.00065842,0.05360877,0.03380952,0.02040587,-0.01877999,-0.03489039,0.03730118,-0.00694837,-0.02331696,0.05942218,0.03350349,0.03157579,0.05135152,-0.02960827,-0.02360383,-0.03644821,0.00614421,0.00339486,0.02549476,-0.05391269,-0.02315529,-0.00757145,-0.03012405,-0.00692007,-0.03975873,0.00976578,0.01598789,0.03253971,0.04403614,-0.02653261,0.06927421,0.05404215,-0.22708289,0.06050067,0.04761624,-0.01865499,-0.05146128,0.00162025,-0.00628093,0.01607828,-0.05548847,0.03706388,-0.03589946,0.04971741,0.04216123,0.02759401,-0.03793337,0.01622298,-0.00760222,-0.07193474,-0.03927318,0.0339491,-0.00472765,-0.0086238,-0.01274492,0.02459781,-0.00812044,-0.02830505,0.0223395,-0.0022068,-0.01146681,-0.04298811,0.00079768,0.03062224,0.0045353,0.02745687,0.05492225,0.04412738,-0.10460024,0.11247803,-0.03952381,-0.0396737,-0.0507115,-0.06487149,0.04330277,-0.04965829,-0.01084824,-0.01023943,-0.02001869,0.04195949,-0.07493016,-0.03172328,0.03624971,-0.01181745,0.0334512,0.05572046,0.01801894,0.03939341,-0.02711805,-0.05149738,-0.03172654,0.00989906,0.04257065,0.00534389,0.00134521,-0.06712551,0.038352,0.06882439,0.01381857,0.08872435,0.0878934,0.00200497,-0.05400689,-0.01819233,-0.00085834,-0.00999576,-0.01551892,0.02121374,-0.06272151,-0.01558553,0.01650271,-0.06439275,-0.02048228,-0.0173091,-0.04828748,0.06910111,-0.04740285,0.00906428,0.06946436,-0.06131471,0.016905,0.03846129,-0.01772948,-0.00932712,-0.02554319,0.01564584,0.10836622,0.14414459,-0.05471034,-0.01128573,-0.03194017,-0.02000241,-0.09224921,0.15348743,0.04294859,-0.06534316,-0.03719494,0.01072668,0.01502697,-0.06239046,-0.02037063,-0.03618179,0.01806528,0.05142887,0.05946565,-0.03582185,-0.00463752,-0.02527931,0.00876493,0.003626,0.01382682,-0.02067083,-0.04725928,0.05889454,-0.01385263,-0.0761207,-0.07029817,-0.03798999,0.01149153,-0.09699578,-0.12289447,0.06035462,-0.07519367,0.04641481,-0.07443253,-0.07911773,0.03351537,0.00850247,0.08292351,-0.02679,0.10262607,0.01898682,-0.03368741,-0.06946079,-0.09846294,-0.04338163,0.02527324,0.00662697,0.01710729,0.06031972,-0.01730736,0.04678126,0.01804643,-0.01697245,-0.01274733,0.01272632,0.0316862,0.03667514,0.0653351,0.06408048,-0.01725233,-0.01481853,-0.09957352,-0.21559648,-0.0233989,0.02835676,-0.06258723,0.03139098,-0.00364023,0.00533989,0.03361709,0.10707135,0.07474007,0.07610279,0.01569756,-0.05158796,0.01548791,0.06735344,0.01216584,0.04709827,0.0001971,-0.04772811,-0.03104237,0.02829918,0.03042756,-0.04427463,0.01880914,0.04620361,-0.02514643,0.0845601,-0.00358127,0.03868239,0.02419777,0.01845766,0.00622953,0.01855971,-0.12499847,-0.01870325,0.03370011,-0.00380297,-0.00152721,-0.01836072,0.00466431,-0.01623851,0.0221602,-0.03333202,-0.0460286,0.01652055,-0.02210071,-0.05344246,0.00287222,0.00798908,0.02598431,-0.00423846,0.01217751,-0.00687201,0.00982694,0.01638966,-0.03860206,-0.04565946,-0.06481742,0.0016996,0.03426761,0.01370348,-0.02708983,0.03052798,-0.01203168,0.00819371,-0.00319399,-0.01142764,-0.00967888,-0.05921265,0.01054619,-0.05592097,0.1258717,-0.0128307,-0.02555485,0.03510666,0.012769,-0.00440688,-0.03102156,0.01513532,0.01336804,0.03504146,-0.03073297,0.02947698,-0.00951798,0.01888447,0.03923256,0.08091322,-0.00084824,0.07189765,-0.0456474,-0.03488687,-0.01988419,-0.05029854,0.00257577,0.07756267,-0.00959324,-0.30766404,0.01260942,-0.00323887,0.02224667,0.03492544,0.03346691,0.04594046,0.02049066,-0.05650286,0.04653764,-0.0462664,0.01725224,0.01138148,-0.03013306,-0.02135245,0.01379571,0.045222,-0.02172084,0.02963386,-0.03809976,-0.00000483,0.06723972,0.19706859,-0.00251683,0.05305356,0.0462023,-0.0364526,0.0933762,0.00766191,0.0200895,0.02167593,-0.0848149,0.01635132,-0.01328463,0.05200113,0.03392346,-0.038167,-0.01365852,0.0131975,-0.01730505,-0.07806052,0.04744502,-0.10191309,0.03863527,0.06231426,0.02457201,-0.05547686,-0.01043769,0.02388161,0.04675709,0.00857484,0.01017461,0.03032026,-0.02154237,0.02217444,0.01393459,-0.02511885,-0.04765376,-0.10324728,0.00356529,-0.00707808,-0.00723356,0.07524765,0.0982416,0.02495211],"last_embed":{"hash":"a4163930e6986808c9dccd374934ff874d38c7d443614494e09b74a1315b5c4c","tokens":441}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.04528758,-0.03691911,0.00799135,0.0079423,-0.05151377,0.00225661,-0.00348104,-0.03547679,0.01810321,-0.00898369,0.05230373,0.00655269,-0.03713113,-0.03669661,0.02690708,0.01910747,0.02083964,-0.01394181,0.10044434,-0.01370968,-0.00545201,0.02201026,-0.04889261,0.0338433,0.01269663,-0.02565757,-0.06268982,-0.04810217,0.02065704,0.0368234,0.16558452,-0.0306586,0.10354263,0.01294831,0.027684,0.019864,0.03523457,0.03895417,0.04244682,-0.03239996,-0.01187968,0.03335008,-0.05770251,0.01729894,-0.10435081,-0.00320033,0.0105578,-0.04579997,-0.00821489,0.00487733,-0.02607355,-0.05882438,0.02727256,0.02291624,-0.00392061,-0.00831093,-0.08226136,0.00519645,-0.03867118,-0.01110988,0.00218027,-0.01933698,0.00232315,-0.01064826,0.05149348,-0.00863214,0.01349843,-0.07758106,-0.0237759,0.01207924,-0.02575456,-0.01067188,0.0563907,0.00644104,0.02330863,0.05288419,0.01279481,-0.03753993,-0.04442445,0.01682024,-0.02547508,0.02544223,-0.00509672,0.00985574,-0.01599413,-0.03382846,0.1003276,-0.04653851,-0.0377296,-0.03253676,0.0110195,0.00542225,0.03842612,-0.04745319,0.00646702,-0.00342468,0.00751416,-0.00372348,0.02459943,-0.0096734,-0.08854048,-0.00235176,-0.00167661,-0.02227771,0.00469931,-0.01636088,0.03620645,0.02824344,-0.03685127,0.05652164,0.0232999,-0.01490077,-0.04824334,-0.02539533,-0.00878999,-0.04161787,0.01804144,0.01924038,0.01192164,0.05505095,-0.00392153,0.01397777,-0.00697868,0.00655891,-0.03309063,-0.03475155,0.01571367,-0.01130351,0.0083032,-0.10258129,-0.0234613,0.02247041,-0.02949091,-0.03136377,0.01687197,0.00017066,0.03979231,-0.05688662,-0.03714162,-0.04700823,0.01004563,0.0177817,-0.00109421,-0.00592211,-0.02635428,0.01656996,-0.00569488,0.02127277,-0.0175404,0.00936318,0.00622371,0.0142218,-0.06779549,-0.0194004,-0.02460834,0.02185153,0.04617437,-0.03356227,-0.00243737,-0.06586292,-0.02988695,0.01358848,-0.01923191,-0.00570337,0.00426959,-0.05080473,0.02725585,0.05383645,0.02386262,-0.0100983,-0.0141072,-0.00277508,0.01716155,-0.0288175,-0.02959901,0.01445447,-0.00594452,-0.01641326,-0.06185051,0.04472363,0.02853943,-0.04092006,0.04210315,0.06162541,0.00918222,0.04386206,-0.03719448,-0.03871128,0.03371699,0.01441221,0.0495724,-0.03717667,0.01714319,0.03555314,0.02909931,0.04286359,-0.02575221,-0.0894312,0.09218052,-0.02383398,0.00659277,0.0594244,-0.05598415,-0.10114025,0.06634062,0.02335544,-0.07129092,-0.02916696,-0.00741336,-0.05345789,-0.00298697,0.00434292,0.0303077,0.00512698,-0.00847941,0.0628882,0.02486093,0.03158133,0.00765318,-0.05467483,-0.01504161,0.03853351,0.02059576,-0.02330842,-0.00122142,-0.01878842,0.01856169,-0.00191866,0.04525852,-0.00299109,-0.01652678,0.06626142,0.07892489,-0.02239139,-0.04957508,0.00934771,0.02659678,-0.02857229,0.04600202,0.00594959,-0.02399464,-0.02276524,0.04102086,-0.01918297,-0.01788677,-0.0097481,-0.04216184,0.0069568,0.03374029,0.01631891,0.02795029,0.05118408,-0.05049092,0.04302172,0.01158978,-0.03681089,0.01013173,-0.00254025,0.04500742,-0.01665927,0.02729285,-0.01636149,0.03816629,0.03288189,-0.04948023,-0.02934834,-0.00378273,0.03045771,0.01756205,0.05229811,0.07079291,-0.02307277,0.05368466,0.01818676,-0.02387923,0.02352683,-0.0065558,0.03620718,0.02795552,-0.03506703,0.0378264,-0.00433912,0.03549477,-0.02807515,0.05997014,-0.00624845,0.01213886,-0.05645041,0.03317216,0.01294096,0.03134774,0.01859724,0.0161237,-0.01756668,-0.0407572,-0.06294882,-0.07413958,0.0232061,-0.01969707,-0.02213865,0.0480663,0.04895869,-0.00748462,-0.03875171,0.02884034,0.0230643,0.00206881,-0.01091496,0.01874235,-0.00351643,-0.0005328,-0.04659914,-0.01423134,-0.03214553,0.00398367,-0.00357678,0.07123488,0.01312049,0.00091099,-0.01335468,-0.00031969,0.01206697,-0.12125497,-0.02454402,-0.03625546,-0.04860285,-0.02174401,0.04365217,-0.00881369,0.04546952,-0.03157897,-0.03770502,-0.03754754,-0.04387698,0.0440365,0.04986197,-0.02932327,0.0047183,0.04606872,0.0035561,0.00911948,-0.06941565,0.00563211,-0.02239428,0.01032302,0.04243082,-0.02002977,0.01369057,0.03553203,-0.02377434,0.0082286,0.03447406,-0.05162009,0.00964279,0.00391746,0.04911668,-0.05595119,0.03765941,-0.0646535,0.05890684,-0.03494482,-0.01052611,-0.03482535,-0.0138136,-0.00901391,0.00596448,0.04012635,0.05245233,-0.010297,-0.00897975,-0.055678,0.01624426,0.05322744,0.00760981,-0.03044279,-0.00521718,-0.01038465,-0.00953307,0.07332326,-0.017133,-0.03218532,-0.04059527,0.00721764,-0.05494315,-0.01185051,-0.04026867,-0.0024981,0.0141572,0.04684331,-0.06994894,0.01642045,0.00356192,-0.00429781,0.02847299,0.05540738,0.00804414,-0.03063122,-0.03029938,0.00982913,-0.04966333,0.01273417,0.05387942,-0.06018916,0.00408746,0.00778497,0.00521777,-0.03048337,-0.02966426,-0.03987404,-0.00227947,0.05530723,0.01575537,0.03213391,-0.00936401,0.00208644,-0.04071023,0.01860633,-0.05716439,0.00992288,0.00253121,-0.00980143,-0.02938043,-0.01766999,-0.00239774,-0.04048242,0.07435735,-0.04282417,-0.01497375,-0.04549181,0.03854091,-0.03946573,0.00585551,-0.03193233,-0.08776034,-0.03167883,0.00261827,0.05524595,0.0553502,-0.00774604,0.00258439,-0.0282872,-0.00722699,-0.02609255,-0.05366774,-0.05216054,-0.00745287,0.03606853,-0.0598469,0.01437669,0.02495095,0.04024811,0.02637267,-0.02792723,-0.0301527,0.03431259,0.0262325,-0.01325134,0.01598126,-0.00105232,0.00873109,-0.01972302,0.05850582,0.01058593,-0.04914628,0.00679146,0.01029585,0.06623842,0.00750281,-0.07349361,0.01467766,0.00218756,-0.04218158,0.01146485,-0.02679374,0.03959381,0.02252877,0.05022732,-0.01419309,-0.05343347,0.02003305,-0.0212078,-0.07006805,-0.09862041,0.0233579,-0.05134485,0.05791261,-0.00087514,-0.00832738,0.04151041,-0.08709367,0.01162989,0.03444139,0.02379206,0.01616273,-0.04055114,0.01797874,0.00318023,0.03024941,0.00161768,0.00541963,0.00966985,0.01518637,-0.03820905,-0.00776997,0.02460007,0.02098016,-0.04776055,-0.01939133,0.03023447,-0.00281057,0.00133704,0.01710007,0.00005593,-0.00261748,-0.0498004,-0.05538912,0.02422447,0.06359958,0.0008681,-0.03635067,-0.01628097,0.0431592,-0.03827482,0.01837522,-0.01946834,0.01801433,-0.02136384,0.01698137,0.03132393,0.07316767,0.00353069,-0.0381709,0.05268063,-0.01035318,0.03921878,-0.02331367,0.06774817,-0.00838346,-0.06511699,0.02597655,0.01862397,0.03785554,-0.04423927,-0.00163526,0.06572533,0.03165033,0.04224816,-0.0446057,0.01021606,0.04943206,-0.0024298,0.08207322,0.01423152,0.00837899,0.02104085,-0.0149766,0.04373685,0.00252779,-0.02247317,0.03224279,-0.01541606,0.00038412,-0.02473837,0.02071504,-0.00012072,0.03404638,-0.08434288,0.01139982,0.0001319,0.00925804,-0.06574795,-0.03052228,0.0279365,0.00020598,-0.03282018,0.00500739,-0.0343865,0.01791778,0.04628722,-0.0378424,0.06504804,-0.01300007,-0.0001203,-0.04104402,-0.0320675,0.03467792,-0.00896428,-0.04658321,0.01266325,-0.00324936,0.05520148,-0.00054997,0.00228199,-0.02869093,-0.0255511,0.0657106,-0.01730255,0.05026844,0.0151941,-0.0264575,-0.00693388,-0.01788984,-0.02448813,0.0334482,0.00457999,-0.03095919,-0.00911098,-0.01350792,0.02836482,0.02568693,0.05192581,0.10024686,-0.007152,-0.00333502,0.02359359,-0.00376478,0.03669211,-0.04933458,-0.03261818,0.03485448,-0.05550155,-0.08231065,-0.02065056,-0.04911121,-0.01207124,0.03846758,0.02681546,0.01602294,0.04931911,-0.04160839,0.01447753,-0.02830474,0.01902145,0.02429107,0.03153483,0.01018932,-0.04978905,-0.03558332,-0.01681577,-0.03071562,0.07330557,-0.03933209,0.00313365,-0.01715822,-0.00325427,-0.02077882,-0.04124346,0.01080587,-0.01926438,-0.03606718,0.0317779,-0.02805568,-0.01622234,-0.03396513,-0.05047617,-0.02169102,-0.00273712,-0.04103681,0.00224026,0.00589361,-0.0575189,0.02192972,-0.03656387,-0.01311199,-0.02864497,-0.04558675,0.04770086,0.00033692,-0.08216996,-0.03350565,0.05680388,0.00154329,-0.0318363,0.01356111,-0.01875463,0.00889197,-0.00256963,0.05631375,-0.04397929,0.02184827,-0.04470494,-0.02721269,0.027232,0.0280752,-0.02944714,-0.03377277,0.03006116,0.00286692,-0.06735291,-0.01907695,0.00384097,0.04330481,-0.02415112,-0.0155303,-0.00639875,0.01591197,0.02721946,0.01614287,0.03042552,-0.00667511,-0.03020519,0.0525552,-0.01877763,0.05312742,-0.0574939,0.01271929,0.0146368,-0.01159666,-0.02180836,0.024376,0.02441368,-0.01807952,-0.00186923,-0.03967737,-0.01130268,0.03258774,0.04655973,0.01450997,-0.06574814,0.0313421,0.05365689,0.01126429,0.0296998,0.05741539,-0.10706085,-0.04511776,-0.00956847,-0.0119306,0.00980004,0.05699573,-0.02424486,-0.04215459,0.00576643,-0.02541559,0.04641245,-0.0598969,0.06160795,-0.03090824,0.03744741,0.03558581,0.01031842,-0.01962377,0.00611124,0.0326724,-0.02754988,-0.01338418,0.02644166,0.05096094,-0.00073539,0.03991658,-0.01218388,-0.00360802,0.00889763,0.04212105,0.03734382,0.0612052,0.00130813,-0.03478597,9e-7,0.04237253,0.02893769,0.01708989,-0.04523512,-0.01560865,0.00297321,0.0001184,0.0696889,0.02259886],"last_embed":{"tokens":814,"hash":"11anfyd"}}},"last_read":{"hash":"11anfyd","at":1751251726001},"class_name":"SmartSource","outlinks":[{"title":"IPv4协议","target":"IPv4协议","line":12},{"title":"ISP","target":"ISP","line":51},{"title":"VPN","target":"VPN","line":63}],"metadata":{"aliases":["网络地址转换","Network Address Translation"],"英文":"Network Address Translation","cssclasses":["editor-full"]},"blocks":{"#---frontmatter---":[1,8],"#简介":[9,19],"#简介#{1}":[10,10],"#简介#{2}":[11,13],"#简介#{3}":[14,19],"#静态NAT（Static NAT）":[20,32],"#静态NAT（Static NAT）#{1}":[21,21],"#静态NAT（Static NAT）#{2}":[22,22],"#静态NAT（Static NAT）#{3}":[23,23],"#静态NAT（Static NAT）#{4}":[24,26],"#静态NAT（Static NAT）#{5}":[27,28],"#静态NAT（Static NAT）#{6}":[29,30],"#静态NAT（Static NAT）#{7}":[31,32],"#动态NAT（Dynamic NAT）":[33,45],"#动态NAT（Dynamic NAT）#{1}":[34,34],"#动态NAT（Dynamic NAT）#{2}":[35,35],"#动态NAT（Dynamic NAT）#{3}":[36,36],"#动态NAT（Dynamic NAT）#{4}":[37,39],"#动态NAT（Dynamic NAT）#{5}":[40,41],"#动态NAT（Dynamic NAT）#{6}":[42,43],"#动态NAT（Dynamic NAT）#{7}":[44,45],"#PAT（Port Address Translation，端口地址转换）":[46,60],"#PAT（Port Address Translation，端口地址转换）#{1}":[47,47],"#PAT（Port Address Translation，端口地址转换）#{2}":[48,48],"#PAT（Port Address Translation，端口地址转换）#{3}":[49,50],"#PAT（Port Address Translation，端口地址转换）#{4}":[51,53],"#PAT（Port Address Translation，端口地址转换）#{5}":[54,56],"#PAT（Port Address Translation，端口地址转换）#{6}":[57,58],"#PAT（Port Address Translation，端口地址转换）#{7}":[59,60],"#双向NAT（Bidirectional NAT）":[61,78],"#双向NAT（Bidirectional NAT）#{1}":[62,62],"#双向NAT（Bidirectional NAT）#{2}":[63,63],"#双向NAT（Bidirectional NAT）#{3}":[64,65],"#双向NAT（Bidirectional NAT）#{4}":[66,66],"#双向NAT（Bidirectional NAT）#{5}":[67,67],"#双向NAT（Bidirectional NAT）#{6}":[68,70],"#双向NAT（Bidirectional NAT）#{7}":[71,73],"#双向NAT（Bidirectional NAT）#{8}":[74,74],"#双向NAT（Bidirectional NAT）#{9}":[75,76],"#双向NAT（Bidirectional NAT）#{10}":[77,78]},"last_import":{"mtime":1740737712341,"size":3222,"at":1749024987540,"hash":"11anfyd"},"key":"Rational thinking/其他仓库/计算机科学/🌎计算机网络/网络模型/OSI层级模型/网络层协议/NAT.md","last_embed":{"hash":"11anfyd","at":1751251726001}},