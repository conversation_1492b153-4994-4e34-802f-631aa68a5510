"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md","last_embed":{"hash":"n9gugz","at":1750993392832},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0973172,-0.0492562,-0.00988989,-0.03042753,-0.00125219,-0.00111335,0.00493904,0.03210568,0.04271434,0.01762144,0.01770304,-0.014333,0.09754674,0.04389488,0.05495001,0.00149179,-0.01271417,0.04541886,-0.00151966,-0.010184,0.03166457,-0.02324345,-0.07259627,-0.04315784,-0.01047216,0.06381116,0.01166462,-0.01640175,-0.02996933,-0.16911317,0.01334284,0.03412356,0.04423567,0.00655352,0.02928475,-0.0596888,0.0099623,0.05816168,-0.03263385,-0.00197442,-0.01796802,0.00469352,0.03469685,0.00372281,-0.00780042,-0.03556611,-0.04434933,0.00369597,0.0164079,-0.02357568,-0.05947193,-0.02138466,-0.05834144,-0.0163496,-0.01847886,0.01409644,0.03849848,0.03381282,0.03181846,-0.01912132,0.0515612,0.01815366,-0.17525226,0.04863996,-0.0065429,0.00479542,-0.04576221,-0.03318573,0.04244939,0.03244253,-0.06051749,0.00078961,-0.02376464,0.06813122,0.05827751,-0.03197272,-0.03613936,-0.05528733,-0.03092669,-0.03845094,-0.02145584,0.07622939,-0.0341277,-0.00130572,0.00457045,0.06858821,-0.04963598,-0.046778,0.00235675,-0.00075631,0.01011556,-0.00824988,-0.01048157,0.01828386,-0.00078766,0.01997772,0.02095509,0.0217471,-0.0393143,0.11281291,-0.06022467,0.03100259,-0.03721159,-0.03964544,-0.03369377,-0.05603848,-0.00990529,-0.03281822,-0.00167431,0.01931906,-0.05154525,-0.04891898,0.05299667,-0.04049559,0.06243326,0.00445516,0.02995197,0.00147298,-0.01756053,-0.06659386,-0.00963841,0.03727618,0.08557787,0.02707498,-0.00216705,-0.04702724,0.03883534,0.05540302,0.01716554,0.04622357,0.06207765,0.00957009,-0.0856807,-0.04615767,-0.01842437,-0.00371379,0.00966342,0.04833297,0.02334347,0.00661757,-0.02753103,-0.08011714,0.00036535,-0.07447036,-0.09611619,0.10507908,-0.07467341,-0.00825006,-0.00914316,-0.02253209,0.03097157,0.05392805,0.02937645,-0.00849622,-0.05354957,0.00644235,0.06759068,0.11480968,-0.03672738,-0.0261304,-0.03945798,-0.01940263,-0.07111046,0.16869721,-0.03294767,-0.07282136,-0.02519595,0.02245282,0.02817167,-0.04092134,0.02480666,-0.01644699,-0.00638135,0.00742696,0.03862331,-0.02486784,-0.0219909,-0.04692608,0.02223896,-0.00906184,0.11710166,-0.00454846,-0.02544883,0.03493961,0.0215436,-0.12163831,-0.05960547,-0.04405719,-0.00908142,-0.05261786,-0.09352456,0.05070426,-0.0423389,0.01645653,-0.03978668,-0.0794664,0.05844842,0.02579399,0.04073266,-0.06388292,0.10328776,0.03352413,-0.01397119,0.03491603,-0.00618316,0.00248272,0.01329568,-0.04013375,0.01485811,0.01899269,0.02806408,0.02455818,-0.04830092,0.01691435,-0.00633954,0.03357574,0.01918121,0.03287603,0.00136431,0.08095522,0.02747184,0.00362061,-0.01638879,-0.21489288,0.00304078,-0.0215453,-0.0531428,-0.03333087,-0.00615875,0.00213884,0.01132473,0.02367213,0.11248018,0.03903237,0.0301958,-0.04727301,-0.02830514,0.0020286,-0.01962343,-0.01165864,-0.00998242,-0.06530318,-0.0499822,0.02551206,0.03062423,0.00333463,-0.02883081,0.0852779,-0.0189222,0.1639967,-0.03579154,-0.0004484,0.07884282,0.05285867,0.00482768,-0.00820787,-0.07833037,0.03123247,-0.01297418,-0.06687273,-0.04974903,-0.01954471,-0.03232901,-0.02379836,-0.01058747,-0.06163393,-0.10562836,-0.01014011,0.01915587,-0.02305081,-0.01613561,0.0445722,0.09766801,-0.01451521,0.01573149,0.03804737,0.01926723,0.00340225,-0.03563359,-0.06867882,-0.04326525,-0.03609984,0.07313147,0.02881922,-0.01694765,-0.02116181,0.01489449,0.00214286,-0.04562949,-0.01582219,-0.02012139,-0.03397627,-0.00774623,-0.03137358,0.16284025,-0.00227661,-0.0318818,0.08101851,-0.00973432,0.0003915,-0.02678138,0.01189943,0.04001593,-0.00669527,-0.0341672,0.0735393,0.02839907,0.00924299,0.00644753,0.04659254,-0.01335013,0.06932303,-0.04582556,-0.06339426,-0.06675947,-0.06179901,0.05860003,0.07439785,-0.02802574,-0.28689837,0.06413225,-0.05631701,0.01520964,0.02543022,0.02883415,-0.00490881,0.01376404,-0.02063956,-0.00730904,0.00653656,0.0190211,0.03410717,-0.05351874,0.0032918,-0.00580318,0.04807075,-0.01005348,0.07798685,-0.01404896,0.01925383,0.05546372,0.18172409,0.03830016,0.06101946,0.01615304,0.00531224,0.04472994,0.00881203,0.09888931,0.01215764,-0.01851311,0.05597648,-0.02627069,0.02070886,0.08902852,-0.06325777,0.06281633,0.03651721,0.00004055,-0.07539894,0.04002633,-0.08932204,0.04699539,0.07867622,0.03425407,-0.02894951,-0.07322731,0.04090529,0.03141604,0.02926616,0.04731809,-0.02332326,-0.01388844,0.03558044,0.02635832,0.03223157,0.0044367,-0.05938476,-0.00610093,0.04042695,0.01079242,0.07771176,0.10398852,0.02694501],"last_embed":{"hash":"911b9f2e9e56c586d81392166ab847d8aa64948c8089ce95a733155a0f15db11","tokens":456}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.06893116,0.01595083,0.00404761,0.04807838,-0.01798203,-0.01196769,0.02048283,-0.04093843,-0.0344785,-0.03731462,-0.00089999,0.04349105,0.05204976,-0.00545696,0.03578536,-0.01032243,-0.00124726,-0.0082544,0.01825065,-0.01811326,0.06061333,-0.06791247,0.02948092,0.03308354,0.01783624,-0.01295794,-0.02163211,-0.04506579,-0.02127095,0.04577839,0.08839197,-0.05278686,-0.02782267,-0.01958106,-0.01565621,0.00453029,0.02811497,0.04447692,-0.03717641,0.00860553,-0.03649118,0.01377382,-0.03211927,0.00650656,-0.03413095,-0.02896104,0.07936022,-0.02335681,-0.02267455,0.02537927,-0.01063505,-0.04614836,0.08342505,0.04006173,-0.02011993,0.00977213,-0.00731035,-0.04160993,-0.02574699,-0.0394218,0.01381437,-0.02464553,0.05040466,0.07495771,-0.05610057,0.0198078,0.01944405,-0.02508824,0.02970206,-0.00268819,-0.0139888,0.00906703,0.00143747,0.05284757,-0.05852593,-0.00320949,0.03533601,-0.02622391,-0.0636182,0.02114972,-0.00504079,0.03987805,-0.02996797,0.02770217,-0.03465354,-0.05650499,0.05091594,-0.07963721,-0.03697847,-0.00167602,0.0516688,-0.01075775,-0.02721914,-0.05895362,0.02923069,0.02352774,0.0276774,0.01213914,0.03300234,-0.01600959,0.03329894,-0.00789665,-0.04138708,-0.04756871,0.01047476,-0.01594448,-0.04374504,-0.00917471,-0.02278055,-0.0167445,-0.00641232,-0.00313598,-0.07119684,-0.05074022,-0.01599718,0.00397733,-0.01073817,0.03495265,-0.03695113,0.05076537,0.00327225,0.06193733,0.03166566,0.02392332,0.02485788,0.04670204,-0.04212219,-0.04682741,0.01091439,-0.05364475,0.00281648,0.05035809,-0.02474124,0.00639568,0.01787207,0.01924359,0.01760816,-0.01439559,-0.03375816,-0.01455216,0.01543567,-0.04266076,0.08567946,-0.01105562,-0.00731488,-0.01357785,-0.02327226,-0.01202595,-0.06916851,0.07484105,-0.0263865,0.02115218,-0.0623601,-0.04703262,-0.04424836,-0.0548091,-0.04782904,-0.04483486,0.0541908,0.00302011,-0.01792233,0.03050476,0.00452586,0.03341829,0.00681209,0.05052076,0.01692587,-0.00725641,-0.04256755,-0.01056294,-0.04512345,0.01398635,0.04644839,0.02053833,0.01103969,-0.01834255,0.02613204,0.05180972,-0.00470211,0.0403203,0.03285123,-0.0431747,-0.00198977,0.00637606,-0.02472807,0.03817323,-0.06152388,0.04125544,0.03216078,-0.00629397,0.04459791,-0.02848785,-0.03502297,0.03842167,0.04013696,0.00636965,0.01433583,-0.07226039,0.04956839,0.00575317,-0.04130307,0.0133228,-0.01870036,-0.07302292,0.05319793,0.01482884,-0.03503362,-0.00823289,0.0333907,-0.03631625,-0.00451903,0.05361965,-0.00221274,0.01524774,-0.04637108,-0.01994991,-0.03535814,0.06874189,0.03107549,-0.05177305,-0.08915648,-0.00200034,-0.02433401,-0.00816311,-0.00406239,-0.05932388,0.00595523,0.0235193,0.04999624,-0.01851374,-0.00362273,0.0002482,0.03349563,0.00091327,-0.04462843,-0.01641658,0.03423389,-0.00359883,0.04760144,-0.02394594,0.0309629,-0.00843558,0.03552082,-0.05838932,-0.03908923,0.02278078,0.00809622,0.00652501,0.05816796,0.038528,0.11454642,-0.01466494,-0.06797177,0.03359222,-0.06868698,-0.04023292,-0.04279902,-0.00118714,0.03051811,-0.01449429,0.04000008,-0.00938976,-0.00843661,0.03940683,-0.00918692,-0.05966849,0.02914293,0.03979196,0.03848853,-0.01515283,-0.00661752,-0.03680114,0.03546419,0.04773089,-0.05878685,0.01549836,-0.00642527,-0.02590162,0.02745537,-0.00623993,0.03248179,-0.02541387,0.03193994,-0.01137653,-0.05684861,0.0801139,-0.02269235,0.04083373,0.03276578,0.04042451,0.03765506,-0.03960564,-0.02432176,0.0458972,-0.03487208,-0.01546631,0.01665567,0.02093153,0.00674201,-0.04312802,0.02032856,0.00776946,-0.00343651,0.00540408,0.03042628,0.07272466,0.00377021,0.03265383,0.04073359,-0.03532435,0.00981636,-0.00605918,0.03179472,0.0111651,0.00795796,0.08836158,-0.01059503,-0.00607293,-0.02305626,-0.02473195,-0.06284682,-0.0121968,-0.07802903,0.00356992,-0.00769259,-0.06715836,0.05215015,0.05491173,0.0049182,-0.00373712,-0.07630278,-0.01836056,-0.04910409,-0.05957432,0.03519568,0.00166435,0.04041224,-0.02246707,0.00268232,-0.01207839,-0.04724307,0.00659221,-0.01367091,0.04554962,0.02397598,0.1101773,0.00927468,0.04106577,-0.04625975,-0.04512308,-0.00476634,-0.01002248,-0.08887282,0.03291171,0.00648024,-0.00992542,0.03030329,0.00178245,-0.00255109,-0.00799075,-0.08481061,-0.04345006,-0.00852938,0.06307796,0.02547576,-0.02487881,0.00999819,-0.01649265,-0.01543588,-0.00142798,0.09755972,0.02244083,0.05940644,0.06219103,-0.05097916,-0.00373361,-0.00096567,-0.00636848,0.11629738,0.00531455,0.01382583,-0.03786457,-0.01766504,-0.04955956,-0.03412953,-0.0266574,0.0036137,0.0006585,-0.05196872,-0.02610609,0.03353951,-0.00613061,-0.04924203,-0.02316803,0.06753239,0.00899383,0.03586017,-0.02511058,-0.01464389,-0.02611923,-0.00023467,-0.02163595,0.02705534,-0.04752215,0.00207428,-0.01736487,-0.05379707,0.02405521,-0.0142527,0.01706606,0.03312492,-0.02692051,-0.04184769,0.06040078,-0.03253919,-0.02351236,-0.01558183,-0.01732426,0.00467183,-0.0382935,0.0076978,-0.0324396,0.02218782,0.00470097,-0.00983865,-0.03575439,-0.02743778,0.01492343,0.02780912,-0.01191037,-0.05428033,0.00649965,-0.08956829,-0.0276433,-0.01604638,-0.06117059,0.06127068,0.04247264,0.02775557,-0.07659804,-0.02854836,0.02555853,0.02032406,-0.02834131,0.02921146,-0.01926378,0.0112564,-0.01826633,0.0425233,0.01305473,-0.04209039,-0.04545854,0.03946111,-0.02978754,0.016026,-0.01959821,0.00146861,-0.0098113,0.03222714,-0.01162579,-0.0232799,-0.01128595,0.0019592,0.06474955,0.06684167,0.01624778,0.03242366,0.03664455,-0.01539242,-0.0101565,-0.0050361,-0.05390282,-0.01019993,-0.01164657,0.05971808,0.01001412,-0.01373219,-0.02849698,-0.06446951,-0.00597963,-0.08823396,-0.05165482,-0.07658523,0.02095815,-0.0556141,-0.0154932,0.02234712,0.0117844,-0.00260446,-0.03749534,-0.00313937,0.01645783,-0.01114185,0.00041031,0.02974842,-0.05456647,-0.04812047,0.06898049,0.00090753,0.04955505,0.02945846,0.03284519,-0.00055541,0.03793536,0.00522687,0.02281031,-0.04080472,-0.0128603,0.03718177,-0.02693981,0.00493588,-0.01902946,0.02594203,0.02705239,-0.05284287,-0.01483872,0.01710006,0.04915455,-0.03466671,-0.02834536,0.03571081,0.00812519,0.01267703,0.00235209,0.01157446,-0.02964437,0.03556253,0.01769601,0.04604886,0.02276761,0.06115528,0.01197793,0.02764863,-0.0396635,0.0035185,0.00848925,0.05689119,-0.01159926,-0.04810099,-0.01145804,-0.03502651,0.02634666,-0.02948206,0.06801655,0.02975182,-0.0352045,-0.00179473,-0.00964556,0.01520136,0.05346645,0.00749365,0.03003794,0.02987908,-0.04818161,0.02463364,0.01648968,-0.04399748,-0.00181425,0.00578179,0.07540473,-0.03866407,-0.0087503,0.05068236,0.00236298,-0.01489107,-0.00209377,-0.02705697,-0.006733,-0.01198888,0.01463629,-0.05856372,0.02590822,-0.01358919,0.0401125,-0.06480646,-0.02637478,-0.04420194,0.00942455,0.04313807,-0.00957545,0.08003286,-0.03177626,0.03679918,-0.06622943,-0.01513808,0.02005284,0.06173008,0.03915351,0.02844377,0.0378777,0.02335187,-0.01595887,-0.02737087,-0.02285652,0.04022267,0.00025172,-0.0801362,0.05055238,-0.01300318,-0.03263728,-0.036154,-0.01678663,0.05981139,0.00269397,0.00675397,-0.03220236,-0.01769846,-0.08005262,0.00546263,-0.03350325,0.014525,0.08060101,0.0066247,0.00017748,-0.01055138,0.00345631,-0.01892536,0.04035403,0.00929308,0.00170994,-0.00728919,0.05095373,-0.02691631,-0.07881881,-0.00720501,0.05475994,0.02100341,-0.04338344,0.0154522,-0.0461235,-0.02560023,-0.03845109,0.00050839,0.01482875,0.00376192,0.04810049,-0.05637724,-0.0360485,0.00553897,-0.01406573,-0.02196606,-0.04014893,0.00374791,-0.01861766,-0.01931822,-0.00546228,-0.07138135,0.03078754,-0.03741608,0.0090798,-0.01472603,0.05546347,-0.0101022,-0.05576556,-0.04567864,0.0376683,-0.04378053,0.00854504,-0.02213264,-0.02127357,-0.02586456,-0.01845368,0.05239583,-0.02247445,-0.06722275,-0.01449644,-0.00232166,-0.00127461,-0.05865123,0.00878218,-0.00645448,-0.03455948,-0.05227537,0.03588085,-0.00549388,-0.0031433,-0.01143621,-0.00816941,0.00622589,0.02059227,-0.03716455,0.01655137,0.07983131,-0.02345557,-0.00182158,0.06347748,0.06102312,0.02444885,-0.03484994,-0.07903423,-0.02833607,-0.00903859,0.02761571,0.02818263,0.00398428,0.05227957,0.01848742,0.00468788,-0.00790291,-0.0074096,0.0055542,0.05543787,0.00533285,-0.01245153,0.00121984,-0.02294133,-0.01035827,0.01222827,0.05655791,0.0217275,-0.04966301,0.02155845,-0.00418538,-0.01965068,-0.01618768,0.01402535,-0.01163854,0.02075904,0.00644398,0.03471337,0.0010258,0.02985875,0.0381277,0.07065012,-0.05152127,-0.01733863,-0.00847788,0.03668084,-0.00100099,0.02666741,-0.0727843,-0.03742534,-0.00738786,0.03893026,0.04940855,-0.05049413,-0.03335305,-0.02216516,0.05126967,0.03668034,-0.01801717,-0.03788946,-0.03660508,0.0052039,-0.00912022,-0.03400423,-0.0073153,-0.00160789,0.00096549,0.08745405,0.03143574,0.01011631,-0.06135354,0.03652101,0.01965944,0.00675799,-0.01098991,-0.03425214,0.00000119,0.01952264,-0.0203687,0.01971896,-0.00026033,-0.05519506,-0.00935389,0.00059571,0.01647651,-0.02526147],"last_embed":{"tokens":1584,"hash":"n9gugz"}}},"last_read":{"hash":"n9gugz","at":1750993392832},"class_name":"SmartSource","outlinks":[{"title":"SQL语言","target":"SQL语言","line":7},{"title":"matrix","target":"matrix","line":25},{"title":"https://www.baidu.com/","target":"https://www.baidu.com/","line":35},{"title":"https://www.taobao.com/","target":"https://www.taobao.com/","line":36},{"title":"http://c.biancheng.net/","target":"http://c.biancheng.net/","line":37},{"title":"https://www.google.com/","target":"https://www.google.com/","line":38},{"title":"https://github.com/","target":"https://github.com/","line":39},{"title":"https://stackoverflow.com/","target":"https://stackoverflow.com/","line":40},{"title":"http://www.yandex.ru/","target":"http://www.yandex.ru/","line":41},{"title":"https://vk.com/","target":"https://vk.com/","line":42},{"title":"NOT NULL","target":"https://c.biancheng.net/sql/not-null.html","line":113},{"title":"DEFAULT","target":"https://c.biancheng.net/sql/default.html","line":114},{"title":"UNIQUE","target":"https://c.biancheng.net/sql/unique.html","line":115},{"title":"PRIMARY KEY","target":"https://c.biancheng.net/sql/primary-key.html","line":116},{"title":"FOREIGN KEY","target":"https://c.biancheng.net/sql/foreign-key.html","line":117},{"title":"CHECK","target":"https://c.biancheng.net/sql/check.html","line":118},{"title":"INDEX","target":"https://c.biancheng.net/sql/indexes.html","line":119}],"metadata":{"aliases":["Relational Database Management System"]},"blocks":{"#---frontmatter---":[1,4],"#简介":[5,9],"#简介#{1}":[6,6],"#简介#{2}":[7,7],"#简介#{3}":[8,9],"#逻辑模型":[10,103],"#逻辑模型#Table":[11,45],"#逻辑模型#Table#{1}":[12,17],"#逻辑模型#Table#{2}":[18,20],"#逻辑模型#Table#{3}":[21,23],"#逻辑模型#Table#{4}":[24,26],"#逻辑模型#Table#{5}":[27,30],"#逻辑模型#Table#{6}":[31,42],"#逻辑模型#Table#{7}":[43,43],"#逻辑模型#Table#{8}":[44,45],"#逻辑模型#Field / Column":[46,67],"#逻辑模型#Field / Column#{1}":[47,47],"#逻辑模型#Field / Column#{2}":[48,50],"#逻辑模型#Field / Column#{3}":[51,63],"#逻辑模型#Field / Column#{4}":[64,64],"#逻辑模型#Field / Column#{5}":[65,66],"#逻辑模型#Field / Column#{6}":[67,67],"#逻辑模型#Record / Row":[68,72],"#逻辑模型#Record / Row#{1}":[69,69],"#逻辑模型#Record / Row#{2}":[70,70],"#逻辑模型#Record / Row#{3}":[71,72],"#逻辑模型#NULL / 空值":[73,103],"#逻辑模型#NULL / 空值#{1}":[75,75],"#逻辑模型#NULL / 空值#{2}":[76,77],"#逻辑模型#NULL / 空值#{3}":[78,90],"#逻辑模型#NULL / 空值#{4}":[79,90],"#逻辑模型#NULL / 空值#{5}":[91,91],"#逻辑模型#NULL / 空值#{6}":[92,93],"#逻辑模型#NULL / 空值#{7}":[94,94],"#逻辑模型#NULL / 空值#{8}":[95,103],"#逻辑模型#NULL / 空值#{9}":[97,103],"#SQL约束 (Constraint)":[104,121],"#SQL约束 (Constraint)#{1}":[106,106],"#SQL约束 (Constraint)#{2}":[107,107],"#SQL约束 (Constraint)#{3}":[108,110],"#SQL约束 (Constraint)#{4}":[111,121],"#需要注意的方面(数据原则)":[122,145],"#需要注意的方面(数据原则)#{1}":[124,145]},"last_import":{"mtime":1731737549659,"size":6684,"at":1748488128912,"hash":"n9gugz"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型": {"path":null,"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.02892282,0.01954008,-0.01820611,0.01912895,-0.00436525,-0.03022079,-0.00679289,-0.02910402,-0.00402915,-0.00054294,-0.02216098,0.05062819,0.05400157,-0.0135235,0.04290585,0.00663188,-0.03525009,0.02667853,0.0296165,-0.01865095,0.04528332,-0.0456207,0.0343452,0.01993671,-0.00423534,0.00121421,0.00207107,-0.03964323,-0.00758941,0.06341834,0.05933632,-0.05230178,-0.03596144,0.00817367,-0.04219133,0.00595716,0.01837633,0.06677963,-0.05281287,-0.00777194,-0.03097082,-0.00267593,-0.0004168,-0.00070025,-0.04351637,-0.05406385,0.06632082,-0.05669525,-0.00436967,0.02745877,-0.01786374,-0.03013539,0.06722053,0.03533082,-0.04031823,-0.01996569,-0.01215126,-0.01290524,-0.01364283,-0.03230304,-0.01024851,-0.06226296,0.06358146,0.05897964,-0.06024055,-0.00026573,0.01448861,-0.01069972,0.02567731,-0.01701417,0.00159495,-0.00412592,0.00236777,0.05524314,-0.01591552,-0.00111121,0.02918758,-0.01205187,-0.05347398,0.01326963,-0.03691385,0.01834828,-0.03701205,-0.00476728,-0.03134592,-0.03651677,0.03578292,-0.06077984,-0.04910774,-0.02525613,0.06937589,0.01837553,0.01477944,-0.06338256,0.03506011,0.04672772,-0.00140445,-0.00155944,0.03566772,-0.00075318,0.02472981,-0.00552756,-0.06130574,-0.07047852,-0.01860115,-0.01482574,-0.04186855,-0.00232058,-0.01793343,0.00277888,0.0302794,-0.01600423,-0.0732538,-0.0578817,0.04098331,0.00171851,-0.00539007,0.06367199,-0.05295521,0.0220376,-0.00870823,0.05958486,0.02675953,0.02237393,0.0375492,0.03530811,-0.01491823,-0.04510377,0.00975051,-0.02696441,-0.01337507,0.02492961,-0.02803685,0.03543903,0.00996121,0.03166715,-0.00266189,-0.03556472,-0.02395116,-0.00359045,0.05195092,-0.0210489,0.08047567,0.01058675,0.00567443,-0.02110904,0.00183793,-0.00790383,-0.0352922,0.06761395,-0.0396667,0.02756441,-0.06243011,-0.04161907,-0.0632494,-0.03195291,-0.05476497,-0.02526683,0.06492308,-0.01235912,-0.0178064,0.04792651,0.0066679,0.04598331,-0.00646911,0.07141697,-0.00478465,-0.01423436,-0.06172735,-0.02508202,-0.03686918,0.01929244,0.01591923,0.01513996,-0.00542871,0.01644356,-0.01327356,0.04338991,0.00604461,0.00512247,0.0234131,-0.03872705,-0.04227822,0.00984179,-0.02473812,0.03442497,-0.04463282,0.06759407,0.01782626,-0.00300116,0.03814065,-0.01627934,-0.02035675,0.04202109,0.04796029,0.01048265,0.01450754,-0.03217483,0.0410069,-0.01632896,-0.05141531,-0.00931993,-0.00708228,-0.05246337,0.03177785,0.02572791,-0.04834304,-0.01319247,0.02766674,-0.06894617,0.04730052,0.06623149,0.02488412,0.04479004,-0.05967676,-0.01799421,-0.0123439,0.07477026,0.03365227,-0.05571952,-0.06890119,0.0093079,-0.02023426,-0.06620321,0.00982492,-0.05193269,-0.01994215,0.02631448,0.06059896,-0.00955191,-0.01097236,0.00607764,0.0484656,-0.00338334,-0.03395866,-0.00960496,0.02614384,-0.00419488,0.03349365,-0.00943192,0.03875264,-0.01512284,0.0489074,-0.08524274,-0.04265026,-0.00646784,-0.00473424,0.00676493,0.03855927,0.05918006,0.12290513,-0.0141496,-0.06791297,0.03352954,-0.05241905,-0.01918229,-0.06190212,0.00816545,0.02889119,-0.01995981,0.03243282,0.00457929,0.00242893,0.00793277,-0.01661263,-0.02642154,0.04527714,0.05152511,0.03718272,0.00974857,-0.01396687,-0.03006515,0.049757,0.03986778,-0.11565626,0.03485602,-0.00626449,-0.00149362,-0.00914108,0.00161376,0.01649501,-0.03043301,0.014235,0.00392813,-0.05433797,0.06521992,-0.03999306,0.06102856,0.0442451,0.01164542,0.04108589,-0.04920704,-0.02290915,0.04940182,-0.02501321,0.00060445,0.00483,0.03813513,-0.02123814,-0.05126657,0.0096373,0.01737762,-0.00850884,0.01828922,0.06488196,0.07181022,0.00311857,0.03709896,0.0165583,-0.03679572,0.02171477,-0.01857327,0.05199024,0.01075659,0.01192663,0.08025433,-0.01435549,-0.00081842,-0.00265298,-0.01583547,-0.05872134,-0.00729971,-0.10363688,0.00148018,0.00931822,-0.06559694,0.03147576,0.05109746,-0.02055833,0.03137065,-0.07852718,-0.02412135,-0.01830788,-0.0580368,0.03236815,0.0143375,0.0544168,-0.03992901,-0.01203313,-0.00545788,-0.04989756,0.01875575,-0.00391014,0.02885031,0.02120932,0.08041373,0.01657833,0.01482648,-0.034498,-0.05438388,0.00598065,-0.03793554,-0.12710541,0.01472097,0.01439453,-0.01589405,0.01847885,0.00571773,-0.00678438,0.00766485,-0.07506564,-0.03656214,-0.0011608,0.03505768,0.05676855,-0.03752696,-0.00653723,0.00656318,-0.00969727,-0.00531365,0.0801504,0.00261215,0.06392857,0.03939733,-0.04094027,-0.00456026,-0.00356147,-0.00272539,0.08609084,-0.01597379,0.0259948,-0.05320291,-0.03820045,-0.03040694,-0.02489547,-0.03112181,-0.00687194,-0.01611812,-0.00609998,-0.03149245,0.0165473,0.00556385,-0.03781176,-0.01236059,0.0723474,0.01896303,0.03720622,-0.01453862,-0.03638776,-0.03016301,-0.01984755,0.01425525,0.00571385,-0.05549951,-0.01583774,-0.00417734,-0.03767792,-0.00665591,-0.056937,0.0218295,0.0309027,-0.0374114,-0.04541741,0.08304595,-0.04542371,-0.0105835,-0.01199255,-0.01773415,-0.01436374,-0.04483363,-0.00529494,-0.02457182,0.01776808,-0.00613751,0.01665248,-0.05367866,-0.01278606,0.02150838,0.01926911,0.00746038,-0.04891646,-0.00535113,-0.0606223,-0.06014876,0.01276406,-0.05247653,0.05533141,0.07773261,0.01852079,-0.07482941,-0.0158654,0.01001552,0.00853686,-0.01733403,0.02351793,0.00682933,0.02147052,0.02668965,0.03503141,0.00546107,-0.06430428,-0.01356546,0.05683648,-0.04901404,0.02664767,0.01638462,-0.00679036,-0.03143515,0.03189395,-0.01378702,-0.0517913,-0.02411976,0.02059788,0.05467571,0.04768813,-0.00578432,0.03549352,0.02211116,-0.01025939,0.00374844,-0.01769443,-0.00469313,0.01112893,-0.00930327,0.04877226,0.0384338,-0.03516779,-0.03909115,-0.07847326,-0.0017785,-0.09080227,-0.02586193,-0.03486915,0.01931863,-0.04705296,-0.01078061,0.01615773,0.03780928,0.0013184,-0.03657431,0.01967409,0.02248933,-0.01514823,-0.00944931,0.05600825,-0.06585714,-0.03181276,0.08809207,-0.00143195,0.05131041,0.04568619,0.02547361,-0.00247869,0.04555425,0.01645762,0.02307268,0.0063246,-0.01751341,0.03084788,-0.03385341,-0.00732229,-0.01870165,0.01491685,0.02376307,-0.08384041,-0.01645116,0.0298352,0.0261967,-0.00435631,-0.03062994,0.03168441,0.01928892,0.04265675,-0.00367397,0.01100163,-0.01895791,0.03280961,0.01014887,0.03296142,0.02562074,0.06807962,0.02899668,0.03897141,-0.02486509,-0.00731836,0.03434648,0.0427924,-0.02718121,-0.04528819,-0.00790424,-0.04838091,0.02824279,-0.02862077,0.04516019,0.05238144,-0.02868294,-0.00328246,0.00211225,0.01840581,0.01230135,0.0114736,0.04477897,0.03857057,-0.06504584,0.02922205,0.00663987,-0.02658801,0.03425138,0.00255702,0.05883645,-0.04289408,-0.02465113,0.00459826,-0.00823774,0.00427263,0.03373997,-0.02872319,0.00042542,0.01131058,0.00739476,-0.06798717,0.0241256,-0.02529593,0.04326285,-0.04622496,-0.02914619,-0.0282336,0.00535951,0.05241499,0.0155088,0.08761933,-0.01503343,-0.00331194,-0.05071352,-0.05976888,0.01781555,0.05283678,0.03662977,0.02339288,0.04405504,0.00891724,-0.00745096,-0.00855548,-0.01706535,0.0821186,0.01175097,-0.05210434,0.04076329,-0.05346052,-0.01588915,-0.03995792,-0.01326545,0.0305967,0.00001292,-0.01059916,-0.0307299,-0.04104428,-0.06882409,0.00549774,-0.01914326,0.00718669,0.06241234,-0.0090851,0.05360354,-0.01508171,-0.03789435,-0.03320915,0.00433523,0.02519418,0.01869352,0.01640481,0.05082891,-0.04627817,-0.04922576,0.00076133,0.03099124,0.01473522,-0.02582012,0.0225996,-0.05061344,0.01347922,-0.04633744,0.00683529,0.01179473,0.03307825,0.03163172,-0.06161785,-0.02604268,0.01990622,-0.01871095,0.01705219,-0.04904759,-0.00805312,-0.01419174,-0.01973898,-0.00013634,-0.05667679,-0.00661731,-0.04966871,0.00313455,-0.03833091,0.05417636,-0.02312508,-0.04820422,-0.02573776,0.03593003,-0.05597689,0.00068557,0.00244019,0.0014063,-0.01934615,-0.02860718,0.02493167,-0.03571142,-0.04685704,-0.03855085,0.02115916,-0.03250203,-0.020738,-0.01193633,-0.00979513,-0.02823625,-0.02969237,0.03835498,0.01184672,0.00836526,-0.00069828,-0.00965206,0.00505112,0.03939981,-0.02742026,0.01873793,0.07673227,-0.03744485,-0.02875533,0.07952462,0.05038413,0.0113244,-0.02770962,-0.05223165,-0.02588979,0.05862719,0.02538689,0.01894021,-0.00566943,0.05103095,0.01634081,-0.04353341,0.01336279,-0.0227388,0.01005731,0.04632739,0.00711638,-0.03606081,-0.01246073,-0.02453098,-0.02100391,0.01076908,0.05192374,0.02775609,-0.04044145,0.02091241,0.01579198,-0.05727091,-0.02232186,0.00559959,0.01373782,0.0105627,0.02123483,-0.01536498,-0.00924235,0.03267187,0.03513117,0.056706,-0.0437924,-0.01439528,0.01260264,0.04413743,-0.02257474,0.01943515,-0.04618341,-0.07530277,-0.02966391,0.02967744,0.03485789,-0.04295435,-0.0229902,-0.02143005,0.03776975,0.04139571,-0.00101163,-0.01124585,-0.04348236,0.00977656,-0.02037707,-0.02779292,-0.03077632,-0.00288935,-0.02522416,0.10723378,0.04329919,0.00342583,-0.04656744,0.01458544,0.00666145,-0.00201181,-0.02064062,-0.05366629,0.00000108,0.0418922,-0.01735838,-0.01378,0.01488454,-0.03499119,-0.01796517,-0.02209564,-0.01540618,-0.01389468],"last_embed":{"hash":"ck1mg3","tokens":1015}}},"text":null,"length":0,"last_read":{"hash":"ck1mg3","at":1749002744711},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型","lines":[10,103],"size":2824,"outlinks":[{"title":"matrix","target":"matrix","line":16},{"title":"https://www.baidu.com/","target":"https://www.baidu.com/","line":26},{"title":"https://www.taobao.com/","target":"https://www.taobao.com/","line":27},{"title":"http://c.biancheng.net/","target":"http://c.biancheng.net/","line":28},{"title":"https://www.google.com/","target":"https://www.google.com/","line":29},{"title":"https://github.com/","target":"https://github.com/","line":30},{"title":"https://stackoverflow.com/","target":"https://stackoverflow.com/","line":31},{"title":"http://www.yandex.ru/","target":"http://www.yandex.ru/","line":32},{"title":"https://vk.com/","target":"https://vk.com/","line":33}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Table": {"path":null,"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.03915225,0.01532262,-0.02485436,0.02049875,0.00003543,-0.03212338,-0.00937354,-0.02230012,0.00181247,0.00692137,-0.0237616,0.04836436,0.04609183,-0.01336714,0.04651051,-0.00268741,-0.03522419,0.01077115,0.0364469,-0.01082765,0.04887432,-0.03501352,0.02760858,0.01865711,0.00016642,0.00308972,-0.00913045,-0.03862608,-0.00963803,0.07031146,0.04246436,-0.05513564,-0.01921459,0.0118753,-0.04011326,0.00458166,0.00645358,0.06906381,-0.06702568,-0.00963433,-0.03146952,-0.00807938,0.0077645,0.00138918,-0.04069543,-0.05397554,0.06360843,-0.05907865,-0.00831089,0.0268345,-0.01746212,-0.02855223,0.06774659,0.03545988,-0.04383983,-0.02521166,-0.0107667,-0.00270529,-0.00728655,-0.03228906,0.00055244,-0.06594447,0.05470192,0.05523568,-0.05594407,-0.00988099,0.01844191,0.00223963,0.02884995,-0.01163673,-0.01171151,-0.00554709,-0.00614406,0.04878232,-0.02213697,-0.0067675,0.02889369,-0.01404231,-0.05425444,0.0049827,-0.0189136,0.02204609,-0.02914525,-0.01467119,-0.03328225,-0.03219823,0.03823325,-0.0593451,-0.04154351,-0.02739161,0.0776615,0.0121279,0.0132304,-0.06995935,0.04485822,0.05009917,0.00663023,-0.008187,0.03369157,0.00626772,0.02867065,-0.01248261,-0.06177144,-0.07040926,-0.02123305,-0.00974015,-0.04568829,-0.00018119,-0.02414106,0.00673519,0.0254302,-0.01512935,-0.06553993,-0.04925708,0.04587242,0.0039638,-0.00639247,0.05650812,-0.05373636,0.02105708,-0.00885296,0.05990567,0.02304027,0.02854965,0.0334704,0.04364006,-0.01217582,-0.03959332,0.00633261,-0.01608496,-0.02144202,0.01763079,-0.0302544,0.0254481,0.01972895,0.04049063,-0.01632074,-0.03849103,-0.02128124,-0.01080955,0.04514296,-0.01714036,0.06973951,0.02583873,-0.00825658,-0.0142281,0.01693136,-0.00634755,-0.0377127,0.06383647,-0.03737512,0.0239314,-0.06874848,-0.03503458,-0.05980377,-0.0300746,-0.06644597,-0.02841729,0.07238519,-0.00968407,-0.01526737,0.05272745,0.00201676,0.04844672,-0.01315797,0.05224723,0.00135351,-0.01093088,-0.05207376,-0.01632128,-0.04243544,0.01251271,0.0202947,0.01711284,-0.01613814,0.01766664,-0.02001326,0.04499999,-0.00394861,0.00658313,0.02484068,-0.03402821,-0.04220705,-0.0054958,-0.01504667,0.03645803,-0.05125496,0.05390634,0.01889404,-0.00040533,0.04076238,-0.01821108,-0.02190104,0.05767414,0.06109557,0.00255865,0.01733327,-0.02702022,0.04640315,-0.01559385,-0.05018957,-0.00655486,-0.00990654,-0.0506683,0.03605783,0.02560687,-0.04725925,-0.01540907,0.03386239,-0.07665034,0.03741019,0.06207441,0.02372981,0.04926629,-0.0534592,-0.02727681,-0.00312348,0.06916579,0.03728346,-0.05690169,-0.06870713,0.01599533,-0.03616152,-0.07373463,0.01419867,-0.0523733,-0.03661165,0.0230869,0.05412192,-0.00782327,-0.02528183,0.00247107,0.04424826,0.00118174,-0.03238397,-0.01919579,0.02560486,-0.00583912,0.0440491,-0.0046705,0.04070953,-0.0140594,0.0541478,-0.07302754,-0.05035848,-0.004851,0.01117513,0.01299797,0.03856421,0.0591783,0.1208017,-0.01481263,-0.06908037,0.02429399,-0.0579048,-0.01988759,-0.06016712,0.00228541,0.02914749,-0.01749459,0.03585267,0.01091374,0.01092819,0.00418978,-0.01915708,-0.02112874,0.05868385,0.04723114,0.03608125,0.00702016,-0.01450543,-0.03575354,0.05123634,0.03123329,-0.11386436,0.03578422,-0.01127003,0.0156807,-0.01595788,0.00023117,0.01171716,-0.02901038,0.01720902,0.00748236,-0.06755252,0.05769136,-0.04107796,0.05503859,0.03518728,0.0093295,0.03648038,-0.04939864,-0.00987163,0.05011788,-0.02500615,-0.00012003,-0.00221302,0.03333485,-0.02640457,-0.0447447,0.00508264,0.01612577,-0.00925996,0.01821177,0.05345455,0.07631518,0.00356602,0.0475176,0.01421037,-0.03296962,0.02240455,-0.02434933,0.03839689,0.00697946,0.02116071,0.07868495,-0.00772181,-0.00730588,-0.00346471,-0.00943616,-0.06085256,-0.0117127,-0.1000957,-0.00366265,0.00317277,-0.05701575,0.02162907,0.0494967,-0.02708034,0.02990561,-0.08188659,-0.03116894,-0.01638163,-0.05278068,0.03219223,0.01239015,0.05031501,-0.04241718,-0.00934224,-0.00459998,-0.05639029,0.02056493,-0.00818781,0.05023511,0.02614088,0.07727202,0.01670191,0.0144487,-0.02596688,-0.06493214,0.00585109,-0.03237094,-0.1215166,0.01611733,0.01700854,-0.02093934,0.01541303,0.00574649,-0.0001142,0.0050182,-0.08371433,-0.02937486,0.00471021,0.04131839,0.0612047,-0.03995086,-0.00835329,0.00335682,-0.00193108,-0.00006184,0.07124238,0.00929309,0.06598787,0.03209424,-0.04932931,-0.00299491,-0.00405576,0.00404676,0.09077819,-0.00470257,0.03270682,-0.06122712,-0.03349277,-0.02941647,-0.01781745,-0.0338768,0.00236655,-0.01218856,-0.00656985,-0.03386283,0.00842973,0.00770349,-0.03754209,-0.0091369,0.07242359,0.01249524,0.03343749,-0.0138267,-0.04067125,-0.02537618,-0.02317896,0.00858463,0.00372986,-0.05763157,-0.01613443,-0.00799686,-0.03428935,-0.00190959,-0.06853139,0.02786892,0.03507407,-0.03915174,-0.04053795,0.0690927,-0.0498214,-0.01741691,-0.00239827,-0.02094439,-0.02535925,-0.05163456,-0.00130835,-0.02793036,0.01232611,-0.00557321,0.01345982,-0.05501267,-0.00492448,0.0141129,0.01465826,0.00200314,-0.06163099,-0.011689,-0.05001938,-0.0593763,0.01285538,-0.05124428,0.0530078,0.07718416,0.01911139,-0.07703857,-0.02298611,0.00647245,0.00720197,-0.01653302,0.00681354,0.00986771,0.02810399,0.02304298,0.04909876,-0.01197875,-0.07177891,-0.00357483,0.06553287,-0.04991283,0.02688584,0.01326016,-0.01413678,-0.04326996,0.02640545,-0.01899277,-0.04949991,-0.02055165,0.01717872,0.04490528,0.05003306,-0.00590642,0.03660987,0.02187901,-0.01248411,0.00002918,-0.02611892,-0.0068724,0.01824532,-0.0067178,0.04056896,0.04058262,-0.03439837,-0.02457481,-0.07205074,0.00944189,-0.08800055,-0.03644623,-0.04194842,0.02269172,-0.05242205,-0.01984642,0.01745477,0.04619548,0.00168283,-0.04010519,0.02708768,0.02678151,-0.02413215,-0.00456866,0.05757348,-0.0626469,-0.0396757,0.08697398,0.00489031,0.05139822,0.04861855,0.01306523,-0.00380599,0.03810256,0.0228999,0.02784105,0.00480207,-0.01414919,0.0294114,-0.03026839,-0.00540964,-0.01966558,0.01878535,0.02175483,-0.09041628,-0.01771436,0.02001561,0.02356184,-0.00496339,-0.03340822,0.02200203,0.01855709,0.03587839,-9.8e-7,0.00616513,-0.00725547,0.04163478,0.00352375,0.01764354,0.02987615,0.07233984,0.02533939,0.03715518,-0.02159282,0.00185157,0.02686773,0.04532383,-0.02761201,-0.03689111,-0.01337181,-0.04247829,0.02608594,-0.02708331,0.03546106,0.04638644,-0.0325949,0.00313039,0.00171891,0.02113889,0.00985731,0.00998734,0.04104237,0.03896585,-0.06097945,0.02207088,0.00770794,-0.03065673,0.03720924,0.00134761,0.0650151,-0.04230548,-0.03227669,-0.00440065,-0.00607468,0.01731565,0.0347327,-0.02683389,0.00069206,0.01702991,0.00921154,-0.06977024,0.02014993,-0.01964943,0.03849764,-0.03678229,-0.01696525,-0.02850987,0.00073092,0.05383153,0.02214481,0.07659137,-0.01153104,0.00623991,-0.05384028,-0.05546779,0.02091014,0.05898832,0.03487424,0.01797394,0.0374951,0.01268946,-0.01576603,0.00542052,-0.04074591,0.065909,0.00947652,-0.04360956,0.04221056,-0.04734685,-0.02191485,-0.0419992,-0.01002952,0.02868926,-0.00488554,-0.01583206,-0.0391323,-0.045697,-0.06907699,-0.0029239,-0.01660286,0.00206912,0.05462047,-0.00710293,0.06423541,-0.00822518,-0.03922336,-0.03036339,0.01567908,0.02749066,0.0185614,0.02159258,0.04381132,-0.04569755,-0.0459779,0.00289773,0.04577696,0.01800773,-0.03233815,0.01421824,-0.04771937,0.01253031,-0.04410018,0.01009751,0.00906751,0.0344209,0.02690604,-0.0581302,-0.03131371,0.01785092,-0.02172819,0.01632118,-0.05399654,-0.00306026,-0.0151535,-0.01691164,0.01372339,-0.05860778,-0.007287,-0.05285804,0.00314423,-0.04465659,0.04789437,-0.03257764,-0.0443692,-0.02324574,0.03951192,-0.05574211,-0.0001329,0.01573665,0.01324412,-0.02051466,-0.02184283,0.0314036,-0.04138004,-0.03430627,-0.03772806,0.02277529,-0.03928283,-0.01713432,-0.00731533,-0.0065436,-0.02861331,-0.03399109,0.04579653,0.00842697,0.00486859,-0.00721159,-0.00264811,0.00914368,0.04222567,-0.01913541,0.02376057,0.07314884,-0.02861362,-0.035076,0.06778591,0.04116329,0.00570336,-0.01272315,-0.04829998,-0.03113249,0.06561439,0.02460381,0.00778846,-0.01294205,0.04745076,0.01681849,-0.05514525,0.01021195,-0.02440555,0.00812294,0.04592337,0.00023224,-0.03394331,-0.00110939,-0.02654267,-0.0234017,0.00394486,0.06060865,0.02629195,-0.04039543,0.02869963,0.01764619,-0.05844451,-0.01667906,0.0172469,0.01923431,0.00349004,0.02513692,-0.02090776,-0.00290929,0.03732601,0.03157044,0.05745228,-0.04974236,-0.02159342,0.00632948,0.05527991,-0.02439327,0.02553455,-0.0475495,-0.07653721,-0.02259996,0.02607749,0.03129418,-0.04950546,-0.0112953,-0.01515718,0.04332218,0.04370147,-0.00115641,-0.01133308,-0.04592397,0.00669974,-0.02596562,-0.02165372,-0.02506638,0.0018672,-0.02497421,0.1119476,0.04505874,0.0043368,-0.04142823,0.02375445,0.00240759,0.00359703,-0.00771287,-0.05724262,0.00000119,0.04235245,-0.01455743,-0.01512726,0.01265701,-0.03429378,-0.02559293,-0.0209696,-0.0159824,-0.0158948],"last_embed":{"hash":"cksduh","tokens":490}}},"text":null,"length":0,"last_read":{"hash":"cksduh","at":1749002744773},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Table","lines":[11,45],"size":1375,"outlinks":[{"title":"matrix","target":"matrix","line":15},{"title":"https://www.baidu.com/","target":"https://www.baidu.com/","line":25},{"title":"https://www.taobao.com/","target":"https://www.taobao.com/","line":26},{"title":"http://c.biancheng.net/","target":"http://c.biancheng.net/","line":27},{"title":"https://www.google.com/","target":"https://www.google.com/","line":28},{"title":"https://github.com/","target":"https://github.com/","line":29},{"title":"https://stackoverflow.com/","target":"https://stackoverflow.com/","line":30},{"title":"http://www.yandex.ru/","target":"http://www.yandex.ru/","line":31},{"title":"https://vk.com/","target":"https://vk.com/","line":32}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#SQL约束 (Constraint)": {"path":null,"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.05096995,0.04634358,-0.00560387,0.0288763,0.0192973,-0.01354609,-0.00273294,-0.02418178,-0.01916395,-0.05674303,-0.0452079,0.07509204,0.03634588,-0.01660348,0.01849414,-0.01984457,-0.03453986,0.0263276,0.01067666,-0.02499603,0.03186505,-0.02935259,0.00963732,-0.00742003,-0.03363898,-0.04029698,0.01870089,-0.02916786,-0.00044137,0.0106998,0.06792346,-0.00020667,-0.0337144,-0.00871991,-0.01678454,0.00779895,0.00302923,0.07960635,0.00877347,-0.01311003,-0.04548641,0.03567483,0.02531198,0.01571175,0.02268931,-0.02817142,0.0544268,-0.00044325,0.0331369,0.03137011,0.0168169,0.01394447,0.05033391,0.02204651,-0.0292845,0.01316731,0.01432108,-0.01835735,0.03430638,0.01404855,0.08057018,-0.03924546,0.03080726,0.05529524,0.00020578,0.01990381,0.03326934,0.00824214,-0.03508778,0.01425369,0.0173343,0.0360199,0.0010389,-0.01753341,0.004976,-0.01514973,-0.00033631,-0.01114357,-0.03289115,0.01909819,-0.02981929,0.02464325,-0.0343512,-0.03723707,0.01333303,-0.02300906,0.04403161,0.02107043,-0.03531237,-0.0090103,0.01524201,-0.02627992,0.0124179,-0.01987836,0.0001985,-0.0061288,0.03910694,0.02473231,0.01174803,0.02482147,-0.02722493,-0.00524031,-0.04287496,-0.03404333,-0.06073371,0.00826997,-0.05230786,-0.03709839,0.01520197,0.02525415,0.01984485,-0.01933204,-0.00006748,-0.07891463,-0.05607156,-0.01685976,0.01558315,0.04929668,-0.00966515,0.05607195,-0.01249536,0.0610108,0.0422525,-0.01105541,0.05937843,0.00873582,0.00737168,0.03072797,0.03061498,0.03528375,0.01607528,-0.00092984,-0.05887166,-0.01336789,0.05240711,0.02383435,0.00826135,-0.03313748,-0.04429935,-0.05216902,0.02777651,-0.01257294,0.02466409,-0.00327836,-0.03974231,0.00625224,-0.01761907,0.02909604,-0.05245817,0.01559647,0.04187038,0.02709933,-0.03539331,-0.01310975,-0.01144383,-0.07280387,-0.04184273,-0.03298323,-0.03732585,0.00131903,-0.05249916,0.08929266,-0.01645726,0.02763627,0.01320981,0.019961,0.00825327,0.01314458,-0.07307055,0.00155279,-0.08895502,0.01728512,-0.00679204,0.02961439,-0.01483949,-0.02636804,0.02506766,0.05512802,-0.00197073,0.02380461,-0.00087451,-0.06581921,0.0509338,0.02702366,-0.03418836,0.03128306,-0.1093053,0.05411194,-0.03424497,0.03650384,0.03547586,-0.01317405,-0.04155422,0.02181192,0.06302047,0.05549066,0.01725502,-0.00913923,0.02089876,0.01927546,0.00710953,-0.01696866,0.00531993,-0.06509105,0.02120718,0.0587404,0.02798098,-0.00462202,0.01712631,-0.01005421,-0.01065477,0.03620543,-0.00647729,0.01744696,-0.02626281,0.00649655,-0.08481458,0.01453006,0.01609574,-0.05240595,-0.04314912,-0.01654122,-0.00842341,-0.04583282,-0.00071041,-0.02410066,-0.01351788,0.01940932,0.02605387,-0.01054895,0.00259399,0.02788467,0.02231466,-0.0149221,-0.01688153,-0.01737871,0.02089421,0.01870055,0.08775106,0.00003617,-0.01082439,-0.01696941,0.04989443,-0.03777637,-0.00517594,-0.01834919,-0.04782943,0.03146586,0.01256101,0.04376615,0.07019914,-0.06581227,-0.0196093,0.01705466,-0.01607068,-0.02149935,0.01879788,-0.0029118,-0.02392097,-0.06733477,0.03014439,-0.02231257,-0.04896615,0.04221041,0.00273502,-0.03560263,0.0412114,0.05942843,0.0105255,0.02539947,0.01546869,-0.06024155,0.0052735,0.07666129,-0.05159048,-0.00963793,0.03357213,0.03152537,-0.01830372,0.04814891,0.00786173,-0.01901876,0.06107689,-0.04491624,-0.07587755,0.05698714,-0.07296959,0.05984814,0.0126323,0.03330568,0.00284113,-0.05616872,-0.05446516,0.03869639,-0.04889181,0.00345641,0.02819343,-0.00603766,0.02637816,-0.04920533,-0.01987294,0.04043792,0.0374516,0.01591482,0.02263866,0.03994756,0.00211976,0.06343833,0.01292193,-0.06823436,0.05364307,-0.00118705,0.028229,0.00647147,-0.00861555,0.08581017,-0.02370348,0.02476173,-0.05159194,-0.01919216,-0.09176502,-0.02182197,-0.07777473,0.01942528,-0.00346757,-0.08373117,0.01316852,0.02282552,0.00790866,0.04383218,-0.02389367,0.02082806,-0.03938707,-0.06487352,0.0485366,0.00713289,0.06848763,-0.05644251,-0.01553844,0.00882772,-0.03452155,0.04076289,0.03940233,-0.00266815,-0.00279478,0.05262148,0.01320644,-0.03083413,-0.06821246,-0.02900022,0.04825681,0.02008206,-0.06708938,0.02860222,-0.07266385,-0.02999849,-0.04641219,0.01092205,0.03685267,-0.01176347,-0.02090705,-0.03938604,0.01260158,0.06454261,0.0567918,0.00750313,-0.01801795,0.01289171,-0.02740081,0.03070999,0.04494463,-0.03156549,0.06715875,-0.00678197,0.03120911,-0.00158801,-0.03985436,-0.0055279,0.06284109,-0.0123738,0.0389717,0.00240148,-0.03344518,0.00128676,0.00391469,-0.02154858,0.05535873,-0.00870546,0.07365387,-0.04063784,0.00899135,-0.00671544,-0.04037702,-0.00078526,0.03221734,0.03568818,0.04203415,0.02673338,-0.03295863,-0.06108532,-0.0076729,-0.05200068,0.07524254,-0.0733064,0.00878217,0.03272623,-0.03372432,0.01651209,-0.03796236,0.01757668,-0.00430249,-0.0216981,-0.07063328,0.02862499,-0.1025833,0.00669619,-0.08357526,-0.04561259,0.00041315,-0.0293045,-0.00581721,-0.06947532,-0.00905927,-0.02376697,-0.00908305,-0.06126745,0.0258022,0.01472358,0.02353228,-0.02952003,-0.03078513,-0.01146588,-0.05888255,-0.02452167,-0.0233518,-0.00889123,-0.01303611,0.0011483,-0.04688982,-0.0175335,-0.01758482,0.03303508,0.00915214,-0.03797818,0.03097493,-0.01513285,0.0129552,-0.01322579,0.03781689,-0.03219475,-0.07622683,-0.02851948,-0.01047857,-0.01313893,-0.01313047,-0.02647663,-0.02642936,-0.06699545,0.00336341,-0.01801477,0.0411741,-0.02756283,0.0193846,0.05735043,0.01698951,-0.01902785,-0.02721006,-0.01565106,0.01338495,-0.01127481,-0.03816495,-0.02550136,-0.03335183,0.00369149,0.04123183,0.11598814,-0.05013704,0.01755588,-0.05628482,0.0457936,-0.00945596,-0.05488598,0.02904407,-0.0240944,-0.04293472,-0.01434238,0.04537159,0.04811354,0.01947441,-0.02267323,-0.01218751,-0.01588116,0.05053532,-0.03597749,-0.00456189,-0.05716569,-0.05114051,0.04026375,-0.00608028,0.02912898,0.02504102,0.02473207,0.00162239,0.07286002,0.0047548,0.03706048,-0.04154481,0.015183,-0.05051804,0.01512804,-0.01769407,-0.07820386,0.04759001,-0.03268993,-0.02237664,0.03160692,-0.02161543,0.03514335,0.02175,-0.009473,-0.01678982,-0.02997515,-0.00754774,0.00692064,0.02258774,-0.06662793,-0.00388555,0.0614315,0.04592274,0.00533894,0.06588043,0.00368039,-0.02102269,0.00660588,-0.03426318,0.04482863,0.04376673,-0.01286601,-0.01441356,0.04731895,-0.03158552,0.03663919,-0.02280785,0.0843171,-0.01714906,-0.0180108,0.03036086,0.01873674,-0.02774507,-0.04066067,0.00127605,0.0067314,0.0744021,-0.05915636,0.02159968,0.00302763,-0.00541717,0.03466878,0.01384881,0.05474455,-0.04729564,-0.01082229,0.05357635,0.00371197,-0.05205986,0.04065022,0.03093339,-0.0241282,-0.06759809,-0.0034885,-0.04601261,0.00903709,0.02353888,0.08878653,-0.04741359,-0.05039874,0.02472028,0.02125491,0.02121459,0.02879607,0.04216464,0.00462512,0.04484893,-0.04766022,0.00036122,0.04971807,0.06589623,0.0408993,0.02102029,0.04560316,-0.0223395,-0.02516613,0.00962393,-0.00071181,0.04808582,0.00070423,-0.0504027,-0.0074252,-0.04246267,-0.01099889,-0.00885975,-0.02853618,0.04641726,-0.02620329,0.04365768,-0.02766277,-0.01260635,-0.05782584,-0.01981701,-0.01459717,0.02019788,0.07785397,0.0019681,0.00584855,0.002328,0.00152038,-0.0290079,0.00298977,0.00596912,0.03196014,-0.00464869,-0.00379795,-0.05282612,-0.02213925,0.00278968,0.03138698,-0.01001555,-0.01245504,-0.01843981,0.00167571,-0.05877124,-0.0395061,0.02330097,-0.01849994,0.01570954,0.01909501,-0.06058662,-0.00241011,-0.02588626,0.04211396,0.00166675,-0.01925671,-0.01283912,0.02004161,0.01286767,0.00620779,-0.00759229,-0.03657034,-0.04625491,0.07446702,-0.02914781,0.03286639,-0.0215874,0.00541689,0.01391537,0.0880006,-0.01558721,0.00946807,-0.00525493,0.02250927,0.01530325,-0.02057429,0.03858744,-0.01426302,0.0177152,-0.0122874,0.00909908,-0.05314824,-0.00595114,0.02244751,0.00116818,0.02733375,-0.02096855,0.04821415,-0.02820576,-0.01306908,-0.02140806,0.02356474,0.00019124,0.0105976,-0.07712422,0.0247554,0.01709189,-0.05182175,0.0103288,0.05205834,0.05992544,-0.01453976,0.03941841,-0.02534525,-0.09187324,0.04813661,0.01886887,0.00438401,-0.00340517,0.02678213,0.00811072,-0.07735006,-0.00369923,0.01953998,0.00775556,-0.00978618,-0.04678366,-0.03224351,-0.03393257,-0.04844701,-0.0127694,0.02343264,0.0668596,0.08529229,-0.0529286,-0.0444372,0.00732144,-0.05896023,0.00059537,-0.00473309,-0.00601458,0.04219898,-0.05904207,0.01961773,-0.04618707,0.04598011,0.03407395,0.05576152,0.02893197,-0.02447573,-0.02434951,0.03077069,-0.02641959,-0.04000745,-0.03873331,-0.04235356,-0.00814284,0.00735968,-0.0044586,-0.03262958,-0.030497,-0.01688505,0.00284186,0.03897616,-0.01617258,-0.00473882,-0.04388574,0.02784146,-0.0251586,-0.01761943,0.02008402,-0.05215311,-0.03495794,0.02251106,0.04391921,-0.00637746,0.01926027,0.08895934,-0.00340667,-0.03458062,-0.00539951,-0.03942145,0.00000101,-0.01367158,0.01372063,0.01003569,-0.02403574,0.02920694,-0.04200735,0.0287915,-0.04728614,0.03605793],"last_embed":{"hash":"1lh7zdd","tokens":373}}},"text":null,"length":0,"last_read":{"hash":"1lh7zdd","at":1749002744835},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#SQL约束 (Constraint)","lines":[104,121],"size":1008,"outlinks":[{"title":"NOT NULL","target":"https://c.biancheng.net/sql/not-null.html","line":10},{"title":"DEFAULT","target":"https://c.biancheng.net/sql/default.html","line":11},{"title":"UNIQUE","target":"https://c.biancheng.net/sql/unique.html","line":12},{"title":"PRIMARY KEY","target":"https://c.biancheng.net/sql/primary-key.html","line":13},{"title":"FOREIGN KEY","target":"https://c.biancheng.net/sql/foreign-key.html","line":14},{"title":"CHECK","target":"https://c.biancheng.net/sql/check.html","line":15},{"title":"INDEX","target":"https://c.biancheng.net/sql/indexes.html","line":16}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#---frontmatter---","lines":[1,4],"size":58,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#简介": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#简介","lines":[5,9],"size":86,"outlinks":[{"title":"SQL语言","target":"SQL语言","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#简介#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#简介#{1}","lines":[6,6],"size":21,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#简介#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#简介#{2}","lines":[7,7],"size":54,"outlinks":[{"title":"SQL语言","target":"SQL语言","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#简介#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#简介#{3}","lines":[8,9],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Table#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Table#{1}","lines":[12,17],"size":174,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Table#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Table#{2}","lines":[18,20],"size":59,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Table#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Table#{3}","lines":[21,23],"size":52,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Table#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Table#{4}","lines":[24,26],"size":59,"outlinks":[{"title":"matrix","target":"matrix","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Table#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Table#{5}","lines":[27,30],"size":47,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Table#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Table#{6}","lines":[31,42],"size":944,"outlinks":[{"title":"https://www.baidu.com/","target":"https://www.baidu.com/","line":5},{"title":"https://www.taobao.com/","target":"https://www.taobao.com/","line":6},{"title":"http://c.biancheng.net/","target":"http://c.biancheng.net/","line":7},{"title":"https://www.google.com/","target":"https://www.google.com/","line":8},{"title":"https://github.com/","target":"https://github.com/","line":9},{"title":"https://stackoverflow.com/","target":"https://stackoverflow.com/","line":10},{"title":"http://www.yandex.ru/","target":"http://www.yandex.ru/","line":11},{"title":"https://vk.com/","target":"https://vk.com/","line":12}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Table#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Table#{7}","lines":[43,43],"size":20,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Table#{8}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Table#{8}","lines":[44,45],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Field / Column": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Field / Column","lines":[46,67],"size":498,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Field / Column#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Field / Column#{1}","lines":[47,47],"size":35,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Field / Column#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Field / Column#{2}","lines":[48,50],"size":111,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Field / Column#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Field / Column#{3}","lines":[51,63],"size":221,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Field / Column#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Field / Column#{4}","lines":[64,64],"size":57,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Field / Column#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Field / Column#{5}","lines":[65,66],"size":48,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Field / Column#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Field / Column#{6}","lines":[67,67],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Record / Row": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Record / Row","lines":[68,72],"size":144,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Record / Row#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Record / Row#{1}","lines":[69,69],"size":36,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Record / Row#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Record / Row#{2}","lines":[70,70],"size":86,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Record / Row#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#Record / Row#{3}","lines":[71,72],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#NULL / 空值": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#NULL / 空值","lines":[73,103],"size":797,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#NULL / 空值#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#NULL / 空值#{1}","lines":[75,75],"size":31,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#NULL / 空值#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#NULL / 空值#{2}","lines":[76,77],"size":64,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#NULL / 空值#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#NULL / 空值#{3}","lines":[78,90],"size":403,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#NULL / 空值#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#NULL / 空值#{4}","lines":[79,90],"size":362,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#NULL / 空值#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#NULL / 空值#{5}","lines":[91,91],"size":35,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#NULL / 空值#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#NULL / 空值#{6}","lines":[92,93],"size":43,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#NULL / 空值#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#NULL / 空值#{7}","lines":[94,94],"size":27,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#NULL / 空值#{8}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#NULL / 空值#{8}","lines":[95,103],"size":174,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#NULL / 空值#{9}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#逻辑模型#NULL / 空值#{9}","lines":[97,103],"size":85,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#SQL约束 (Constraint)#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#SQL约束 (Constraint)#{1}","lines":[106,106],"size":30,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#SQL约束 (Constraint)#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#SQL约束 (Constraint)#{2}","lines":[107,107],"size":36,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#SQL约束 (Constraint)#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#SQL约束 (Constraint)#{3}","lines":[108,110],"size":67,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#SQL约束 (Constraint)#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#SQL约束 (Constraint)#{4}","lines":[111,121],"size":850,"outlinks":[{"title":"NOT NULL","target":"https://c.biancheng.net/sql/not-null.html","line":3},{"title":"DEFAULT","target":"https://c.biancheng.net/sql/default.html","line":4},{"title":"UNIQUE","target":"https://c.biancheng.net/sql/unique.html","line":5},{"title":"PRIMARY KEY","target":"https://c.biancheng.net/sql/primary-key.html","line":6},{"title":"FOREIGN KEY","target":"https://c.biancheng.net/sql/foreign-key.html","line":7},{"title":"CHECK","target":"https://c.biancheng.net/sql/check.html","line":8},{"title":"INDEX","target":"https://c.biancheng.net/sql/indexes.html","line":9}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#需要注意的方面(数据原则)": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#需要注意的方面(数据原则)","lines":[122,145],"size":365,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#需要注意的方面(数据原则)#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md#需要注意的方面(数据原则)#{1}","lines":[124,145],"size":348,"outlinks":[],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md","last_embed":{"hash":"n9gugz","at":1751079981967},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.0973172,-0.0492562,-0.00988989,-0.03042753,-0.00125219,-0.00111335,0.00493904,0.03210568,0.04271434,0.01762144,0.01770304,-0.014333,0.09754674,0.04389488,0.05495001,0.00149179,-0.01271417,0.04541886,-0.00151966,-0.010184,0.03166457,-0.02324345,-0.07259627,-0.04315784,-0.01047216,0.06381116,0.01166462,-0.01640175,-0.02996933,-0.16911317,0.01334284,0.03412356,0.04423567,0.00655352,0.02928475,-0.0596888,0.0099623,0.05816168,-0.03263385,-0.00197442,-0.01796802,0.00469352,0.03469685,0.00372281,-0.00780042,-0.03556611,-0.04434933,0.00369597,0.0164079,-0.02357568,-0.05947193,-0.02138466,-0.05834144,-0.0163496,-0.01847886,0.01409644,0.03849848,0.03381282,0.03181846,-0.01912132,0.0515612,0.01815366,-0.17525226,0.04863996,-0.0065429,0.00479542,-0.04576221,-0.03318573,0.04244939,0.03244253,-0.06051749,0.00078961,-0.02376464,0.06813122,0.05827751,-0.03197272,-0.03613936,-0.05528733,-0.03092669,-0.03845094,-0.02145584,0.07622939,-0.0341277,-0.00130572,0.00457045,0.06858821,-0.04963598,-0.046778,0.00235675,-0.00075631,0.01011556,-0.00824988,-0.01048157,0.01828386,-0.00078766,0.01997772,0.02095509,0.0217471,-0.0393143,0.11281291,-0.06022467,0.03100259,-0.03721159,-0.03964544,-0.03369377,-0.05603848,-0.00990529,-0.03281822,-0.00167431,0.01931906,-0.05154525,-0.04891898,0.05299667,-0.04049559,0.06243326,0.00445516,0.02995197,0.00147298,-0.01756053,-0.06659386,-0.00963841,0.03727618,0.08557787,0.02707498,-0.00216705,-0.04702724,0.03883534,0.05540302,0.01716554,0.04622357,0.06207765,0.00957009,-0.0856807,-0.04615767,-0.01842437,-0.00371379,0.00966342,0.04833297,0.02334347,0.00661757,-0.02753103,-0.08011714,0.00036535,-0.07447036,-0.09611619,0.10507908,-0.07467341,-0.00825006,-0.00914316,-0.02253209,0.03097157,0.05392805,0.02937645,-0.00849622,-0.05354957,0.00644235,0.06759068,0.11480968,-0.03672738,-0.0261304,-0.03945798,-0.01940263,-0.07111046,0.16869721,-0.03294767,-0.07282136,-0.02519595,0.02245282,0.02817167,-0.04092134,0.02480666,-0.01644699,-0.00638135,0.00742696,0.03862331,-0.02486784,-0.0219909,-0.04692608,0.02223896,-0.00906184,0.11710166,-0.00454846,-0.02544883,0.03493961,0.0215436,-0.12163831,-0.05960547,-0.04405719,-0.00908142,-0.05261786,-0.09352456,0.05070426,-0.0423389,0.01645653,-0.03978668,-0.0794664,0.05844842,0.02579399,0.04073266,-0.06388292,0.10328776,0.03352413,-0.01397119,0.03491603,-0.00618316,0.00248272,0.01329568,-0.04013375,0.01485811,0.01899269,0.02806408,0.02455818,-0.04830092,0.01691435,-0.00633954,0.03357574,0.01918121,0.03287603,0.00136431,0.08095522,0.02747184,0.00362061,-0.01638879,-0.21489288,0.00304078,-0.0215453,-0.0531428,-0.03333087,-0.00615875,0.00213884,0.01132473,0.02367213,0.11248018,0.03903237,0.0301958,-0.04727301,-0.02830514,0.0020286,-0.01962343,-0.01165864,-0.00998242,-0.06530318,-0.0499822,0.02551206,0.03062423,0.00333463,-0.02883081,0.0852779,-0.0189222,0.1639967,-0.03579154,-0.0004484,0.07884282,0.05285867,0.00482768,-0.00820787,-0.07833037,0.03123247,-0.01297418,-0.06687273,-0.04974903,-0.01954471,-0.03232901,-0.02379836,-0.01058747,-0.06163393,-0.10562836,-0.01014011,0.01915587,-0.02305081,-0.01613561,0.0445722,0.09766801,-0.01451521,0.01573149,0.03804737,0.01926723,0.00340225,-0.03563359,-0.06867882,-0.04326525,-0.03609984,0.07313147,0.02881922,-0.01694765,-0.02116181,0.01489449,0.00214286,-0.04562949,-0.01582219,-0.02012139,-0.03397627,-0.00774623,-0.03137358,0.16284025,-0.00227661,-0.0318818,0.08101851,-0.00973432,0.0003915,-0.02678138,0.01189943,0.04001593,-0.00669527,-0.0341672,0.0735393,0.02839907,0.00924299,0.00644753,0.04659254,-0.01335013,0.06932303,-0.04582556,-0.06339426,-0.06675947,-0.06179901,0.05860003,0.07439785,-0.02802574,-0.28689837,0.06413225,-0.05631701,0.01520964,0.02543022,0.02883415,-0.00490881,0.01376404,-0.02063956,-0.00730904,0.00653656,0.0190211,0.03410717,-0.05351874,0.0032918,-0.00580318,0.04807075,-0.01005348,0.07798685,-0.01404896,0.01925383,0.05546372,0.18172409,0.03830016,0.06101946,0.01615304,0.00531224,0.04472994,0.00881203,0.09888931,0.01215764,-0.01851311,0.05597648,-0.02627069,0.02070886,0.08902852,-0.06325777,0.06281633,0.03651721,0.00004055,-0.07539894,0.04002633,-0.08932204,0.04699539,0.07867622,0.03425407,-0.02894951,-0.07322731,0.04090529,0.03141604,0.02926616,0.04731809,-0.02332326,-0.01388844,0.03558044,0.02635832,0.03223157,0.0044367,-0.05938476,-0.00610093,0.04042695,0.01079242,0.07771176,0.10398852,0.02694501],"last_embed":{"hash":"911b9f2e9e56c586d81392166ab847d8aa64948c8089ce95a733155a0f15db11","tokens":456}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.06893116,0.01595083,0.00404761,0.04807838,-0.01798203,-0.01196769,0.02048283,-0.04093843,-0.0344785,-0.03731462,-0.00089999,0.04349105,0.05204976,-0.00545696,0.03578536,-0.01032243,-0.00124726,-0.0082544,0.01825065,-0.01811326,0.06061333,-0.06791247,0.02948092,0.03308354,0.01783624,-0.01295794,-0.02163211,-0.04506579,-0.02127095,0.04577839,0.08839197,-0.05278686,-0.02782267,-0.01958106,-0.01565621,0.00453029,0.02811497,0.04447692,-0.03717641,0.00860553,-0.03649118,0.01377382,-0.03211927,0.00650656,-0.03413095,-0.02896104,0.07936022,-0.02335681,-0.02267455,0.02537927,-0.01063505,-0.04614836,0.08342505,0.04006173,-0.02011993,0.00977213,-0.00731035,-0.04160993,-0.02574699,-0.0394218,0.01381437,-0.02464553,0.05040466,0.07495771,-0.05610057,0.0198078,0.01944405,-0.02508824,0.02970206,-0.00268819,-0.0139888,0.00906703,0.00143747,0.05284757,-0.05852593,-0.00320949,0.03533601,-0.02622391,-0.0636182,0.02114972,-0.00504079,0.03987805,-0.02996797,0.02770217,-0.03465354,-0.05650499,0.05091594,-0.07963721,-0.03697847,-0.00167602,0.0516688,-0.01075775,-0.02721914,-0.05895362,0.02923069,0.02352774,0.0276774,0.01213914,0.03300234,-0.01600959,0.03329894,-0.00789665,-0.04138708,-0.04756871,0.01047476,-0.01594448,-0.04374504,-0.00917471,-0.02278055,-0.0167445,-0.00641232,-0.00313598,-0.07119684,-0.05074022,-0.01599718,0.00397733,-0.01073817,0.03495265,-0.03695113,0.05076537,0.00327225,0.06193733,0.03166566,0.02392332,0.02485788,0.04670204,-0.04212219,-0.04682741,0.01091439,-0.05364475,0.00281648,0.05035809,-0.02474124,0.00639568,0.01787207,0.01924359,0.01760816,-0.01439559,-0.03375816,-0.01455216,0.01543567,-0.04266076,0.08567946,-0.01105562,-0.00731488,-0.01357785,-0.02327226,-0.01202595,-0.06916851,0.07484105,-0.0263865,0.02115218,-0.0623601,-0.04703262,-0.04424836,-0.0548091,-0.04782904,-0.04483486,0.0541908,0.00302011,-0.01792233,0.03050476,0.00452586,0.03341829,0.00681209,0.05052076,0.01692587,-0.00725641,-0.04256755,-0.01056294,-0.04512345,0.01398635,0.04644839,0.02053833,0.01103969,-0.01834255,0.02613204,0.05180972,-0.00470211,0.0403203,0.03285123,-0.0431747,-0.00198977,0.00637606,-0.02472807,0.03817323,-0.06152388,0.04125544,0.03216078,-0.00629397,0.04459791,-0.02848785,-0.03502297,0.03842167,0.04013696,0.00636965,0.01433583,-0.07226039,0.04956839,0.00575317,-0.04130307,0.0133228,-0.01870036,-0.07302292,0.05319793,0.01482884,-0.03503362,-0.00823289,0.0333907,-0.03631625,-0.00451903,0.05361965,-0.00221274,0.01524774,-0.04637108,-0.01994991,-0.03535814,0.06874189,0.03107549,-0.05177305,-0.08915648,-0.00200034,-0.02433401,-0.00816311,-0.00406239,-0.05932388,0.00595523,0.0235193,0.04999624,-0.01851374,-0.00362273,0.0002482,0.03349563,0.00091327,-0.04462843,-0.01641658,0.03423389,-0.00359883,0.04760144,-0.02394594,0.0309629,-0.00843558,0.03552082,-0.05838932,-0.03908923,0.02278078,0.00809622,0.00652501,0.05816796,0.038528,0.11454642,-0.01466494,-0.06797177,0.03359222,-0.06868698,-0.04023292,-0.04279902,-0.00118714,0.03051811,-0.01449429,0.04000008,-0.00938976,-0.00843661,0.03940683,-0.00918692,-0.05966849,0.02914293,0.03979196,0.03848853,-0.01515283,-0.00661752,-0.03680114,0.03546419,0.04773089,-0.05878685,0.01549836,-0.00642527,-0.02590162,0.02745537,-0.00623993,0.03248179,-0.02541387,0.03193994,-0.01137653,-0.05684861,0.0801139,-0.02269235,0.04083373,0.03276578,0.04042451,0.03765506,-0.03960564,-0.02432176,0.0458972,-0.03487208,-0.01546631,0.01665567,0.02093153,0.00674201,-0.04312802,0.02032856,0.00776946,-0.00343651,0.00540408,0.03042628,0.07272466,0.00377021,0.03265383,0.04073359,-0.03532435,0.00981636,-0.00605918,0.03179472,0.0111651,0.00795796,0.08836158,-0.01059503,-0.00607293,-0.02305626,-0.02473195,-0.06284682,-0.0121968,-0.07802903,0.00356992,-0.00769259,-0.06715836,0.05215015,0.05491173,0.0049182,-0.00373712,-0.07630278,-0.01836056,-0.04910409,-0.05957432,0.03519568,0.00166435,0.04041224,-0.02246707,0.00268232,-0.01207839,-0.04724307,0.00659221,-0.01367091,0.04554962,0.02397598,0.1101773,0.00927468,0.04106577,-0.04625975,-0.04512308,-0.00476634,-0.01002248,-0.08887282,0.03291171,0.00648024,-0.00992542,0.03030329,0.00178245,-0.00255109,-0.00799075,-0.08481061,-0.04345006,-0.00852938,0.06307796,0.02547576,-0.02487881,0.00999819,-0.01649265,-0.01543588,-0.00142798,0.09755972,0.02244083,0.05940644,0.06219103,-0.05097916,-0.00373361,-0.00096567,-0.00636848,0.11629738,0.00531455,0.01382583,-0.03786457,-0.01766504,-0.04955956,-0.03412953,-0.0266574,0.0036137,0.0006585,-0.05196872,-0.02610609,0.03353951,-0.00613061,-0.04924203,-0.02316803,0.06753239,0.00899383,0.03586017,-0.02511058,-0.01464389,-0.02611923,-0.00023467,-0.02163595,0.02705534,-0.04752215,0.00207428,-0.01736487,-0.05379707,0.02405521,-0.0142527,0.01706606,0.03312492,-0.02692051,-0.04184769,0.06040078,-0.03253919,-0.02351236,-0.01558183,-0.01732426,0.00467183,-0.0382935,0.0076978,-0.0324396,0.02218782,0.00470097,-0.00983865,-0.03575439,-0.02743778,0.01492343,0.02780912,-0.01191037,-0.05428033,0.00649965,-0.08956829,-0.0276433,-0.01604638,-0.06117059,0.06127068,0.04247264,0.02775557,-0.07659804,-0.02854836,0.02555853,0.02032406,-0.02834131,0.02921146,-0.01926378,0.0112564,-0.01826633,0.0425233,0.01305473,-0.04209039,-0.04545854,0.03946111,-0.02978754,0.016026,-0.01959821,0.00146861,-0.0098113,0.03222714,-0.01162579,-0.0232799,-0.01128595,0.0019592,0.06474955,0.06684167,0.01624778,0.03242366,0.03664455,-0.01539242,-0.0101565,-0.0050361,-0.05390282,-0.01019993,-0.01164657,0.05971808,0.01001412,-0.01373219,-0.02849698,-0.06446951,-0.00597963,-0.08823396,-0.05165482,-0.07658523,0.02095815,-0.0556141,-0.0154932,0.02234712,0.0117844,-0.00260446,-0.03749534,-0.00313937,0.01645783,-0.01114185,0.00041031,0.02974842,-0.05456647,-0.04812047,0.06898049,0.00090753,0.04955505,0.02945846,0.03284519,-0.00055541,0.03793536,0.00522687,0.02281031,-0.04080472,-0.0128603,0.03718177,-0.02693981,0.00493588,-0.01902946,0.02594203,0.02705239,-0.05284287,-0.01483872,0.01710006,0.04915455,-0.03466671,-0.02834536,0.03571081,0.00812519,0.01267703,0.00235209,0.01157446,-0.02964437,0.03556253,0.01769601,0.04604886,0.02276761,0.06115528,0.01197793,0.02764863,-0.0396635,0.0035185,0.00848925,0.05689119,-0.01159926,-0.04810099,-0.01145804,-0.03502651,0.02634666,-0.02948206,0.06801655,0.02975182,-0.0352045,-0.00179473,-0.00964556,0.01520136,0.05346645,0.00749365,0.03003794,0.02987908,-0.04818161,0.02463364,0.01648968,-0.04399748,-0.00181425,0.00578179,0.07540473,-0.03866407,-0.0087503,0.05068236,0.00236298,-0.01489107,-0.00209377,-0.02705697,-0.006733,-0.01198888,0.01463629,-0.05856372,0.02590822,-0.01358919,0.0401125,-0.06480646,-0.02637478,-0.04420194,0.00942455,0.04313807,-0.00957545,0.08003286,-0.03177626,0.03679918,-0.06622943,-0.01513808,0.02005284,0.06173008,0.03915351,0.02844377,0.0378777,0.02335187,-0.01595887,-0.02737087,-0.02285652,0.04022267,0.00025172,-0.0801362,0.05055238,-0.01300318,-0.03263728,-0.036154,-0.01678663,0.05981139,0.00269397,0.00675397,-0.03220236,-0.01769846,-0.08005262,0.00546263,-0.03350325,0.014525,0.08060101,0.0066247,0.00017748,-0.01055138,0.00345631,-0.01892536,0.04035403,0.00929308,0.00170994,-0.00728919,0.05095373,-0.02691631,-0.07881881,-0.00720501,0.05475994,0.02100341,-0.04338344,0.0154522,-0.0461235,-0.02560023,-0.03845109,0.00050839,0.01482875,0.00376192,0.04810049,-0.05637724,-0.0360485,0.00553897,-0.01406573,-0.02196606,-0.04014893,0.00374791,-0.01861766,-0.01931822,-0.00546228,-0.07138135,0.03078754,-0.03741608,0.0090798,-0.01472603,0.05546347,-0.0101022,-0.05576556,-0.04567864,0.0376683,-0.04378053,0.00854504,-0.02213264,-0.02127357,-0.02586456,-0.01845368,0.05239583,-0.02247445,-0.06722275,-0.01449644,-0.00232166,-0.00127461,-0.05865123,0.00878218,-0.00645448,-0.03455948,-0.05227537,0.03588085,-0.00549388,-0.0031433,-0.01143621,-0.00816941,0.00622589,0.02059227,-0.03716455,0.01655137,0.07983131,-0.02345557,-0.00182158,0.06347748,0.06102312,0.02444885,-0.03484994,-0.07903423,-0.02833607,-0.00903859,0.02761571,0.02818263,0.00398428,0.05227957,0.01848742,0.00468788,-0.00790291,-0.0074096,0.0055542,0.05543787,0.00533285,-0.01245153,0.00121984,-0.02294133,-0.01035827,0.01222827,0.05655791,0.0217275,-0.04966301,0.02155845,-0.00418538,-0.01965068,-0.01618768,0.01402535,-0.01163854,0.02075904,0.00644398,0.03471337,0.0010258,0.02985875,0.0381277,0.07065012,-0.05152127,-0.01733863,-0.00847788,0.03668084,-0.00100099,0.02666741,-0.0727843,-0.03742534,-0.00738786,0.03893026,0.04940855,-0.05049413,-0.03335305,-0.02216516,0.05126967,0.03668034,-0.01801717,-0.03788946,-0.03660508,0.0052039,-0.00912022,-0.03400423,-0.0073153,-0.00160789,0.00096549,0.08745405,0.03143574,0.01011631,-0.06135354,0.03652101,0.01965944,0.00675799,-0.01098991,-0.03425214,0.00000119,0.01952264,-0.0203687,0.01971896,-0.00026033,-0.05519506,-0.00935389,0.00059571,0.01647651,-0.02526147],"last_embed":{"tokens":1584,"hash":"n9gugz"}}},"last_read":{"hash":"n9gugz","at":1751079981967},"class_name":"SmartSource","outlinks":[{"title":"SQL语言","target":"SQL语言","line":7},{"title":"matrix","target":"matrix","line":25},{"title":"https://www.baidu.com/","target":"https://www.baidu.com/","line":35},{"title":"https://www.taobao.com/","target":"https://www.taobao.com/","line":36},{"title":"http://c.biancheng.net/","target":"http://c.biancheng.net/","line":37},{"title":"https://www.google.com/","target":"https://www.google.com/","line":38},{"title":"https://github.com/","target":"https://github.com/","line":39},{"title":"https://stackoverflow.com/","target":"https://stackoverflow.com/","line":40},{"title":"http://www.yandex.ru/","target":"http://www.yandex.ru/","line":41},{"title":"https://vk.com/","target":"https://vk.com/","line":42},{"title":"NOT NULL","target":"https://c.biancheng.net/sql/not-null.html","line":113},{"title":"DEFAULT","target":"https://c.biancheng.net/sql/default.html","line":114},{"title":"UNIQUE","target":"https://c.biancheng.net/sql/unique.html","line":115},{"title":"PRIMARY KEY","target":"https://c.biancheng.net/sql/primary-key.html","line":116},{"title":"FOREIGN KEY","target":"https://c.biancheng.net/sql/foreign-key.html","line":117},{"title":"CHECK","target":"https://c.biancheng.net/sql/check.html","line":118},{"title":"INDEX","target":"https://c.biancheng.net/sql/indexes.html","line":119}],"metadata":{"aliases":["Relational Database Management System"]},"blocks":{"#---frontmatter---":[1,4],"#简介":[5,9],"#简介#{1}":[6,6],"#简介#{2}":[7,7],"#简介#{3}":[8,9],"#逻辑模型":[10,103],"#逻辑模型#Table":[11,45],"#逻辑模型#Table#{1}":[12,17],"#逻辑模型#Table#{2}":[18,20],"#逻辑模型#Table#{3}":[21,23],"#逻辑模型#Table#{4}":[24,26],"#逻辑模型#Table#{5}":[27,30],"#逻辑模型#Table#{6}":[31,42],"#逻辑模型#Table#{7}":[43,43],"#逻辑模型#Table#{8}":[44,45],"#逻辑模型#Field / Column":[46,67],"#逻辑模型#Field / Column#{1}":[47,47],"#逻辑模型#Field / Column#{2}":[48,50],"#逻辑模型#Field / Column#{3}":[51,63],"#逻辑模型#Field / Column#{4}":[64,64],"#逻辑模型#Field / Column#{5}":[65,66],"#逻辑模型#Field / Column#{6}":[67,67],"#逻辑模型#Record / Row":[68,72],"#逻辑模型#Record / Row#{1}":[69,69],"#逻辑模型#Record / Row#{2}":[70,70],"#逻辑模型#Record / Row#{3}":[71,72],"#逻辑模型#NULL / 空值":[73,103],"#逻辑模型#NULL / 空值#{1}":[75,75],"#逻辑模型#NULL / 空值#{2}":[76,77],"#逻辑模型#NULL / 空值#{3}":[78,90],"#逻辑模型#NULL / 空值#{4}":[79,90],"#逻辑模型#NULL / 空值#{5}":[91,91],"#逻辑模型#NULL / 空值#{6}":[92,93],"#逻辑模型#NULL / 空值#{7}":[94,94],"#逻辑模型#NULL / 空值#{8}":[95,103],"#逻辑模型#NULL / 空值#{9}":[97,103],"#SQL约束 (Constraint)":[104,121],"#SQL约束 (Constraint)#{1}":[106,106],"#SQL约束 (Constraint)#{2}":[107,107],"#SQL约束 (Constraint)#{3}":[108,110],"#SQL约束 (Constraint)#{4}":[111,121],"#需要注意的方面(数据原则)":[122,145],"#需要注意的方面(数据原则)#{1}":[124,145]},"last_import":{"mtime":1731737549659,"size":6684,"at":1748488128912,"hash":"n9gugz"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/工具数据库/网络扫描工具/RDBMS.md"},