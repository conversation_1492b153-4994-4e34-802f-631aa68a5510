"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md","last_embed":{"hash":"1hgbwtd","at":1750993393259},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08077376,-0.01777902,-0.01254177,-0.00612449,0.03045,-0.03268399,0.027998,0.05654181,0.04594906,0.01661911,0.01933315,-0.06527179,0.04194526,0.0687044,0.07041863,0.06308429,-0.03412628,0.02974945,0.00256499,-0.00276143,0.07035189,-0.03927253,0.00981568,-0.04023534,0.00413182,0.03293448,0.01046654,-0.02928478,0.00139297,-0.16752915,0.00620921,0.03281755,0.02465075,0.04615619,-0.01748636,-0.00538613,0.00148207,0.06225132,-0.0320581,0.02232191,0.00899407,0.04479819,0.0177695,-0.01577489,-0.00627269,-0.00346619,-0.02244876,-0.05145404,-0.02277394,-0.01845115,-0.01337666,-0.0239716,-0.0412232,-0.02559722,-0.05567502,-0.00221953,0.02311695,0.01791422,0.05571576,-0.00907493,0.01318304,0.01398348,-0.20850673,0.05769637,0.00275522,-0.03872895,-0.04864587,-0.00304546,-0.01454845,0.04218692,-0.01534486,0.01190337,0.00101009,0.05050859,0.01985273,0.00107773,0.02183261,-0.04320372,-0.01898095,-0.05906733,-0.02137718,0.0387019,-0.0339472,-0.01464907,0.03309531,0.02849973,-0.01730378,-0.01126013,-0.00117559,0.01449028,0.00906508,-0.01208905,0.00777362,0.0288308,0.01686653,-0.01497795,0.0232719,0.07083903,-0.11794183,0.11271449,-0.05401837,0.00278999,-0.03299188,-0.08577681,-0.01129259,-0.05463255,0.01990249,-0.06056519,0.02300641,-0.00975356,-0.09810541,0.00076682,0.07240303,-0.03870812,0.08266792,0.01038505,0.04475413,-0.01076229,-0.01185801,-0.02889962,-0.00871995,0.024853,0.05430625,-0.02038043,-0.04266774,-0.03523246,0.00381186,0.07440671,0.03780744,0.03037159,0.09219126,-0.0155117,-0.05602583,-0.01645635,-0.04245694,-0.02075104,-0.04232527,0.02002417,0.01338769,-0.05735858,-0.01329685,-0.0398047,-0.033428,-0.10576735,-0.08978979,0.0858441,-0.05038697,-0.02269758,-0.01177455,-0.05168222,0.02430023,0.02513973,-0.03450642,0.00566797,-0.0098357,0.01584116,0.13009298,0.14000027,-0.03855549,-0.02428499,-0.00219019,0.02094044,-0.10879237,0.15727542,-0.0052533,-0.06989514,-0.0624817,-0.01942008,-0.00087843,-0.02994772,0.05805416,-0.01093216,0.01888946,0.01153264,0.07200868,-0.01870833,-0.00739704,-0.02401608,-0.00740147,0.04541752,0.1004163,-0.03752675,-0.09140354,0.03975572,0.02683959,-0.06169917,-0.04795675,-0.03755395,0.01855027,-0.0806646,-0.10298872,0.04980064,-0.00984146,0.05257186,-0.06336574,-0.10018463,0.02849809,-0.02073417,0.05042353,-0.04269536,0.0872616,0.0469704,-0.02807462,0.02726185,-0.01146862,0.00450988,-0.01905006,-0.01526783,0.05776061,0.03337532,0.0337519,0.00296637,-0.01179452,0.00893615,-0.02357363,0.03785756,0.02484237,0.00112191,0.02661994,0.04530394,0.02069377,-0.01255454,-0.05086945,-0.22402731,-0.01817678,-0.00094381,-0.04510317,0.0093129,-0.0302783,0.02550698,0.00419803,0.09133971,0.08600603,0.03091143,-0.00455323,-0.07699588,-0.00940278,0.0220069,0.03558815,-0.00476096,-0.00132821,0.00550144,-0.01218662,-0.00301547,0.05986254,-0.00970247,-0.03294383,0.05419247,-0.03455092,0.12516831,0.04470894,0.03201244,0.07393242,0.02937352,0.05730027,0.02716083,-0.10097475,0.01545386,0.02677751,-0.03345161,-0.03585163,-0.01812842,-0.04446869,-0.00969945,0.03051205,-0.06837678,-0.06192813,-0.02454268,-0.03526678,-0.06134015,0.00481766,-0.01380456,0.04106316,-0.02138704,0.00306782,-0.01615411,-0.03616609,-0.02010764,-0.04864969,-0.05817112,-0.01260619,-0.02959984,0.06690602,0.011065,0.01369411,0.03701168,-0.01427847,-0.0029423,-0.03510062,-0.0416914,-0.04075832,-0.07368404,0.03066354,-0.03303755,0.15661421,-0.00280873,-0.05332464,0.06323039,0.01574262,0.00037161,-0.00049122,0.01320212,0.01565348,0.00209628,0.0080755,0.02040647,-0.02006904,-0.02546925,0.02274065,0.02481559,-0.00106131,0.06127342,-0.04961542,-0.04762706,-0.00467245,-0.06563134,0.01815331,0.09860307,-0.02637327,-0.30221659,0.04811158,-0.03805639,-0.01826082,0.05186561,0.07272581,0.02695361,0.02419064,-0.04042669,0.02796752,-0.03925212,0.05011505,-0.01304934,-0.02151152,-0.04193185,-0.01709652,0.03750166,-0.01172404,0.06471919,-0.01964093,0.00166163,0.03775198,0.21350813,-0.01057451,0.01513064,0.02508169,0.00476774,0.02164372,0.04010678,0.04163199,0.0146285,-0.03576062,0.05639862,-0.02020625,0.0062747,0.07491535,-0.08093255,-0.00391113,0.02446893,0.04681034,-0.01845209,0.03731744,-0.08121283,0.03712304,0.08410615,0.02058795,-0.00763553,-0.01645459,-0.00804434,0.06226725,0.03854992,0.0167723,-0.01363322,0.02653866,0.02629214,0.04963073,0.03016323,-0.03859838,-0.04345677,-0.0005814,0.01459973,0.04510867,0.0700459,0.06174408,0.09914179],"last_embed":{"hash":"3433363353d362e1a358952ca9cae9ca01788dbee0d15cf2fe10089ac0387783","tokens":480}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.01359458,-0.02845416,-0.01488588,0.01194843,-0.05683875,0.00078855,0.04337599,0.02943349,-0.02011203,-0.06589823,0.04461497,0.00203884,-0.03422609,-0.01843183,0.00205411,0.03686717,0.07206032,0.03386666,0.01748959,0.01706344,-0.06603319,-0.04065707,0.04389386,0.0048321,0.03619702,-0.06126071,-0.02610887,-0.04459607,-0.03066474,0.02241413,0.05428569,-0.03346022,0.0375589,0.034139,0.00730216,0.02289476,0.05469709,-0.05631993,0.0020119,0.03531211,-0.0319898,-0.01110695,-0.04784022,-0.01343642,-0.08075035,0.01889618,0.00805344,-0.03644232,0.01856706,-0.01941812,-0.00779359,-0.05588728,0.0209609,-0.00645211,-0.01911571,0.0233723,-0.05692542,-0.01145113,-0.00887558,-0.03688408,-0.00426761,-0.05208932,0.06205353,-0.06364953,0.06150025,0.0745948,0.08070109,-0.05478406,-0.0851052,-0.02276904,-0.05334166,-0.07103982,0.06744941,0.02784081,-0.04165073,0.02797368,0.00046135,-0.00196691,-0.01182956,0.02506498,-0.03767105,0.0081013,0.02567538,0.06896293,0.01986823,-0.00734845,0.03935955,-0.01079627,0.00945668,-0.01937279,-0.01775087,-0.03067612,-0.04351369,0.04010091,0.01636451,-0.05831348,0.03630376,-0.00625038,-0.00031795,-0.02182732,-0.06928056,0.02718506,-0.0087069,-0.01988466,-0.00903775,0.04177354,-0.04735107,0.03768151,-0.01075762,-0.01241618,-0.0344316,0.03294004,0.00668465,-0.0865598,-0.10701416,-0.00600886,-0.0139579,-0.00759268,0.04552645,0.03669151,0.07307898,0.02722574,-0.01802996,-0.02779672,-0.03901952,-0.0351831,-0.03627242,-0.0243438,-0.00198915,-0.09294619,0.03456759,0.0046478,-0.04469286,-0.01103154,0.0268828,-0.03303352,0.02177063,-0.08268864,-0.04878819,-0.04144844,0.02256321,0.00305229,0.05295597,0.01305384,-0.02931384,0.04073077,-0.04309126,-0.0160858,-0.02407492,0.05471727,-0.0188802,0.04934233,0.00167149,-0.04385062,-0.04056514,-0.00976287,0.03511243,-0.03765763,-0.03282616,-0.03163618,-0.0275732,-0.0211862,0.00144588,0.00020306,-0.01024672,0.01478241,0.0350086,0.0078364,0.09002038,-0.03623173,0.00821151,-0.02034951,0.01037262,0.00002597,0.0442197,0.03206135,0.03710177,0.05005452,-0.03181445,0.00243267,0.02316951,0.01260679,0.00321877,0.06909555,-0.04807785,0.02599845,0.00206318,-0.00618559,0.01095813,0.00105179,0.00953634,-0.03197569,0.00675219,0.00999104,0.00605738,0.03527258,-0.0296826,-0.03563019,0.01903904,0.01170391,0.02829765,-0.01007964,-0.02782422,-0.09603142,0.07493258,0.02455499,-0.02188462,-0.02478802,-0.07319498,0.05994181,0.01410943,0.00629661,0.094107,0.00367477,-0.00003745,0.05206557,-0.02361116,0.02906299,-0.02361006,-0.00502887,0.01190201,0.05081149,-0.03851112,-0.0221103,-0.00765844,-0.03951893,0.04268612,-0.00822877,0.0380762,0.01683043,-0.00820046,0.15226032,0.01734316,-0.03481219,-0.0644749,0.06461354,-0.00201261,-0.00627449,0.04273346,0.02425522,-0.01626418,0.01194053,-0.01055551,0.00960068,-0.01889727,0.00230102,-0.02779154,0.00020931,-0.00996394,0.02187061,0.07541088,0.01671088,-0.005486,0.05464226,0.04271647,-0.05176796,-0.0246706,0.00477882,-0.03931196,-0.08021382,0.02459597,-0.01321887,0.01693918,0.05589383,-0.02062966,-0.0234469,-0.01742062,0.00420772,0.01019668,0.03141238,-0.01107316,-0.04151992,0.02702161,0.01028646,-0.0150268,0.04844002,-0.0035953,-0.02976395,0.00435816,-0.04038362,0.03991475,0.00555518,0.02089335,-0.00559583,-0.04793397,-0.02469934,0.02164762,0.00069016,0.01268054,0.04341613,-0.01228076,-0.02779054,0.00918126,0.006362,-0.05682276,-0.05776427,-0.00321782,0.02079339,-0.0080597,-0.05746226,0.05135735,-0.05617884,-0.02357722,-0.01995632,0.00260941,0.03097287,0.00232493,-0.0288353,-0.02820376,-0.01136421,-0.042911,-0.04802813,-0.01844668,0.01228945,0.02736606,0.03051849,0.0513397,0.02379578,-0.03966018,-0.0410377,-0.07093284,0.00583334,-0.05714412,0.01505734,0.04898707,-0.04805208,0.03215581,0.00255613,0.04880696,0.02581802,-0.00590941,0.02492114,-0.04827956,-0.03478836,0.00758938,0.02741297,-0.01343547,0.00540536,0.01813453,-0.05816979,0.06939676,-0.12107719,0.03372936,0.01901762,-0.00840448,0.05277889,-0.00078202,0.01565737,-0.0087648,0.00238591,-0.00757365,-0.00891068,0.00520583,-0.00520094,0.01054944,0.04682366,0.00529686,-0.01554804,-0.03795862,-0.02251525,0.00068574,-0.05426458,0.02078725,0.02742044,-0.03771487,0.02298654,0.08739287,0.011474,-0.03029342,-0.01041663,0.01696206,-0.04489163,0.00164675,-0.00288033,-0.01348402,-0.01148377,-0.03345465,0.0103146,-0.04766427,0.0088593,-0.02781066,-0.05773433,0.003839,-0.06313618,-0.02471331,0.01025983,-0.00632682,0.00418406,0.01471096,-0.00754206,0.06821024,0.05201258,0.00552195,-0.02480075,0.0433435,0.00829656,0.03327743,-0.00336653,0.01637411,0.04175019,0.02830346,-0.01600841,-0.01236694,-0.0106687,0.01281654,0.07009712,-0.0430312,-0.01887051,-0.01914611,0.01478157,0.06453731,0.04393476,-0.0649678,0.00736331,-0.02093908,-0.0297274,-0.0064802,-0.07362079,-0.00130662,0.02294687,0.00329207,-0.00651452,0.00879096,-0.00264511,-0.04866115,0.03944609,0.02239572,-0.00124572,0.03615322,0.01107476,0.01979876,-0.02083776,-0.08274806,0.00669365,0.00011502,-0.02789344,0.03599948,-0.01749546,-0.00015706,-0.03373147,-0.01610168,-0.01067321,0.01784934,-0.02588726,-0.00104548,0.00720929,0.02861687,-0.05821289,-0.05259787,0.03536328,0.00004172,-0.00419063,-0.09127511,-0.02547819,0.00049249,-0.0101193,-0.01614946,0.02587847,0.04163308,0.04209422,0.00736339,-0.00540516,-0.04934206,-0.03752312,-0.04363869,0.0322347,0.03169569,0.01016798,-0.07314652,0.01670468,-0.00125971,-0.04230411,0.0153361,-0.03744321,0.03368562,0.01121365,-0.01793198,0.01866379,-0.07896681,-0.0029772,0.01304054,0.01320401,0.00605151,0.04018623,-0.03970617,0.01189711,-0.0399936,0.00145229,0.05752399,-0.05131131,-0.03783144,0.09674226,0.0487536,0.04586649,-0.05449685,0.01115326,0.038625,0.00917424,0.03396189,0.04185089,0.02190897,0.01357013,0.00101489,0.0038538,0.00988105,-0.00726645,-0.05619537,-0.03041258,0.01092913,-0.06447011,-0.01502917,-0.02018238,0.02288745,0.06511395,0.00967258,-0.08110093,0.03713861,0.04281022,0.02603369,-0.01917534,-0.02841175,0.04634433,-0.02795945,0.01870213,-0.00295623,-0.03082403,-0.05203625,0.02384659,0.04865169,-0.00366272,0.01717349,-0.00106933,-0.00596562,-0.01248644,0.00975154,0.00106907,0.02752746,0.00210147,-0.02825083,-0.01643093,-0.0407737,0.02689068,-0.00381414,0.01418995,-0.02290632,-0.01291424,0.00804734,-0.02675468,0.01071597,0.02131385,0.00331051,0.00089153,0.03876946,0.02045153,0.01819791,0.05507783,0.04692544,-0.06372488,-0.00194225,0.02175746,0.0222673,0.06714842,0.01720282,0.05336388,-0.01421939,0.04135207,-0.03595524,0.00053474,-0.02159717,-0.01793899,-0.02305924,0.00583835,0.05593212,0.04276992,-0.08350224,-0.0104464,0.00799881,0.00470567,0.02254635,0.00339084,0.04999441,0.00975653,0.0398774,0.00841066,-0.05814906,-0.00315291,-0.01941306,0.02562418,0.03808332,0.04152357,0.03848367,0.03488423,-0.01629805,0.0278636,-0.00093787,-0.00845389,-0.02886003,0.02232085,0.05972919,-0.00534949,0.02663304,0.00990064,-0.00367904,-0.01202404,-0.02387197,-0.04141189,-0.04293168,0.03149063,0.03726562,0.01616499,0.03590529,0.026712,-0.04410373,-0.05742551,0.03090651,0.08468245,0.10153611,0.00280123,-0.03321743,-0.03107296,-0.04328541,-0.00293805,-0.03158252,-0.03080943,-0.01806885,0.03522025,-0.00394074,0.08034492,0.04342352,-0.05701629,0.00940352,-0.01860562,-0.00427448,0.02801588,0.00044244,-0.00175748,-0.08176718,-0.01399638,0.01997028,0.01094978,0.04459012,-0.01900174,0.01726225,-0.00393359,0.03732255,0.02259427,-0.03917869,0.02934321,0.0246432,-0.0265724,0.0018576,0.00103512,-0.01124165,-0.04674329,-0.01326804,0.01391493,0.05813889,-0.00454011,0.01199507,0.00936102,-0.02164436,-0.02493246,-0.00076739,-0.0082838,-0.07645474,-0.00249875,0.09655436,0.04062615,-0.00705793,0.02493207,0.01401277,-0.02093048,-0.02187183,0.03489019,0.00115535,-0.01554303,0.02254122,-0.00140221,0.02320815,-0.00377949,-0.01490468,-0.02115493,-0.00742468,0.00187602,0.01460386,-0.0541341,0.04801755,0.02859333,-0.02381763,-0.04510711,-0.05891035,-0.07876747,-0.02764416,-0.03371402,0.03712308,-0.01357856,0.03807627,0.01322247,0.04998367,0.01461468,-0.01299836,-0.01633777,0.0343847,-0.04008112,-0.08186621,0.04031483,-0.00242254,0.04483783,0.00361163,-0.00218336,-0.02997427,0.01496896,-0.04962104,-0.02320061,0.04526679,-0.0747774,0.02954895,0.06586128,-0.05011318,0.04779529,0.00441984,0.00253964,-0.01354612,0.01519417,-0.03923937,0.00303038,-0.02789011,-0.0138075,-0.00821984,0.01409576,-0.06199424,-0.06282193,-0.00954873,0.04981326,0.06855921,-0.05036509,0.01435867,0.0154857,0.05524635,-0.05858929,0.06971866,-0.06123843,0.01093166,-0.00874452,-0.02058808,0.03444714,-0.01476407,0.02579198,-0.01654415,-0.00044449,-0.05708202,-0.02029308,-0.05364104,0.06030736,0.02026736,0.0010678,-0.02414843,0.02052067,9.1e-7,0.01883757,-0.02142028,0.03510778,-0.03951418,-0.00333446,-0.00349657,-0.08106401,0.03123784,0.04792837],"last_embed":{"tokens":881,"hash":"1hgbwtd"}}},"last_read":{"hash":"1hgbwtd","at":1750993393259},"class_name":"SmartSource","outlinks":[{"title":"ettercap","target":"ettercap","line":7},{"title":"bettercap","target":"bettercap","line":52},{"title":"#ARP欺骗攻击","target":"#ARP欺骗攻击","line":82}],"metadata":{"aliases":null,"tags":["网络安全/中间人攻击"]},"blocks":{"#---frontmatter---":[1,5],"#简介":[6,18],"#简介#{1}":[7,7],"#简介#{2}":[8,11],"#简介#{3}":[12,13],"#简介#{4}":[14,17],"#简介#{5}":[18,18],"#参数解释":[19,50],"#参数解释#{1}":[21,50],"#实际测试":[51,106],"#实际测试#{1}":[52,52],"#实际测试#{2}":[53,53],"#实际测试#{3}":[54,54],"#实际测试#嗅探模块":[55,67],"#实际测试#嗅探模块#{1}":[56,58],"#实际测试#嗅探模块#{2}":[59,59],"#实际测试#嗅探模块#{3}":[60,64],"#实际测试#嗅探模块#{4}":[62,64],"#实际测试#嗅探模块#{5}":[65,66],"#实际测试#嗅探模块#{6}":[67,67],"#实际测试#ARP欺骗攻击":[68,80],"#实际测试#ARP欺骗攻击#{1}":[69,71],"#实际测试#ARP欺骗攻击#{2}":[72,76],"#实际测试#ARP欺骗攻击#{3}":[74,76],"#实际测试#ARP欺骗攻击#{4}":[77,78],"#实际测试#ARP欺骗攻击#{5}":[79,80],"#实际测试#DNS欺骗":[81,105],"#实际测试#DNS欺骗#{1}":[82,82],"#实际测试#DNS欺骗#{2}":[83,86],"#实际测试#DNS欺骗#{3}":[84,86],"#实际测试#DNS欺骗#{4}":[87,91],"#实际测试#DNS欺骗#{5}":[89,91],"#实际测试#DNS欺骗#{6}":[92,92],"#实际测试#DNS欺骗#{7}":[93,93],"#实际测试#DNS欺骗#{8}":[94,97],"#实际测试#DNS欺骗#{9}":[98,98],"#实际测试#DNS欺骗#{10}":[99,102],"#实际测试#DNS欺骗#{11}":[103,103],"#实际测试#DNS欺骗#{12}":[104,105],"#实际测试#注入脚本":[106,106]},"last_import":{"mtime":1723643446697,"size":3735,"at":1748488128912,"hash":"1hgbwtd"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#参数解释": {"path":null,"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.02365251,-0.01346088,-0.00469698,0.00029832,-0.04811095,-0.00295975,0.01991512,0.02642342,0.01099222,-0.05911488,0.01489556,0.03218104,-0.05375767,-0.02960806,0.00284143,0.05107697,0.06095991,0.0432198,0.00146914,0.02588006,-0.02983825,-0.04151902,0.00564859,-0.01656741,0.02030936,-0.04273598,-0.03432255,-0.05246212,-0.04711876,0.05989609,0.03297008,-0.00789303,0.03160575,0.03192971,0.0066041,0.0073103,0.05236323,-0.03952885,0.02992245,0.0028915,-0.00879865,-0.03960235,-0.03534484,-0.00734577,-0.10609376,0.05721177,0.0079489,-0.02036085,0.04173977,-0.01662398,0.00510384,-0.03236979,0.01636932,-0.00666107,0.01638763,0.04093901,-0.08194971,0.02153893,-0.00988774,-0.05660517,0.00425401,-0.02342008,0.0297446,-0.0522151,0.07594987,0.05930565,0.0870226,-0.04343907,-0.07844359,-0.01129894,-0.05291372,-0.05710672,0.08017373,-0.00881918,-0.06539201,0.02354787,-0.039476,-0.01049858,-0.02937044,0.00271394,-0.02463903,0.02987697,0.03527695,0.02513232,0.05742118,-0.00435549,0.0525549,0.03763648,0.00500776,-0.03258921,-0.01745564,-0.0598116,-0.06224298,0.01532469,0.027391,-0.02512827,0.03452793,-0.02502289,0.00446678,-0.03364943,-0.08417485,0.0340827,-0.05037912,0.00076917,-0.01258256,0.05045233,-0.06760386,-0.01766428,-0.011542,-0.01707754,-0.01526408,-0.00082403,-0.00158281,-0.06583142,-0.10176143,-0.02065318,-0.01118508,0.03021424,0.00701567,0.02945602,0.07278577,0.00667733,0.0196186,0.00087539,-0.11497635,-0.02430377,-0.00329114,-0.03054674,0.01644431,-0.05729315,0.02299331,-0.01004675,-0.03467826,-0.02749318,-0.00529673,-0.01725323,-0.00503351,-0.07703833,-0.0855634,-0.0374545,-0.00518583,0.01219143,0.04202461,0.02194775,-0.00918756,0.04019923,-0.05569437,-0.00035659,-0.02334359,0.03138286,-0.01690624,0.05576883,0.02132268,-0.0122483,-0.03363979,-0.04700675,0.03348316,-0.0249348,-0.01608711,-0.0375675,-0.06752757,0.01421233,0.01318138,0.00807305,-0.02619405,0.05002947,-0.01056319,0.00863553,0.02312355,-0.02692338,0.02984081,-0.025296,-0.00547771,-0.01368417,0.01447959,0.03500611,-0.00284111,0.04154914,-0.01741073,0.00586016,0.02417742,0.01514537,-0.00024807,0.04991722,-0.03290081,0.06555554,-0.00633666,0.00267129,-0.01691399,0.01161539,-0.00619141,-0.00745247,-0.01430426,0.03275849,0.03900865,0.04533947,-0.04705896,-0.01070738,0.00893557,0.01289994,0.03510881,-0.02948821,-0.04418972,-0.09944042,0.060348,0.02227991,-0.0022061,-0.05244986,-0.1011886,0.04746175,0.03429396,0.02741847,0.10246578,0.01531565,-0.00250936,0.01488583,-0.0307936,0.01542909,0.00847276,-0.00310762,0.04935405,0.03373855,-0.0005399,-0.02732512,0.00758121,-0.01267517,0.04757069,-0.02147413,-0.0067145,0.00634589,-0.02709952,0.13511929,-0.01206768,-0.00404654,-0.05502167,0.09333588,0.02606956,-0.010688,0.00620807,0.00939024,-0.01630646,-0.01315673,0.02308418,-0.02657155,-0.04637164,-0.01146498,-0.00454,0.01016398,0.02344589,0.0409687,0.08820955,-0.01909574,-0.02140527,0.03155121,0.03954192,-0.03848521,-0.01143598,0.01554586,-0.0417094,-0.0519219,0.05788316,0.00249718,0.01481138,0.06311902,0.02385637,0.03787966,0.00722144,0.01875145,0.02125975,0.01982627,0.03655587,-0.03776869,0.01880274,0.01935431,-0.05316026,0.01335038,0.03391201,-0.01712731,0.00034794,-0.03645781,0.08081991,0.0189645,0.04327094,-0.00201696,-0.01263942,-0.00684466,0.02455459,0.00963664,-0.00017475,0.02803024,0.01154342,-0.0579457,-0.01518879,0.01690152,-0.06791481,-0.02912019,-0.03413806,-0.00304487,0.02887833,-0.04479039,0.01393075,-0.03396956,-0.03118636,-0.01423682,0.00426229,0.0586152,0.00169918,-0.0219187,-0.01113075,-0.04564945,-0.01923881,-0.04477752,-0.03710508,-0.01129299,0.01777713,0.04059438,0.01774599,0.01449488,-0.02986259,-0.05823933,-0.0973722,0.06166485,-0.09095786,0.03495466,0.02035831,-0.07999332,0.0389622,0.0068479,0.0248968,0.02662344,-0.00674997,0.01934428,-0.04108983,-0.01342641,0.03283644,0.02777654,0.01553138,-0.0110089,0.03962831,-0.01151943,0.04394504,-0.07950514,0.03706638,0.0145479,0.02800513,0.02108284,0.02973681,0.00390853,0.02636932,-0.00206492,0.03280167,-0.01697986,0.00133482,0.01198736,0.00780715,0.03509281,-0.00907666,-0.01598964,-0.01042283,-0.0435277,-0.00955605,-0.06377633,0.01234822,0.02930636,0.00803881,0.01574879,0.03250899,0.04826388,-0.02128526,-0.03652512,0.00102997,-0.04843844,-0.01642391,-0.0635573,0.01292181,-0.00458905,-0.06388183,0.04412568,-0.04716994,0.00607504,-0.04304026,-0.0315376,0.01210835,-0.01539041,0.00836215,0.00471018,-0.02850277,-0.03715917,0.02513566,-0.0141112,0.03004205,0.04738409,-0.0037676,-0.04275287,0.05206383,-0.00055105,0.02703604,0.03201073,0.01532201,0.01058471,0.00663269,0.00290675,-0.02915502,-0.00800621,-0.00350689,0.04672297,-0.024193,0.00043483,-0.00623894,-0.0034624,0.00826003,0.04451688,-0.05384833,-0.00780904,-0.02805384,0.00176135,-0.0219709,-0.04957302,-0.04788457,-0.00650177,0.01334589,-0.03611465,-0.03627993,-0.00960536,-0.03934309,0.00280058,0.05238207,0.01207025,0.00301621,0.02978684,0.02223196,-0.03834109,-0.05443999,-0.00662343,0.01612934,-0.01903478,0.01335466,-0.03141811,-0.04274013,-0.03356609,0.00529339,-0.00610125,0.00668435,-0.00931371,-0.01016788,0.02340241,0.03752866,-0.07109188,-0.00994732,0.03219024,-0.00668304,-0.02588489,-0.07399686,-0.00529249,0.01961802,-0.0029437,-0.01294534,-0.00866683,0.03568374,0.00890855,0.02670904,0.03490018,-0.03633453,-0.02143721,-0.04201177,0.0217049,0.04953634,0.05096153,-0.05016791,-0.03576755,-0.00330803,-0.05524424,0.0173493,0.00050009,-0.01362063,0.02886238,-0.02807061,0.03384553,-0.05440693,0.00110555,-0.00476232,0.05062696,0.02326522,0.02979413,-0.00543193,0.01670576,-0.05497405,0.03759537,0.03157365,-0.01610878,-0.00445859,0.05025886,0.04050447,0.02720217,-0.09943117,0.00936533,-0.00013332,0.00039303,0.00831931,0.03290995,-0.02674109,0.02388461,-0.00311836,0.00623165,0.00464918,0.01822941,-0.01418015,-0.011365,-0.00104731,-0.04569852,-0.04769653,-0.00355762,0.03237639,0.05657606,-0.00360544,-0.05856471,0.0184139,0.03008537,0.04107411,-0.02302674,-0.00442718,0.06258926,-0.04660308,0.01609302,0.00028768,-0.035956,-0.0401925,-0.01090658,0.05174435,0.00829856,0.06815802,0.00825032,-0.01981529,0.00620825,0.00814074,0.00075347,0.03702288,0.00216045,-0.02510792,0.01381651,-0.06063579,0.01787856,-0.03494068,0.04266281,0.03122067,-0.03367572,0.01688273,-0.01816453,0.00817124,0.01320335,-0.00227184,-0.03309892,0.03546859,0.04897707,0.03285499,0.05209095,0.04415309,-0.06244573,0.00476667,0.03881693,0.0228939,0.04486642,0.03007725,0.05285105,0.00847442,0.06641246,-0.01979788,0.01742321,-0.00213862,-0.04058882,-0.00491564,0.00549243,0.05788472,0.01242243,-0.0602021,0.02030979,-0.02579085,0.01833676,0.00430524,-0.01679908,0.05451934,-0.00543386,0.07079858,-0.00150956,-0.07373947,0.00250858,-0.00281106,0.01792144,-0.01666185,0.03696538,0.02488411,0.02099443,-0.02225479,-0.00461355,-0.01362254,0.01533941,-0.02044724,0.01255607,0.02197963,-0.01375888,0.02913184,0.04194159,-0.04353592,-0.03913626,0.00911731,-0.00779358,-0.04592212,-0.00843467,0.00996819,-0.00292814,0.04015442,-0.01139245,-0.04045187,-0.05163711,0.02723593,0.03826653,0.04097513,-0.01465059,-0.01590982,0.01035803,-0.03271132,0.00020586,-0.03428786,-0.04229178,-0.03103678,-0.00825836,0.01681393,0.05338312,0.02230524,-0.03134093,0.04531565,-0.04615361,-0.01386728,0.03280345,0.01904346,-0.05972414,-0.05947541,0.02172921,0.03340005,0.02633217,0.05595883,0.00742559,0.03106013,0.01801648,0.02514543,0.04531162,-0.0359438,0.03035891,-0.00002163,-0.03185126,-0.0031451,-0.00565772,-0.04024102,-0.00864897,0.0270786,0.01015826,0.05916004,-0.00702539,-0.00179272,0.04059092,-0.0240347,-0.00469521,0.00258394,0.00383706,-0.08622315,0.00469027,0.15132888,0.032784,0.03698656,0.03301214,0.0545167,-0.02345233,-0.03753401,-0.00188412,-0.00751318,-0.01854516,0.00244122,0.00934883,0.00515836,-0.01495381,0.03103779,-0.06921805,0.04265191,-0.02300247,0.0366738,-0.05166789,0.04164977,0.03694524,0.0174038,-0.02032587,-0.08365632,-0.03097453,-0.03404826,-0.03877282,0.0142837,-0.01986548,0.03454331,-0.0036307,0.03532224,-0.02197807,0.01475682,-0.04981543,-0.00816512,-0.06931446,-0.06269985,0.04689434,-0.00366619,0.03939361,0.0199368,0.00250257,-0.03706389,0.00160428,-0.01929469,-0.04455276,0.05622,-0.11577973,0.02985864,0.08846794,-0.06987114,0.029527,-0.00071517,0.07504396,-0.0230674,-0.01073187,-0.02935304,-0.02931737,-0.02074433,-0.00225417,0.01344819,0.03134076,-0.09366514,-0.0826361,0.00137378,0.03998527,0.0349015,-0.04114594,0.04529053,0.02355671,0.04153841,0.00531477,0.04076752,-0.04874426,0.00426214,-0.02666105,0.01859384,0.03581306,0.00423356,0.00534898,-0.00442389,0.00994342,-0.03748781,-0.01439789,-0.01452848,-0.00205874,-0.00307629,0.01049028,-0.01426967,0.01191122,9.6e-7,0.01430282,-0.06174219,0.04331618,-0.05145659,0.00468009,-0.01521596,-0.08386894,0.01510198,0.04127834],"last_embed":{"hash":"1gibuuw","tokens":406}}},"text":null,"length":0,"last_read":{"hash":"1gibuuw","at":1749002745191},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#参数解释","lines":[19,50],"size":1310,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#参数解释#{1}": {"path":null,"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.02612929,-0.01369557,-0.00607653,-0.00136155,-0.04684106,-0.00745633,0.01200704,0.0177706,0.01331501,-0.0583042,0.01880179,0.03594246,-0.05424041,-0.02925532,-0.00498375,0.05399572,0.0590679,0.04501197,0.00011195,0.02209092,-0.02612535,-0.03676223,0.00355524,-0.01682684,0.01704493,-0.04576624,-0.02932638,-0.05853931,-0.04978667,0.06350527,0.02926867,-0.00357317,0.02347304,0.03309731,-0.00194857,0.00418061,0.05048054,-0.03744057,0.03816935,-0.00155597,-0.00847594,-0.0455451,-0.0350843,-0.00735073,-0.11006068,0.05399869,0.00982173,-0.0188985,0.04210318,-0.0147909,0.00704176,-0.03086705,0.02007257,0.00036661,0.01595473,0.04308172,-0.08316913,0.02470278,-0.01180453,-0.05818251,0.00030398,-0.02345312,0.02932163,-0.04575323,0.07058416,0.0582375,0.08652575,-0.04389649,-0.07244081,-0.01036271,-0.04994728,-0.0592874,0.07909504,-0.01014117,-0.06292082,0.0242604,-0.04183266,-0.01251485,-0.02930304,0.00151258,-0.02241939,0.03006499,0.0383038,0.01972252,0.0584458,-0.00434355,0.04808132,0.03945229,0.00231015,-0.03302898,-0.0240558,-0.06150501,-0.05906756,0.01356435,0.02657434,-0.01824087,0.03263032,-0.02383843,0.00750252,-0.0360271,-0.08583014,0.02781748,-0.05374482,0.00195229,-0.01454589,0.04998872,-0.06624845,-0.02272728,-0.01234865,-0.02082721,-0.01359177,-0.00303529,-0.00501765,-0.06635778,-0.10392448,-0.01896314,-0.01461835,0.02945324,0.00446682,0.02952426,0.07300272,0.00292687,0.02199088,0.00336453,-0.11077373,-0.01903418,-0.00183519,-0.03164872,0.02039406,-0.05553389,0.02588015,-0.00980228,-0.03384471,-0.02829742,-0.00194727,-0.01147015,-0.00180434,-0.07277978,-0.08940499,-0.03960041,-0.00384321,0.00570574,0.04205203,0.02271665,-0.00557829,0.04153333,-0.05402253,-0.0007288,-0.02302963,0.02836418,-0.01134703,0.05107384,0.02033327,-0.01036926,-0.03648704,-0.04654127,0.03530985,-0.02067153,-0.01159813,-0.03610114,-0.0693176,0.01666422,0.01560093,0.00944347,-0.02164438,0.04850667,-0.01910505,0.0097175,0.01992667,-0.02444909,0.02780671,-0.02210818,-0.00993431,-0.01825161,0.01306393,0.02880162,-0.00855297,0.04200441,-0.01206127,0.00350873,0.0222242,0.01350001,-0.00814664,0.04893607,-0.03206089,0.07248557,-0.00832662,0.00956788,-0.01775876,0.01323685,-0.00698541,-0.00233755,-0.00864455,0.03226994,0.03961871,0.04181926,-0.05500471,-0.01469383,0.0061893,0.00817626,0.03666007,-0.03289187,-0.04165034,-0.09972079,0.06304667,0.02197464,-0.00498155,-0.05590116,-0.09634328,0.04346418,0.03930348,0.02718688,0.09856848,0.01363124,-0.00134082,0.01579533,-0.03108015,0.01162798,0.01485264,-0.0054714,0.0513684,0.03735681,0.00028284,-0.02381875,0.00794586,-0.01012571,0.04820433,-0.01966377,-0.01400555,0.00403496,-0.0262394,0.12819068,-0.01255375,-0.00660613,-0.05180461,0.09369162,0.03130056,-0.01023314,0.00397314,0.00425207,-0.01698623,-0.01559461,0.02493478,-0.03416565,-0.04302653,-0.00985448,-0.00786018,0.00838717,0.02779269,0.0501835,0.09313086,-0.01969616,-0.02596336,0.03112294,0.04228076,-0.03415137,-0.00858893,0.01543423,-0.04313118,-0.05058911,0.06006799,-0.00007333,0.01728685,0.06136658,0.02613142,0.03973897,0.01086991,0.02177458,0.02437963,0.02093973,0.03650491,-0.03524846,0.01898706,0.01749111,-0.05836447,0.00727003,0.03378253,-0.01150943,-0.00043896,-0.03520062,0.08000361,0.01860715,0.0411418,0.00251839,-0.01288003,-0.00720841,0.02098569,0.01109873,0.00071592,0.03293499,0.01558485,-0.05759884,-0.01845016,0.01688585,-0.06162031,-0.0231534,-0.03823429,-0.00218434,0.03251797,-0.04219614,0.01112061,-0.02703915,-0.03551814,-0.01795936,0.00737419,0.059425,0.00177174,-0.01935549,-0.00767891,-0.04670575,-0.01316249,-0.04358135,-0.03650668,-0.00799553,0.01543007,0.04399,0.00719882,0.01171487,-0.0311482,-0.06504884,-0.09844872,0.06689471,-0.08979669,0.03328396,0.01540077,-0.08081009,0.03715263,0.00799875,0.01490114,0.02708799,-0.00703086,0.02048094,-0.03522519,-0.0156186,0.03016199,0.02628604,0.01824337,-0.01296653,0.04379707,-0.00043828,0.0449384,-0.07844874,0.04002348,0.00950383,0.03411721,0.01844048,0.03078831,0.00250463,0.03401972,-0.00043923,0.02856533,-0.01723556,0.00261933,0.01250219,0.00774044,0.03207508,-0.00944419,-0.01212408,-0.00844893,-0.04899343,-0.01405641,-0.06286776,0.00609062,0.0308463,0.01671051,0.01359089,0.02588755,0.04871947,-0.0235646,-0.04189366,0.00207107,-0.04239363,-0.01704786,-0.06708013,0.01489882,-0.00408805,-0.06264852,0.04598507,-0.04598357,0.00343393,-0.03958673,-0.0286536,0.00618204,-0.00994197,0.01172193,0.00130973,-0.02890793,-0.03622835,0.02202342,-0.00942114,0.0281741,0.04891969,-0.00604803,-0.0420563,0.0537734,0.00056454,0.02781456,0.03611787,0.0149888,0.00710114,0.00528808,0.00339466,-0.03364812,-0.00670453,-0.00927955,0.04108561,-0.02159814,0.00163197,-0.0068266,-0.00464898,0.00231942,0.04984005,-0.05374508,-0.00259997,-0.0199696,0.00251421,-0.0207745,-0.04873515,-0.0552178,-0.0081782,0.01699762,-0.04147159,-0.03878149,-0.00573342,-0.03902509,-0.00101845,0.05198518,0.01264952,-0.00138726,0.03293304,0.02199057,-0.03904649,-0.05459959,-0.00919151,0.01827537,-0.01594923,0.01382833,-0.0295755,-0.04094355,-0.03188728,0.00999114,-0.0026271,0.00856376,-0.00392377,-0.01239637,0.02419049,0.03167059,-0.06881581,-0.00672486,0.03246185,-0.00430282,-0.02542855,-0.07351186,0.0034811,0.01865749,0.00003007,-0.00789825,-0.01270008,0.0370586,0.00531896,0.02499118,0.03576647,-0.03540013,-0.01267992,-0.03966054,0.01894927,0.05325183,0.05137287,-0.05158425,-0.03983298,-0.00254997,-0.05568906,0.01555189,-0.00328348,-0.01891236,0.02757555,-0.03130292,0.02939581,-0.05607455,0.0040312,-0.01019933,0.05733092,0.02131801,0.03233118,-0.00283853,0.0182115,-0.05006089,0.0428468,0.0312947,-0.0126431,0.00181095,0.04958078,0.04055613,0.02418524,-0.10307124,0.01330957,-0.00495495,-0.000136,0.00861691,0.02938301,-0.03119927,0.02699175,-0.00457927,0.01016461,0.00254016,0.0205496,-0.00959848,-0.00597091,-0.00475088,-0.03907058,-0.04997197,-0.00485103,0.03752485,0.04909326,-0.01053195,-0.05790467,0.01458458,0.03231345,0.03841749,-0.02460756,-0.00712891,0.06551615,-0.04787463,0.01692431,0.0025079,-0.03627094,-0.03911926,-0.01318709,0.0510644,0.00205247,0.06306559,0.00685879,-0.0192063,0.00595816,0.00603934,0.00438623,0.03691712,-0.00189989,-0.01971322,0.01682729,-0.05741916,0.0108298,-0.03283741,0.0430822,0.03620423,-0.0378795,0.01648284,-0.01581778,0.01098396,0.01013615,-0.00410972,-0.03396611,0.03424406,0.05376406,0.03139281,0.04975885,0.04572841,-0.06305365,0.00684472,0.03701428,0.02268405,0.04408488,0.03066462,0.05316158,0.01005923,0.06816689,-0.01747994,0.01848794,0.00001603,-0.03981474,-0.00731042,0.00645631,0.05932247,0.01356525,-0.05224334,0.01839219,-0.02718533,0.02057447,0.00104089,-0.01368551,0.04773385,-0.00990787,0.06432265,0.00022937,-0.07789561,0.00409774,-0.00206396,0.02327098,-0.01985347,0.03501922,0.01627715,0.01998256,-0.02593991,-0.00405847,-0.01513095,0.01236733,-0.02612018,0.00881663,0.01561971,-0.0180894,0.02767802,0.04043494,-0.04978795,-0.04223003,0.01179325,-0.00708019,-0.04345942,-0.0103253,0.00938605,-0.00256981,0.04105939,-0.01532933,-0.03722756,-0.0559385,0.02441548,0.03715915,0.03544026,-0.01700082,-0.01577621,0.01461429,-0.03306641,0.00395058,-0.03067443,-0.0468482,-0.03525682,-0.0139096,0.01933718,0.04856043,0.02228195,-0.02595081,0.04424437,-0.05046441,-0.01076152,0.03663442,0.01984715,-0.06026607,-0.06023644,0.02976207,0.03247835,0.02377866,0.05571982,0.01154454,0.03519237,0.01715212,0.02024407,0.0461662,-0.03474811,0.03420009,0.00167336,-0.03607105,-0.00227183,-0.00808784,-0.04579686,-0.00460929,0.02648689,0.01597982,0.05723942,-0.01106307,-0.00280738,0.04472563,-0.02084767,0.00075804,0.00452691,0.00109573,-0.0787884,0.00256205,0.15199836,0.03219946,0.04050822,0.02764581,0.05580013,-0.02082925,-0.03740191,-0.00177871,-0.00751442,-0.0173491,-0.00007373,0.00952466,0.00371594,-0.01318726,0.04317585,-0.07191946,0.04569409,-0.03061768,0.03526803,-0.05054587,0.03798892,0.03815328,0.01644657,-0.01625133,-0.08263607,-0.02875691,-0.03567766,-0.03810017,0.01162011,-0.01898028,0.03469211,-0.00924796,0.03114997,-0.02344579,0.0176082,-0.05752268,-0.01131457,-0.07121214,-0.0590256,0.04313675,-0.00233823,0.04051681,0.01740813,0.00226648,-0.03916912,0.00216073,-0.01546022,-0.04898205,0.05882358,-0.11634997,0.03020347,0.08487601,-0.07926489,0.02902734,0.00063396,0.07865197,-0.0246128,-0.01286066,-0.02442784,-0.03047768,-0.01648159,0.00187662,0.01721874,0.03323697,-0.0893005,-0.08015315,0.0094802,0.03583657,0.03277166,-0.03612131,0.04708895,0.02384663,0.03953144,0.01141513,0.03834439,-0.046121,0.00257934,-0.02606963,0.0158397,0.03739376,0.00769984,0.00429336,-0.00137471,0.01692782,-0.03245534,-0.00921663,-0.009588,-0.00383455,-0.00862703,0.01014944,-0.01667445,0.00815239,9.7e-7,0.00974254,-0.06335273,0.05192116,-0.0506821,0.00137508,-0.016857,-0.08371814,0.00870547,0.04049545],"last_embed":{"hash":"ontsjv","tokens":406}}},"text":null,"length":0,"last_read":{"hash":"ontsjv","at":1749002745239},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#参数解释#{1}","lines":[21,50],"size":1302,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#---frontmatter---","lines":[1,5],"size":38,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#简介": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#简介","lines":[6,18],"size":270,"outlinks":[{"title":"ettercap","target":"ettercap","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#简介#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#简介#{1}","lines":[7,7],"size":28,"outlinks":[{"title":"ettercap","target":"ettercap","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#简介#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#简介#{2}","lines":[8,11],"size":123,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#简介#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#简介#{3}","lines":[12,13],"size":26,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#简介#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#简介#{4}","lines":[14,17],"size":81,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#简介#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#简介#{5}","lines":[18,18],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试","lines":[51,106],"size":706,"outlinks":[{"title":"bettercap","target":"bettercap","line":2},{"title":"#ARP欺骗攻击","target":"#ARP欺骗攻击","line":32}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#{1}","lines":[52,52],"size":34,"outlinks":[{"title":"bettercap","target":"bettercap","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#{2}","lines":[53,53],"size":24,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#{3}","lines":[54,54],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#嗅探模块": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#嗅探模块","lines":[55,67],"size":126,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#嗅探模块#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#嗅探模块#{1}","lines":[56,58],"size":30,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#嗅探模块#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#嗅探模块#{2}","lines":[59,59],"size":11,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#嗅探模块#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#嗅探模块#{3}","lines":[60,64],"size":54,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#嗅探模块#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#嗅探模块#{4}","lines":[62,64],"size":20,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#嗅探模块#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#嗅探模块#{5}","lines":[65,66],"size":16,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#嗅探模块#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#嗅探模块#{6}","lines":[67,67],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#ARP欺骗攻击": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#ARP欺骗攻击","lines":[68,80],"size":126,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#ARP欺骗攻击#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#ARP欺骗攻击#{1}","lines":[69,71],"size":47,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#ARP欺骗攻击#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#ARP欺骗攻击#{2}","lines":[72,76],"size":54,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#ARP欺骗攻击#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#ARP欺骗攻击#{3}","lines":[74,76],"size":24,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#ARP欺骗攻击#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#ARP欺骗攻击#{4}","lines":[77,78],"size":7,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#ARP欺骗攻击#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#ARP欺骗攻击#{5}","lines":[79,80],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#DNS欺骗": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#DNS欺骗","lines":[81,105],"size":372,"outlinks":[{"title":"#ARP欺骗攻击","target":"#ARP欺骗攻击","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#DNS欺骗#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#DNS欺骗#{1}","lines":[82,82],"size":27,"outlinks":[{"title":"#ARP欺骗攻击","target":"#ARP欺骗攻击","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#DNS欺骗#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#DNS欺骗#{2}","lines":[83,86],"size":67,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#DNS欺骗#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#DNS欺骗#{3}","lines":[84,86],"size":46,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#DNS欺骗#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#DNS欺骗#{4}","lines":[87,91],"size":88,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#DNS欺骗#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#DNS欺骗#{5}","lines":[89,91],"size":46,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#DNS欺骗#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#DNS欺骗#{6}","lines":[92,92],"size":19,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#DNS欺骗#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#DNS欺骗#{7}","lines":[93,93],"size":26,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#DNS欺骗#{8}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#DNS欺骗#{8}","lines":[94,97],"size":46,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#DNS欺骗#{9}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#DNS欺骗#{9}","lines":[98,98],"size":37,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#DNS欺骗#{10}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#DNS欺骗#{10}","lines":[99,102],"size":29,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#DNS欺骗#{11}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#DNS欺骗#{11}","lines":[103,103],"size":11,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#DNS欺骗#{12}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#DNS欺骗#{12}","lines":[104,105],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#注入脚本": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md#实际测试#注入脚本","lines":[106,106],"size":7,"outlinks":[],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md","last_embed":{"hash":"1hgbwtd","at":1751079979219},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08077376,-0.01777902,-0.01254177,-0.00612449,0.03045,-0.03268399,0.027998,0.05654181,0.04594906,0.01661911,0.01933315,-0.06527179,0.04194526,0.0687044,0.07041863,0.06308429,-0.03412628,0.02974945,0.00256499,-0.00276143,0.07035189,-0.03927253,0.00981568,-0.04023534,0.00413182,0.03293448,0.01046654,-0.02928478,0.00139297,-0.16752915,0.00620921,0.03281755,0.02465075,0.04615619,-0.01748636,-0.00538613,0.00148207,0.06225132,-0.0320581,0.02232191,0.00899407,0.04479819,0.0177695,-0.01577489,-0.00627269,-0.00346619,-0.02244876,-0.05145404,-0.02277394,-0.01845115,-0.01337666,-0.0239716,-0.0412232,-0.02559722,-0.05567502,-0.00221953,0.02311695,0.01791422,0.05571576,-0.00907493,0.01318304,0.01398348,-0.20850673,0.05769637,0.00275522,-0.03872895,-0.04864587,-0.00304546,-0.01454845,0.04218692,-0.01534486,0.01190337,0.00101009,0.05050859,0.01985273,0.00107773,0.02183261,-0.04320372,-0.01898095,-0.05906733,-0.02137718,0.0387019,-0.0339472,-0.01464907,0.03309531,0.02849973,-0.01730378,-0.01126013,-0.00117559,0.01449028,0.00906508,-0.01208905,0.00777362,0.0288308,0.01686653,-0.01497795,0.0232719,0.07083903,-0.11794183,0.11271449,-0.05401837,0.00278999,-0.03299188,-0.08577681,-0.01129259,-0.05463255,0.01990249,-0.06056519,0.02300641,-0.00975356,-0.09810541,0.00076682,0.07240303,-0.03870812,0.08266792,0.01038505,0.04475413,-0.01076229,-0.01185801,-0.02889962,-0.00871995,0.024853,0.05430625,-0.02038043,-0.04266774,-0.03523246,0.00381186,0.07440671,0.03780744,0.03037159,0.09219126,-0.0155117,-0.05602583,-0.01645635,-0.04245694,-0.02075104,-0.04232527,0.02002417,0.01338769,-0.05735858,-0.01329685,-0.0398047,-0.033428,-0.10576735,-0.08978979,0.0858441,-0.05038697,-0.02269758,-0.01177455,-0.05168222,0.02430023,0.02513973,-0.03450642,0.00566797,-0.0098357,0.01584116,0.13009298,0.14000027,-0.03855549,-0.02428499,-0.00219019,0.02094044,-0.10879237,0.15727542,-0.0052533,-0.06989514,-0.0624817,-0.01942008,-0.00087843,-0.02994772,0.05805416,-0.01093216,0.01888946,0.01153264,0.07200868,-0.01870833,-0.00739704,-0.02401608,-0.00740147,0.04541752,0.1004163,-0.03752675,-0.09140354,0.03975572,0.02683959,-0.06169917,-0.04795675,-0.03755395,0.01855027,-0.0806646,-0.10298872,0.04980064,-0.00984146,0.05257186,-0.06336574,-0.10018463,0.02849809,-0.02073417,0.05042353,-0.04269536,0.0872616,0.0469704,-0.02807462,0.02726185,-0.01146862,0.00450988,-0.01905006,-0.01526783,0.05776061,0.03337532,0.0337519,0.00296637,-0.01179452,0.00893615,-0.02357363,0.03785756,0.02484237,0.00112191,0.02661994,0.04530394,0.02069377,-0.01255454,-0.05086945,-0.22402731,-0.01817678,-0.00094381,-0.04510317,0.0093129,-0.0302783,0.02550698,0.00419803,0.09133971,0.08600603,0.03091143,-0.00455323,-0.07699588,-0.00940278,0.0220069,0.03558815,-0.00476096,-0.00132821,0.00550144,-0.01218662,-0.00301547,0.05986254,-0.00970247,-0.03294383,0.05419247,-0.03455092,0.12516831,0.04470894,0.03201244,0.07393242,0.02937352,0.05730027,0.02716083,-0.10097475,0.01545386,0.02677751,-0.03345161,-0.03585163,-0.01812842,-0.04446869,-0.00969945,0.03051205,-0.06837678,-0.06192813,-0.02454268,-0.03526678,-0.06134015,0.00481766,-0.01380456,0.04106316,-0.02138704,0.00306782,-0.01615411,-0.03616609,-0.02010764,-0.04864969,-0.05817112,-0.01260619,-0.02959984,0.06690602,0.011065,0.01369411,0.03701168,-0.01427847,-0.0029423,-0.03510062,-0.0416914,-0.04075832,-0.07368404,0.03066354,-0.03303755,0.15661421,-0.00280873,-0.05332464,0.06323039,0.01574262,0.00037161,-0.00049122,0.01320212,0.01565348,0.00209628,0.0080755,0.02040647,-0.02006904,-0.02546925,0.02274065,0.02481559,-0.00106131,0.06127342,-0.04961542,-0.04762706,-0.00467245,-0.06563134,0.01815331,0.09860307,-0.02637327,-0.30221659,0.04811158,-0.03805639,-0.01826082,0.05186561,0.07272581,0.02695361,0.02419064,-0.04042669,0.02796752,-0.03925212,0.05011505,-0.01304934,-0.02151152,-0.04193185,-0.01709652,0.03750166,-0.01172404,0.06471919,-0.01964093,0.00166163,0.03775198,0.21350813,-0.01057451,0.01513064,0.02508169,0.00476774,0.02164372,0.04010678,0.04163199,0.0146285,-0.03576062,0.05639862,-0.02020625,0.0062747,0.07491535,-0.08093255,-0.00391113,0.02446893,0.04681034,-0.01845209,0.03731744,-0.08121283,0.03712304,0.08410615,0.02058795,-0.00763553,-0.01645459,-0.00804434,0.06226725,0.03854992,0.0167723,-0.01363322,0.02653866,0.02629214,0.04963073,0.03016323,-0.03859838,-0.04345677,-0.0005814,0.01459973,0.04510867,0.0700459,0.06174408,0.09914179],"last_embed":{"hash":"3433363353d362e1a358952ca9cae9ca01788dbee0d15cf2fe10089ac0387783","tokens":480}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.01359458,-0.02845416,-0.01488588,0.01194843,-0.05683875,0.00078855,0.04337599,0.02943349,-0.02011203,-0.06589823,0.04461497,0.00203884,-0.03422609,-0.01843183,0.00205411,0.03686717,0.07206032,0.03386666,0.01748959,0.01706344,-0.06603319,-0.04065707,0.04389386,0.0048321,0.03619702,-0.06126071,-0.02610887,-0.04459607,-0.03066474,0.02241413,0.05428569,-0.03346022,0.0375589,0.034139,0.00730216,0.02289476,0.05469709,-0.05631993,0.0020119,0.03531211,-0.0319898,-0.01110695,-0.04784022,-0.01343642,-0.08075035,0.01889618,0.00805344,-0.03644232,0.01856706,-0.01941812,-0.00779359,-0.05588728,0.0209609,-0.00645211,-0.01911571,0.0233723,-0.05692542,-0.01145113,-0.00887558,-0.03688408,-0.00426761,-0.05208932,0.06205353,-0.06364953,0.06150025,0.0745948,0.08070109,-0.05478406,-0.0851052,-0.02276904,-0.05334166,-0.07103982,0.06744941,0.02784081,-0.04165073,0.02797368,0.00046135,-0.00196691,-0.01182956,0.02506498,-0.03767105,0.0081013,0.02567538,0.06896293,0.01986823,-0.00734845,0.03935955,-0.01079627,0.00945668,-0.01937279,-0.01775087,-0.03067612,-0.04351369,0.04010091,0.01636451,-0.05831348,0.03630376,-0.00625038,-0.00031795,-0.02182732,-0.06928056,0.02718506,-0.0087069,-0.01988466,-0.00903775,0.04177354,-0.04735107,0.03768151,-0.01075762,-0.01241618,-0.0344316,0.03294004,0.00668465,-0.0865598,-0.10701416,-0.00600886,-0.0139579,-0.00759268,0.04552645,0.03669151,0.07307898,0.02722574,-0.01802996,-0.02779672,-0.03901952,-0.0351831,-0.03627242,-0.0243438,-0.00198915,-0.09294619,0.03456759,0.0046478,-0.04469286,-0.01103154,0.0268828,-0.03303352,0.02177063,-0.08268864,-0.04878819,-0.04144844,0.02256321,0.00305229,0.05295597,0.01305384,-0.02931384,0.04073077,-0.04309126,-0.0160858,-0.02407492,0.05471727,-0.0188802,0.04934233,0.00167149,-0.04385062,-0.04056514,-0.00976287,0.03511243,-0.03765763,-0.03282616,-0.03163618,-0.0275732,-0.0211862,0.00144588,0.00020306,-0.01024672,0.01478241,0.0350086,0.0078364,0.09002038,-0.03623173,0.00821151,-0.02034951,0.01037262,0.00002597,0.0442197,0.03206135,0.03710177,0.05005452,-0.03181445,0.00243267,0.02316951,0.01260679,0.00321877,0.06909555,-0.04807785,0.02599845,0.00206318,-0.00618559,0.01095813,0.00105179,0.00953634,-0.03197569,0.00675219,0.00999104,0.00605738,0.03527258,-0.0296826,-0.03563019,0.01903904,0.01170391,0.02829765,-0.01007964,-0.02782422,-0.09603142,0.07493258,0.02455499,-0.02188462,-0.02478802,-0.07319498,0.05994181,0.01410943,0.00629661,0.094107,0.00367477,-0.00003745,0.05206557,-0.02361116,0.02906299,-0.02361006,-0.00502887,0.01190201,0.05081149,-0.03851112,-0.0221103,-0.00765844,-0.03951893,0.04268612,-0.00822877,0.0380762,0.01683043,-0.00820046,0.15226032,0.01734316,-0.03481219,-0.0644749,0.06461354,-0.00201261,-0.00627449,0.04273346,0.02425522,-0.01626418,0.01194053,-0.01055551,0.00960068,-0.01889727,0.00230102,-0.02779154,0.00020931,-0.00996394,0.02187061,0.07541088,0.01671088,-0.005486,0.05464226,0.04271647,-0.05176796,-0.0246706,0.00477882,-0.03931196,-0.08021382,0.02459597,-0.01321887,0.01693918,0.05589383,-0.02062966,-0.0234469,-0.01742062,0.00420772,0.01019668,0.03141238,-0.01107316,-0.04151992,0.02702161,0.01028646,-0.0150268,0.04844002,-0.0035953,-0.02976395,0.00435816,-0.04038362,0.03991475,0.00555518,0.02089335,-0.00559583,-0.04793397,-0.02469934,0.02164762,0.00069016,0.01268054,0.04341613,-0.01228076,-0.02779054,0.00918126,0.006362,-0.05682276,-0.05776427,-0.00321782,0.02079339,-0.0080597,-0.05746226,0.05135735,-0.05617884,-0.02357722,-0.01995632,0.00260941,0.03097287,0.00232493,-0.0288353,-0.02820376,-0.01136421,-0.042911,-0.04802813,-0.01844668,0.01228945,0.02736606,0.03051849,0.0513397,0.02379578,-0.03966018,-0.0410377,-0.07093284,0.00583334,-0.05714412,0.01505734,0.04898707,-0.04805208,0.03215581,0.00255613,0.04880696,0.02581802,-0.00590941,0.02492114,-0.04827956,-0.03478836,0.00758938,0.02741297,-0.01343547,0.00540536,0.01813453,-0.05816979,0.06939676,-0.12107719,0.03372936,0.01901762,-0.00840448,0.05277889,-0.00078202,0.01565737,-0.0087648,0.00238591,-0.00757365,-0.00891068,0.00520583,-0.00520094,0.01054944,0.04682366,0.00529686,-0.01554804,-0.03795862,-0.02251525,0.00068574,-0.05426458,0.02078725,0.02742044,-0.03771487,0.02298654,0.08739287,0.011474,-0.03029342,-0.01041663,0.01696206,-0.04489163,0.00164675,-0.00288033,-0.01348402,-0.01148377,-0.03345465,0.0103146,-0.04766427,0.0088593,-0.02781066,-0.05773433,0.003839,-0.06313618,-0.02471331,0.01025983,-0.00632682,0.00418406,0.01471096,-0.00754206,0.06821024,0.05201258,0.00552195,-0.02480075,0.0433435,0.00829656,0.03327743,-0.00336653,0.01637411,0.04175019,0.02830346,-0.01600841,-0.01236694,-0.0106687,0.01281654,0.07009712,-0.0430312,-0.01887051,-0.01914611,0.01478157,0.06453731,0.04393476,-0.0649678,0.00736331,-0.02093908,-0.0297274,-0.0064802,-0.07362079,-0.00130662,0.02294687,0.00329207,-0.00651452,0.00879096,-0.00264511,-0.04866115,0.03944609,0.02239572,-0.00124572,0.03615322,0.01107476,0.01979876,-0.02083776,-0.08274806,0.00669365,0.00011502,-0.02789344,0.03599948,-0.01749546,-0.00015706,-0.03373147,-0.01610168,-0.01067321,0.01784934,-0.02588726,-0.00104548,0.00720929,0.02861687,-0.05821289,-0.05259787,0.03536328,0.00004172,-0.00419063,-0.09127511,-0.02547819,0.00049249,-0.0101193,-0.01614946,0.02587847,0.04163308,0.04209422,0.00736339,-0.00540516,-0.04934206,-0.03752312,-0.04363869,0.0322347,0.03169569,0.01016798,-0.07314652,0.01670468,-0.00125971,-0.04230411,0.0153361,-0.03744321,0.03368562,0.01121365,-0.01793198,0.01866379,-0.07896681,-0.0029772,0.01304054,0.01320401,0.00605151,0.04018623,-0.03970617,0.01189711,-0.0399936,0.00145229,0.05752399,-0.05131131,-0.03783144,0.09674226,0.0487536,0.04586649,-0.05449685,0.01115326,0.038625,0.00917424,0.03396189,0.04185089,0.02190897,0.01357013,0.00101489,0.0038538,0.00988105,-0.00726645,-0.05619537,-0.03041258,0.01092913,-0.06447011,-0.01502917,-0.02018238,0.02288745,0.06511395,0.00967258,-0.08110093,0.03713861,0.04281022,0.02603369,-0.01917534,-0.02841175,0.04634433,-0.02795945,0.01870213,-0.00295623,-0.03082403,-0.05203625,0.02384659,0.04865169,-0.00366272,0.01717349,-0.00106933,-0.00596562,-0.01248644,0.00975154,0.00106907,0.02752746,0.00210147,-0.02825083,-0.01643093,-0.0407737,0.02689068,-0.00381414,0.01418995,-0.02290632,-0.01291424,0.00804734,-0.02675468,0.01071597,0.02131385,0.00331051,0.00089153,0.03876946,0.02045153,0.01819791,0.05507783,0.04692544,-0.06372488,-0.00194225,0.02175746,0.0222673,0.06714842,0.01720282,0.05336388,-0.01421939,0.04135207,-0.03595524,0.00053474,-0.02159717,-0.01793899,-0.02305924,0.00583835,0.05593212,0.04276992,-0.08350224,-0.0104464,0.00799881,0.00470567,0.02254635,0.00339084,0.04999441,0.00975653,0.0398774,0.00841066,-0.05814906,-0.00315291,-0.01941306,0.02562418,0.03808332,0.04152357,0.03848367,0.03488423,-0.01629805,0.0278636,-0.00093787,-0.00845389,-0.02886003,0.02232085,0.05972919,-0.00534949,0.02663304,0.00990064,-0.00367904,-0.01202404,-0.02387197,-0.04141189,-0.04293168,0.03149063,0.03726562,0.01616499,0.03590529,0.026712,-0.04410373,-0.05742551,0.03090651,0.08468245,0.10153611,0.00280123,-0.03321743,-0.03107296,-0.04328541,-0.00293805,-0.03158252,-0.03080943,-0.01806885,0.03522025,-0.00394074,0.08034492,0.04342352,-0.05701629,0.00940352,-0.01860562,-0.00427448,0.02801588,0.00044244,-0.00175748,-0.08176718,-0.01399638,0.01997028,0.01094978,0.04459012,-0.01900174,0.01726225,-0.00393359,0.03732255,0.02259427,-0.03917869,0.02934321,0.0246432,-0.0265724,0.0018576,0.00103512,-0.01124165,-0.04674329,-0.01326804,0.01391493,0.05813889,-0.00454011,0.01199507,0.00936102,-0.02164436,-0.02493246,-0.00076739,-0.0082838,-0.07645474,-0.00249875,0.09655436,0.04062615,-0.00705793,0.02493207,0.01401277,-0.02093048,-0.02187183,0.03489019,0.00115535,-0.01554303,0.02254122,-0.00140221,0.02320815,-0.00377949,-0.01490468,-0.02115493,-0.00742468,0.00187602,0.01460386,-0.0541341,0.04801755,0.02859333,-0.02381763,-0.04510711,-0.05891035,-0.07876747,-0.02764416,-0.03371402,0.03712308,-0.01357856,0.03807627,0.01322247,0.04998367,0.01461468,-0.01299836,-0.01633777,0.0343847,-0.04008112,-0.08186621,0.04031483,-0.00242254,0.04483783,0.00361163,-0.00218336,-0.02997427,0.01496896,-0.04962104,-0.02320061,0.04526679,-0.0747774,0.02954895,0.06586128,-0.05011318,0.04779529,0.00441984,0.00253964,-0.01354612,0.01519417,-0.03923937,0.00303038,-0.02789011,-0.0138075,-0.00821984,0.01409576,-0.06199424,-0.06282193,-0.00954873,0.04981326,0.06855921,-0.05036509,0.01435867,0.0154857,0.05524635,-0.05858929,0.06971866,-0.06123843,0.01093166,-0.00874452,-0.02058808,0.03444714,-0.01476407,0.02579198,-0.01654415,-0.00044449,-0.05708202,-0.02029308,-0.05364104,0.06030736,0.02026736,0.0010678,-0.02414843,0.02052067,9.1e-7,0.01883757,-0.02142028,0.03510778,-0.03951418,-0.00333446,-0.00349657,-0.08106401,0.03123784,0.04792837],"last_embed":{"tokens":881,"hash":"1hgbwtd"}}},"last_read":{"hash":"1hgbwtd","at":1751079979219},"class_name":"SmartSource","outlinks":[{"title":"ettercap","target":"ettercap","line":7},{"title":"bettercap","target":"bettercap","line":52},{"title":"#ARP欺骗攻击","target":"#ARP欺骗攻击","line":82}],"metadata":{"aliases":null,"tags":["网络安全/中间人攻击"]},"blocks":{"#---frontmatter---":[1,5],"#简介":[6,18],"#简介#{1}":[7,7],"#简介#{2}":[8,11],"#简介#{3}":[12,13],"#简介#{4}":[14,17],"#简介#{5}":[18,18],"#参数解释":[19,50],"#参数解释#{1}":[21,50],"#实际测试":[51,106],"#实际测试#{1}":[52,52],"#实际测试#{2}":[53,53],"#实际测试#{3}":[54,54],"#实际测试#嗅探模块":[55,67],"#实际测试#嗅探模块#{1}":[56,58],"#实际测试#嗅探模块#{2}":[59,59],"#实际测试#嗅探模块#{3}":[60,64],"#实际测试#嗅探模块#{4}":[62,64],"#实际测试#嗅探模块#{5}":[65,66],"#实际测试#嗅探模块#{6}":[67,67],"#实际测试#ARP欺骗攻击":[68,80],"#实际测试#ARP欺骗攻击#{1}":[69,71],"#实际测试#ARP欺骗攻击#{2}":[72,76],"#实际测试#ARP欺骗攻击#{3}":[74,76],"#实际测试#ARP欺骗攻击#{4}":[77,78],"#实际测试#ARP欺骗攻击#{5}":[79,80],"#实际测试#DNS欺骗":[81,105],"#实际测试#DNS欺骗#{1}":[82,82],"#实际测试#DNS欺骗#{2}":[83,86],"#实际测试#DNS欺骗#{3}":[84,86],"#实际测试#DNS欺骗#{4}":[87,91],"#实际测试#DNS欺骗#{5}":[89,91],"#实际测试#DNS欺骗#{6}":[92,92],"#实际测试#DNS欺骗#{7}":[93,93],"#实际测试#DNS欺骗#{8}":[94,97],"#实际测试#DNS欺骗#{9}":[98,98],"#实际测试#DNS欺骗#{10}":[99,102],"#实际测试#DNS欺骗#{11}":[103,103],"#实际测试#DNS欺骗#{12}":[104,105],"#实际测试#注入脚本":[106,106]},"last_import":{"mtime":1723643446697,"size":3735,"at":1748488128912,"hash":"1hgbwtd"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/中间人攻击/bettercap.md"},