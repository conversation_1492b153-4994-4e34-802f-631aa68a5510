"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md","last_embed":{"hash":"7bkm7v","at":1750993393462},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10651953,-0.00197337,-0.00508996,-0.08108622,-0.02246788,-0.00646923,-0.02173372,0.01178418,0.03798487,-0.0062135,0.00722612,-0.04900204,0.00736773,0.01820658,0.07094712,0.01974324,-0.01366533,0.04183168,-0.018223,0.00111983,0.04685222,-0.02832075,0.00195626,-0.06868172,-0.02019536,0.01289668,0.00104544,-0.01617572,-0.03657393,-0.16303761,0.0210806,-0.02112023,0.00266488,0.02924697,0.01904911,-0.06308626,0.00282456,0.06869567,-0.02290376,0.02923383,0.01521668,0.03208926,0.03010088,-0.031378,-0.01465898,-0.06398974,-0.03786014,-0.0132003,0.02090947,-0.04020385,-0.03058663,-0.04123231,-0.0547669,-0.00674351,-0.06995191,0.00038761,-0.01716731,0.00507111,0.11738765,0.02330259,0.02972053,0.02478357,-0.21171606,0.01933378,0.03667513,-0.01682436,-0.01637252,-0.01032908,0.02400566,0.02022312,-0.06553947,-0.01037217,-0.02961647,0.05677334,0.05121325,-0.03527061,-0.00835691,0.00891916,0.00863493,-0.01473726,-0.01568056,0.06409905,0.01484143,0.01568193,0.0019686,0.0316322,-0.0457709,0.00583925,0.03543194,-0.00762978,-0.02058266,-0.0491944,0.02072896,0.02098818,0.01585783,0.03334177,0.00651011,0.01553705,-0.12808898,0.09836119,-0.0626821,-0.00991702,-0.00360496,-0.08135629,0.00764952,-0.01252298,-0.01087916,-0.01379041,-0.03574909,-0.01332033,-0.05441922,-0.03695219,0.05352699,-0.01524542,0.01706235,0.00489107,0.00463718,-0.02898459,-0.02581113,0.02756153,-0.00191962,-0.04076449,0.09883073,-0.00657688,0.02437527,-0.05991374,0.02873335,0.07501686,0.05417802,0.04608052,0.07456484,-0.02833371,-0.04058854,-0.03908226,0.02788296,0.01117114,-0.08064459,0.02837143,0.0417257,-0.0472894,-0.01935579,-0.0231283,0.03463653,-0.09630755,-0.08173214,0.04506872,-0.04684845,-0.00184216,0.0597202,-0.0236527,0.02901258,0.05648211,-0.03890179,-0.02946389,-0.02174813,0.01863091,0.08593509,0.14102247,-0.0305087,-0.00910564,-0.01698289,0.00017875,-0.07368308,0.17924921,0.01649228,-0.04815388,-0.00948485,-0.00569002,0.01499551,-0.0484487,0.00104382,-0.01157842,0.01942692,-0.01486073,0.05268074,-0.02896277,-0.06138488,-0.01320735,0.02306482,0.02617532,0.03227863,-0.01990483,-0.05674221,0.01391565,0.02717396,-0.07793977,-0.01263972,-0.06019416,-0.00945571,-0.04978551,-0.0794071,0.00084575,-0.04737157,-0.02785207,-0.0716008,-0.03319462,0.0476028,0.01789363,0.03309761,0.00680268,0.07660214,0.00643111,-0.0596596,-0.01209066,-0.06173128,0.00646036,-0.00515659,-0.03180677,0.03846018,-0.00051071,0.01552599,-0.01087668,0.01300644,0.01367952,-0.03454155,0.00823641,-0.01071197,-0.0084942,0.02844787,0.07255793,-0.03461318,0.02663128,-0.14328468,-0.214076,0.00140968,0.02244991,-0.05343938,0.01075425,-0.03877201,0.03305038,-0.01491436,0.07569388,0.06414152,0.10497222,0.03481641,-0.03966497,-0.01404403,0.04975568,0.0395717,0.0166813,-0.01621277,-0.10832394,0.02753489,-0.0215192,0.04301588,-0.02727302,-0.04472352,0.00442847,-0.06711502,0.12099802,0.01517784,0.06845455,0.07162071,0.03829879,0.0358553,-0.00267005,-0.11911953,0.0091431,0.03924641,-0.00947248,0.00104139,-0.00209608,-0.02376035,-0.00802559,0.04265061,-0.03400497,-0.08827938,-0.00562743,-0.04664835,-0.01329711,0.03857129,-0.00797682,0.08426721,0.02348122,0.03327965,0.02375088,0.04172159,0.00508097,-0.05017431,-0.04156038,-0.03797768,-0.01212676,0.04141404,0.04934597,-0.04043809,0.0328637,0.01358084,0.00586828,-0.02223837,0.04189912,-0.02234768,0.01253147,0.00392662,-0.00519996,0.14873381,0.02283891,-0.02219863,0.04044577,0.01720686,0.01964487,0.00106566,0.01600099,0.01107761,0.03652403,0.02315751,0.01187588,-0.01302626,0.01435077,0.0011668,0.01711547,-0.01162816,0.08570696,-0.0700708,-0.05926603,-0.02627405,-0.02250915,0.02271853,0.07042509,-0.01648055,-0.30031681,0.05544633,0.03083311,-0.00502381,0.02757531,-0.02007171,0.06239722,-0.02031299,-0.06165845,0.00014743,-0.05598024,0.05109129,-0.00248477,-0.02503944,0.00846275,-0.05741569,0.04801452,-0.0303495,0.0389387,-0.03268786,-0.01038099,0.08965638,0.21033451,0.00047115,0.0807331,-0.00231217,0.0227909,0.07642933,0.07290583,0.03457185,0.0483353,-0.03955738,0.03344842,-0.05697668,0.06029512,0.01957873,-0.01408532,-0.00535068,0.03406858,0.00428536,-0.05487578,0.05206616,-0.04622307,0.02010707,0.11244866,-0.0038973,0.03061122,-0.05793713,-0.01699037,0.04064333,-0.01375751,0.0249454,0.03860326,-0.02464176,0.05343189,0.04328643,0.02533174,-0.05620655,-0.03222667,-0.00351494,0.02191751,0.00423837,0.08258258,0.11725594,0.07527129],"last_embed":{"hash":"5872d48a9e9222c60d713fe1970fd605261421be6a7cc7c721003909c8b0909b","tokens":456}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[0.00578035,-0.01675728,0.05092258,0.0554311,-0.04558223,0.00715386,0.01676871,0.00143821,-0.06236081,-0.01622666,0.02674524,0.0389287,-0.00497541,0.00830445,-0.04028191,0.05291726,-0.00797035,0.04878649,-0.02668937,-0.04785572,-0.01668865,-0.03248908,0.01411723,0.00906276,0.03539225,-0.05814322,-0.03013403,-0.06617661,0.04554404,0.01809879,0.03494226,-0.00634034,-0.02930435,-0.0201837,-0.00255305,-0.01792862,0.01111288,-0.01836604,0.03800452,0.00826387,-0.00816675,0.02728894,-0.03451994,-0.00772203,-0.06173402,0.02286035,0.07395916,-0.05553461,-0.03788633,0.02591031,0.02906765,-0.08402258,-0.0033414,0.0620006,0.00721666,-0.00883926,-0.03759497,-0.04828887,0.00034023,-0.03271456,0.00337263,-0.04473059,0.05738264,-0.0164547,-0.0166671,0.06867106,0.07110858,-0.0502744,-0.01833894,0.04345822,-0.0009232,0.03226091,0.01166467,-0.01376019,0.01277903,-0.01015066,0.02596493,-0.03320419,-0.03126178,-0.0672473,-0.00742808,-0.0113734,0.01583629,0.03397052,0.07805625,-0.03665724,0.04463738,-0.04873506,-0.03992071,0.002339,0.00809848,-0.03307419,-0.07489605,-0.00146535,-0.00774432,-0.039891,0.02872017,-0.01435626,-0.03356444,-0.01958905,-0.00278285,0.0241934,-0.04329271,-0.02021568,0.00926734,-0.01563008,-0.08550218,-0.0203284,-0.03095632,0.01295026,0.03010557,0.05508514,0.0178208,-0.03576021,-0.04112862,-0.03658635,-0.01312842,-0.00364383,0.04871621,0.04931651,0.03672102,0.04667594,-0.03186782,-0.04016855,-0.00749159,0.00617053,-0.02649763,-0.01809966,0.00531864,-0.03742163,-0.06314122,0.02504708,-0.01643134,-0.04805005,0.01224091,0.074016,-0.01484409,-0.05975983,-0.03207828,-0.02607402,0.08727479,0.01219015,0.05806004,-0.04086064,0.02692366,0.01795834,-0.01766874,-0.07581836,-0.06194187,0.02487236,-0.04764781,0.03635766,-0.01287207,-0.02979866,-0.00066656,-0.06738248,0.01202512,-0.04716968,0.01434698,-0.02265857,-0.03640031,0.03101831,0.00395123,-0.00776423,0.01811464,0.04240397,-0.0179848,0.01222035,0.05886965,-0.01784737,-0.02887794,0.00433885,-0.01618882,-0.00036256,0.03843169,-0.01883151,-0.01414475,-0.04317675,-0.06460521,-0.01318563,0.06033232,0.01181843,-0.00718398,0.03306375,-0.01179117,0.05313519,0.03963235,0.01876559,0.05191908,-0.00679943,0.01030372,0.07812925,-0.01652624,0.01427204,0.03811214,0.00692225,-0.04297145,-0.0698089,0.04787619,0.01380788,-0.04427182,0.00102366,-0.05902691,-0.04711073,0.03843587,0.00663808,-0.05283999,0.01488726,-0.0215628,-0.01191776,0.01479555,-0.01253642,0.01171946,0.00938991,-0.0442885,-0.03077638,-0.03167573,0.04554366,-0.04703414,-0.05054872,0.02244793,0.02120318,-0.03146644,0.02715918,0.04192515,-0.00029891,0.05602785,0.07302023,0.03224776,-0.00406909,0.03453796,0.05768048,-0.03475399,-0.00335897,-0.04332055,0.04756045,0.07560679,-0.01864318,0.0745123,-0.01497184,0.02245245,0.07506037,0.00740952,0.04297667,-0.0132619,-0.03145303,-0.04512804,0.06621031,-0.01148277,0.04489456,0.13829784,0.0211861,-0.09228945,0.02992934,0.00349904,-0.01427853,-0.04723618,0.08210044,-0.03131233,-0.08385117,0.02423892,-0.06601439,-0.02655385,0.03783504,-0.00814748,-0.02871334,-0.03921649,0.00607165,0.02238781,-0.00049195,0.04171608,-0.04748758,0.02818027,-0.00774745,0.02353983,-0.01044101,0.05593522,0.01021127,-0.00819146,-0.03629196,-0.02513436,-0.00589395,0.00558042,-0.06258696,-0.06823323,-0.01783314,0.03550262,-0.01457586,0.0047244,0.01481876,0.00455232,-0.0378701,-0.02514984,0.0317274,-0.00840069,-0.01854777,0.0086603,0.04755112,0.03068827,-0.03967832,0.01117428,-0.01246952,-0.01108453,-0.01229193,-0.01083826,0.03105709,0.0049792,-0.00185536,-0.01935585,-0.02719026,-0.03776572,0.00191182,-0.04489711,0.02176699,0.00680949,0.08417509,0.02296038,-0.01619054,-0.04732468,-0.04995991,-0.03831886,0.02090275,-0.0291111,-0.00324699,-0.02844796,-0.07457004,0.09843452,0.0713124,0.04158038,-0.01469371,-0.03968942,-0.01253162,-0.01301739,-0.04479385,0.04863679,0.05246544,-0.0115812,-0.00973469,0.06969538,-0.00766208,-0.04358242,-0.02685458,-0.00793003,-0.01370515,-0.00874539,0.05548579,0.0074096,0.03618089,-0.04126013,-0.02788808,0.01263032,-0.01924609,-0.00785904,-0.01590226,0.01817784,0.06054047,0.04048027,-0.01241287,-0.01121597,0.00718703,-0.03859805,-0.06014599,0.02257117,0.0364235,-0.04071205,-0.00534453,-0.01305316,0.07046331,-0.0316847,-0.02730695,0.00641628,-0.02243179,0.06403344,0.03060377,-0.00932269,-0.00917008,0.02303617,-0.01192668,-0.0196596,-0.01356866,-0.04628894,-0.01596512,-0.00402724,-0.01711562,-0.05193456,0.01960674,-0.00941343,-0.02844433,0.01717572,-0.02980419,-0.00333557,0.00205099,0.01619524,-0.04542398,0.11343964,-0.03309414,0.02011992,-0.05647071,0.02553679,-0.02406973,-0.00057428,-0.04853211,-0.02194049,0.00880778,0.01102411,0.01124681,-0.07851887,0.00864465,-0.02723869,-0.00973035,0.01769175,0.02772028,0.04804489,-0.01599632,-0.02322379,0.02179153,0.03229276,-0.03345579,-0.02259516,-0.00604654,0.0757058,0.01925061,-0.08811157,0.00606114,-0.02696408,-0.01794216,-0.02782949,-0.02672806,0.02135479,-0.0115629,-0.01398893,-0.04554458,-0.05173471,0.02450169,-0.0015928,0.02850619,0.04357935,-0.01930396,-0.01925823,-0.00475714,-0.03738381,0.03447017,0.02027616,-0.04935139,0.06129057,0.01184761,-0.03345592,-0.03983771,0.01273762,-0.0222276,-0.00784389,-0.00902803,-0.07791748,-0.01663509,0.03784497,0.06080019,-0.02352187,-0.0084003,0.02826157,-0.01146477,-0.01578516,0.03245828,0.02322558,-0.01888272,-0.01235444,0.03827585,0.04651258,-0.00896286,-0.04366563,-0.03773446,0.0125649,-0.00902555,0.01694652,-0.03279378,0.00577031,-0.02678945,-0.02947006,0.0810184,-0.06364778,0.01168942,0.03152701,-0.03540462,0.00874271,-0.00518318,-0.03264861,-0.00504738,0.01048881,0.04812041,0.02019636,-0.02794497,0.02358787,0.02657091,-0.00698667,0.04323652,-0.02217387,-0.05046031,-0.04806,0.03074286,-0.01284325,0.10054495,-0.04963678,0.05575416,0.03760105,0.03599766,0.00005803,-0.01690643,-0.04573983,0.0189445,0.0627418,-0.0231693,-0.00925261,-0.02847359,0.06426822,0.06389599,-0.04921699,-0.01157861,-0.00478904,-0.0089506,0.0358726,0.02359385,-0.03392902,0.03378442,-0.03911886,0.03766972,-0.01879414,-0.02547457,-0.02376284,-0.00960938,0.00058331,-0.06461187,0.01712887,0.00818564,0.0092648,-0.03451865,0.02947541,-0.00469141,0.03672501,-0.01079901,-0.04695668,0.01879396,0.01804574,0.01273595,0.03581762,0.00480747,-0.00638273,-0.02377668,0.02270705,-0.02369784,0.01725504,-0.01315788,0.01003614,-0.07401596,0.0021015,0.00680026,-0.00074957,0.0058808,0.00754857,-0.0768929,-0.02417173,0.06250525,0.00552654,0.06464683,-0.01016471,0.02141113,-0.03620908,-0.05103606,-0.00425489,-0.02688552,0.08953684,0.02793191,-0.01372068,-0.05105773,0.00752358,0.08785518,-0.06895017,-0.03767728,-0.04411325,-0.01530584,0.0465442,-0.05150053,-0.00109036,-0.02384688,0.01212762,-0.00133886,0.00282043,0.01915981,0.00281476,0.01927436,0.06583881,0.05372668,0.00763163,0.04808446,-0.06574284,0.01301841,-0.01680865,-0.04184514,-0.03445459,0.0238772,0.03256249,-0.02059947,-0.01889049,0.00213464,0.00895014,0.00440994,-0.00303598,-0.09935875,-0.04157781,0.01752547,0.05447684,-0.01515054,0.05038263,0.03612377,-0.00168743,-0.04982559,0.01292758,-0.04651245,-0.00491806,0.03485681,0.01432271,0.02021409,-0.03042208,-0.00118938,0.00102312,0.03058124,-0.02544757,0.06688535,0.02776623,0.02747102,-0.01128475,-0.02831395,-0.02145696,-0.02669122,-0.01105194,0.06024484,0.00093079,0.03325049,-0.04836011,-0.00452434,-0.01642174,0.01227192,0.04741629,-0.03069939,0.02849759,0.02606773,0.01502938,0.00646801,-0.00601865,-0.00007665,-0.00278843,-0.02406284,-0.0194075,0.00536238,-0.03271759,-0.02475902,-0.01556257,-0.00044257,0.00094436,0.01676811,-0.01149059,0.03025912,0.03256749,-0.00684515,0.00306301,0.00005699,-0.01365181,0.04208137,0.05908676,-0.02652127,-0.03480762,0.05519759,-0.05149676,0.00489037,-0.01288516,-0.01200522,0.01833722,-0.03554998,0.04126962,0.03388266,0.05803639,-0.06678741,-0.04023209,-0.00067335,0.02608396,-0.00657306,0.04066487,-0.032561,0.04918471,0.00674836,0.0211776,0.01405154,-0.06737634,-0.05946844,0.01630505,0.02815094,0.01652806,0.01821696,0.01776253,0.00182676,-0.01506908,-0.02759149,0.00005658,-0.01422592,-0.05292324,-0.06609686,-0.04314299,0.03132484,-0.00402652,0.02817278,0.03517408,0.02065943,-0.02004094,0.00084185,-0.02558276,-0.05330957,0.05145877,-0.08541206,0.00988477,0.045871,-0.07838873,-0.05492353,0.0156968,0.01350163,-0.07208382,0.02632192,-0.06885187,-0.00595995,-0.03997617,-0.00133077,0.03060002,0.03341191,0.01208673,-0.08702308,0.00906082,0.06146494,0.038541,0.03791376,0.02358674,-0.02646505,0.01713905,0.00960901,-0.0555579,-0.02794411,-0.0288477,-0.03118158,-0.01849958,-0.021664,0.02669842,0.00863871,0.00617731,0.04872007,0.03247054,0.00646305,0.01145849,0.03785478,0.04529293,0.00801953,-0.00076833,-0.04282786,0.000001,-0.02906155,0.03139344,0.03160615,-0.0013174,-0.03191725,-0.01005302,-0.01376294,-0.02882246,0.00050957],"last_embed":{"tokens":381,"hash":"7bkm7v"}}},"last_read":{"hash":"7bkm7v","at":1750993393462},"class_name":"SmartSource","outlinks":[{"title":"Powershell","target":"Powershell","line":15},{"title":".NET","target":"NET Framework","line":16},{"title":"Python","target":"Python","line":17},{"title":"Windows Defender","target":"Windows Defender","line":30},{"title":"Windows Defender","target":"Windows Defender","line":34},{"title":"Windows Defender","target":"Windows Defender","line":35},{"title":"恶意软件","target":"Malware","line":39},{"title":"C2","target":"C2","line":42},{"title":"Metasploit","target":"Metasploit","line":45},{"title":"Sliver C2","target":"Sliver C2","line":49},{"title":"内存","target":"内存","line":56},{"title":"内存","target":"内存","line":57}],"metadata":{"mindmap-plugin":"basic"},"blocks":{"#---frontmatter---":[1,3],"#免杀入门教程":[5,64],"#免杀入门教程#免杀基础知识":[7,41],"#免杀入门教程#免杀基础知识#免杀语言的选择":[8,25],"#免杀入门教程#免杀基础知识#免杀语言的选择#常见免杀语言特点":[9,25],"#免杀入门教程#免杀基础知识#免杀语言的选择#常见免杀语言特点#{1}":[10,12],"#免杀入门教程#免杀基础知识#免杀语言的选择#常见免杀语言特点#{2}":[13,14],"#免杀入门教程#免杀基础知识#免杀语言的选择#常见免杀语言特点#{3}":[15,16],"#免杀入门教程#免杀基础知识#免杀语言的选择#常见免杀语言特点#{4}":[17,18],"#免杀入门教程#免杀基础知识#免杀语言的选择#常见免杀语言特点#{5}":[19,20],"#免杀入门教程#免杀基础知识#免杀语言的选择#常见免杀语言特点#{6}":[21,23],"#免杀入门教程#免杀基础知识#免杀语言的选择#常见免杀语言特点#{7}":[24,25],"#免杀入门教程#免杀基础知识#常见的杀软及其特点":[26,37],"#免杀入门教程#免杀基础知识#常见的杀软及其特点#常见的杀软":[27,31],"#免杀入门教程#免杀基础知识#常见的杀软及其特点#常见的杀软#{1}":[28,28],"#免杀入门教程#免杀基础知识#常见的杀软及其特点#常见的杀软#{2}":[29,29],"#免杀入门教程#免杀基础知识#常见的杀软及其特点#常见的杀软#{3}":[30,31],"#免杀入门教程#免杀基础知识#常见的杀软及其特点#常见杀软的组合":[32,37],"#免杀入门教程#免杀基础知识#常见的杀软及其特点#常见杀软的组合#{1}":[33,33],"#免杀入门教程#免杀基础知识#常见的杀软及其特点#常见杀软的组合#{2}":[34,34],"#免杀入门教程#免杀基础知识#常见的杀软及其特点#常见杀软的组合#{3}":[35,36],"#免杀入门教程#免杀基础知识#常见的杀软及其特点#常见杀软的组合#{4}":[37,37],"#免杀入门教程#免杀基础知识#常见杀软特点总结":[38,41],"#免杀入门教程#免杀基础知识#常见杀软特点总结#{1}":[39,40],"#免杀入门教程#免杀基础知识#常见杀软特点总结#{2}":[41,41],"#免杀入门教程#[[C2]]推荐":[42,52],"#免杀入门教程#[[C2]]推荐#{1}":[43,44],"#免杀入门教程#[[C2]]推荐#{2}":[45,46],"#免杀入门教程#[[C2]]推荐#{3}":[47,48],"#免杀入门教程#[[C2]]推荐#{4}":[49,49],"#免杀入门教程#[[C2]]推荐#{5}":[50,51],"#免杀入门教程#[[C2]]推荐#{6}":[52,52],"#免杀入门教程#免杀加载器原理":[53,64],"#免杀入门教程#免杀加载器原理#{1}":[54,55],"#免杀入门教程#免杀加载器原理#{2}":[56,57],"#免杀入门教程#免杀加载器原理#{3}":[58,59],"#免杀入门教程#免杀加载器原理#{4}":[60,64]},"last_import":{"mtime":1747536242414,"size":1418,"at":1749024987320,"hash":"7bkm7v"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#---frontmatter---","lines":[1,3],"size":29,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程","lines":[5,64],"size":769,"outlinks":[{"title":"Powershell","target":"Powershell","line":11},{"title":".NET","target":"NET Framework","line":12},{"title":"Python","target":"Python","line":13},{"title":"Windows Defender","target":"Windows Defender","line":26},{"title":"Windows Defender","target":"Windows Defender","line":30},{"title":"Windows Defender","target":"Windows Defender","line":31},{"title":"恶意软件","target":"Malware","line":35},{"title":"C2","target":"C2","line":38},{"title":"Metasploit","target":"Metasploit","line":41},{"title":"Sliver C2","target":"Sliver C2","line":45},{"title":"内存","target":"内存","line":52},{"title":"内存","target":"内存","line":53}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识","lines":[7,41],"size":461,"outlinks":[{"title":"Powershell","target":"Powershell","line":9},{"title":".NET","target":"NET Framework","line":10},{"title":"Python","target":"Python","line":11},{"title":"Windows Defender","target":"Windows Defender","line":24},{"title":"Windows Defender","target":"Windows Defender","line":28},{"title":"Windows Defender","target":"Windows Defender","line":29},{"title":"恶意软件","target":"Malware","line":33}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#免杀语言的选择": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#免杀语言的选择","lines":[8,25],"size":241,"outlinks":[{"title":"Powershell","target":"Powershell","line":8},{"title":".NET","target":"NET Framework","line":9},{"title":"Python","target":"Python","line":10}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#免杀语言的选择#常见免杀语言特点": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#免杀语言的选择#常见免杀语言特点","lines":[9,25],"size":229,"outlinks":[{"title":"Powershell","target":"Powershell","line":7},{"title":".NET","target":"NET Framework","line":8},{"title":"Python","target":"Python","line":9}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#免杀语言的选择#常见免杀语言特点#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#免杀语言的选择#常见免杀语言特点#{1}","lines":[10,12],"size":47,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#免杀语言的选择#常见免杀语言特点#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#免杀语言的选择#常见免杀语言特点#{2}","lines":[13,14],"size":21,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#免杀语言的选择#常见免杀语言特点#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#免杀语言的选择#常见免杀语言特点#{3}","lines":[15,16],"size":54,"outlinks":[{"title":"Powershell","target":"Powershell","line":1},{"title":".NET","target":"NET Framework","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#免杀语言的选择#常见免杀语言特点#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#免杀语言的选择#常见免杀语言特点#{4}","lines":[17,18],"size":31,"outlinks":[{"title":"Python","target":"Python","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#免杀语言的选择#常见免杀语言特点#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#免杀语言的选择#常见免杀语言特点#{5}","lines":[19,20],"size":24,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#免杀语言的选择#常见免杀语言特点#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#免杀语言的选择#常见免杀语言特点#{6}","lines":[21,23],"size":28,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#免杀语言的选择#常见免杀语言特点#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#免杀语言的选择#常见免杀语言特点#{7}","lines":[24,25],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#常见的杀软及其特点": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#常见的杀软及其特点","lines":[26,37],"size":145,"outlinks":[{"title":"Windows Defender","target":"Windows Defender","line":5},{"title":"Windows Defender","target":"Windows Defender","line":9},{"title":"Windows Defender","target":"Windows Defender","line":10}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#常见的杀软及其特点#常见的杀软": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#常见的杀软及其特点#常见的杀软","lines":[27,31],"size":45,"outlinks":[{"title":"Windows Defender","target":"Windows Defender","line":4}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#常见的杀软及其特点#常见的杀软#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#常见的杀软及其特点#常见的杀软#{1}","lines":[28,28],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#常见的杀软及其特点#常见的杀软#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#常见的杀软及其特点#常见的杀软#{2}","lines":[29,29],"size":5,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#常见的杀软及其特点#常见的杀软#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#常见的杀软及其特点#常见的杀软#{3}","lines":[30,31],"size":23,"outlinks":[{"title":"Windows Defender","target":"Windows Defender","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#常见的杀软及其特点#常见杀软的组合": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#常见的杀软及其特点#常见杀软的组合","lines":[32,37],"size":85,"outlinks":[{"title":"Windows Defender","target":"Windows Defender","line":3},{"title":"Windows Defender","target":"Windows Defender","line":4}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#常见的杀软及其特点#常见杀软的组合#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#常见的杀软及其特点#常见杀软的组合#{1}","lines":[33,33],"size":10,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#常见的杀软及其特点#常见杀软的组合#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#常见的杀软及其特点#常见杀软的组合#{2}","lines":[34,34],"size":27,"outlinks":[{"title":"Windows Defender","target":"Windows Defender","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#常见的杀软及其特点#常见杀软的组合#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#常见的杀软及其特点#常见杀软的组合#{3}","lines":[35,36],"size":29,"outlinks":[{"title":"Windows Defender","target":"Windows Defender","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#常见的杀软及其特点#常见杀软的组合#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#常见的杀软及其特点#常见杀软的组合#{4}","lines":[37,37],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#常见杀软特点总结": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#常见杀软特点总结","lines":[38,41],"size":63,"outlinks":[{"title":"恶意软件","target":"Malware","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#常见杀软特点总结#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#常见杀软特点总结#{1}","lines":[39,40],"size":46,"outlinks":[{"title":"恶意软件","target":"Malware","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#常见杀软特点总结#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀基础知识#常见杀软特点总结#{2}","lines":[41,41],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#[[C2]]推荐": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#[[C2]]推荐","lines":[42,52],"size":156,"outlinks":[{"title":"C2","target":"C2","line":1},{"title":"Metasploit","target":"Metasploit","line":4},{"title":"Sliver C2","target":"Sliver C2","line":8}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#[[C2]]推荐#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#[[C2]]推荐#{1}","lines":[43,44],"size":36,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#[[C2]]推荐#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#[[C2]]推荐#{2}","lines":[45,46],"size":36,"outlinks":[{"title":"Metasploit","target":"Metasploit","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#[[C2]]推荐#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#[[C2]]推荐#{3}","lines":[47,48],"size":34,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#[[C2]]推荐#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#[[C2]]推荐#{4}","lines":[49,49],"size":16,"outlinks":[{"title":"Sliver C2","target":"Sliver C2","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#[[C2]]推荐#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#[[C2]]推荐#{5}","lines":[50,51],"size":13,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#[[C2]]推荐#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#[[C2]]推荐#{6}","lines":[52,52],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀加载器原理": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀加载器原理","lines":[53,64],"size":140,"outlinks":[{"title":"内存","target":"内存","line":4},{"title":"内存","target":"内存","line":5}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀加载器原理#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀加载器原理#{1}","lines":[54,55],"size":23,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀加载器原理#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀加载器原理#{2}","lines":[56,57],"size":36,"outlinks":[{"title":"内存","target":"内存","line":1},{"title":"内存","target":"内存","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀加载器原理#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀加载器原理#{3}","lines":[58,59],"size":27,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀加载器原理#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md#免杀入门教程#免杀加载器原理#{4}","lines":[60,64],"size":40,"outlinks":[],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md","last_embed":{"hash":"7bkm7v","at":1751079979494},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10651953,-0.00197337,-0.00508996,-0.08108622,-0.02246788,-0.00646923,-0.02173372,0.01178418,0.03798487,-0.0062135,0.00722612,-0.04900204,0.00736773,0.01820658,0.07094712,0.01974324,-0.01366533,0.04183168,-0.018223,0.00111983,0.04685222,-0.02832075,0.00195626,-0.06868172,-0.02019536,0.01289668,0.00104544,-0.01617572,-0.03657393,-0.16303761,0.0210806,-0.02112023,0.00266488,0.02924697,0.01904911,-0.06308626,0.00282456,0.06869567,-0.02290376,0.02923383,0.01521668,0.03208926,0.03010088,-0.031378,-0.01465898,-0.06398974,-0.03786014,-0.0132003,0.02090947,-0.04020385,-0.03058663,-0.04123231,-0.0547669,-0.00674351,-0.06995191,0.00038761,-0.01716731,0.00507111,0.11738765,0.02330259,0.02972053,0.02478357,-0.21171606,0.01933378,0.03667513,-0.01682436,-0.01637252,-0.01032908,0.02400566,0.02022312,-0.06553947,-0.01037217,-0.02961647,0.05677334,0.05121325,-0.03527061,-0.00835691,0.00891916,0.00863493,-0.01473726,-0.01568056,0.06409905,0.01484143,0.01568193,0.0019686,0.0316322,-0.0457709,0.00583925,0.03543194,-0.00762978,-0.02058266,-0.0491944,0.02072896,0.02098818,0.01585783,0.03334177,0.00651011,0.01553705,-0.12808898,0.09836119,-0.0626821,-0.00991702,-0.00360496,-0.08135629,0.00764952,-0.01252298,-0.01087916,-0.01379041,-0.03574909,-0.01332033,-0.05441922,-0.03695219,0.05352699,-0.01524542,0.01706235,0.00489107,0.00463718,-0.02898459,-0.02581113,0.02756153,-0.00191962,-0.04076449,0.09883073,-0.00657688,0.02437527,-0.05991374,0.02873335,0.07501686,0.05417802,0.04608052,0.07456484,-0.02833371,-0.04058854,-0.03908226,0.02788296,0.01117114,-0.08064459,0.02837143,0.0417257,-0.0472894,-0.01935579,-0.0231283,0.03463653,-0.09630755,-0.08173214,0.04506872,-0.04684845,-0.00184216,0.0597202,-0.0236527,0.02901258,0.05648211,-0.03890179,-0.02946389,-0.02174813,0.01863091,0.08593509,0.14102247,-0.0305087,-0.00910564,-0.01698289,0.00017875,-0.07368308,0.17924921,0.01649228,-0.04815388,-0.00948485,-0.00569002,0.01499551,-0.0484487,0.00104382,-0.01157842,0.01942692,-0.01486073,0.05268074,-0.02896277,-0.06138488,-0.01320735,0.02306482,0.02617532,0.03227863,-0.01990483,-0.05674221,0.01391565,0.02717396,-0.07793977,-0.01263972,-0.06019416,-0.00945571,-0.04978551,-0.0794071,0.00084575,-0.04737157,-0.02785207,-0.0716008,-0.03319462,0.0476028,0.01789363,0.03309761,0.00680268,0.07660214,0.00643111,-0.0596596,-0.01209066,-0.06173128,0.00646036,-0.00515659,-0.03180677,0.03846018,-0.00051071,0.01552599,-0.01087668,0.01300644,0.01367952,-0.03454155,0.00823641,-0.01071197,-0.0084942,0.02844787,0.07255793,-0.03461318,0.02663128,-0.14328468,-0.214076,0.00140968,0.02244991,-0.05343938,0.01075425,-0.03877201,0.03305038,-0.01491436,0.07569388,0.06414152,0.10497222,0.03481641,-0.03966497,-0.01404403,0.04975568,0.0395717,0.0166813,-0.01621277,-0.10832394,0.02753489,-0.0215192,0.04301588,-0.02727302,-0.04472352,0.00442847,-0.06711502,0.12099802,0.01517784,0.06845455,0.07162071,0.03829879,0.0358553,-0.00267005,-0.11911953,0.0091431,0.03924641,-0.00947248,0.00104139,-0.00209608,-0.02376035,-0.00802559,0.04265061,-0.03400497,-0.08827938,-0.00562743,-0.04664835,-0.01329711,0.03857129,-0.00797682,0.08426721,0.02348122,0.03327965,0.02375088,0.04172159,0.00508097,-0.05017431,-0.04156038,-0.03797768,-0.01212676,0.04141404,0.04934597,-0.04043809,0.0328637,0.01358084,0.00586828,-0.02223837,0.04189912,-0.02234768,0.01253147,0.00392662,-0.00519996,0.14873381,0.02283891,-0.02219863,0.04044577,0.01720686,0.01964487,0.00106566,0.01600099,0.01107761,0.03652403,0.02315751,0.01187588,-0.01302626,0.01435077,0.0011668,0.01711547,-0.01162816,0.08570696,-0.0700708,-0.05926603,-0.02627405,-0.02250915,0.02271853,0.07042509,-0.01648055,-0.30031681,0.05544633,0.03083311,-0.00502381,0.02757531,-0.02007171,0.06239722,-0.02031299,-0.06165845,0.00014743,-0.05598024,0.05109129,-0.00248477,-0.02503944,0.00846275,-0.05741569,0.04801452,-0.0303495,0.0389387,-0.03268786,-0.01038099,0.08965638,0.21033451,0.00047115,0.0807331,-0.00231217,0.0227909,0.07642933,0.07290583,0.03457185,0.0483353,-0.03955738,0.03344842,-0.05697668,0.06029512,0.01957873,-0.01408532,-0.00535068,0.03406858,0.00428536,-0.05487578,0.05206616,-0.04622307,0.02010707,0.11244866,-0.0038973,0.03061122,-0.05793713,-0.01699037,0.04064333,-0.01375751,0.0249454,0.03860326,-0.02464176,0.05343189,0.04328643,0.02533174,-0.05620655,-0.03222667,-0.00351494,0.02191751,0.00423837,0.08258258,0.11725594,0.07527129],"last_embed":{"hash":"5872d48a9e9222c60d713fe1970fd605261421be6a7cc7c721003909c8b0909b","tokens":456}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[0.00578035,-0.01675728,0.05092258,0.0554311,-0.04558223,0.00715386,0.01676871,0.00143821,-0.06236081,-0.01622666,0.02674524,0.0389287,-0.00497541,0.00830445,-0.04028191,0.05291726,-0.00797035,0.04878649,-0.02668937,-0.04785572,-0.01668865,-0.03248908,0.01411723,0.00906276,0.03539225,-0.05814322,-0.03013403,-0.06617661,0.04554404,0.01809879,0.03494226,-0.00634034,-0.02930435,-0.0201837,-0.00255305,-0.01792862,0.01111288,-0.01836604,0.03800452,0.00826387,-0.00816675,0.02728894,-0.03451994,-0.00772203,-0.06173402,0.02286035,0.07395916,-0.05553461,-0.03788633,0.02591031,0.02906765,-0.08402258,-0.0033414,0.0620006,0.00721666,-0.00883926,-0.03759497,-0.04828887,0.00034023,-0.03271456,0.00337263,-0.04473059,0.05738264,-0.0164547,-0.0166671,0.06867106,0.07110858,-0.0502744,-0.01833894,0.04345822,-0.0009232,0.03226091,0.01166467,-0.01376019,0.01277903,-0.01015066,0.02596493,-0.03320419,-0.03126178,-0.0672473,-0.00742808,-0.0113734,0.01583629,0.03397052,0.07805625,-0.03665724,0.04463738,-0.04873506,-0.03992071,0.002339,0.00809848,-0.03307419,-0.07489605,-0.00146535,-0.00774432,-0.039891,0.02872017,-0.01435626,-0.03356444,-0.01958905,-0.00278285,0.0241934,-0.04329271,-0.02021568,0.00926734,-0.01563008,-0.08550218,-0.0203284,-0.03095632,0.01295026,0.03010557,0.05508514,0.0178208,-0.03576021,-0.04112862,-0.03658635,-0.01312842,-0.00364383,0.04871621,0.04931651,0.03672102,0.04667594,-0.03186782,-0.04016855,-0.00749159,0.00617053,-0.02649763,-0.01809966,0.00531864,-0.03742163,-0.06314122,0.02504708,-0.01643134,-0.04805005,0.01224091,0.074016,-0.01484409,-0.05975983,-0.03207828,-0.02607402,0.08727479,0.01219015,0.05806004,-0.04086064,0.02692366,0.01795834,-0.01766874,-0.07581836,-0.06194187,0.02487236,-0.04764781,0.03635766,-0.01287207,-0.02979866,-0.00066656,-0.06738248,0.01202512,-0.04716968,0.01434698,-0.02265857,-0.03640031,0.03101831,0.00395123,-0.00776423,0.01811464,0.04240397,-0.0179848,0.01222035,0.05886965,-0.01784737,-0.02887794,0.00433885,-0.01618882,-0.00036256,0.03843169,-0.01883151,-0.01414475,-0.04317675,-0.06460521,-0.01318563,0.06033232,0.01181843,-0.00718398,0.03306375,-0.01179117,0.05313519,0.03963235,0.01876559,0.05191908,-0.00679943,0.01030372,0.07812925,-0.01652624,0.01427204,0.03811214,0.00692225,-0.04297145,-0.0698089,0.04787619,0.01380788,-0.04427182,0.00102366,-0.05902691,-0.04711073,0.03843587,0.00663808,-0.05283999,0.01488726,-0.0215628,-0.01191776,0.01479555,-0.01253642,0.01171946,0.00938991,-0.0442885,-0.03077638,-0.03167573,0.04554366,-0.04703414,-0.05054872,0.02244793,0.02120318,-0.03146644,0.02715918,0.04192515,-0.00029891,0.05602785,0.07302023,0.03224776,-0.00406909,0.03453796,0.05768048,-0.03475399,-0.00335897,-0.04332055,0.04756045,0.07560679,-0.01864318,0.0745123,-0.01497184,0.02245245,0.07506037,0.00740952,0.04297667,-0.0132619,-0.03145303,-0.04512804,0.06621031,-0.01148277,0.04489456,0.13829784,0.0211861,-0.09228945,0.02992934,0.00349904,-0.01427853,-0.04723618,0.08210044,-0.03131233,-0.08385117,0.02423892,-0.06601439,-0.02655385,0.03783504,-0.00814748,-0.02871334,-0.03921649,0.00607165,0.02238781,-0.00049195,0.04171608,-0.04748758,0.02818027,-0.00774745,0.02353983,-0.01044101,0.05593522,0.01021127,-0.00819146,-0.03629196,-0.02513436,-0.00589395,0.00558042,-0.06258696,-0.06823323,-0.01783314,0.03550262,-0.01457586,0.0047244,0.01481876,0.00455232,-0.0378701,-0.02514984,0.0317274,-0.00840069,-0.01854777,0.0086603,0.04755112,0.03068827,-0.03967832,0.01117428,-0.01246952,-0.01108453,-0.01229193,-0.01083826,0.03105709,0.0049792,-0.00185536,-0.01935585,-0.02719026,-0.03776572,0.00191182,-0.04489711,0.02176699,0.00680949,0.08417509,0.02296038,-0.01619054,-0.04732468,-0.04995991,-0.03831886,0.02090275,-0.0291111,-0.00324699,-0.02844796,-0.07457004,0.09843452,0.0713124,0.04158038,-0.01469371,-0.03968942,-0.01253162,-0.01301739,-0.04479385,0.04863679,0.05246544,-0.0115812,-0.00973469,0.06969538,-0.00766208,-0.04358242,-0.02685458,-0.00793003,-0.01370515,-0.00874539,0.05548579,0.0074096,0.03618089,-0.04126013,-0.02788808,0.01263032,-0.01924609,-0.00785904,-0.01590226,0.01817784,0.06054047,0.04048027,-0.01241287,-0.01121597,0.00718703,-0.03859805,-0.06014599,0.02257117,0.0364235,-0.04071205,-0.00534453,-0.01305316,0.07046331,-0.0316847,-0.02730695,0.00641628,-0.02243179,0.06403344,0.03060377,-0.00932269,-0.00917008,0.02303617,-0.01192668,-0.0196596,-0.01356866,-0.04628894,-0.01596512,-0.00402724,-0.01711562,-0.05193456,0.01960674,-0.00941343,-0.02844433,0.01717572,-0.02980419,-0.00333557,0.00205099,0.01619524,-0.04542398,0.11343964,-0.03309414,0.02011992,-0.05647071,0.02553679,-0.02406973,-0.00057428,-0.04853211,-0.02194049,0.00880778,0.01102411,0.01124681,-0.07851887,0.00864465,-0.02723869,-0.00973035,0.01769175,0.02772028,0.04804489,-0.01599632,-0.02322379,0.02179153,0.03229276,-0.03345579,-0.02259516,-0.00604654,0.0757058,0.01925061,-0.08811157,0.00606114,-0.02696408,-0.01794216,-0.02782949,-0.02672806,0.02135479,-0.0115629,-0.01398893,-0.04554458,-0.05173471,0.02450169,-0.0015928,0.02850619,0.04357935,-0.01930396,-0.01925823,-0.00475714,-0.03738381,0.03447017,0.02027616,-0.04935139,0.06129057,0.01184761,-0.03345592,-0.03983771,0.01273762,-0.0222276,-0.00784389,-0.00902803,-0.07791748,-0.01663509,0.03784497,0.06080019,-0.02352187,-0.0084003,0.02826157,-0.01146477,-0.01578516,0.03245828,0.02322558,-0.01888272,-0.01235444,0.03827585,0.04651258,-0.00896286,-0.04366563,-0.03773446,0.0125649,-0.00902555,0.01694652,-0.03279378,0.00577031,-0.02678945,-0.02947006,0.0810184,-0.06364778,0.01168942,0.03152701,-0.03540462,0.00874271,-0.00518318,-0.03264861,-0.00504738,0.01048881,0.04812041,0.02019636,-0.02794497,0.02358787,0.02657091,-0.00698667,0.04323652,-0.02217387,-0.05046031,-0.04806,0.03074286,-0.01284325,0.10054495,-0.04963678,0.05575416,0.03760105,0.03599766,0.00005803,-0.01690643,-0.04573983,0.0189445,0.0627418,-0.0231693,-0.00925261,-0.02847359,0.06426822,0.06389599,-0.04921699,-0.01157861,-0.00478904,-0.0089506,0.0358726,0.02359385,-0.03392902,0.03378442,-0.03911886,0.03766972,-0.01879414,-0.02547457,-0.02376284,-0.00960938,0.00058331,-0.06461187,0.01712887,0.00818564,0.0092648,-0.03451865,0.02947541,-0.00469141,0.03672501,-0.01079901,-0.04695668,0.01879396,0.01804574,0.01273595,0.03581762,0.00480747,-0.00638273,-0.02377668,0.02270705,-0.02369784,0.01725504,-0.01315788,0.01003614,-0.07401596,0.0021015,0.00680026,-0.00074957,0.0058808,0.00754857,-0.0768929,-0.02417173,0.06250525,0.00552654,0.06464683,-0.01016471,0.02141113,-0.03620908,-0.05103606,-0.00425489,-0.02688552,0.08953684,0.02793191,-0.01372068,-0.05105773,0.00752358,0.08785518,-0.06895017,-0.03767728,-0.04411325,-0.01530584,0.0465442,-0.05150053,-0.00109036,-0.02384688,0.01212762,-0.00133886,0.00282043,0.01915981,0.00281476,0.01927436,0.06583881,0.05372668,0.00763163,0.04808446,-0.06574284,0.01301841,-0.01680865,-0.04184514,-0.03445459,0.0238772,0.03256249,-0.02059947,-0.01889049,0.00213464,0.00895014,0.00440994,-0.00303598,-0.09935875,-0.04157781,0.01752547,0.05447684,-0.01515054,0.05038263,0.03612377,-0.00168743,-0.04982559,0.01292758,-0.04651245,-0.00491806,0.03485681,0.01432271,0.02021409,-0.03042208,-0.00118938,0.00102312,0.03058124,-0.02544757,0.06688535,0.02776623,0.02747102,-0.01128475,-0.02831395,-0.02145696,-0.02669122,-0.01105194,0.06024484,0.00093079,0.03325049,-0.04836011,-0.00452434,-0.01642174,0.01227192,0.04741629,-0.03069939,0.02849759,0.02606773,0.01502938,0.00646801,-0.00601865,-0.00007665,-0.00278843,-0.02406284,-0.0194075,0.00536238,-0.03271759,-0.02475902,-0.01556257,-0.00044257,0.00094436,0.01676811,-0.01149059,0.03025912,0.03256749,-0.00684515,0.00306301,0.00005699,-0.01365181,0.04208137,0.05908676,-0.02652127,-0.03480762,0.05519759,-0.05149676,0.00489037,-0.01288516,-0.01200522,0.01833722,-0.03554998,0.04126962,0.03388266,0.05803639,-0.06678741,-0.04023209,-0.00067335,0.02608396,-0.00657306,0.04066487,-0.032561,0.04918471,0.00674836,0.0211776,0.01405154,-0.06737634,-0.05946844,0.01630505,0.02815094,0.01652806,0.01821696,0.01776253,0.00182676,-0.01506908,-0.02759149,0.00005658,-0.01422592,-0.05292324,-0.06609686,-0.04314299,0.03132484,-0.00402652,0.02817278,0.03517408,0.02065943,-0.02004094,0.00084185,-0.02558276,-0.05330957,0.05145877,-0.08541206,0.00988477,0.045871,-0.07838873,-0.05492353,0.0156968,0.01350163,-0.07208382,0.02632192,-0.06885187,-0.00595995,-0.03997617,-0.00133077,0.03060002,0.03341191,0.01208673,-0.08702308,0.00906082,0.06146494,0.038541,0.03791376,0.02358674,-0.02646505,0.01713905,0.00960901,-0.0555579,-0.02794411,-0.0288477,-0.03118158,-0.01849958,-0.021664,0.02669842,0.00863871,0.00617731,0.04872007,0.03247054,0.00646305,0.01145849,0.03785478,0.04529293,0.00801953,-0.00076833,-0.04282786,0.000001,-0.02906155,0.03139344,0.03160615,-0.0013174,-0.03191725,-0.01005302,-0.01376294,-0.02882246,0.00050957],"last_embed":{"tokens":381,"hash":"7bkm7v"}}},"last_read":{"hash":"7bkm7v","at":1751079979494},"class_name":"SmartSource","outlinks":[{"title":"Powershell","target":"Powershell","line":15},{"title":".NET","target":"NET Framework","line":16},{"title":"Python","target":"Python","line":17},{"title":"Windows Defender","target":"Windows Defender","line":30},{"title":"Windows Defender","target":"Windows Defender","line":34},{"title":"Windows Defender","target":"Windows Defender","line":35},{"title":"恶意软件","target":"Malware","line":39},{"title":"C2","target":"C2","line":42},{"title":"Metasploit","target":"Metasploit","line":45},{"title":"Sliver C2","target":"Sliver C2","line":49},{"title":"内存","target":"内存","line":56},{"title":"内存","target":"内存","line":57}],"metadata":{"mindmap-plugin":"basic"},"blocks":{"#---frontmatter---":[1,3],"#免杀入门教程":[5,64],"#免杀入门教程#免杀基础知识":[7,41],"#免杀入门教程#免杀基础知识#免杀语言的选择":[8,25],"#免杀入门教程#免杀基础知识#免杀语言的选择#常见免杀语言特点":[9,25],"#免杀入门教程#免杀基础知识#免杀语言的选择#常见免杀语言特点#{1}":[10,12],"#免杀入门教程#免杀基础知识#免杀语言的选择#常见免杀语言特点#{2}":[13,14],"#免杀入门教程#免杀基础知识#免杀语言的选择#常见免杀语言特点#{3}":[15,16],"#免杀入门教程#免杀基础知识#免杀语言的选择#常见免杀语言特点#{4}":[17,18],"#免杀入门教程#免杀基础知识#免杀语言的选择#常见免杀语言特点#{5}":[19,20],"#免杀入门教程#免杀基础知识#免杀语言的选择#常见免杀语言特点#{6}":[21,23],"#免杀入门教程#免杀基础知识#免杀语言的选择#常见免杀语言特点#{7}":[24,25],"#免杀入门教程#免杀基础知识#常见的杀软及其特点":[26,37],"#免杀入门教程#免杀基础知识#常见的杀软及其特点#常见的杀软":[27,31],"#免杀入门教程#免杀基础知识#常见的杀软及其特点#常见的杀软#{1}":[28,28],"#免杀入门教程#免杀基础知识#常见的杀软及其特点#常见的杀软#{2}":[29,29],"#免杀入门教程#免杀基础知识#常见的杀软及其特点#常见的杀软#{3}":[30,31],"#免杀入门教程#免杀基础知识#常见的杀软及其特点#常见杀软的组合":[32,37],"#免杀入门教程#免杀基础知识#常见的杀软及其特点#常见杀软的组合#{1}":[33,33],"#免杀入门教程#免杀基础知识#常见的杀软及其特点#常见杀软的组合#{2}":[34,34],"#免杀入门教程#免杀基础知识#常见的杀软及其特点#常见杀软的组合#{3}":[35,36],"#免杀入门教程#免杀基础知识#常见的杀软及其特点#常见杀软的组合#{4}":[37,37],"#免杀入门教程#免杀基础知识#常见杀软特点总结":[38,41],"#免杀入门教程#免杀基础知识#常见杀软特点总结#{1}":[39,40],"#免杀入门教程#免杀基础知识#常见杀软特点总结#{2}":[41,41],"#免杀入门教程#[[C2]]推荐":[42,52],"#免杀入门教程#[[C2]]推荐#{1}":[43,44],"#免杀入门教程#[[C2]]推荐#{2}":[45,46],"#免杀入门教程#[[C2]]推荐#{3}":[47,48],"#免杀入门教程#[[C2]]推荐#{4}":[49,49],"#免杀入门教程#[[C2]]推荐#{5}":[50,51],"#免杀入门教程#[[C2]]推荐#{6}":[52,52],"#免杀入门教程#免杀加载器原理":[53,64],"#免杀入门教程#免杀加载器原理#{1}":[54,55],"#免杀入门教程#免杀加载器原理#{2}":[56,57],"#免杀入门教程#免杀加载器原理#{3}":[58,59],"#免杀入门教程#免杀加载器原理#{4}":[60,64]},"last_import":{"mtime":1747536242414,"size":1418,"at":1749024987320,"hash":"7bkm7v"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/免杀技术/免杀技术知识图谱.md"},