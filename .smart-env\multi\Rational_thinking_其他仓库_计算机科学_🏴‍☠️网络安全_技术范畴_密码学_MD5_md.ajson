"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md","last_embed":{"hash":"1xudr5c","at":1750993393585},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06732788,-0.01571889,-0.00645235,-0.03759772,-0.00795972,-0.03631099,-0.00903132,0.07517868,0.05648889,-0.00789491,0.02133442,-0.04720683,0.03807361,0.02720442,0.01054484,0.03614957,0.02952888,-0.02108114,0.00014897,-0.0366902,0.11120227,0.00475095,0.00250798,-0.0906006,-0.00692273,0.0041209,0.03311472,-0.04282993,-0.04947917,-0.18969582,0.00490087,-0.01877956,0.06652465,0.00257382,-0.00499658,-0.02452022,0.0191073,0.1120675,-0.05754454,0.05029704,-0.00375172,0.04004892,0.01096887,-0.01559008,-0.02690643,-0.06359346,-0.02134468,-0.04307663,0.00085203,-0.05280749,-0.0438166,-0.05203138,-0.04871244,-0.00675348,-0.03161425,0.01717688,0.04851616,0.00010358,0.02609424,-0.00227541,0.0122042,0.04010197,-0.21044889,0.09594005,0.01773006,-0.00001549,0.01128264,-0.01961516,0.03906848,0.07610987,-0.05715994,0.00599178,-0.04581544,0.05516368,0.02827025,0.03929086,0.00885748,-0.0940514,-0.0391016,-0.02845404,-0.04884168,0.03807484,-0.02490257,0.0240423,-0.01737425,0.03856578,-0.03754745,0.02668742,0.01410038,-0.00311605,0.04212016,-0.04553649,0.00113892,0.02095613,-0.02017047,-0.03772435,0.05428795,0.03491281,-0.06256893,0.10548549,-0.05839821,-0.00304878,0.00777844,-0.05924957,0.01436478,-0.00550924,0.00459715,-0.02433071,-0.0275882,-0.04904643,-0.04524778,-0.00867199,0.09828923,-0.03837991,0.00287002,0.05537881,0.02471945,-0.03227972,-0.06038418,-0.01753152,-0.01273106,0.01322868,0.06986009,-0.01554142,-0.00991141,-0.00625896,0.00537415,0.08037014,0.01142049,0.01930217,0.07729624,-0.00283091,-0.03329641,-0.03482661,0.02403644,-0.0422383,-0.04566833,0.03142362,0.00899663,-0.05429256,-0.02291513,-0.07669034,-0.01344749,-0.05181666,-0.0776453,0.10565962,-0.02868402,-0.01297177,0.00627391,-0.0525049,0.01659066,0.03239783,0.01540781,0.0464166,-0.02392956,0.02480778,0.08908059,0.13397294,-0.06540818,-0.00191671,-0.01891953,0.00074405,-0.10349461,0.16675931,0.04179572,-0.0536913,-0.04941744,-0.01229489,0.02570802,-0.06657904,0.03296399,0.00527066,-0.00993189,0.03125952,0.02220318,-0.02213488,-0.00515601,-0.0160439,0.03080115,0.01427066,0.04752144,-0.00884444,-0.02711521,0.06756951,-0.01939988,-0.08644625,-0.02932579,-0.05764756,0.02674005,-0.05706387,-0.0643981,0.03663526,-0.0398404,0.0025282,-0.1095764,-0.10127894,0.02101603,0.00403652,0.07125887,-0.04215416,0.06709053,0.03150544,-0.02362544,0.00491191,-0.02203108,-0.06202273,0.01944187,-0.01040929,0.03493706,0.03984503,0.03160783,0.03031354,-0.00281903,0.04578363,-0.00685278,0.03979944,0.0639419,0.00225252,-0.00484571,0.02339961,0.0195901,0.04165725,-0.08236033,-0.22477579,-0.04140045,0.00804511,-0.03616058,0.01326979,-0.0076785,-0.00469359,0.01009644,0.07143071,0.09581703,0.00641681,0.01907553,-0.09546857,-0.02025714,0.0504669,-0.00698318,-0.01750861,-0.00786878,-0.01709322,0.00956678,0.01833013,0.09105261,-0.01678721,-0.02760598,0.04168311,0.01598415,0.1123107,0.04559251,0.03186382,0.06468537,0.02926235,0.02022077,0.03115477,-0.10150468,0.05882411,0.04480884,-0.04281589,-0.06467342,-0.01310661,-0.03096053,-0.01257279,0.0278837,-0.03773398,-0.07768778,0.00047347,-0.0506412,-0.04455093,-0.0054498,-0.0128117,0.0242542,-0.00825061,0.04411208,0.00109351,0.0159355,-0.02924308,-0.05288769,-0.02641041,-0.02288073,-0.01539728,0.01782512,-0.0126017,-0.02815969,0.04200345,0.01917495,-0.00164076,-0.02244642,-0.03640487,-0.03465728,-0.02474811,0.00153162,-0.0246018,0.16967687,-0.01654836,-0.05040506,0.09278486,0.0530542,-0.00101268,-0.043324,0.01290548,-0.02002322,0.04307397,0.01337767,0.05176602,0.06553023,-0.01286572,0.04628628,0.01095129,-0.01689692,0.04493107,-0.07434519,-0.02302474,-0.01072804,-0.01019002,0.00155059,0.07437105,-0.02498434,-0.28522646,0.03570745,-0.01293661,-0.00758682,0.02036559,0.06397742,0.05165977,0.01207291,-0.03188923,0.048219,-0.03954993,0.06126594,0.00578428,-0.03511816,-0.01164561,-0.03947977,0.03230564,-0.03878233,0.02525499,0.01064232,-0.02146881,0.02040716,0.19115636,0.01625818,-0.00386222,0.01318061,0.01089827,0.04889423,0.09387583,0.00743849,0.02727547,-0.05738992,0.08541015,-0.05093507,0.06298395,0.04323376,0.00008421,0.02356786,0.02758656,0.02902403,-0.04361424,0.01609072,-0.06107865,0.03807735,0.06273603,0.03593593,-0.03208158,-0.07768662,-0.01514881,0.0810487,0.01926967,-0.00523568,0.00290572,-0.01549278,0.02252817,0.05904741,-0.00870269,-0.0288669,-0.05123023,-0.033894,0.03153287,0.02181029,0.06284046,0.10999634,0.01462313],"last_embed":{"hash":"de18d8c1c234c0a4f3b37870e69af29f2c4a09b0605ad2bf11787e0a9d423478","tokens":411}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.08040208,-0.02472468,-0.03800515,0.01096171,-0.04448455,0.03759443,0.07427799,-0.03612882,0.02392665,-0.02704225,0.03014888,0.00168041,-0.05193123,0.00552947,0.00035144,0.02098278,-0.03156999,0.04610604,0.03621627,-0.09381458,-0.00857666,0.01242899,-0.00202059,0.03770863,-0.04400896,-0.02621246,-0.00220278,-0.06233228,0.01062927,0.0211796,0.02098746,0.00873001,-0.03144563,-0.00974891,-0.01622682,0.01427944,0.01168155,0.00943258,-0.01886205,0.0369039,-0.03446503,0.00673152,-0.00427569,-0.02351829,-0.02810329,-0.00656888,0.00789198,0.00589495,0.01421716,0.05190978,0.01192393,-0.06288281,0.01955207,0.0419709,0.0301856,0.03987418,-0.0087297,-0.02367435,-0.02844006,-0.01902068,0.05177495,0.00631872,0.0379312,0.02297548,-0.01654302,0.02477873,0.03919381,0.01477404,-0.00858192,-0.01864623,-0.05667574,0.00484516,0.04844883,-0.04778826,0.04511433,-0.06109884,0.01907328,-0.02240938,-0.0940382,0.0191031,-0.02529945,0.03068233,-0.02619998,-0.02369587,0.01832397,-0.00819043,0.0436227,-0.02693235,0.03346329,0.02643725,0.02871531,-0.0390033,0.02865324,-0.01087805,0.07953093,-0.01912152,0.0644471,0.00816518,0.00084196,-0.00838352,-0.01414073,0.0065329,0.01556094,0.00216976,0.01834544,-0.00971666,-0.03265315,0.01381149,-0.03990762,-0.01169888,-0.00615832,0.02599888,-0.03604401,-0.07249273,-0.00290091,0.03824061,-0.00971107,0.03726839,0.03056582,0.09106837,0.09308908,0.06846491,0.00011466,-0.03579324,-0.06599876,-0.08573903,0.0622534,-0.04710324,0.04447857,0.04466708,0.07539119,-0.03460985,-0.04282393,-0.02081443,-0.0206484,0.0126071,0.03408363,-0.02769749,-0.00557083,-0.04488122,-0.00805281,-0.00566191,0.00267946,-0.01526191,-0.01353954,0.02895484,-0.07863764,0.01293755,-0.03228447,-0.00064808,0.00435662,-0.042036,-0.01573674,-0.04688952,-0.02121941,0.06485129,-0.00201797,0.00633595,0.03148346,0.02164919,-0.0400014,0.01951545,0.03041734,0.06250252,0.02060492,0.00909993,0.03028034,0.02192306,0.04771645,-0.04286135,-0.01793162,0.03409923,0.01471921,0.05050042,0.02026275,-0.01635677,0.01219501,0.03650945,-0.0076811,0.01851353,-0.03191105,0.02337303,-0.03825762,0.01780758,0.03416054,-0.00784872,0.02851392,0.01357929,0.04793692,-0.01672263,0.0344553,-0.02020254,-0.00965887,0.05974049,0.02542269,0.00983281,0.01828999,-0.03585114,-0.0333165,0.0172113,-0.04419363,-0.01220758,-0.0509993,-0.02080057,0.06094314,0.03405029,-0.0006768,-0.03300475,-0.01606105,0.06266591,0.03636229,0.02735175,0.04275397,0.04609557,-0.0298705,-0.05451994,-0.02107544,0.0382016,0.01141946,-0.0321666,0.01709279,-0.00613248,0.02094446,-0.05895083,0.01512316,-0.05864917,-0.0131167,0.05706732,-0.01365739,0.03051957,0.01293552,0.02732804,-0.05118624,-0.00189302,-0.05081428,-0.02467096,-0.00673111,0.02690384,0.00901274,-0.03437327,0.00623059,-0.01056581,-0.00256654,-0.02727148,-0.10751788,-0.01908834,0.02079231,-0.02420781,0.03611436,-0.02710741,0.06744473,0.04169328,-0.05163138,0.13433442,-0.05022023,-0.04165866,-0.02456554,-0.01945984,-0.01071889,-0.01604531,0.03216518,0.0500412,-0.01411748,0.05828966,-0.07201472,-0.00940833,-0.0041769,-0.00361479,-0.01555047,-0.02095383,0.01912178,0.00511052,0.04192689,-0.02572175,0.0364983,-0.03888687,0.00855991,0.0072112,-0.0197491,-0.03696334,-0.01256894,-0.02364376,0.08004818,0.0224847,-0.02998171,-0.0104474,0.01924274,0.0526036,0.0185009,0.03033808,0.0474719,0.01016654,-0.04957687,0.02939086,-0.03177455,-0.04897685,0.07606944,-0.00987212,0.00165806,-0.03772623,-0.04024327,0.02851237,-0.00278231,-0.00359333,-0.0489649,0.04771821,0.00294504,0.04027752,0.04704498,-0.0398654,-0.02398385,-0.05276769,-0.00377491,0.03669839,-0.03118087,0.07796827,0.02660582,-0.02159316,0.01236886,0.02036264,-0.03685893,0.00061757,-0.00586557,-0.01573126,-0.01678758,-0.02804342,-0.00072799,0.01690181,0.04924024,-0.01004259,-0.01672692,0.02000238,-0.04066888,-0.00635386,0.0893658,0.01540379,-0.03917003,-0.07588653,0.07335372,-0.07172064,0.03195694,-0.04392978,0.05010097,0.01547903,0.00914429,-0.03913832,-0.00207592,-0.0092107,-0.02285607,-0.04883119,-0.0243606,-0.03834466,0.03215199,0.01374894,0.0519602,-0.0157098,-0.03528489,0.04078083,-0.01895496,-0.03033378,0.002157,-0.040048,-0.09645075,-0.01303723,0.00945813,-0.00888526,0.08480093,0.03487615,0.0056,-0.00128396,-0.0165639,0.02040395,0.03494198,-0.04567712,0.01520176,-0.00512479,-0.08498291,-0.00979417,-0.00563919,-0.03295095,0.00842528,0.02052612,0.02362512,0.01195847,-0.07257877,-0.00821387,0.04676804,-0.01082117,0.01316833,-0.00356168,-0.03420885,-0.03663784,-0.01014389,-0.00737782,0.0388779,-0.02096924,0.01243108,0.04423445,-0.00515253,0.04287703,0.01286748,-0.0720429,-0.03471652,0.01084621,0.01145497,-0.0138491,-0.01798725,0.00163332,-0.01761598,-0.01022535,-0.01202764,0.07309206,-0.01926816,0.04641889,0.0598941,0.06169654,-0.02043181,-0.0899996,-0.02435283,0.00701197,0.02905769,-0.01894814,0.02499048,0.0256189,-0.03948177,-0.00828659,-0.00482656,0.03064888,-0.02548086,-0.05780302,0.01976201,-0.00630429,-0.02628561,-0.05457199,0.04177056,-0.02773065,0.075638,-0.00658594,0.01297315,0.03382468,-0.01035252,-0.06373837,-0.01704494,-0.04172071,0.06618911,-0.03532482,-0.01006177,-0.01721724,-0.00051754,0.00735896,-0.00753817,0.00734428,-0.07779951,-0.08403069,0.01152697,-0.0115652,-0.02760584,-0.05010263,-0.00683762,0.02558222,-0.02751067,0.05618889,-0.00874491,-0.01443393,0.01503253,0.01824453,0.02700463,0.03253045,0.02143137,-0.03653233,0.05768272,-0.03784065,0.00170607,-0.0008297,0.04755475,-0.00432523,-0.00062945,-0.00381004,-0.03618053,0.00136729,-0.01023021,0.0119151,0.02032013,0.00415924,-0.04061293,0.0723966,-0.04574901,0.00585536,0.02445968,-0.0539191,0.02537681,-0.00007292,0.0203689,-0.03292491,-0.00648291,0.01702819,0.00784032,0.04143647,-0.02871719,0.0492133,0.02878869,0.011947,-0.04526581,0.02808029,0.03709015,0.03901468,-0.0351544,-0.00100757,0.05715474,-0.05745038,-0.01685406,-0.010143,0.05776086,-0.03601885,0.03953481,-0.01285368,-0.01647423,0.05574543,0.00031078,-0.05077202,-0.05410557,-0.0047589,-0.02274901,0.00995698,0.00085597,-0.04070977,0.0228993,0.02798842,0.06269997,0.01204149,-0.02015336,-0.02215858,0.03297336,-0.01659065,0.01988318,-0.00027698,0.0034978,0.01906493,0.04209608,0.03794289,0.07245075,-0.01061953,0.01684375,0.0451078,0.01243378,-0.00076526,0.04030799,-0.02805993,0.04611333,0.01897719,0.01799976,-0.06458289,0.02006586,-0.00855063,0.01539567,0.01645307,0.00843851,-0.08821113,0.00898463,0.03824539,-0.02066687,-0.05654311,-0.01639643,0.05071346,-0.06845608,0.01053553,-0.09065408,0.04921869,-0.00750537,-0.00740285,-0.0182549,-0.01682563,0.0435292,0.02712328,-0.0304681,0.02145595,0.00968021,-0.03829808,0.00523723,0.03709427,0.0407197,-0.03093179,-0.01972609,-0.04646852,-0.00708468,0.01566953,-0.02525963,0.00387435,-0.01409399,0.00079057,0.06342046,-0.05827469,-0.00240492,-0.01514819,0.01914678,0.00944758,-0.01150173,0.03998945,0.08638931,-0.02560125,0.00065584,-0.00062133,0.01301637,-0.00348756,-0.02660118,-0.06189405,-0.01163083,-0.10341354,-0.00445079,0.0158618,-0.00591566,-0.00390251,-0.03800531,0.07419919,-0.01151568,-0.00712229,-0.01367473,-0.00265572,-0.00799207,0.00502221,-0.02320371,-0.03511216,-0.03083902,-0.0064269,0.04834227,-0.03099818,0.03828712,-0.00601182,0.02445884,-0.04338004,-0.02859528,-0.07446033,-0.02761962,0.00884734,-0.00803909,0.00098248,-0.02456184,-0.07822955,-0.04665958,-0.04043781,-0.01781117,-0.0284758,0.00825238,0.10219391,-0.03381509,-0.01180387,-0.04734677,0.001,-0.01853014,0.01664929,0.00327763,0.0136221,-0.04530355,0.00284174,-0.04378653,-0.06950466,-0.01761749,0.02263552,0.02159291,0.06215461,-0.02583156,-0.00708318,0.02799785,-0.00741227,0.00997977,0.00456556,0.02258557,0.00256603,-0.04149508,0.00611925,0.00146412,-0.05857661,-0.01383616,-0.01565823,-0.00579208,0.01106989,0.00182531,0.02578912,0.03791601,-0.01567387,-0.13033564,0.02432479,0.03045561,-0.00973829,0.04669993,-0.045788,0.03474484,-0.01966457,0.00890719,-0.01022786,0.04543406,0.0519688,-0.01765289,-0.06434368,0.07315517,-0.01732809,0.01607845,-0.06889578,-0.002835,0.0135891,0.0253048,-0.00186627,-0.05259873,-0.04123865,-0.03349966,-0.00753623,0.00879323,-0.0134006,0.03006775,-0.04056307,-0.01658957,0.01297499,-0.00922113,0.03312487,0.02404634,-0.03505512,-0.00347121,0.04505229,-0.0584496,-0.01708699,-0.02627319,0.05255738,0.05006741,0.00505992,-0.09132924,-0.02010971,0.0253225,0.06843065,0.00788798,-0.00230957,-0.0568721,-0.05155585,0.03534289,-0.00454071,0.05669323,0.01460151,0.0111364,0.00313812,0.02930814,0.03312829,0.02637084,-0.02929872,-0.05416712,-0.04826395,0.01351536,-0.03167967,0.00166007,0.0129824,-0.05243686,0.04560117,0.0256341,0.01374403,-0.01001847,0.0203183,0.07670203,0.05829391,-0.04781146,-0.02549225,9.2e-7,-0.03677217,-0.02377736,-0.03057954,-0.01327156,0.05444435,0.00062167,-0.03825058,-0.0520431,0.01528353],"last_embed":{"tokens":1078,"hash":"1xudr5c"}}},"last_read":{"hash":"1xudr5c","at":1750993393585},"class_name":"SmartSource","outlinks":[{"title":"散列函数","target":"散列函数","line":18},{"title":"SHA-1","target":"SHA-1","line":77},{"title":"X.509证书","target":"X.509证书","line":85},{"title":"RFC 6151","target":"https://datatracker.ietf.org/doc/html/rfc6151","line":90}],"metadata":{"aliases":["Message Digest Algorithm 5"],"英文":"Message Digest Algorithm 5","tags":null,"cssclasses":["editor-full"],"输出长度(位)":128,"算法安全等级":"低","发布时间":"1992-08-01"},"blocks":{"#---frontmatter---":[1,11],"#已被破解的加密算法  #哈希算法":[12,14],"#已被破解的加密算法  #哈希算法#{1}":[14,14],"#简介":[15,37],"#简介#{1}":[16,37],"#算法原理":[38,64],"#算法原理#{1}":[40,56],"#算法原理#{2}":[57,57],"#算法原理#{3}":[58,58],"#算法原理#{4}":[59,59],"#算法原理#{5}":[60,64],"#时间线":[65,95],"#时间线#{1}":[66,66],"#时间线#{2}":[67,68],"#时间线#{3}":[69,70],"#时间线#{4}":[71,72],"#时间线#{5}":[73,74],"#时间线#{6}":[75,75],"#时间线#{7}":[76,77],"#时间线#{8}":[78,79],"#时间线#{9}":[80,81],"#时间线#{10}":[82,83],"#时间线#{11}":[84,85],"#时间线#{12}":[86,86],"#时间线#{13}":[87,88],"#时间线#{14}":[89,90],"#时间线#{15}":[91,93],"#时间线#{16}":[94,95]},"last_import":{"mtime":1737964449164,"size":4121,"at":1749024987320,"hash":"1xudr5c"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#---frontmatter---","lines":[1,11],"size":153,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#已被破解的加密算法  #哈希算法": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#已被破解的加密算法  #哈希算法","lines":[12,14],"size":22,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#已被破解的加密算法  #哈希算法#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#已被破解的加密算法  #哈希算法#{1}","lines":[14,14],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#简介": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#简介","lines":[15,37],"size":580,"outlinks":[{"title":"散列函数","target":"散列函数","line":4}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#简介#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#简介#{1}","lines":[16,37],"size":575,"outlinks":[{"title":"散列函数","target":"散列函数","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#算法原理": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#算法原理","lines":[38,64],"size":462,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#算法原理#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#算法原理#{1}","lines":[40,56],"size":240,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#算法原理#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#算法原理#{2}","lines":[57,57],"size":10,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#算法原理#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#算法原理#{3}","lines":[58,58],"size":91,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#算法原理#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#算法原理#{4}","lines":[59,59],"size":8,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#算法原理#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#算法原理#{5}","lines":[60,64],"size":101,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#时间线": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#时间线","lines":[65,95],"size":816,"outlinks":[{"title":"SHA-1","target":"SHA-1","line":13},{"title":"X.509证书","target":"X.509证书","line":21},{"title":"RFC 6151","target":"https://datatracker.ietf.org/doc/html/rfc6151","line":26}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#时间线#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#时间线#{1}","lines":[66,66],"size":15,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#时间线#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#时间线#{2}","lines":[67,68],"size":40,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#时间线#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#时间线#{3}","lines":[69,70],"size":31,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#时间线#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#时间线#{4}","lines":[71,72],"size":56,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#时间线#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#时间线#{5}","lines":[73,74],"size":62,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#时间线#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#时间线#{6}","lines":[75,75],"size":45,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#时间线#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#时间线#{7}","lines":[76,77],"size":57,"outlinks":[{"title":"SHA-1","target":"SHA-1","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#时间线#{8}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#时间线#{8}","lines":[78,79],"size":79,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#时间线#{9}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#时间线#{9}","lines":[80,81],"size":63,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#时间线#{10}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#时间线#{10}","lines":[82,83],"size":57,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#时间线#{11}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#时间线#{11}","lines":[84,85],"size":71,"outlinks":[{"title":"X.509证书","target":"X.509证书","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#时间线#{12}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#时间线#{12}","lines":[86,86],"size":11,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#时间线#{13}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#时间线#{13}","lines":[87,88],"size":52,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#时间线#{14}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#时间线#{14}","lines":[89,90],"size":85,"outlinks":[{"title":"RFC 6151","target":"https://datatracker.ietf.org/doc/html/rfc6151","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#时间线#{15}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#时间线#{15}","lines":[91,93],"size":67,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#时间线#{16}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md#时间线#{16}","lines":[94,95],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md","last_embed":{"hash":"1xudr5c","at":1751079979676},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.06732788,-0.01571889,-0.00645235,-0.03759772,-0.00795972,-0.03631099,-0.00903132,0.07517868,0.05648889,-0.00789491,0.02133442,-0.04720683,0.03807361,0.02720442,0.01054484,0.03614957,0.02952888,-0.02108114,0.00014897,-0.0366902,0.11120227,0.00475095,0.00250798,-0.0906006,-0.00692273,0.0041209,0.03311472,-0.04282993,-0.04947917,-0.18969582,0.00490087,-0.01877956,0.06652465,0.00257382,-0.00499658,-0.02452022,0.0191073,0.1120675,-0.05754454,0.05029704,-0.00375172,0.04004892,0.01096887,-0.01559008,-0.02690643,-0.06359346,-0.02134468,-0.04307663,0.00085203,-0.05280749,-0.0438166,-0.05203138,-0.04871244,-0.00675348,-0.03161425,0.01717688,0.04851616,0.00010358,0.02609424,-0.00227541,0.0122042,0.04010197,-0.21044889,0.09594005,0.01773006,-0.00001549,0.01128264,-0.01961516,0.03906848,0.07610987,-0.05715994,0.00599178,-0.04581544,0.05516368,0.02827025,0.03929086,0.00885748,-0.0940514,-0.0391016,-0.02845404,-0.04884168,0.03807484,-0.02490257,0.0240423,-0.01737425,0.03856578,-0.03754745,0.02668742,0.01410038,-0.00311605,0.04212016,-0.04553649,0.00113892,0.02095613,-0.02017047,-0.03772435,0.05428795,0.03491281,-0.06256893,0.10548549,-0.05839821,-0.00304878,0.00777844,-0.05924957,0.01436478,-0.00550924,0.00459715,-0.02433071,-0.0275882,-0.04904643,-0.04524778,-0.00867199,0.09828923,-0.03837991,0.00287002,0.05537881,0.02471945,-0.03227972,-0.06038418,-0.01753152,-0.01273106,0.01322868,0.06986009,-0.01554142,-0.00991141,-0.00625896,0.00537415,0.08037014,0.01142049,0.01930217,0.07729624,-0.00283091,-0.03329641,-0.03482661,0.02403644,-0.0422383,-0.04566833,0.03142362,0.00899663,-0.05429256,-0.02291513,-0.07669034,-0.01344749,-0.05181666,-0.0776453,0.10565962,-0.02868402,-0.01297177,0.00627391,-0.0525049,0.01659066,0.03239783,0.01540781,0.0464166,-0.02392956,0.02480778,0.08908059,0.13397294,-0.06540818,-0.00191671,-0.01891953,0.00074405,-0.10349461,0.16675931,0.04179572,-0.0536913,-0.04941744,-0.01229489,0.02570802,-0.06657904,0.03296399,0.00527066,-0.00993189,0.03125952,0.02220318,-0.02213488,-0.00515601,-0.0160439,0.03080115,0.01427066,0.04752144,-0.00884444,-0.02711521,0.06756951,-0.01939988,-0.08644625,-0.02932579,-0.05764756,0.02674005,-0.05706387,-0.0643981,0.03663526,-0.0398404,0.0025282,-0.1095764,-0.10127894,0.02101603,0.00403652,0.07125887,-0.04215416,0.06709053,0.03150544,-0.02362544,0.00491191,-0.02203108,-0.06202273,0.01944187,-0.01040929,0.03493706,0.03984503,0.03160783,0.03031354,-0.00281903,0.04578363,-0.00685278,0.03979944,0.0639419,0.00225252,-0.00484571,0.02339961,0.0195901,0.04165725,-0.08236033,-0.22477579,-0.04140045,0.00804511,-0.03616058,0.01326979,-0.0076785,-0.00469359,0.01009644,0.07143071,0.09581703,0.00641681,0.01907553,-0.09546857,-0.02025714,0.0504669,-0.00698318,-0.01750861,-0.00786878,-0.01709322,0.00956678,0.01833013,0.09105261,-0.01678721,-0.02760598,0.04168311,0.01598415,0.1123107,0.04559251,0.03186382,0.06468537,0.02926235,0.02022077,0.03115477,-0.10150468,0.05882411,0.04480884,-0.04281589,-0.06467342,-0.01310661,-0.03096053,-0.01257279,0.0278837,-0.03773398,-0.07768778,0.00047347,-0.0506412,-0.04455093,-0.0054498,-0.0128117,0.0242542,-0.00825061,0.04411208,0.00109351,0.0159355,-0.02924308,-0.05288769,-0.02641041,-0.02288073,-0.01539728,0.01782512,-0.0126017,-0.02815969,0.04200345,0.01917495,-0.00164076,-0.02244642,-0.03640487,-0.03465728,-0.02474811,0.00153162,-0.0246018,0.16967687,-0.01654836,-0.05040506,0.09278486,0.0530542,-0.00101268,-0.043324,0.01290548,-0.02002322,0.04307397,0.01337767,0.05176602,0.06553023,-0.01286572,0.04628628,0.01095129,-0.01689692,0.04493107,-0.07434519,-0.02302474,-0.01072804,-0.01019002,0.00155059,0.07437105,-0.02498434,-0.28522646,0.03570745,-0.01293661,-0.00758682,0.02036559,0.06397742,0.05165977,0.01207291,-0.03188923,0.048219,-0.03954993,0.06126594,0.00578428,-0.03511816,-0.01164561,-0.03947977,0.03230564,-0.03878233,0.02525499,0.01064232,-0.02146881,0.02040716,0.19115636,0.01625818,-0.00386222,0.01318061,0.01089827,0.04889423,0.09387583,0.00743849,0.02727547,-0.05738992,0.08541015,-0.05093507,0.06298395,0.04323376,0.00008421,0.02356786,0.02758656,0.02902403,-0.04361424,0.01609072,-0.06107865,0.03807735,0.06273603,0.03593593,-0.03208158,-0.07768662,-0.01514881,0.0810487,0.01926967,-0.00523568,0.00290572,-0.01549278,0.02252817,0.05904741,-0.00870269,-0.0288669,-0.05123023,-0.033894,0.03153287,0.02181029,0.06284046,0.10999634,0.01462313],"last_embed":{"hash":"de18d8c1c234c0a4f3b37870e69af29f2c4a09b0605ad2bf11787e0a9d423478","tokens":411}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.08040208,-0.02472468,-0.03800515,0.01096171,-0.04448455,0.03759443,0.07427799,-0.03612882,0.02392665,-0.02704225,0.03014888,0.00168041,-0.05193123,0.00552947,0.00035144,0.02098278,-0.03156999,0.04610604,0.03621627,-0.09381458,-0.00857666,0.01242899,-0.00202059,0.03770863,-0.04400896,-0.02621246,-0.00220278,-0.06233228,0.01062927,0.0211796,0.02098746,0.00873001,-0.03144563,-0.00974891,-0.01622682,0.01427944,0.01168155,0.00943258,-0.01886205,0.0369039,-0.03446503,0.00673152,-0.00427569,-0.02351829,-0.02810329,-0.00656888,0.00789198,0.00589495,0.01421716,0.05190978,0.01192393,-0.06288281,0.01955207,0.0419709,0.0301856,0.03987418,-0.0087297,-0.02367435,-0.02844006,-0.01902068,0.05177495,0.00631872,0.0379312,0.02297548,-0.01654302,0.02477873,0.03919381,0.01477404,-0.00858192,-0.01864623,-0.05667574,0.00484516,0.04844883,-0.04778826,0.04511433,-0.06109884,0.01907328,-0.02240938,-0.0940382,0.0191031,-0.02529945,0.03068233,-0.02619998,-0.02369587,0.01832397,-0.00819043,0.0436227,-0.02693235,0.03346329,0.02643725,0.02871531,-0.0390033,0.02865324,-0.01087805,0.07953093,-0.01912152,0.0644471,0.00816518,0.00084196,-0.00838352,-0.01414073,0.0065329,0.01556094,0.00216976,0.01834544,-0.00971666,-0.03265315,0.01381149,-0.03990762,-0.01169888,-0.00615832,0.02599888,-0.03604401,-0.07249273,-0.00290091,0.03824061,-0.00971107,0.03726839,0.03056582,0.09106837,0.09308908,0.06846491,0.00011466,-0.03579324,-0.06599876,-0.08573903,0.0622534,-0.04710324,0.04447857,0.04466708,0.07539119,-0.03460985,-0.04282393,-0.02081443,-0.0206484,0.0126071,0.03408363,-0.02769749,-0.00557083,-0.04488122,-0.00805281,-0.00566191,0.00267946,-0.01526191,-0.01353954,0.02895484,-0.07863764,0.01293755,-0.03228447,-0.00064808,0.00435662,-0.042036,-0.01573674,-0.04688952,-0.02121941,0.06485129,-0.00201797,0.00633595,0.03148346,0.02164919,-0.0400014,0.01951545,0.03041734,0.06250252,0.02060492,0.00909993,0.03028034,0.02192306,0.04771645,-0.04286135,-0.01793162,0.03409923,0.01471921,0.05050042,0.02026275,-0.01635677,0.01219501,0.03650945,-0.0076811,0.01851353,-0.03191105,0.02337303,-0.03825762,0.01780758,0.03416054,-0.00784872,0.02851392,0.01357929,0.04793692,-0.01672263,0.0344553,-0.02020254,-0.00965887,0.05974049,0.02542269,0.00983281,0.01828999,-0.03585114,-0.0333165,0.0172113,-0.04419363,-0.01220758,-0.0509993,-0.02080057,0.06094314,0.03405029,-0.0006768,-0.03300475,-0.01606105,0.06266591,0.03636229,0.02735175,0.04275397,0.04609557,-0.0298705,-0.05451994,-0.02107544,0.0382016,0.01141946,-0.0321666,0.01709279,-0.00613248,0.02094446,-0.05895083,0.01512316,-0.05864917,-0.0131167,0.05706732,-0.01365739,0.03051957,0.01293552,0.02732804,-0.05118624,-0.00189302,-0.05081428,-0.02467096,-0.00673111,0.02690384,0.00901274,-0.03437327,0.00623059,-0.01056581,-0.00256654,-0.02727148,-0.10751788,-0.01908834,0.02079231,-0.02420781,0.03611436,-0.02710741,0.06744473,0.04169328,-0.05163138,0.13433442,-0.05022023,-0.04165866,-0.02456554,-0.01945984,-0.01071889,-0.01604531,0.03216518,0.0500412,-0.01411748,0.05828966,-0.07201472,-0.00940833,-0.0041769,-0.00361479,-0.01555047,-0.02095383,0.01912178,0.00511052,0.04192689,-0.02572175,0.0364983,-0.03888687,0.00855991,0.0072112,-0.0197491,-0.03696334,-0.01256894,-0.02364376,0.08004818,0.0224847,-0.02998171,-0.0104474,0.01924274,0.0526036,0.0185009,0.03033808,0.0474719,0.01016654,-0.04957687,0.02939086,-0.03177455,-0.04897685,0.07606944,-0.00987212,0.00165806,-0.03772623,-0.04024327,0.02851237,-0.00278231,-0.00359333,-0.0489649,0.04771821,0.00294504,0.04027752,0.04704498,-0.0398654,-0.02398385,-0.05276769,-0.00377491,0.03669839,-0.03118087,0.07796827,0.02660582,-0.02159316,0.01236886,0.02036264,-0.03685893,0.00061757,-0.00586557,-0.01573126,-0.01678758,-0.02804342,-0.00072799,0.01690181,0.04924024,-0.01004259,-0.01672692,0.02000238,-0.04066888,-0.00635386,0.0893658,0.01540379,-0.03917003,-0.07588653,0.07335372,-0.07172064,0.03195694,-0.04392978,0.05010097,0.01547903,0.00914429,-0.03913832,-0.00207592,-0.0092107,-0.02285607,-0.04883119,-0.0243606,-0.03834466,0.03215199,0.01374894,0.0519602,-0.0157098,-0.03528489,0.04078083,-0.01895496,-0.03033378,0.002157,-0.040048,-0.09645075,-0.01303723,0.00945813,-0.00888526,0.08480093,0.03487615,0.0056,-0.00128396,-0.0165639,0.02040395,0.03494198,-0.04567712,0.01520176,-0.00512479,-0.08498291,-0.00979417,-0.00563919,-0.03295095,0.00842528,0.02052612,0.02362512,0.01195847,-0.07257877,-0.00821387,0.04676804,-0.01082117,0.01316833,-0.00356168,-0.03420885,-0.03663784,-0.01014389,-0.00737782,0.0388779,-0.02096924,0.01243108,0.04423445,-0.00515253,0.04287703,0.01286748,-0.0720429,-0.03471652,0.01084621,0.01145497,-0.0138491,-0.01798725,0.00163332,-0.01761598,-0.01022535,-0.01202764,0.07309206,-0.01926816,0.04641889,0.0598941,0.06169654,-0.02043181,-0.0899996,-0.02435283,0.00701197,0.02905769,-0.01894814,0.02499048,0.0256189,-0.03948177,-0.00828659,-0.00482656,0.03064888,-0.02548086,-0.05780302,0.01976201,-0.00630429,-0.02628561,-0.05457199,0.04177056,-0.02773065,0.075638,-0.00658594,0.01297315,0.03382468,-0.01035252,-0.06373837,-0.01704494,-0.04172071,0.06618911,-0.03532482,-0.01006177,-0.01721724,-0.00051754,0.00735896,-0.00753817,0.00734428,-0.07779951,-0.08403069,0.01152697,-0.0115652,-0.02760584,-0.05010263,-0.00683762,0.02558222,-0.02751067,0.05618889,-0.00874491,-0.01443393,0.01503253,0.01824453,0.02700463,0.03253045,0.02143137,-0.03653233,0.05768272,-0.03784065,0.00170607,-0.0008297,0.04755475,-0.00432523,-0.00062945,-0.00381004,-0.03618053,0.00136729,-0.01023021,0.0119151,0.02032013,0.00415924,-0.04061293,0.0723966,-0.04574901,0.00585536,0.02445968,-0.0539191,0.02537681,-0.00007292,0.0203689,-0.03292491,-0.00648291,0.01702819,0.00784032,0.04143647,-0.02871719,0.0492133,0.02878869,0.011947,-0.04526581,0.02808029,0.03709015,0.03901468,-0.0351544,-0.00100757,0.05715474,-0.05745038,-0.01685406,-0.010143,0.05776086,-0.03601885,0.03953481,-0.01285368,-0.01647423,0.05574543,0.00031078,-0.05077202,-0.05410557,-0.0047589,-0.02274901,0.00995698,0.00085597,-0.04070977,0.0228993,0.02798842,0.06269997,0.01204149,-0.02015336,-0.02215858,0.03297336,-0.01659065,0.01988318,-0.00027698,0.0034978,0.01906493,0.04209608,0.03794289,0.07245075,-0.01061953,0.01684375,0.0451078,0.01243378,-0.00076526,0.04030799,-0.02805993,0.04611333,0.01897719,0.01799976,-0.06458289,0.02006586,-0.00855063,0.01539567,0.01645307,0.00843851,-0.08821113,0.00898463,0.03824539,-0.02066687,-0.05654311,-0.01639643,0.05071346,-0.06845608,0.01053553,-0.09065408,0.04921869,-0.00750537,-0.00740285,-0.0182549,-0.01682563,0.0435292,0.02712328,-0.0304681,0.02145595,0.00968021,-0.03829808,0.00523723,0.03709427,0.0407197,-0.03093179,-0.01972609,-0.04646852,-0.00708468,0.01566953,-0.02525963,0.00387435,-0.01409399,0.00079057,0.06342046,-0.05827469,-0.00240492,-0.01514819,0.01914678,0.00944758,-0.01150173,0.03998945,0.08638931,-0.02560125,0.00065584,-0.00062133,0.01301637,-0.00348756,-0.02660118,-0.06189405,-0.01163083,-0.10341354,-0.00445079,0.0158618,-0.00591566,-0.00390251,-0.03800531,0.07419919,-0.01151568,-0.00712229,-0.01367473,-0.00265572,-0.00799207,0.00502221,-0.02320371,-0.03511216,-0.03083902,-0.0064269,0.04834227,-0.03099818,0.03828712,-0.00601182,0.02445884,-0.04338004,-0.02859528,-0.07446033,-0.02761962,0.00884734,-0.00803909,0.00098248,-0.02456184,-0.07822955,-0.04665958,-0.04043781,-0.01781117,-0.0284758,0.00825238,0.10219391,-0.03381509,-0.01180387,-0.04734677,0.001,-0.01853014,0.01664929,0.00327763,0.0136221,-0.04530355,0.00284174,-0.04378653,-0.06950466,-0.01761749,0.02263552,0.02159291,0.06215461,-0.02583156,-0.00708318,0.02799785,-0.00741227,0.00997977,0.00456556,0.02258557,0.00256603,-0.04149508,0.00611925,0.00146412,-0.05857661,-0.01383616,-0.01565823,-0.00579208,0.01106989,0.00182531,0.02578912,0.03791601,-0.01567387,-0.13033564,0.02432479,0.03045561,-0.00973829,0.04669993,-0.045788,0.03474484,-0.01966457,0.00890719,-0.01022786,0.04543406,0.0519688,-0.01765289,-0.06434368,0.07315517,-0.01732809,0.01607845,-0.06889578,-0.002835,0.0135891,0.0253048,-0.00186627,-0.05259873,-0.04123865,-0.03349966,-0.00753623,0.00879323,-0.0134006,0.03006775,-0.04056307,-0.01658957,0.01297499,-0.00922113,0.03312487,0.02404634,-0.03505512,-0.00347121,0.04505229,-0.0584496,-0.01708699,-0.02627319,0.05255738,0.05006741,0.00505992,-0.09132924,-0.02010971,0.0253225,0.06843065,0.00788798,-0.00230957,-0.0568721,-0.05155585,0.03534289,-0.00454071,0.05669323,0.01460151,0.0111364,0.00313812,0.02930814,0.03312829,0.02637084,-0.02929872,-0.05416712,-0.04826395,0.01351536,-0.03167967,0.00166007,0.0129824,-0.05243686,0.04560117,0.0256341,0.01374403,-0.01001847,0.0203183,0.07670203,0.05829391,-0.04781146,-0.02549225,9.2e-7,-0.03677217,-0.02377736,-0.03057954,-0.01327156,0.05444435,0.00062167,-0.03825058,-0.0520431,0.01528353],"last_embed":{"tokens":1078,"hash":"1xudr5c"}}},"last_read":{"hash":"1xudr5c","at":1751079979676},"class_name":"SmartSource","outlinks":[{"title":"散列函数","target":"散列函数","line":18},{"title":"SHA-1","target":"SHA-1","line":77},{"title":"X.509证书","target":"X.509证书","line":85},{"title":"RFC 6151","target":"https://datatracker.ietf.org/doc/html/rfc6151","line":90}],"metadata":{"aliases":["Message Digest Algorithm 5"],"英文":"Message Digest Algorithm 5","tags":null,"cssclasses":["editor-full"],"输出长度(位)":128,"算法安全等级":"低","发布时间":"1992-08-01"},"blocks":{"#---frontmatter---":[1,11],"#已被破解的加密算法  #哈希算法":[12,14],"#已被破解的加密算法  #哈希算法#{1}":[14,14],"#简介":[15,37],"#简介#{1}":[16,37],"#算法原理":[38,64],"#算法原理#{1}":[40,56],"#算法原理#{2}":[57,57],"#算法原理#{3}":[58,58],"#算法原理#{4}":[59,59],"#算法原理#{5}":[60,64],"#时间线":[65,95],"#时间线#{1}":[66,66],"#时间线#{2}":[67,68],"#时间线#{3}":[69,70],"#时间线#{4}":[71,72],"#时间线#{5}":[73,74],"#时间线#{6}":[75,75],"#时间线#{7}":[76,77],"#时间线#{8}":[78,79],"#时间线#{9}":[80,81],"#时间线#{10}":[82,83],"#时间线#{11}":[84,85],"#时间线#{12}":[86,86],"#时间线#{13}":[87,88],"#时间线#{14}":[89,90],"#时间线#{15}":[91,93],"#时间线#{16}":[94,95]},"last_import":{"mtime":1737964449164,"size":4121,"at":1749024987320,"hash":"1xudr5c"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/MD5.md"},