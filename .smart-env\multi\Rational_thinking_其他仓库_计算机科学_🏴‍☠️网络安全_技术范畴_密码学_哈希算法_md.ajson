"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md","last_embed":{"hash":"1sf2ttw","at":1750993393861},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07715219,-0.00072927,-0.02640039,-0.05574419,0.00182848,-0.00299448,0.00293245,0.04326639,0.04246547,0.02996299,-0.01819062,-0.04367503,0.04793756,0.04883799,0.0401596,0.05103969,-0.00506774,0.05457934,-0.00001902,0.00507533,0.10344055,-0.01334675,-0.01136077,-0.05538842,-0.02387303,0.0251155,0.02791307,-0.01268815,-0.0145403,-0.18456188,-0.01199607,0.01764121,0.04760815,0.0101367,-0.03452471,-0.03042567,0.04082165,0.06772988,-0.0275699,0.04141835,0.0034978,0.03643185,0.02552502,-0.01900548,-0.01174523,-0.03459849,-0.01223051,-0.02123332,0.00376411,-0.046753,-0.05789801,-0.02863765,-0.03853361,-0.01478422,-0.07525309,-0.00857317,0.04814374,0.03041369,0.03006122,-0.02928725,-0.01249431,0.04328435,-0.19347569,0.04944018,0.05604191,-0.01150772,0.01448077,-0.05638752,0.02249552,0.04317873,-0.03778483,0.01788476,-0.02459636,0.09319472,0.02760877,-0.00044097,-0.00952146,-0.06061675,-0.03555587,-0.04917188,-0.07343579,-0.01701099,0.00350045,0.01340303,-0.00563836,0.01630925,-0.02087339,-0.01879356,0.03296849,-0.01729061,0.04760545,-0.08912978,-0.00209876,0.03503966,-0.01467422,0.01045305,0.0580329,0.03545101,-0.0713915,0.11971368,-0.043706,-0.01754598,-0.03985321,-0.09315902,-0.00282788,-0.00039478,0.00259476,-0.02202712,-0.01999044,-0.03163654,-0.05398764,0.00552222,0.08292597,-0.03887503,0.00004303,0.03191782,0.01407833,0.00661593,-0.0076297,-0.0437324,-0.01206993,0.00846762,0.06728151,-0.00442794,-0.01908313,-0.03242207,0.00242011,0.09702561,0.00588582,0.03466927,0.07992344,-0.01469028,-0.05720582,-0.0311462,-0.02561551,-0.0251665,-0.06648739,0.04779876,-0.00164,-0.048345,-0.02188773,-0.03509564,-0.0428676,-0.0623156,-0.08933848,0.06414573,-0.07348397,-0.00885757,0.01763751,-0.05202221,0.01375342,0.03936825,-0.02737998,0.01033027,-0.00163716,0.01240527,0.08829082,0.12687691,-0.01736778,-0.00063504,-0.05065874,0.01699563,-0.07856567,0.18632466,0.01553628,-0.04233022,-0.02008898,-0.02033225,0.02473291,-0.03824211,0.01703586,-0.0191434,0.01770135,0.05999687,0.05239179,-0.00774838,-0.00620962,-0.0113797,-0.00828601,0.03557012,0.09266269,-0.02301648,-0.1092881,0.0663771,0.00166213,-0.09788547,-0.04768663,-0.04635644,0.00906271,-0.04725843,-0.12850296,0.07958835,-0.03999695,0.00444218,-0.06326562,-0.09896591,0.03035543,-0.00297083,0.06600865,-0.03045101,0.08694685,0.07473813,-0.02305298,0.00641658,-0.03380584,-0.05888196,0.01332927,-0.02431822,0.03582282,0.04712449,0.00324465,0.02155124,-0.02680734,0.00222006,0.0114695,0.03357841,0.04151541,0.01277462,0.00619422,0.03600178,-0.01036684,-0.05116011,-0.05887076,-0.22357261,-0.02349319,0.02321153,-0.05935563,0.01382286,-0.01829877,0.03040092,-0.00250963,0.05170486,0.06457151,0.04369776,0.04516559,-0.11188246,-0.03520331,0.01257923,-0.00779948,0.01688309,-0.03873733,-0.02340002,0.02808088,0.03014791,0.03890154,0.0031486,-0.0420535,0.05703121,-0.03334801,0.09601801,0.02110712,0.03708242,0.04700743,0.02138461,0.01047881,0.00978235,-0.10782754,0.03364439,0.01667882,-0.03648688,-0.04196461,0.00641945,-0.01696396,-0.01477786,0.02271703,-0.05283627,-0.05872637,0.01533488,-0.02456152,-0.05001119,-0.01905295,0.0170547,0.04486255,-0.03184343,-0.00260369,-0.00004684,-0.00529345,-0.03376143,-0.00993319,-0.05925378,-0.04933519,0.00225329,0.04512526,0.00668229,-0.00600634,0.0567045,0.01120141,-0.00179531,-0.01762582,-0.02747939,-0.02645681,-0.05145755,0.00101429,-0.05149915,0.14963317,0.01133537,-0.03084707,0.0512014,0.03969389,-0.00775298,-0.01998308,0.02683866,0.04200151,0.02680711,0.03074321,0.02356547,0.04909104,0.0193248,0.04684265,0.03045264,-0.00220931,0.06044322,-0.08948269,-0.0565494,0.00722698,-0.03741186,0.02946171,0.06090385,-0.04125255,-0.28406096,0.00206469,-0.01736705,0.02199172,0.04103706,0.04763189,0.05883328,0.00165966,-0.05015705,0.01681064,-0.02774655,0.0561114,0.03231918,-0.01542999,-0.00409088,-0.05316745,0.06614841,-0.01905338,0.05032803,-0.02050752,0.01162198,0.05962459,0.1982483,-0.01543897,0.06481186,0.01323915,-0.00120248,0.04677763,0.0488954,0.03661174,0.03411414,-0.05360784,0.10107206,-0.00929946,0.03658319,0.06229029,-0.07499164,0.00600022,0.01720484,0.03459082,-0.03922072,0.04533048,-0.07629414,0.03739364,0.06612481,0.03070499,-0.01138745,-0.06782635,-0.00683047,0.05165672,0.04139603,0.03400407,-0.00115555,-0.00426306,0.0198743,0.0364897,0.00968875,-0.02903574,-0.07275511,0.01039073,0.0205734,0.06676223,0.07304183,0.1306394,0.0469451],"last_embed":{"hash":"d5d5dcfe9a4f34723aafedcdb825e03eb638ae3576aecace8a9c04221b4c4cd8","tokens":464}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.00450055,-0.02687905,-0.06951948,0.05721435,-0.04280322,0.06840847,0.06787219,-0.03275571,0.02469877,-0.02619434,0.0127793,0.01850333,0.00331076,0.00018548,-0.00075163,0.04531994,-0.00340785,0.08132759,0.01080339,-0.07133435,-0.02755457,0.01177956,0.02144089,0.07126491,-0.02872809,-0.03390938,0.01034454,-0.09221028,0.02264849,0.01968394,-0.00157208,-0.00860038,-0.01579668,0.0112798,-0.00741921,-0.01007189,0.04345983,-0.02657777,-0.03410341,0.00620861,-0.0364003,-0.00852466,-0.02526275,0.0048699,-0.0956379,-0.01326996,0.06309237,-0.06375028,0.03928017,0.0300325,0.01991873,-0.03766352,0.06238018,0.01386203,0.02449375,0.04737577,-0.02113495,-0.02658866,0.01199015,0.00853636,0.00081913,-0.01830483,0.06150644,0.01192372,-0.05911501,0.01614276,0.03344995,0.02499694,-0.02960233,-0.0295607,-0.05436506,0.00007879,0.02305801,-0.00329689,0.02890312,-0.03004856,0.02502413,-0.00035811,-0.03118865,0.01273808,-0.03504972,0.0118219,0.0254008,-0.05158281,0.0366354,-0.02199537,0.02586261,-0.00585042,-0.00305684,0.01762689,0.03597039,-0.01281781,0.04626501,0.01583943,0.03763145,0.00849639,0.01922265,0.01159159,-0.00724768,-0.03834559,0.0712209,-0.01879814,-0.03547023,-0.01288311,-0.05214693,0.02284021,-0.04560436,0.03731267,-0.02868953,-0.01018819,-0.04265926,0.02041338,-0.05623251,-0.08631834,-0.05755221,0.04256536,-0.04365458,0.02326568,0.0539286,0.10752795,0.02720798,0.06266259,0.00489007,-0.03187037,-0.05358861,-0.08007965,0.04053946,0.00409661,0.04840395,-0.00336575,0.01097861,-0.045531,-0.06488486,-0.02784422,-0.02151029,0.04929098,-0.00229853,0.01109465,-0.00590904,-0.06618077,-0.04857996,0.03464811,0.00123211,0.00193078,-0.05251465,0.03550668,-0.07472122,-0.0307332,0.006319,0.03039483,0.05442518,-0.03792014,-0.01975958,-0.04098679,-0.03185324,0.02573733,-0.03772724,-0.01201221,-0.01180747,0.00598886,-0.05022356,0.06019745,0.05671825,0.0536895,0.0179917,-0.00524559,0.02881903,0.02074182,0.09674797,-0.03261856,-0.0265422,0.00304302,0.0301045,0.0340681,0.0104216,0.00305032,0.00202277,0.01409858,-0.03722987,-0.00227565,0.00383004,0.00413847,-0.05335359,0.01065917,0.03422368,-0.00650142,0.02071565,0.04103954,0.03360902,0.01824116,0.03509745,-0.00511539,-0.02992482,0.05769166,0.02671085,0.01338834,0.04036893,-0.03893063,-0.0149915,0.00615602,-0.04085043,-0.05311157,-0.06290855,-0.07494084,0.03051637,0.01635703,-0.01035036,-0.02813865,-0.05926765,0.04529602,0.00904348,0.00813666,-0.01007912,0.06278037,-0.02508281,-0.04543456,-0.00783767,-0.03132147,0.0377719,0.00980298,0.03806201,0.04329444,-0.00988683,-0.03980897,-0.02253994,-0.03962843,-0.00893973,0.03003828,-0.00271464,-0.01114556,-0.01894168,0.04091736,0.00208715,0.02028858,-0.04393211,-0.01593874,0.03910799,0.01813103,0.00600945,0.00967833,-0.02570356,0.01396673,0.01849304,-0.00478104,-0.09295367,0.0108493,-0.00227963,-0.0123208,0.04180521,0.03628188,0.06970244,-0.03793559,-0.05372501,0.11286168,-0.00793776,-0.05221649,0.00594578,0.02851009,-0.02308644,-0.03765467,0.02109199,-0.02232168,-0.00295361,0.07624943,-0.05263456,-0.02460661,0.0329375,0.01506073,0.02097684,-0.00111166,-0.00270788,-0.06939842,0.07153179,-0.00999454,0.00468099,-0.04558137,-0.02377502,0.02151635,0.02231188,-0.01168673,-0.0126531,-0.03763622,0.04305049,-0.00028654,-0.09535554,-0.00308288,0.01535079,0.00397221,0.01723424,0.02905384,0.02410553,0.03040062,-0.04314241,0.03067415,-0.01367421,-0.06183204,0.03052216,-0.02115154,-0.01882901,-0.02023311,-0.07095273,0.00325654,-0.00967129,0.00918659,-0.02519873,0.03636577,0.00150104,0.06432742,0.03909659,-0.06071531,0.01876433,-0.06190306,-0.02059943,-0.01095703,0.01625477,0.08170588,0.03030671,0.01000422,-0.00708438,0.02068534,-0.05279851,-0.00593237,-0.01588496,0.01354953,-0.00192735,-0.05395419,0.04363246,0.034516,-0.00534524,-0.00582482,-0.04004664,0.01493761,-0.03046796,-0.02455321,0.09413077,0.01118489,-0.04019857,-0.0424642,0.05222108,-0.02966684,0.00491505,-0.05967143,0.04054806,-0.0255313,0.02286217,0.04295365,0.00680643,0.02876824,-0.06479305,-0.05736683,-0.00534798,-0.0543968,0.05660203,0.02313072,0.03075536,-0.03303163,-0.04202246,0.02678712,0.00333918,-0.0007957,-0.01409008,-0.02344412,-0.03081358,-0.01907156,0.03304471,0.00989527,0.05094613,0.04929852,0.00804669,-0.07129131,-0.01196801,0.07314016,0.07745755,-0.05868061,-0.01469256,-0.00428479,-0.00853746,-0.02726684,0.03146611,-0.0013258,0.02658116,-0.01695403,0.06279172,-0.01103528,-0.05885266,0.03200957,0.04223669,-0.03479417,0.0157127,-0.02946616,-0.01128224,-0.03112131,-0.05161494,-0.00415562,0.03385171,-0.02599088,0.00257686,0.02152678,0.00928843,-0.01787316,0.03259087,0.02228565,-0.01828847,-0.01646476,0.00244459,0.02349358,-0.05164279,0.0421787,-0.01859548,-0.00003058,-0.02774671,0.07616838,-0.01210848,-0.00315055,-0.01644572,-0.00076939,-0.00269114,-0.07797095,0.01620812,0.04056215,0.04546205,-0.0007711,-0.03011382,0.04809651,0.00917513,-0.03301195,0.01093298,0.00212827,-0.04469785,-0.00784,0.00581881,0.00230581,-0.08685497,-0.04334842,0.02330268,-0.001943,0.05564315,0.02066311,0.01891833,0.00902306,-0.03736331,-0.00827585,-0.04691548,-0.01033485,0.06486683,-0.06077589,0.03213704,0.04924645,-0.00843324,0.03006017,-0.00673461,-0.01400766,-0.08812919,-0.05122266,0.01657666,0.01429807,-0.02340823,0.00320231,-0.0036848,0.00892419,-0.00897399,0.04589726,0.00602067,-0.0428356,0.01598153,0.00940261,0.03609374,0.01937948,0.02846905,-0.04508168,0.05494543,-0.05236125,-0.01510504,-0.01364595,0.01284295,0.05338106,-0.00432327,0.04340767,-0.05439053,-0.00736376,0.01615663,0.00767107,-0.01056286,0.02449897,-0.02461666,0.04944361,-0.02534799,0.01921076,0.03410086,-0.01271961,0.04579952,0.04135966,0.04494758,-0.02765316,0.02566899,-0.00086763,0.02538497,0.05205052,-0.00458708,0.03426632,0.00143199,0.03304918,-0.04148684,-0.0026476,0.04795251,0.02894885,-0.00293911,0.00057285,0.07848946,-0.04960461,0.00980608,-0.01750912,0.04262961,-0.01317399,0.00417407,-0.04463096,-0.00135294,-0.01044463,0.02415285,-0.03301156,-0.02931698,0.02989955,-0.04650187,0.027849,0.0005922,-0.02559968,0.05084594,-0.0007275,0.01321841,-0.0587771,-0.03872725,-0.01716547,0.03604314,-0.01479138,0.00578613,-0.03317457,-0.01937295,0.02713837,0.00968701,0.04117135,-0.00957613,-0.06473217,-0.01026458,0.03852855,0.0127605,-0.02549668,0.03327267,-0.04611766,0.05273734,-0.00381448,0.0064368,-0.02282791,0.04152242,-0.03643039,-0.00926827,-0.01666205,0.01776227,-0.08059523,0.03480441,0.07993157,-0.01794408,-0.02064855,-0.01289111,0.04479032,-0.07622772,0.06173471,-0.11499896,0.02848518,-0.03384286,-0.02925279,-0.03671642,-0.02236099,0.03143334,-0.01505568,-0.02038364,0.01173533,0.01979666,-0.00269497,0.02262349,0.0053183,-0.00175965,-0.00434575,0.07377214,-0.04473447,-0.01433517,0.02669954,0.00093561,0.00926122,-0.02853316,-0.01534716,0.0043969,-0.0347132,0.01587131,-0.06284833,0.03843717,-0.0029423,-0.05848763,0.05239425,0.11966646,-0.03138153,0.02354206,-0.03093514,0.02213752,0.01707872,0.01234805,-0.08345968,-0.04378757,-0.10040832,-0.018718,0.02935929,-0.00411516,0.03847389,-0.01231775,0.01137172,0.05718782,-0.03091405,0.01287901,0.00712889,0.01064465,0.04575428,-0.01393802,-0.00642551,-0.0402264,-0.02494206,0.02709116,0.03882175,0.00793048,-0.00432992,0.04928782,-0.03756211,-0.00596047,-0.07768449,-0.06199635,0.00163488,-0.00225179,0.00767228,-0.01958194,-0.04357598,-0.03515779,-0.01645001,-0.01129378,0.0356681,0.03709295,0.04025911,-0.00543139,-0.02383891,-0.00939817,0.00234631,-0.0099507,0.01095309,-0.05079998,-0.00224699,-0.07375608,0.02916645,-0.03027616,-0.05196678,-0.02164406,0.01652803,0.02020855,0.05324388,-0.01436718,-0.02674078,0.02826553,-0.04606469,0.01933957,-0.00428611,-0.00046985,-0.01408327,-0.03269147,0.04809427,0.00697315,-0.03037665,-0.02615394,0.01295108,0.01337688,0.01334358,-0.00490347,0.08602129,0.00961091,-0.0086996,-0.05919605,-0.01674338,-0.02346046,-0.0107002,0.00438224,0.03453956,0.04153012,-0.0093909,-0.0126167,0.01776693,0.0571453,0.05110132,0.03465401,-0.03582606,0.03427707,-0.00020539,0.01811584,-0.0621425,0.00523512,0.00760086,-0.00923531,0.04023778,-0.07422944,-0.05930394,-0.01538454,-0.03395372,0.00008284,-0.03139054,0.07409245,-0.04310191,0.02707203,0.04827035,-0.01454284,0.00627638,0.03474967,-0.00959723,0.03454104,0.0577777,-0.07965843,0.00038418,-0.03134539,0.03812875,0.02766309,0.00630527,-0.03709251,-0.01390644,-0.00236141,-0.03416913,0.01737767,-0.00645082,-0.03296445,-0.0423128,0.05482664,0.00706898,0.03018882,-0.00072842,-0.00939562,-0.02909065,0.04528199,0.03849671,0.0507973,-0.00612414,-0.03901792,-0.05523625,0.00012803,0.00476798,0.00818556,0.02288776,-0.02340941,0.03779157,0.01770864,0.06389526,-0.0276298,0.02692089,0.01738151,0.0397505,-0.02318218,-0.05122282,7.3e-7,-0.01016549,-0.02366467,-0.01386561,-0.02210101,0.0147565,-0.00934422,-0.01297153,-0.02858103,0.0081782],"last_embed":{"tokens":541,"hash":"1sf2ttw"}}},"last_read":{"hash":"1sf2ttw","at":1750993393861},"class_name":"SmartSource","outlinks":[{"title":"数字证书","target":"数字证书","line":29},{"title":"散列函数","target":"散列函数","line":48},{"title":"MD5","target":"MD5","line":59},{"title":"SHA-1","target":"SHA-1","line":62},{"title":"SHA-2","target":"sha-512","line":65},{"title":"SHA-256","target":"SHA-256","line":66}],"metadata":{"aliases":["哈希值","Hash Code","哈希算法","密码杂凑算法"],"tags":["网络安全/密码学"],"类型":["加密解密"],"cssclasses":["editor-full"]},"blocks":{"#---frontmatter---":[1,13],"#简介":[15,55],"#简介#{1}":[16,16],"#简介#{2}":[17,20],"#简介#{3}":[21,37],"#简介#实现原理":[38,43],"#简介#实现原理#{1}":[39,41],"#简介#实现原理#{2}":[42,43],"#简介#哈希算法的安全性":[44,55],"#简介#哈希算法的安全性#{1}":[46,46],"#简介#哈希算法的安全性#{2}":[47,48],"#简介#哈希算法的安全性#{3}":[49,50],"#简介#哈希算法的安全性#{4}":[51,53],"#简介#哈希算法的安全性#{5}":[54,55],"#常见的哈希算法":[56,76],"#常见的哈希算法#{1}":[58,58],"#常见的哈希算法#{2}":[59,61],"#常见的哈希算法#{3}":[62,64],"#常见的哈希算法#{4}":[65,67],"#常见的哈希算法#{5}":[68,70],"#常见的哈希算法#{6}":[71,74],"#常见的哈希算法#{7}":[75,76]},"last_import":{"mtime":1737964712164,"size":2060,"at":1749024987320,"hash":"1sf2ttw"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#---frontmatter---","lines":[1,13],"size":118,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#简介": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#简介","lines":[15,55],"size":606,"outlinks":[{"title":"数字证书","target":"数字证书","line":15},{"title":"散列函数","target":"散列函数","line":34}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#简介#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#简介#{1}","lines":[16,16],"size":45,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#简介#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#简介#{2}","lines":[17,20],"size":101,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#简介#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#简介#{3}","lines":[21,37],"size":214,"outlinks":[{"title":"数字证书","target":"数字证书","line":9}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#简介#实现原理": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#简介#实现原理","lines":[38,43],"size":66,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#简介#实现原理#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#简介#实现原理#{1}","lines":[39,41],"size":52,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#简介#实现原理#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#简介#实现原理#{2}","lines":[42,43],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#简介#哈希算法的安全性": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#简介#哈希算法的安全性","lines":[44,55],"size":171,"outlinks":[{"title":"散列函数","target":"散列函数","line":5}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#简介#哈希算法的安全性#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#简介#哈希算法的安全性#{1}","lines":[46,46],"size":11,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#简介#哈希算法的安全性#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#简介#哈希算法的安全性#{2}","lines":[47,48],"size":50,"outlinks":[{"title":"散列函数","target":"散列函数","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#简介#哈希算法的安全性#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#简介#哈希算法的安全性#{3}","lines":[49,50],"size":43,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#简介#哈希算法的安全性#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#简介#哈希算法的安全性#{4}","lines":[51,53],"size":46,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#简介#哈希算法的安全性#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#简介#哈希算法的安全性#{5}","lines":[54,55],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#常见的哈希算法": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#常见的哈希算法","lines":[56,76],"size":305,"outlinks":[{"title":"MD5","target":"MD5","line":4},{"title":"SHA-1","target":"SHA-1","line":7},{"title":"SHA-2","target":"sha-512","line":10},{"title":"SHA-256","target":"SHA-256","line":11}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#常见的哈希算法#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#常见的哈希算法#{1}","lines":[58,58],"size":11,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#常见的哈希算法#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#常见的哈希算法#{2}","lines":[59,61],"size":49,"outlinks":[{"title":"MD5","target":"MD5","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#常见的哈希算法#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#常见的哈希算法#{3}","lines":[62,64],"size":53,"outlinks":[{"title":"SHA-1","target":"SHA-1","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#常见的哈希算法#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#常见的哈希算法#{4}","lines":[65,67],"size":72,"outlinks":[{"title":"SHA-2","target":"sha-512","line":1},{"title":"SHA-256","target":"SHA-256","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#常见的哈希算法#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#常见的哈希算法#{5}","lines":[68,70],"size":51,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#常见的哈希算法#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#常见的哈希算法#{6}","lines":[71,74],"size":46,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#常见的哈希算法#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md#常见的哈希算法#{7}","lines":[75,76],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md","last_embed":{"hash":"1sf2ttw","at":1751079980259},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07715219,-0.00072927,-0.02640039,-0.05574419,0.00182848,-0.00299448,0.00293245,0.04326639,0.04246547,0.02996299,-0.01819062,-0.04367503,0.04793756,0.04883799,0.0401596,0.05103969,-0.00506774,0.05457934,-0.00001902,0.00507533,0.10344055,-0.01334675,-0.01136077,-0.05538842,-0.02387303,0.0251155,0.02791307,-0.01268815,-0.0145403,-0.18456188,-0.01199607,0.01764121,0.04760815,0.0101367,-0.03452471,-0.03042567,0.04082165,0.06772988,-0.0275699,0.04141835,0.0034978,0.03643185,0.02552502,-0.01900548,-0.01174523,-0.03459849,-0.01223051,-0.02123332,0.00376411,-0.046753,-0.05789801,-0.02863765,-0.03853361,-0.01478422,-0.07525309,-0.00857317,0.04814374,0.03041369,0.03006122,-0.02928725,-0.01249431,0.04328435,-0.19347569,0.04944018,0.05604191,-0.01150772,0.01448077,-0.05638752,0.02249552,0.04317873,-0.03778483,0.01788476,-0.02459636,0.09319472,0.02760877,-0.00044097,-0.00952146,-0.06061675,-0.03555587,-0.04917188,-0.07343579,-0.01701099,0.00350045,0.01340303,-0.00563836,0.01630925,-0.02087339,-0.01879356,0.03296849,-0.01729061,0.04760545,-0.08912978,-0.00209876,0.03503966,-0.01467422,0.01045305,0.0580329,0.03545101,-0.0713915,0.11971368,-0.043706,-0.01754598,-0.03985321,-0.09315902,-0.00282788,-0.00039478,0.00259476,-0.02202712,-0.01999044,-0.03163654,-0.05398764,0.00552222,0.08292597,-0.03887503,0.00004303,0.03191782,0.01407833,0.00661593,-0.0076297,-0.0437324,-0.01206993,0.00846762,0.06728151,-0.00442794,-0.01908313,-0.03242207,0.00242011,0.09702561,0.00588582,0.03466927,0.07992344,-0.01469028,-0.05720582,-0.0311462,-0.02561551,-0.0251665,-0.06648739,0.04779876,-0.00164,-0.048345,-0.02188773,-0.03509564,-0.0428676,-0.0623156,-0.08933848,0.06414573,-0.07348397,-0.00885757,0.01763751,-0.05202221,0.01375342,0.03936825,-0.02737998,0.01033027,-0.00163716,0.01240527,0.08829082,0.12687691,-0.01736778,-0.00063504,-0.05065874,0.01699563,-0.07856567,0.18632466,0.01553628,-0.04233022,-0.02008898,-0.02033225,0.02473291,-0.03824211,0.01703586,-0.0191434,0.01770135,0.05999687,0.05239179,-0.00774838,-0.00620962,-0.0113797,-0.00828601,0.03557012,0.09266269,-0.02301648,-0.1092881,0.0663771,0.00166213,-0.09788547,-0.04768663,-0.04635644,0.00906271,-0.04725843,-0.12850296,0.07958835,-0.03999695,0.00444218,-0.06326562,-0.09896591,0.03035543,-0.00297083,0.06600865,-0.03045101,0.08694685,0.07473813,-0.02305298,0.00641658,-0.03380584,-0.05888196,0.01332927,-0.02431822,0.03582282,0.04712449,0.00324465,0.02155124,-0.02680734,0.00222006,0.0114695,0.03357841,0.04151541,0.01277462,0.00619422,0.03600178,-0.01036684,-0.05116011,-0.05887076,-0.22357261,-0.02349319,0.02321153,-0.05935563,0.01382286,-0.01829877,0.03040092,-0.00250963,0.05170486,0.06457151,0.04369776,0.04516559,-0.11188246,-0.03520331,0.01257923,-0.00779948,0.01688309,-0.03873733,-0.02340002,0.02808088,0.03014791,0.03890154,0.0031486,-0.0420535,0.05703121,-0.03334801,0.09601801,0.02110712,0.03708242,0.04700743,0.02138461,0.01047881,0.00978235,-0.10782754,0.03364439,0.01667882,-0.03648688,-0.04196461,0.00641945,-0.01696396,-0.01477786,0.02271703,-0.05283627,-0.05872637,0.01533488,-0.02456152,-0.05001119,-0.01905295,0.0170547,0.04486255,-0.03184343,-0.00260369,-0.00004684,-0.00529345,-0.03376143,-0.00993319,-0.05925378,-0.04933519,0.00225329,0.04512526,0.00668229,-0.00600634,0.0567045,0.01120141,-0.00179531,-0.01762582,-0.02747939,-0.02645681,-0.05145755,0.00101429,-0.05149915,0.14963317,0.01133537,-0.03084707,0.0512014,0.03969389,-0.00775298,-0.01998308,0.02683866,0.04200151,0.02680711,0.03074321,0.02356547,0.04909104,0.0193248,0.04684265,0.03045264,-0.00220931,0.06044322,-0.08948269,-0.0565494,0.00722698,-0.03741186,0.02946171,0.06090385,-0.04125255,-0.28406096,0.00206469,-0.01736705,0.02199172,0.04103706,0.04763189,0.05883328,0.00165966,-0.05015705,0.01681064,-0.02774655,0.0561114,0.03231918,-0.01542999,-0.00409088,-0.05316745,0.06614841,-0.01905338,0.05032803,-0.02050752,0.01162198,0.05962459,0.1982483,-0.01543897,0.06481186,0.01323915,-0.00120248,0.04677763,0.0488954,0.03661174,0.03411414,-0.05360784,0.10107206,-0.00929946,0.03658319,0.06229029,-0.07499164,0.00600022,0.01720484,0.03459082,-0.03922072,0.04533048,-0.07629414,0.03739364,0.06612481,0.03070499,-0.01138745,-0.06782635,-0.00683047,0.05165672,0.04139603,0.03400407,-0.00115555,-0.00426306,0.0198743,0.0364897,0.00968875,-0.02903574,-0.07275511,0.01039073,0.0205734,0.06676223,0.07304183,0.1306394,0.0469451],"last_embed":{"hash":"d5d5dcfe9a4f34723aafedcdb825e03eb638ae3576aecace8a9c04221b4c4cd8","tokens":464}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.00450055,-0.02687905,-0.06951948,0.05721435,-0.04280322,0.06840847,0.06787219,-0.03275571,0.02469877,-0.02619434,0.0127793,0.01850333,0.00331076,0.00018548,-0.00075163,0.04531994,-0.00340785,0.08132759,0.01080339,-0.07133435,-0.02755457,0.01177956,0.02144089,0.07126491,-0.02872809,-0.03390938,0.01034454,-0.09221028,0.02264849,0.01968394,-0.00157208,-0.00860038,-0.01579668,0.0112798,-0.00741921,-0.01007189,0.04345983,-0.02657777,-0.03410341,0.00620861,-0.0364003,-0.00852466,-0.02526275,0.0048699,-0.0956379,-0.01326996,0.06309237,-0.06375028,0.03928017,0.0300325,0.01991873,-0.03766352,0.06238018,0.01386203,0.02449375,0.04737577,-0.02113495,-0.02658866,0.01199015,0.00853636,0.00081913,-0.01830483,0.06150644,0.01192372,-0.05911501,0.01614276,0.03344995,0.02499694,-0.02960233,-0.0295607,-0.05436506,0.00007879,0.02305801,-0.00329689,0.02890312,-0.03004856,0.02502413,-0.00035811,-0.03118865,0.01273808,-0.03504972,0.0118219,0.0254008,-0.05158281,0.0366354,-0.02199537,0.02586261,-0.00585042,-0.00305684,0.01762689,0.03597039,-0.01281781,0.04626501,0.01583943,0.03763145,0.00849639,0.01922265,0.01159159,-0.00724768,-0.03834559,0.0712209,-0.01879814,-0.03547023,-0.01288311,-0.05214693,0.02284021,-0.04560436,0.03731267,-0.02868953,-0.01018819,-0.04265926,0.02041338,-0.05623251,-0.08631834,-0.05755221,0.04256536,-0.04365458,0.02326568,0.0539286,0.10752795,0.02720798,0.06266259,0.00489007,-0.03187037,-0.05358861,-0.08007965,0.04053946,0.00409661,0.04840395,-0.00336575,0.01097861,-0.045531,-0.06488486,-0.02784422,-0.02151029,0.04929098,-0.00229853,0.01109465,-0.00590904,-0.06618077,-0.04857996,0.03464811,0.00123211,0.00193078,-0.05251465,0.03550668,-0.07472122,-0.0307332,0.006319,0.03039483,0.05442518,-0.03792014,-0.01975958,-0.04098679,-0.03185324,0.02573733,-0.03772724,-0.01201221,-0.01180747,0.00598886,-0.05022356,0.06019745,0.05671825,0.0536895,0.0179917,-0.00524559,0.02881903,0.02074182,0.09674797,-0.03261856,-0.0265422,0.00304302,0.0301045,0.0340681,0.0104216,0.00305032,0.00202277,0.01409858,-0.03722987,-0.00227565,0.00383004,0.00413847,-0.05335359,0.01065917,0.03422368,-0.00650142,0.02071565,0.04103954,0.03360902,0.01824116,0.03509745,-0.00511539,-0.02992482,0.05769166,0.02671085,0.01338834,0.04036893,-0.03893063,-0.0149915,0.00615602,-0.04085043,-0.05311157,-0.06290855,-0.07494084,0.03051637,0.01635703,-0.01035036,-0.02813865,-0.05926765,0.04529602,0.00904348,0.00813666,-0.01007912,0.06278037,-0.02508281,-0.04543456,-0.00783767,-0.03132147,0.0377719,0.00980298,0.03806201,0.04329444,-0.00988683,-0.03980897,-0.02253994,-0.03962843,-0.00893973,0.03003828,-0.00271464,-0.01114556,-0.01894168,0.04091736,0.00208715,0.02028858,-0.04393211,-0.01593874,0.03910799,0.01813103,0.00600945,0.00967833,-0.02570356,0.01396673,0.01849304,-0.00478104,-0.09295367,0.0108493,-0.00227963,-0.0123208,0.04180521,0.03628188,0.06970244,-0.03793559,-0.05372501,0.11286168,-0.00793776,-0.05221649,0.00594578,0.02851009,-0.02308644,-0.03765467,0.02109199,-0.02232168,-0.00295361,0.07624943,-0.05263456,-0.02460661,0.0329375,0.01506073,0.02097684,-0.00111166,-0.00270788,-0.06939842,0.07153179,-0.00999454,0.00468099,-0.04558137,-0.02377502,0.02151635,0.02231188,-0.01168673,-0.0126531,-0.03763622,0.04305049,-0.00028654,-0.09535554,-0.00308288,0.01535079,0.00397221,0.01723424,0.02905384,0.02410553,0.03040062,-0.04314241,0.03067415,-0.01367421,-0.06183204,0.03052216,-0.02115154,-0.01882901,-0.02023311,-0.07095273,0.00325654,-0.00967129,0.00918659,-0.02519873,0.03636577,0.00150104,0.06432742,0.03909659,-0.06071531,0.01876433,-0.06190306,-0.02059943,-0.01095703,0.01625477,0.08170588,0.03030671,0.01000422,-0.00708438,0.02068534,-0.05279851,-0.00593237,-0.01588496,0.01354953,-0.00192735,-0.05395419,0.04363246,0.034516,-0.00534524,-0.00582482,-0.04004664,0.01493761,-0.03046796,-0.02455321,0.09413077,0.01118489,-0.04019857,-0.0424642,0.05222108,-0.02966684,0.00491505,-0.05967143,0.04054806,-0.0255313,0.02286217,0.04295365,0.00680643,0.02876824,-0.06479305,-0.05736683,-0.00534798,-0.0543968,0.05660203,0.02313072,0.03075536,-0.03303163,-0.04202246,0.02678712,0.00333918,-0.0007957,-0.01409008,-0.02344412,-0.03081358,-0.01907156,0.03304471,0.00989527,0.05094613,0.04929852,0.00804669,-0.07129131,-0.01196801,0.07314016,0.07745755,-0.05868061,-0.01469256,-0.00428479,-0.00853746,-0.02726684,0.03146611,-0.0013258,0.02658116,-0.01695403,0.06279172,-0.01103528,-0.05885266,0.03200957,0.04223669,-0.03479417,0.0157127,-0.02946616,-0.01128224,-0.03112131,-0.05161494,-0.00415562,0.03385171,-0.02599088,0.00257686,0.02152678,0.00928843,-0.01787316,0.03259087,0.02228565,-0.01828847,-0.01646476,0.00244459,0.02349358,-0.05164279,0.0421787,-0.01859548,-0.00003058,-0.02774671,0.07616838,-0.01210848,-0.00315055,-0.01644572,-0.00076939,-0.00269114,-0.07797095,0.01620812,0.04056215,0.04546205,-0.0007711,-0.03011382,0.04809651,0.00917513,-0.03301195,0.01093298,0.00212827,-0.04469785,-0.00784,0.00581881,0.00230581,-0.08685497,-0.04334842,0.02330268,-0.001943,0.05564315,0.02066311,0.01891833,0.00902306,-0.03736331,-0.00827585,-0.04691548,-0.01033485,0.06486683,-0.06077589,0.03213704,0.04924645,-0.00843324,0.03006017,-0.00673461,-0.01400766,-0.08812919,-0.05122266,0.01657666,0.01429807,-0.02340823,0.00320231,-0.0036848,0.00892419,-0.00897399,0.04589726,0.00602067,-0.0428356,0.01598153,0.00940261,0.03609374,0.01937948,0.02846905,-0.04508168,0.05494543,-0.05236125,-0.01510504,-0.01364595,0.01284295,0.05338106,-0.00432327,0.04340767,-0.05439053,-0.00736376,0.01615663,0.00767107,-0.01056286,0.02449897,-0.02461666,0.04944361,-0.02534799,0.01921076,0.03410086,-0.01271961,0.04579952,0.04135966,0.04494758,-0.02765316,0.02566899,-0.00086763,0.02538497,0.05205052,-0.00458708,0.03426632,0.00143199,0.03304918,-0.04148684,-0.0026476,0.04795251,0.02894885,-0.00293911,0.00057285,0.07848946,-0.04960461,0.00980608,-0.01750912,0.04262961,-0.01317399,0.00417407,-0.04463096,-0.00135294,-0.01044463,0.02415285,-0.03301156,-0.02931698,0.02989955,-0.04650187,0.027849,0.0005922,-0.02559968,0.05084594,-0.0007275,0.01321841,-0.0587771,-0.03872725,-0.01716547,0.03604314,-0.01479138,0.00578613,-0.03317457,-0.01937295,0.02713837,0.00968701,0.04117135,-0.00957613,-0.06473217,-0.01026458,0.03852855,0.0127605,-0.02549668,0.03327267,-0.04611766,0.05273734,-0.00381448,0.0064368,-0.02282791,0.04152242,-0.03643039,-0.00926827,-0.01666205,0.01776227,-0.08059523,0.03480441,0.07993157,-0.01794408,-0.02064855,-0.01289111,0.04479032,-0.07622772,0.06173471,-0.11499896,0.02848518,-0.03384286,-0.02925279,-0.03671642,-0.02236099,0.03143334,-0.01505568,-0.02038364,0.01173533,0.01979666,-0.00269497,0.02262349,0.0053183,-0.00175965,-0.00434575,0.07377214,-0.04473447,-0.01433517,0.02669954,0.00093561,0.00926122,-0.02853316,-0.01534716,0.0043969,-0.0347132,0.01587131,-0.06284833,0.03843717,-0.0029423,-0.05848763,0.05239425,0.11966646,-0.03138153,0.02354206,-0.03093514,0.02213752,0.01707872,0.01234805,-0.08345968,-0.04378757,-0.10040832,-0.018718,0.02935929,-0.00411516,0.03847389,-0.01231775,0.01137172,0.05718782,-0.03091405,0.01287901,0.00712889,0.01064465,0.04575428,-0.01393802,-0.00642551,-0.0402264,-0.02494206,0.02709116,0.03882175,0.00793048,-0.00432992,0.04928782,-0.03756211,-0.00596047,-0.07768449,-0.06199635,0.00163488,-0.00225179,0.00767228,-0.01958194,-0.04357598,-0.03515779,-0.01645001,-0.01129378,0.0356681,0.03709295,0.04025911,-0.00543139,-0.02383891,-0.00939817,0.00234631,-0.0099507,0.01095309,-0.05079998,-0.00224699,-0.07375608,0.02916645,-0.03027616,-0.05196678,-0.02164406,0.01652803,0.02020855,0.05324388,-0.01436718,-0.02674078,0.02826553,-0.04606469,0.01933957,-0.00428611,-0.00046985,-0.01408327,-0.03269147,0.04809427,0.00697315,-0.03037665,-0.02615394,0.01295108,0.01337688,0.01334358,-0.00490347,0.08602129,0.00961091,-0.0086996,-0.05919605,-0.01674338,-0.02346046,-0.0107002,0.00438224,0.03453956,0.04153012,-0.0093909,-0.0126167,0.01776693,0.0571453,0.05110132,0.03465401,-0.03582606,0.03427707,-0.00020539,0.01811584,-0.0621425,0.00523512,0.00760086,-0.00923531,0.04023778,-0.07422944,-0.05930394,-0.01538454,-0.03395372,0.00008284,-0.03139054,0.07409245,-0.04310191,0.02707203,0.04827035,-0.01454284,0.00627638,0.03474967,-0.00959723,0.03454104,0.0577777,-0.07965843,0.00038418,-0.03134539,0.03812875,0.02766309,0.00630527,-0.03709251,-0.01390644,-0.00236141,-0.03416913,0.01737767,-0.00645082,-0.03296445,-0.0423128,0.05482664,0.00706898,0.03018882,-0.00072842,-0.00939562,-0.02909065,0.04528199,0.03849671,0.0507973,-0.00612414,-0.03901792,-0.05523625,0.00012803,0.00476798,0.00818556,0.02288776,-0.02340941,0.03779157,0.01770864,0.06389526,-0.0276298,0.02692089,0.01738151,0.0397505,-0.02318218,-0.05122282,7.3e-7,-0.01016549,-0.02366467,-0.01386561,-0.02210101,0.0147565,-0.00934422,-0.01297153,-0.02858103,0.0081782],"last_embed":{"tokens":541,"hash":"1sf2ttw"}}},"last_read":{"hash":"1sf2ttw","at":1751079980259},"class_name":"SmartSource","outlinks":[{"title":"数字证书","target":"数字证书","line":29},{"title":"散列函数","target":"散列函数","line":48},{"title":"MD5","target":"MD5","line":59},{"title":"SHA-1","target":"SHA-1","line":62},{"title":"SHA-2","target":"sha-512","line":65},{"title":"SHA-256","target":"SHA-256","line":66}],"metadata":{"aliases":["哈希值","Hash Code","哈希算法","密码杂凑算法"],"tags":["网络安全/密码学"],"类型":["加密解密"],"cssclasses":["editor-full"]},"blocks":{"#---frontmatter---":[1,13],"#简介":[15,55],"#简介#{1}":[16,16],"#简介#{2}":[17,20],"#简介#{3}":[21,37],"#简介#实现原理":[38,43],"#简介#实现原理#{1}":[39,41],"#简介#实现原理#{2}":[42,43],"#简介#哈希算法的安全性":[44,55],"#简介#哈希算法的安全性#{1}":[46,46],"#简介#哈希算法的安全性#{2}":[47,48],"#简介#哈希算法的安全性#{3}":[49,50],"#简介#哈希算法的安全性#{4}":[51,53],"#简介#哈希算法的安全性#{5}":[54,55],"#常见的哈希算法":[56,76],"#常见的哈希算法#{1}":[58,58],"#常见的哈希算法#{2}":[59,61],"#常见的哈希算法#{3}":[62,64],"#常见的哈希算法#{4}":[65,67],"#常见的哈希算法#{5}":[68,70],"#常见的哈希算法#{6}":[71,74],"#常见的哈希算法#{7}":[75,76]},"last_import":{"mtime":1737964712164,"size":2060,"at":1749024987320,"hash":"1sf2ttw"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/哈希算法.md"},