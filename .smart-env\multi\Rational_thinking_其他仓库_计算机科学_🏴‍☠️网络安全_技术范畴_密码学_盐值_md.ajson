"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md","last_embed":{"hash":"8273z3","at":1750993394165},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10315481,-0.02178109,-0.052216,-0.06135636,-0.01972155,-0.00175016,-0.01380304,0.04228475,0.05494519,-0.00498083,0.02993489,-0.07934379,0.07788002,0.02186408,0.0725668,0.04903689,-0.02374714,0.00540973,-0.04799025,-0.02726175,0.07312294,-0.01552099,0.00884308,-0.0939366,-0.00467852,0.0037618,0.01265436,-0.03581923,-0.0156085,-0.16146605,-0.02947398,0.03050183,-0.02224585,0.01689767,-0.04869057,-0.02324761,-0.00154202,0.06485427,-0.02702461,0.04557335,-0.0163198,0.01270223,0.03864454,-0.00620315,0.0208412,-0.03209624,-0.04195186,-0.01869362,0.02606412,-0.02665855,-0.04636025,-0.03352698,-0.02351548,-0.0317933,-0.04823899,0.01301452,0.02896134,0.01896722,0.0241398,0.02188715,0.05948224,0.06546926,-0.17933278,0.04032658,0.05058365,0.01274713,-0.03374926,-0.02452593,0.01531992,0.0178686,-0.02013642,0.02921884,0.00133013,0.07232012,0.07828738,0.01272568,-0.01089869,-0.05284987,-0.04257356,-0.04974418,-0.03515606,0.03484217,-0.01461089,0.06149083,0.01654488,0.00120731,-0.00263682,-0.01875643,0.0074427,-0.02160807,0.00262684,-0.03895258,-0.04157604,0.03256672,0.01842459,0.03160274,0.050858,0.00503237,-0.12110629,0.1011978,-0.05727781,0.01570389,-0.00807419,-0.07685397,0.01745975,-0.01067742,-0.00338141,-0.03223097,-0.01276037,-0.05910793,0.000558,-0.03817526,0.06478226,-0.06309498,-0.01425469,0.0292995,0.01183967,-0.00974058,-0.00957721,-0.010563,-0.03756839,0.02259506,0.08130664,-0.04903026,-0.02555341,-0.016777,0.0032513,0.08657875,0.00489803,0.03446411,0.07718267,-0.01231122,-0.06653254,-0.01255107,-0.04845259,-0.05061881,-0.07396124,0.02455302,0.02528648,-0.05388376,-0.00834252,-0.0589829,0.00424155,-0.10108907,-0.08893462,0.0391457,-0.0368964,-0.01385789,0.04070104,-0.06189774,0.02995435,0.05406415,-0.03834846,-0.01473161,0.00573021,-0.00365337,0.09148949,0.13075735,-0.01777752,0.00951056,-0.00244183,0.01494832,-0.06028362,0.13953507,0.03729359,-0.06281409,-0.00791599,0.01451488,0.01485619,-0.06975161,0.03281168,0.01164042,0.03385568,0.02205224,0.05548599,-0.00160327,-0.01347277,-0.06086791,-0.01485325,0.06197187,0.02305139,-0.02471022,-0.10920521,0.05419039,-0.0333317,-0.10093533,-0.01690942,-0.02772601,0.018276,-0.00894394,-0.08281773,0.026472,-0.05425848,0.02317211,-0.0706177,-0.10813846,0.01400671,-0.04610933,0.04057233,-0.03898776,0.04444942,0.0472057,-0.06001915,0.02106437,-0.0304565,-0.0245678,0.02529037,-0.03855301,0.04650472,0.03917717,0.02373084,0.05438529,-0.06442574,0.02598627,-0.01329126,0.04520254,0.03864366,0.03147398,0.0412054,0.0176332,0.03506383,-0.046287,-0.11067824,-0.22544527,-0.04859973,-0.01211016,-0.0375267,0.08210903,-0.03609187,0.00490976,0.00023028,0.07315435,0.05459665,0.04937849,0.01399463,-0.06250015,-0.02796523,-0.01823321,0.0074808,0.00373593,0.01503175,0.0146214,0.03950867,0.01831887,0.03665069,0.01404065,-0.03719584,0.04621172,-0.02264263,0.14552672,0.02610519,0.03819136,0.03840434,0.04648476,0.02473328,0.01198963,-0.10314079,0.07445684,0.02416697,-0.08960005,0.02600715,-0.05240148,-0.02496596,0.01306184,0.01735752,-0.0253483,-0.0769216,-0.00256381,-0.02292062,-0.06067368,-0.00442864,0.01105915,0.02498684,-0.00702359,0.02777676,0.01540232,-0.01201288,-0.00568784,-0.02509032,-0.05097471,-0.02117558,-0.04371819,0.02975459,0.01676795,-0.00940283,0.02050025,0.05120303,-0.02153842,-0.01942286,-0.04865689,-0.01405377,-0.04737447,0.01837123,-0.02574222,0.17138036,0.04011351,0.01360869,0.02563248,0.03133256,-0.02578012,-0.04062009,-0.0050228,0.02543258,-0.00855077,-0.007771,0.03356093,0.018446,-0.00419084,0.03560114,0.0258644,-0.00400028,0.09757448,-0.05688849,-0.06575362,0.01810276,-0.03439273,0.02520507,0.07515752,-0.01275391,-0.28616816,0.02915731,-0.04398945,0.01894128,0.05407321,0.05013341,0.07438071,0.02482471,-0.02566341,0.0276086,-0.01513004,0.06709174,0.01780435,-0.04113483,-0.00315418,-0.02413565,0.05296928,-0.03404197,0.03285917,-0.01497444,0.01594213,0.03774232,0.20870395,0.02520817,0.0526221,0.00683231,0.03033133,0.037326,0.05496907,0.02130761,-0.00103365,-0.02024112,0.04182718,-0.03483111,0.03841352,0.03160317,-0.0475913,-0.00564025,0.00203512,-0.02106521,-0.02855483,0.04663084,-0.08832305,0.05691813,0.1222697,0.03539775,0.00434004,-0.05723564,0.04172843,0.05527867,0.00031926,0.01873641,-0.0086529,-0.02818061,0.02344269,0.06830368,0.05212753,-0.01538031,-0.07067554,-0.01840732,0.04281521,0.05253619,0.09676009,0.05343547,0.05230067],"last_embed":{"hash":"6005a656de494d269d09bb29c9731f570acd117cf32d7418504ebc7478430c30","tokens":473}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.02570388,-0.05452882,-0.03184838,0.0397679,-0.04308918,0.04270258,-0.00097715,-0.00511472,0.06784772,-0.03946568,0.00008746,0.04711558,-0.03076049,-0.05682418,0.00992569,0.07208194,-0.0462582,0.04917841,-0.01815872,-0.08046334,-0.03794252,0.01837388,-0.04409846,0.06971901,-0.00846598,-0.00955061,-0.00735111,-0.06556693,-0.01930441,0.03420594,-0.00633257,-0.03770641,0.05431109,0.02527979,-0.00260074,-0.04346673,0.04186315,-0.02068788,0.02409911,0.01863355,0.00567765,0.00267819,-0.05462687,0.01247015,-0.07223187,0.06023131,0.01074338,0.00810461,0.04213805,0.04424283,0.00491127,-0.00302143,0.03517989,0.02564731,-0.00015406,0.01492247,-0.05068853,0.04001273,0.04918122,0.00630727,0.02298833,0.00521805,0.04820874,0.01430402,0.00847445,0.00775821,0.04406371,0.0452781,-0.00818865,-0.04933153,-0.09899522,-0.05844602,0.01991532,-0.01395926,0.02955022,-0.07598452,0.03533764,-0.02491485,-0.06387065,0.048857,-0.02299057,-0.00701282,-0.00142274,-0.04683655,-0.00002659,0.00555084,0.03376319,0.01529164,0.04645265,0.02676682,0.0062993,-0.02816657,0.05370196,0.00896615,0.0326518,0.02261352,0.03327619,0.00992211,0.01645318,-0.01179,0.01922984,-0.03800479,-0.03824512,-0.0383013,-0.031804,-0.00497507,-0.01805703,0.08206484,-0.02225452,0.02327187,0.03786294,0.01998931,-0.02507013,-0.03937485,-0.00994882,0.01981311,-0.00166177,-0.02885478,0.06228572,0.04975811,0.04657334,0.02256318,0.00115077,-0.01825755,-0.09510407,-0.0896218,0.02329626,-0.01434899,0.0783141,-0.00142194,0.00828623,-0.02398569,-0.04066214,-0.07621352,-0.05473792,0.03614302,0.00835644,0.02466408,-0.03611956,-0.05092526,-0.06406559,0.05442846,-0.00061892,-0.0013078,-0.00697,0.0497539,-0.05335472,-0.06874006,0.0065468,0.02569888,0.068586,-0.0274556,0.00875936,-0.03749591,-0.00249753,-0.01465492,-0.00367467,-0.01160358,-0.06851402,-0.00568462,-0.0353617,0.02185008,0.04405198,0.05169091,-0.00273774,-0.02245142,0.05001817,0.01970165,0.06782387,-0.02943686,-0.05593356,0.0433506,0.07259142,0.05101426,-0.006399,-0.03246637,-0.02611174,0.01349522,-0.02773393,-0.03376156,-0.00081603,0.00600079,-0.00634091,0.02776161,0.01744612,0.00759356,0.05384839,-0.00244825,-0.00672898,0.02898133,0.0148362,-0.00146917,-0.01951961,0.04474471,0.03617125,-0.00471591,0.03045855,-0.05646509,0.01119962,0.01901356,-0.01703801,-0.04810392,-0.0932944,-0.0748945,0.01956477,-0.00729642,-0.03156926,-0.02479068,-0.00598077,0.05847141,0.00390186,-0.01877284,0.03719021,0.04567166,0.01131765,0.02820391,-0.02012266,-0.04091676,0.02496879,-0.02119159,0.02783712,0.04564416,0.0169285,-0.00050051,-0.0394201,0.00388616,-0.01818708,0.03291588,-0.02172159,0.01625649,-0.01296394,0.01778282,-0.0625341,0.01258704,-0.02697149,-0.00648202,0.03712904,0.05334968,-0.00240797,0.00064899,-0.01351837,-0.006262,0.07834873,0.02162173,-0.06445993,0.03441981,-0.03655745,0.01456357,0.0507841,0.04107078,-0.00594321,-0.0012985,-0.03233358,0.08334862,-0.0071288,-0.05452216,0.00069651,-0.0092595,-0.00672104,-0.01825824,0.02484049,0.05045421,-0.01818778,0.05241448,-0.02936558,-0.0015596,-0.00281842,0.01846227,0.02553319,-0.00518956,0.02624473,-0.0854563,0.06818607,-0.03482643,-0.01052511,-0.03417039,-0.00424705,0.00807188,0.0178587,0.00108825,-0.01192923,-0.06214801,0.06896023,-0.03361647,-0.04999897,0.04621855,0.03257787,-0.00223876,0.00263207,0.0001149,0.03061881,-0.01892465,-0.01423129,0.00163079,-0.01655259,-0.05190123,-0.010318,-0.01343157,-0.00867089,-0.04843064,-0.04437849,0.05249863,-0.01306626,-0.00098498,-0.02157394,0.01484391,0.00181259,0.04737077,0.00490644,-0.03847307,0.02298404,-0.05109775,-0.02073913,0.00468124,0.01346145,0.04482058,-0.01849817,-0.00414397,-0.03577917,0.04058601,-0.04084733,-0.02942053,-0.05596195,0.01515346,-0.02157221,-0.03948283,-0.01952584,0.060077,0.03729,0.01192045,-0.04043417,0.03858415,0.00242735,-0.02885316,0.03500053,0.02637548,-0.01378421,-0.07889961,0.04399537,-0.01516454,0.01296929,-0.02875384,0.02835934,0.00215271,0.03824094,0.01529338,0.01035733,0.00987422,-0.09408108,-0.0715873,0.01654449,-0.03797249,0.00563764,-0.00325176,0.04087771,-0.02107916,-0.05417442,0.02197948,0.03201874,-0.04648873,0.02236915,-0.01497454,-0.01450984,-0.03140515,0.0056514,0.00386374,0.0413478,-0.00296607,-0.05974687,-0.04162977,-0.03553199,0.07764835,0.0563063,-0.05465275,0.00677703,-0.00165621,-0.02425005,0.00017659,0.00175187,-0.02151654,0.01349811,-0.01588132,0.0555675,-0.03228104,-0.00921278,-0.01810564,0.03688043,-0.02366472,0.01329365,-0.01404595,0.00311601,-0.04250699,-0.0522312,0.01757259,0.04999259,-0.04519193,-0.03318145,-0.00808497,0.0043109,-0.02596141,0.02146573,0.01707004,0.00709011,0.00398003,-0.00774165,0.00529483,-0.04748719,0.00882761,-0.00874347,-0.02169341,-0.02449043,0.10965602,-0.0375914,-0.02349614,0.01273081,0.00063182,0.02109859,-0.0808696,-0.02383204,0.0129107,0.03315699,-0.00927808,-0.0008764,0.02474672,-0.00094569,-0.02525975,0.0436626,-0.01901583,-0.03085211,0.0233927,-0.02825266,-0.00788742,-0.06486392,-0.03762885,0.0199612,-0.00506706,0.02301782,-0.00617706,0.00002419,0.017415,0.01292573,-0.00289324,-0.01995343,-0.01432762,0.01609672,-0.05415148,0.04084205,-0.02969703,-0.04001122,-0.01115704,-0.02567209,-0.02954528,-0.11541145,-0.00843347,0.00940892,-0.000779,-0.02809076,-0.00647716,-0.01883841,0.01557662,-0.03856895,0.04251035,-0.00510008,-0.04202906,0.01874782,0.00543739,0.02506885,0.01530609,0.02947359,-0.05292774,0.06550542,-0.08678345,0.00291172,0.00400418,0.03926972,0.03539839,-0.0088795,0.06577181,-0.01895377,-0.00859517,0.03761295,0.01544273,-0.00190262,0.02183859,-0.00856702,0.04123876,0.00219383,0.03899058,0.01566643,-0.05723661,0.0864742,0.01478413,0.01970843,0.01620131,-0.00103917,0.00880204,0.00394751,0.05165299,0.0179826,0.01614618,0.00836803,0.00606874,-0.00055273,0.00586946,0.04875074,0.06177169,-0.02496318,0.01988069,0.05832485,-0.0312805,0.02388027,0.00351394,0.06184089,0.00210549,-0.01374422,-0.05458778,-0.04157732,0.05482373,0.05402065,-0.06642768,-0.03742694,0.00804409,0.01381757,0.0569141,0.01001152,0.00394039,0.01907218,0.06405771,0.03828027,-0.02463272,-0.04293508,-0.03168435,-0.00367792,-0.03005279,-0.00109207,0.0058125,0.02120222,0.01776485,-0.01622579,0.05426895,0.01819631,-0.03158902,0.02593566,0.06369449,0.07001734,-0.04877215,0.01671649,-0.02261959,0.05815338,-0.00531355,0.01652489,0.0107455,0.02521423,-0.03048022,0.03542525,0.0174013,-0.00137558,-0.03634147,0.04540552,0.03963891,-0.03626299,-0.02272931,0.03313576,0.0231696,-0.01245421,0.01212328,-0.08988465,0.01907743,-0.05901099,-0.06918157,-0.03965093,-0.04449626,0.0518509,-0.02614541,-0.048385,0.03070666,0.00243251,0.00389279,0.03665216,0.03817765,0.01393801,-0.01295939,0.0819979,-0.03862945,-0.01578368,0.00595006,0.02112009,-0.0515312,0.00386097,0.00680843,-0.00163535,-0.01966457,0.024705,-0.02187335,0.02840683,-0.01587083,-0.01296771,0.0480311,0.08660143,-0.0325116,0.01119086,-0.00047158,-0.03273708,-0.01312994,-0.01051788,-0.06804343,-0.06942239,-0.13446291,-0.04862077,0.05454348,0.00829408,-0.01802406,0.00795626,0.0107733,0.03919638,-0.06019042,0.01555701,-0.03323555,0.00044481,0.0386236,-0.01769811,-0.06155004,-0.05399564,-0.0416247,0.03816523,-0.03597964,0.02280858,-0.03278786,0.04680095,-0.01861139,-0.00748535,-0.08029553,-0.04460211,0.04200581,0.00732548,-0.02389389,-0.01039036,-0.0262746,-0.04851805,-0.02861158,0.01637836,-0.02723667,0.03123617,0.0484185,0.00778703,-0.02965786,0.01321166,0.00895596,-0.00647423,0.01276406,-0.03943377,0.01293813,-0.02770092,0.0202706,-0.04887797,-0.05654141,-0.03333781,0.01365413,0.00267737,0.02225974,-0.03343162,0.00113759,-0.00310604,-0.0478103,0.00026871,0.00397647,-0.01513591,-0.02029666,-0.01941689,0.04520119,0.02005761,-0.01641219,0.00362097,-0.02168854,-0.0695506,0.01968893,0.00608087,0.04623051,0.0053554,-0.0187984,0.01397162,-0.02470277,-0.01280253,0.05008414,0.01916328,-0.02299504,0.05678848,-0.02024935,-0.02381919,0.0117416,0.02509476,0.05987378,0.0113298,0.00682238,0.05393084,-0.01830153,0.01340479,-0.04662949,-0.00797567,0.0231261,0.01557764,0.02786111,-0.06023144,-0.02758337,0.00681699,-0.00443679,0.00677925,-0.00495354,0.05143775,-0.02225029,-0.01587063,-0.00302169,0.02476559,0.02243422,0.03132236,-0.00815065,-0.00721942,0.03563191,-0.09268599,-0.00915313,-0.01192298,0.01626552,0.02935021,0.01866975,-0.01473425,-0.0042618,0.00944027,-0.01974754,0.0765618,0.01550802,-0.03126054,-0.03058861,0.06174964,-0.00018037,-0.01297558,-0.03884289,0.01596593,-0.03523885,0.01348933,0.05009146,0.03465569,-0.03546999,-0.04132487,-0.00363074,0.03450261,-0.00022576,0.00564294,-0.00849514,-0.00654705,0.06852525,-0.03752699,0.10503945,-0.06134967,0.0330746,0.01431024,0.10393131,-0.00450637,0.00760036,9.2e-7,-0.11659392,-0.02815123,-0.01398435,-0.04619044,0.02320806,-0.04065461,-0.00095716,-0.00846827,0.03176099],"last_embed":{"tokens":457,"hash":"8273z3"}}},"last_read":{"hash":"8273z3","at":1750993394165},"class_name":"SmartSource","outlinks":[{"title":"哈希算法","target":"哈希算法","line":8},{"title":"哈希值","target":"哈希算法","line":13},{"title":"彩虹表","target":"彩虹表","line":14},{"title":"哈希值","target":"哈希算法","line":14},{"title":"哈希算法","target":"哈希算法","line":21},{"title":"Openssl","target":"Openssl","line":36},{"title":"SHA-256","target":"SHA-256","line":52}],"metadata":{"aliases":["Salt"],"英文":"Salt","tags":["网络安全/密码学"],"基础知识":["[[哈希算法]]"]},"blocks":{"#---frontmatter---":[1,9],"#简介":[10,19],"#简介#{1}":[11,11],"#简介#{2}":[12,12],"#简介#{3}":[13,14],"#简介#{4}":[15,19],"#实例":[20,52],"#实例#{1}":[21,35],"#实例#{2}":[27,35],"#实例#基于[[Openssl]]的实际操作":[36,52],"#实例#基于[[Openssl]]的实际操作#**生成随机盐值**：":[38,46],"#实例#基于[[Openssl]]的实际操作#**生成随机盐值**：#{1}":[40,42],"#实例#基于[[Openssl]]的实际操作#**生成随机盐值**：#{2}":[43,44],"#实例#基于[[Openssl]]的实际操作#**生成随机盐值**：#{3}":[45,46],"#实例#基于[[Openssl]]的实际操作#**使用盐值进行哈希**：":[47,52],"#实例#基于[[Openssl]]的实际操作#**使用盐值进行哈希**：#{1}":[49,51],"#实例#基于[[Openssl]]的实际操作#**使用盐值进行哈希**：#{2}":[52,52]},"last_import":{"mtime":1737964745073,"size":1633,"at":1749024987320,"hash":"8273z3"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md#---frontmatter---","lines":[1,9],"size":74,"outlinks":[{"title":"哈希算法","target":"哈希算法","line":8}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md#简介": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md#简介","lines":[10,19],"size":255,"outlinks":[{"title":"哈希值","target":"哈希算法","line":4},{"title":"彩虹表","target":"彩虹表","line":5},{"title":"哈希值","target":"哈希算法","line":5}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md#简介#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md#简介#{1}","lines":[11,11],"size":22,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md#简介#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md#简介#{2}","lines":[12,12],"size":23,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md#简介#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md#简介#{3}","lines":[13,14],"size":108,"outlinks":[{"title":"哈希值","target":"哈希算法","line":1},{"title":"彩虹表","target":"彩虹表","line":2},{"title":"哈希值","target":"哈希算法","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md#简介#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md#简介#{4}","lines":[15,19],"size":94,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md#实例": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md#实例","lines":[20,52],"size":584,"outlinks":[{"title":"哈希算法","target":"哈希算法","line":2},{"title":"Openssl","target":"Openssl","line":17},{"title":"SHA-256","target":"SHA-256","line":33}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md#实例#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md#实例#{1}","lines":[21,35],"size":353,"outlinks":[{"title":"哈希算法","target":"哈希算法","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md#实例#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md#实例#{2}","lines":[27,35],"size":127,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md#实例#基于[[Openssl]]的实际操作": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md#实例#基于[[Openssl]]的实际操作","lines":[36,52],"size":225,"outlinks":[{"title":"Openssl","target":"Openssl","line":1},{"title":"SHA-256","target":"SHA-256","line":17}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md#实例#基于[[Openssl]]的实际操作#**生成随机盐值**：": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md#实例#基于[[Openssl]]的实际操作#**生成随机盐值**：","lines":[38,46],"size":90,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md#实例#基于[[Openssl]]的实际操作#**生成随机盐值**：#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md#实例#基于[[Openssl]]的实际操作#**生成随机盐值**：#{1}","lines":[40,42],"size":35,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md#实例#基于[[Openssl]]的实际操作#**生成随机盐值**：#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md#实例#基于[[Openssl]]的实际操作#**生成随机盐值**：#{2}","lines":[43,44],"size":32,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md#实例#基于[[Openssl]]的实际操作#**生成随机盐值**：#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md#实例#基于[[Openssl]]的实际操作#**生成随机盐值**：#{3}","lines":[45,46],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md#实例#基于[[Openssl]]的实际操作#**使用盐值进行哈希**：": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md#实例#基于[[Openssl]]的实际操作#**使用盐值进行哈希**：","lines":[47,52],"size":111,"outlinks":[{"title":"SHA-256","target":"SHA-256","line":6}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md#实例#基于[[Openssl]]的实际操作#**使用盐值进行哈希**：#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md#实例#基于[[Openssl]]的实际操作#**使用盐值进行哈希**：#{1}","lines":[49,51],"size":63,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md#实例#基于[[Openssl]]的实际操作#**使用盐值进行哈希**：#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md#实例#基于[[Openssl]]的实际操作#**使用盐值进行哈希**：#{2}","lines":[52,52],"size":28,"outlinks":[{"title":"SHA-256","target":"SHA-256","line":1}],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md","last_embed":{"hash":"8273z3","at":1751079980502},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.10315481,-0.02178109,-0.052216,-0.06135636,-0.01972155,-0.00175016,-0.01380304,0.04228475,0.05494519,-0.00498083,0.02993489,-0.07934379,0.07788002,0.02186408,0.0725668,0.04903689,-0.02374714,0.00540973,-0.04799025,-0.02726175,0.07312294,-0.01552099,0.00884308,-0.0939366,-0.00467852,0.0037618,0.01265436,-0.03581923,-0.0156085,-0.16146605,-0.02947398,0.03050183,-0.02224585,0.01689767,-0.04869057,-0.02324761,-0.00154202,0.06485427,-0.02702461,0.04557335,-0.0163198,0.01270223,0.03864454,-0.00620315,0.0208412,-0.03209624,-0.04195186,-0.01869362,0.02606412,-0.02665855,-0.04636025,-0.03352698,-0.02351548,-0.0317933,-0.04823899,0.01301452,0.02896134,0.01896722,0.0241398,0.02188715,0.05948224,0.06546926,-0.17933278,0.04032658,0.05058365,0.01274713,-0.03374926,-0.02452593,0.01531992,0.0178686,-0.02013642,0.02921884,0.00133013,0.07232012,0.07828738,0.01272568,-0.01089869,-0.05284987,-0.04257356,-0.04974418,-0.03515606,0.03484217,-0.01461089,0.06149083,0.01654488,0.00120731,-0.00263682,-0.01875643,0.0074427,-0.02160807,0.00262684,-0.03895258,-0.04157604,0.03256672,0.01842459,0.03160274,0.050858,0.00503237,-0.12110629,0.1011978,-0.05727781,0.01570389,-0.00807419,-0.07685397,0.01745975,-0.01067742,-0.00338141,-0.03223097,-0.01276037,-0.05910793,0.000558,-0.03817526,0.06478226,-0.06309498,-0.01425469,0.0292995,0.01183967,-0.00974058,-0.00957721,-0.010563,-0.03756839,0.02259506,0.08130664,-0.04903026,-0.02555341,-0.016777,0.0032513,0.08657875,0.00489803,0.03446411,0.07718267,-0.01231122,-0.06653254,-0.01255107,-0.04845259,-0.05061881,-0.07396124,0.02455302,0.02528648,-0.05388376,-0.00834252,-0.0589829,0.00424155,-0.10108907,-0.08893462,0.0391457,-0.0368964,-0.01385789,0.04070104,-0.06189774,0.02995435,0.05406415,-0.03834846,-0.01473161,0.00573021,-0.00365337,0.09148949,0.13075735,-0.01777752,0.00951056,-0.00244183,0.01494832,-0.06028362,0.13953507,0.03729359,-0.06281409,-0.00791599,0.01451488,0.01485619,-0.06975161,0.03281168,0.01164042,0.03385568,0.02205224,0.05548599,-0.00160327,-0.01347277,-0.06086791,-0.01485325,0.06197187,0.02305139,-0.02471022,-0.10920521,0.05419039,-0.0333317,-0.10093533,-0.01690942,-0.02772601,0.018276,-0.00894394,-0.08281773,0.026472,-0.05425848,0.02317211,-0.0706177,-0.10813846,0.01400671,-0.04610933,0.04057233,-0.03898776,0.04444942,0.0472057,-0.06001915,0.02106437,-0.0304565,-0.0245678,0.02529037,-0.03855301,0.04650472,0.03917717,0.02373084,0.05438529,-0.06442574,0.02598627,-0.01329126,0.04520254,0.03864366,0.03147398,0.0412054,0.0176332,0.03506383,-0.046287,-0.11067824,-0.22544527,-0.04859973,-0.01211016,-0.0375267,0.08210903,-0.03609187,0.00490976,0.00023028,0.07315435,0.05459665,0.04937849,0.01399463,-0.06250015,-0.02796523,-0.01823321,0.0074808,0.00373593,0.01503175,0.0146214,0.03950867,0.01831887,0.03665069,0.01404065,-0.03719584,0.04621172,-0.02264263,0.14552672,0.02610519,0.03819136,0.03840434,0.04648476,0.02473328,0.01198963,-0.10314079,0.07445684,0.02416697,-0.08960005,0.02600715,-0.05240148,-0.02496596,0.01306184,0.01735752,-0.0253483,-0.0769216,-0.00256381,-0.02292062,-0.06067368,-0.00442864,0.01105915,0.02498684,-0.00702359,0.02777676,0.01540232,-0.01201288,-0.00568784,-0.02509032,-0.05097471,-0.02117558,-0.04371819,0.02975459,0.01676795,-0.00940283,0.02050025,0.05120303,-0.02153842,-0.01942286,-0.04865689,-0.01405377,-0.04737447,0.01837123,-0.02574222,0.17138036,0.04011351,0.01360869,0.02563248,0.03133256,-0.02578012,-0.04062009,-0.0050228,0.02543258,-0.00855077,-0.007771,0.03356093,0.018446,-0.00419084,0.03560114,0.0258644,-0.00400028,0.09757448,-0.05688849,-0.06575362,0.01810276,-0.03439273,0.02520507,0.07515752,-0.01275391,-0.28616816,0.02915731,-0.04398945,0.01894128,0.05407321,0.05013341,0.07438071,0.02482471,-0.02566341,0.0276086,-0.01513004,0.06709174,0.01780435,-0.04113483,-0.00315418,-0.02413565,0.05296928,-0.03404197,0.03285917,-0.01497444,0.01594213,0.03774232,0.20870395,0.02520817,0.0526221,0.00683231,0.03033133,0.037326,0.05496907,0.02130761,-0.00103365,-0.02024112,0.04182718,-0.03483111,0.03841352,0.03160317,-0.0475913,-0.00564025,0.00203512,-0.02106521,-0.02855483,0.04663084,-0.08832305,0.05691813,0.1222697,0.03539775,0.00434004,-0.05723564,0.04172843,0.05527867,0.00031926,0.01873641,-0.0086529,-0.02818061,0.02344269,0.06830368,0.05212753,-0.01538031,-0.07067554,-0.01840732,0.04281521,0.05253619,0.09676009,0.05343547,0.05230067],"last_embed":{"hash":"6005a656de494d269d09bb29c9731f570acd117cf32d7418504ebc7478430c30","tokens":473}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.02570388,-0.05452882,-0.03184838,0.0397679,-0.04308918,0.04270258,-0.00097715,-0.00511472,0.06784772,-0.03946568,0.00008746,0.04711558,-0.03076049,-0.05682418,0.00992569,0.07208194,-0.0462582,0.04917841,-0.01815872,-0.08046334,-0.03794252,0.01837388,-0.04409846,0.06971901,-0.00846598,-0.00955061,-0.00735111,-0.06556693,-0.01930441,0.03420594,-0.00633257,-0.03770641,0.05431109,0.02527979,-0.00260074,-0.04346673,0.04186315,-0.02068788,0.02409911,0.01863355,0.00567765,0.00267819,-0.05462687,0.01247015,-0.07223187,0.06023131,0.01074338,0.00810461,0.04213805,0.04424283,0.00491127,-0.00302143,0.03517989,0.02564731,-0.00015406,0.01492247,-0.05068853,0.04001273,0.04918122,0.00630727,0.02298833,0.00521805,0.04820874,0.01430402,0.00847445,0.00775821,0.04406371,0.0452781,-0.00818865,-0.04933153,-0.09899522,-0.05844602,0.01991532,-0.01395926,0.02955022,-0.07598452,0.03533764,-0.02491485,-0.06387065,0.048857,-0.02299057,-0.00701282,-0.00142274,-0.04683655,-0.00002659,0.00555084,0.03376319,0.01529164,0.04645265,0.02676682,0.0062993,-0.02816657,0.05370196,0.00896615,0.0326518,0.02261352,0.03327619,0.00992211,0.01645318,-0.01179,0.01922984,-0.03800479,-0.03824512,-0.0383013,-0.031804,-0.00497507,-0.01805703,0.08206484,-0.02225452,0.02327187,0.03786294,0.01998931,-0.02507013,-0.03937485,-0.00994882,0.01981311,-0.00166177,-0.02885478,0.06228572,0.04975811,0.04657334,0.02256318,0.00115077,-0.01825755,-0.09510407,-0.0896218,0.02329626,-0.01434899,0.0783141,-0.00142194,0.00828623,-0.02398569,-0.04066214,-0.07621352,-0.05473792,0.03614302,0.00835644,0.02466408,-0.03611956,-0.05092526,-0.06406559,0.05442846,-0.00061892,-0.0013078,-0.00697,0.0497539,-0.05335472,-0.06874006,0.0065468,0.02569888,0.068586,-0.0274556,0.00875936,-0.03749591,-0.00249753,-0.01465492,-0.00367467,-0.01160358,-0.06851402,-0.00568462,-0.0353617,0.02185008,0.04405198,0.05169091,-0.00273774,-0.02245142,0.05001817,0.01970165,0.06782387,-0.02943686,-0.05593356,0.0433506,0.07259142,0.05101426,-0.006399,-0.03246637,-0.02611174,0.01349522,-0.02773393,-0.03376156,-0.00081603,0.00600079,-0.00634091,0.02776161,0.01744612,0.00759356,0.05384839,-0.00244825,-0.00672898,0.02898133,0.0148362,-0.00146917,-0.01951961,0.04474471,0.03617125,-0.00471591,0.03045855,-0.05646509,0.01119962,0.01901356,-0.01703801,-0.04810392,-0.0932944,-0.0748945,0.01956477,-0.00729642,-0.03156926,-0.02479068,-0.00598077,0.05847141,0.00390186,-0.01877284,0.03719021,0.04567166,0.01131765,0.02820391,-0.02012266,-0.04091676,0.02496879,-0.02119159,0.02783712,0.04564416,0.0169285,-0.00050051,-0.0394201,0.00388616,-0.01818708,0.03291588,-0.02172159,0.01625649,-0.01296394,0.01778282,-0.0625341,0.01258704,-0.02697149,-0.00648202,0.03712904,0.05334968,-0.00240797,0.00064899,-0.01351837,-0.006262,0.07834873,0.02162173,-0.06445993,0.03441981,-0.03655745,0.01456357,0.0507841,0.04107078,-0.00594321,-0.0012985,-0.03233358,0.08334862,-0.0071288,-0.05452216,0.00069651,-0.0092595,-0.00672104,-0.01825824,0.02484049,0.05045421,-0.01818778,0.05241448,-0.02936558,-0.0015596,-0.00281842,0.01846227,0.02553319,-0.00518956,0.02624473,-0.0854563,0.06818607,-0.03482643,-0.01052511,-0.03417039,-0.00424705,0.00807188,0.0178587,0.00108825,-0.01192923,-0.06214801,0.06896023,-0.03361647,-0.04999897,0.04621855,0.03257787,-0.00223876,0.00263207,0.0001149,0.03061881,-0.01892465,-0.01423129,0.00163079,-0.01655259,-0.05190123,-0.010318,-0.01343157,-0.00867089,-0.04843064,-0.04437849,0.05249863,-0.01306626,-0.00098498,-0.02157394,0.01484391,0.00181259,0.04737077,0.00490644,-0.03847307,0.02298404,-0.05109775,-0.02073913,0.00468124,0.01346145,0.04482058,-0.01849817,-0.00414397,-0.03577917,0.04058601,-0.04084733,-0.02942053,-0.05596195,0.01515346,-0.02157221,-0.03948283,-0.01952584,0.060077,0.03729,0.01192045,-0.04043417,0.03858415,0.00242735,-0.02885316,0.03500053,0.02637548,-0.01378421,-0.07889961,0.04399537,-0.01516454,0.01296929,-0.02875384,0.02835934,0.00215271,0.03824094,0.01529338,0.01035733,0.00987422,-0.09408108,-0.0715873,0.01654449,-0.03797249,0.00563764,-0.00325176,0.04087771,-0.02107916,-0.05417442,0.02197948,0.03201874,-0.04648873,0.02236915,-0.01497454,-0.01450984,-0.03140515,0.0056514,0.00386374,0.0413478,-0.00296607,-0.05974687,-0.04162977,-0.03553199,0.07764835,0.0563063,-0.05465275,0.00677703,-0.00165621,-0.02425005,0.00017659,0.00175187,-0.02151654,0.01349811,-0.01588132,0.0555675,-0.03228104,-0.00921278,-0.01810564,0.03688043,-0.02366472,0.01329365,-0.01404595,0.00311601,-0.04250699,-0.0522312,0.01757259,0.04999259,-0.04519193,-0.03318145,-0.00808497,0.0043109,-0.02596141,0.02146573,0.01707004,0.00709011,0.00398003,-0.00774165,0.00529483,-0.04748719,0.00882761,-0.00874347,-0.02169341,-0.02449043,0.10965602,-0.0375914,-0.02349614,0.01273081,0.00063182,0.02109859,-0.0808696,-0.02383204,0.0129107,0.03315699,-0.00927808,-0.0008764,0.02474672,-0.00094569,-0.02525975,0.0436626,-0.01901583,-0.03085211,0.0233927,-0.02825266,-0.00788742,-0.06486392,-0.03762885,0.0199612,-0.00506706,0.02301782,-0.00617706,0.00002419,0.017415,0.01292573,-0.00289324,-0.01995343,-0.01432762,0.01609672,-0.05415148,0.04084205,-0.02969703,-0.04001122,-0.01115704,-0.02567209,-0.02954528,-0.11541145,-0.00843347,0.00940892,-0.000779,-0.02809076,-0.00647716,-0.01883841,0.01557662,-0.03856895,0.04251035,-0.00510008,-0.04202906,0.01874782,0.00543739,0.02506885,0.01530609,0.02947359,-0.05292774,0.06550542,-0.08678345,0.00291172,0.00400418,0.03926972,0.03539839,-0.0088795,0.06577181,-0.01895377,-0.00859517,0.03761295,0.01544273,-0.00190262,0.02183859,-0.00856702,0.04123876,0.00219383,0.03899058,0.01566643,-0.05723661,0.0864742,0.01478413,0.01970843,0.01620131,-0.00103917,0.00880204,0.00394751,0.05165299,0.0179826,0.01614618,0.00836803,0.00606874,-0.00055273,0.00586946,0.04875074,0.06177169,-0.02496318,0.01988069,0.05832485,-0.0312805,0.02388027,0.00351394,0.06184089,0.00210549,-0.01374422,-0.05458778,-0.04157732,0.05482373,0.05402065,-0.06642768,-0.03742694,0.00804409,0.01381757,0.0569141,0.01001152,0.00394039,0.01907218,0.06405771,0.03828027,-0.02463272,-0.04293508,-0.03168435,-0.00367792,-0.03005279,-0.00109207,0.0058125,0.02120222,0.01776485,-0.01622579,0.05426895,0.01819631,-0.03158902,0.02593566,0.06369449,0.07001734,-0.04877215,0.01671649,-0.02261959,0.05815338,-0.00531355,0.01652489,0.0107455,0.02521423,-0.03048022,0.03542525,0.0174013,-0.00137558,-0.03634147,0.04540552,0.03963891,-0.03626299,-0.02272931,0.03313576,0.0231696,-0.01245421,0.01212328,-0.08988465,0.01907743,-0.05901099,-0.06918157,-0.03965093,-0.04449626,0.0518509,-0.02614541,-0.048385,0.03070666,0.00243251,0.00389279,0.03665216,0.03817765,0.01393801,-0.01295939,0.0819979,-0.03862945,-0.01578368,0.00595006,0.02112009,-0.0515312,0.00386097,0.00680843,-0.00163535,-0.01966457,0.024705,-0.02187335,0.02840683,-0.01587083,-0.01296771,0.0480311,0.08660143,-0.0325116,0.01119086,-0.00047158,-0.03273708,-0.01312994,-0.01051788,-0.06804343,-0.06942239,-0.13446291,-0.04862077,0.05454348,0.00829408,-0.01802406,0.00795626,0.0107733,0.03919638,-0.06019042,0.01555701,-0.03323555,0.00044481,0.0386236,-0.01769811,-0.06155004,-0.05399564,-0.0416247,0.03816523,-0.03597964,0.02280858,-0.03278786,0.04680095,-0.01861139,-0.00748535,-0.08029553,-0.04460211,0.04200581,0.00732548,-0.02389389,-0.01039036,-0.0262746,-0.04851805,-0.02861158,0.01637836,-0.02723667,0.03123617,0.0484185,0.00778703,-0.02965786,0.01321166,0.00895596,-0.00647423,0.01276406,-0.03943377,0.01293813,-0.02770092,0.0202706,-0.04887797,-0.05654141,-0.03333781,0.01365413,0.00267737,0.02225974,-0.03343162,0.00113759,-0.00310604,-0.0478103,0.00026871,0.00397647,-0.01513591,-0.02029666,-0.01941689,0.04520119,0.02005761,-0.01641219,0.00362097,-0.02168854,-0.0695506,0.01968893,0.00608087,0.04623051,0.0053554,-0.0187984,0.01397162,-0.02470277,-0.01280253,0.05008414,0.01916328,-0.02299504,0.05678848,-0.02024935,-0.02381919,0.0117416,0.02509476,0.05987378,0.0113298,0.00682238,0.05393084,-0.01830153,0.01340479,-0.04662949,-0.00797567,0.0231261,0.01557764,0.02786111,-0.06023144,-0.02758337,0.00681699,-0.00443679,0.00677925,-0.00495354,0.05143775,-0.02225029,-0.01587063,-0.00302169,0.02476559,0.02243422,0.03132236,-0.00815065,-0.00721942,0.03563191,-0.09268599,-0.00915313,-0.01192298,0.01626552,0.02935021,0.01866975,-0.01473425,-0.0042618,0.00944027,-0.01974754,0.0765618,0.01550802,-0.03126054,-0.03058861,0.06174964,-0.00018037,-0.01297558,-0.03884289,0.01596593,-0.03523885,0.01348933,0.05009146,0.03465569,-0.03546999,-0.04132487,-0.00363074,0.03450261,-0.00022576,0.00564294,-0.00849514,-0.00654705,0.06852525,-0.03752699,0.10503945,-0.06134967,0.0330746,0.01431024,0.10393131,-0.00450637,0.00760036,9.2e-7,-0.11659392,-0.02815123,-0.01398435,-0.04619044,0.02320806,-0.04065461,-0.00095716,-0.00846827,0.03176099],"last_embed":{"tokens":457,"hash":"8273z3"}}},"last_read":{"hash":"8273z3","at":1751079980502},"class_name":"SmartSource","outlinks":[{"title":"哈希算法","target":"哈希算法","line":8},{"title":"哈希值","target":"哈希算法","line":13},{"title":"彩虹表","target":"彩虹表","line":14},{"title":"哈希值","target":"哈希算法","line":14},{"title":"哈希算法","target":"哈希算法","line":21},{"title":"Openssl","target":"Openssl","line":36},{"title":"SHA-256","target":"SHA-256","line":52}],"metadata":{"aliases":["Salt"],"英文":"Salt","tags":["网络安全/密码学"],"基础知识":["[[哈希算法]]"]},"blocks":{"#---frontmatter---":[1,9],"#简介":[10,19],"#简介#{1}":[11,11],"#简介#{2}":[12,12],"#简介#{3}":[13,14],"#简介#{4}":[15,19],"#实例":[20,52],"#实例#{1}":[21,35],"#实例#{2}":[27,35],"#实例#基于[[Openssl]]的实际操作":[36,52],"#实例#基于[[Openssl]]的实际操作#**生成随机盐值**：":[38,46],"#实例#基于[[Openssl]]的实际操作#**生成随机盐值**：#{1}":[40,42],"#实例#基于[[Openssl]]的实际操作#**生成随机盐值**：#{2}":[43,44],"#实例#基于[[Openssl]]的实际操作#**生成随机盐值**：#{3}":[45,46],"#实例#基于[[Openssl]]的实际操作#**使用盐值进行哈希**：":[47,52],"#实例#基于[[Openssl]]的实际操作#**使用盐值进行哈希**：#{1}":[49,51],"#实例#基于[[Openssl]]的实际操作#**使用盐值进行哈希**：#{2}":[52,52]},"last_import":{"mtime":1737964745073,"size":1633,"at":1749024987320,"hash":"8273z3"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/密码学/盐值.md"},