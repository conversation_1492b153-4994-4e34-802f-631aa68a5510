"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05105343,-0.00295328,0.0036657,-0.04205765,0.03783983,-0.05784354,-0.01427677,0.0295327,0.03987896,-0.02077059,0.01424952,-0.02864787,0.05172927,0.02921687,0.01496604,-0.02711667,-0.01112895,0.03795557,-0.01829925,-0.00855374,0.10775201,-0.0275027,-0.00479112,-0.07372158,-0.00596656,0.05398575,0.00072088,0.00576469,0.00367415,-0.18315773,-0.01557636,0.03741232,0.02224145,0.02190524,-0.02477257,-0.03106014,0.00889034,0.03278522,-0.02350884,0.06360239,-0.00852988,-0.00722807,-0.0098362,-0.04321348,-0.02159635,-0.05137813,-0.04351546,-0.02120023,-0.01250482,-0.03866075,-0.03607344,-0.02895129,-0.02429555,-0.03127629,-0.04367311,-0.04018538,0.04383823,-0.01341862,0.02530672,-0.04752484,0.01273046,0.00587068,-0.23663925,0.06117576,0.00583875,-0.02363847,-0.01593881,-0.00053334,0.0028796,0.06385782,-0.05999497,-0.00554659,-0.03005577,0.07943922,0.02771629,0.02602781,0.00893779,-0.05791009,-0.03455707,-0.04591185,-0.02058544,0.03451231,-0.06730057,-0.03222247,-0.01020546,0.02912937,-0.01777859,-0.01803802,0.01746061,0.00090795,-0.00250752,-0.04572903,0.00421652,0.02693077,0.02646053,0.02194845,0.03581264,0.06588728,-0.04990645,0.11584897,-0.0560141,0.00685696,-0.01787067,-0.05196398,0.01752799,-0.00848128,-0.02377485,-0.00550201,0.02122879,-0.01203108,-0.04656572,-0.02380838,0.11188867,-0.03188564,0.02776689,-0.00240754,0.0039009,-0.02731384,0.01006498,-0.00683402,-0.03860318,-0.03321862,0.02829703,-0.01829979,0.01864319,-0.01732193,0.00538335,0.07045791,0.03214666,0.06076269,0.07669735,-0.0434427,-0.04713268,-0.01599238,-0.02475611,0.00396618,-0.02978997,0.02624595,-0.01243215,-0.02163117,-0.00486231,-0.07029859,-0.0572114,-0.04505762,-0.05725738,0.10348445,-0.07527734,0.01256665,0.07013942,-0.02773824,-0.02480998,0.03774435,-0.03285568,-0.01298292,-0.02253468,0.00728056,0.08966123,0.15331602,-0.07327301,-0.01615199,-0.00020262,0.04326193,-0.06623127,0.17599772,-0.00382857,-0.03347311,-0.02704276,-0.01841053,0.00660927,-0.02421935,0.08066752,-0.02021547,0.06191127,0.00141666,0.03593024,-0.03075808,-0.00597878,0.02432659,0.00004957,0.04777455,0.0808173,-0.00512478,-0.07063742,0.03812728,0.01918291,-0.12153234,-0.03752953,-0.00228937,-0.03801116,-0.03704379,-0.12909406,0.04867816,-0.04562781,-0.00296207,0.0013198,-0.09397763,0.00513508,-0.00310043,0.06181633,-0.06107274,0.13655719,0.03408698,-0.00911035,-0.01232257,-0.06465012,-0.0360574,-0.02154585,-0.01221449,-0.00399004,0.0017802,0.01237819,0.0216234,0.00583063,0.02003573,0.00718779,0.03317137,-0.00239017,0.00899536,0.03189297,0.01571749,0.01720223,-0.05229444,-0.06022929,-0.21328035,-0.01984417,0.04671376,-0.01832597,0.02508582,0.00197976,0.02471469,0.01331038,0.09395272,0.10351506,0.03987373,0.01280016,-0.07358499,0.02663338,-0.00221531,-0.00652205,0.00749185,-0.01674191,0.00486959,0.0163449,0.0039398,0.02874662,-0.00327425,-0.01004191,0.05363743,-0.00698929,0.14124712,0.07439836,0.021172,-0.0022064,0.01216791,0.02897757,0.0260678,-0.1012236,0.02995375,0.06608029,-0.04619609,-0.04643707,0.01463941,-0.04622387,0.03074699,0.01267409,-0.02694691,-0.03741856,0.01730615,-0.00930939,-0.00488619,-0.02712593,0.0002661,0.06279155,0.00519853,-0.0034888,0.03453415,-0.04240032,0.00648812,-0.05595755,-0.04651172,-0.04214855,-0.01578197,0.0243213,0.04933241,-0.01784551,0.01568342,-0.00833863,0.00617913,-0.0081906,-0.0144728,-0.03199259,-0.05262714,-0.01669584,-0.00360066,0.14592648,0.02355831,-0.05903789,0.0366139,0.00397878,0.02395068,-0.01819181,-0.02988121,-0.0277717,0.08628485,-0.00633603,0.0407659,-0.00057704,-0.00115216,0.03051523,0.02263776,-0.03979541,0.10067714,-0.08189691,-0.05203752,-0.02054022,-0.0428841,0.02791733,0.06875198,-0.04188079,-0.29579833,0.04403735,-0.02385948,0.02338846,0.03475174,0.04758986,0.03900923,-0.00703675,-0.06415514,0.01711651,-0.04593742,0.0585253,-0.03661209,-0.03026854,-0.01979738,-0.01445453,0.06308527,-0.00420204,0.06623847,-0.01815914,0.0194671,0.05458311,0.19647171,-0.03378221,0.03443946,-0.02094751,-0.00100295,0.00651118,0.02794866,0.0609674,0.02423994,-0.02595612,0.05043845,-0.04404983,0.06884111,0.10507087,-0.04878091,0.0306588,0.02303717,-0.00981241,-0.02237699,0.05619469,-0.02742012,0.04147474,0.04194484,-0.0065843,-0.01169215,-0.02127658,-0.02082028,0.07731014,0.02397747,0.03783101,-0.0283522,-0.02771153,0.01727105,0.05567805,0.00643255,-0.02772929,-0.0662349,0.01730117,0.00309146,0.01959598,0.1041293,0.14824766,0.05209554],"last_embed":{"hash":"a463e60ec2401ad7438671c84d972b1eb99a7206a2813238e6601458f64d5e13","tokens":442}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.08084057,-0.00646502,0.00557234,0.00389144,-0.04176816,0.04248578,0.0173569,-0.02516938,-0.05186383,-0.02803784,0.06528362,0.03908327,-0.02775004,0.06849251,0.00977212,0.03331291,-0.035909,0.05616748,-0.03441692,-0.06017156,-0.01275525,-0.02352243,0.01133223,0.05682446,-0.00781239,-0.03931807,0.0268812,-0.02805516,0.01820191,0.06422482,0.0484863,0.04768804,0.01285211,0.01379915,-0.00133871,-0.02277524,0.00403791,-0.00198442,-0.03257661,0.00477847,-0.01976983,0.02038491,-0.01824438,0.03892782,-0.04379057,-0.00875518,-0.02311321,-0.05845684,-0.01182064,0.00787546,0.01037079,-0.0588149,0.03987678,0.04892788,-0.00682127,-0.00626532,-0.05463079,-0.09238346,-0.02471341,-0.04012016,0.03397337,-0.04547136,0.04601898,0.03977962,-0.03934791,0.06577441,0.06136171,-0.00249608,-0.04917676,-0.010369,0.05829565,-0.01126873,0.03569273,-0.04246638,0.01149164,-0.02290748,0.02220508,-0.01160352,-0.05850849,-0.04765039,-0.05273072,0.00459752,0.02102864,0.01725451,0.02113593,-0.01620646,-0.01339265,-0.0515435,-0.02213531,-0.00584665,-0.00634924,-0.00065058,-0.01637543,-0.00975841,0.05775196,0.04439351,0.05339684,0.00813422,0.00170175,-0.00587914,-0.02753315,0.03595036,-0.10293175,0.00562802,-0.04647759,-0.03742301,-0.06946475,0.0182294,0.00690285,0.0134846,-0.01252582,0.01148418,-0.02108204,-0.01497263,-0.01408331,0.01246635,-0.05334575,0.02723653,0.03665176,0.08539946,-0.00279122,0.00389548,0.02107348,0.00315345,-0.02539811,0.01294072,0.01623368,-0.02410012,-0.00189932,-0.05396521,0.0095266,0.02670217,-0.01543467,0.02755475,0.02379488,0.03723306,0.05585803,-0.04667092,-0.01860934,-0.00318452,0.02916434,-0.00834087,0.06097753,-0.03762385,-0.0048144,-0.03569354,-0.03443681,-0.02681129,-0.0482981,0.04056925,0.05447087,-0.00119325,0.03695484,0.00457033,0.02420712,0.0122672,0.03562025,-0.02253126,0.00368683,-0.05046133,-0.01454432,0.03872284,-0.03216485,0.04790076,0.104447,-0.03115244,-0.03982764,-0.03503847,0.06531456,-0.03830423,0.02836672,0.00301206,-0.00906894,-0.02140089,0.00114112,0.00617273,0.01583343,0.04187817,0.01130501,0.00297638,0.04395236,0.0313083,0.0359123,0.05844101,-0.01887636,0.04080935,-0.04596559,0.06567468,-0.04001554,0.01357228,0.0139927,0.01927619,0.02349973,0.00125825,0.07883721,0.02604903,0.0025143,-0.05619732,0.02059105,0.02283297,0.00846961,-0.02203826,-0.03838558,-0.03031446,0.04654641,0.04300864,-0.0370938,0.00605898,0.00672524,0.01932289,-0.02075663,0.02141223,0.01389457,0.03272492,-0.02164377,-0.03586055,-0.02783111,-0.03291561,0.0109449,0.00053593,0.05431917,-0.01976769,0.0104984,0.0055711,-0.04267329,-0.033326,0.02199283,0.03234879,0.00362981,0.02339989,0.01423493,0.06318472,-0.00107148,-0.05047685,-0.02511831,0.06305443,-0.00345065,0.03264219,0.03506102,-0.03627443,0.01682673,0.0520371,0.0301941,0.0479267,-0.05708228,-0.00742994,-0.00786028,0.00967307,0.00580938,0.07704865,0.07946991,0.01690924,-0.02872412,0.00375214,0.034897,-0.06484595,-0.02061814,0.0531361,0.04838229,-0.00154888,-0.00831935,-0.01953878,-0.03540895,0.08457046,0.04249541,-0.04570288,-0.02499291,0.02885154,0.03714364,-0.03499607,-0.00154033,0.0089205,0.01702075,0.0294297,-0.02777153,-0.0060688,0.02287486,-0.00469932,0.09797558,-0.00743928,0.03351344,-0.0330302,-0.0232602,-0.02540387,-0.05014585,0.02015784,0.01399827,-0.04710549,0.07024416,0.03778595,-0.0093873,-0.02940382,-0.04950179,0.02154041,-0.02674028,-0.05757184,-0.02417708,0.01333758,0.01739524,-0.01951833,-0.00905695,0.03123704,-0.00870974,-0.00980758,-0.00451982,0.03466423,0.00235586,0.02772459,-0.00341691,-0.04482488,0.01141658,-0.02725719,0.04480701,0.02030768,-0.0405739,0.12638722,0.03102649,0.01989639,0.00920462,-0.04914469,-0.10311559,0.00429373,-0.05388441,-0.00460615,0.02005198,-0.08331364,0.05431946,-0.01490451,-0.02232319,0.04021046,-0.0453104,0.018253,-0.06115105,-0.05477498,0.05593444,0.00959893,-0.00326388,-0.02340039,0.05578598,0.00230055,0.08157141,-0.0367497,-0.02647152,-0.01996483,0.00208765,0.03258903,-0.01720274,0.04131553,-0.04901841,0.0028816,0.039142,-0.00639481,-0.00878953,0.03366005,0.00243261,-0.00038946,-0.00452861,0.02089297,-0.03221627,-0.02861245,-0.07699573,-0.0169407,-0.0319229,-0.0288118,0.00151889,-0.02397425,0.0120995,0.0538748,-0.00877119,-0.05757403,-0.02267268,-0.05255742,0.02237317,0.00686553,-0.02811884,-0.00752776,-0.02657473,0.0107016,-0.00736698,-0.02858549,-0.02609923,-0.04660003,0.02597776,0.00344256,-0.02836112,0.03101312,-0.00890281,0.00561679,-0.00584116,-0.04737307,-0.01048891,0.01157954,-0.05631877,0.01368697,0.09289858,0.02021112,0.02499731,0.02030062,-0.01833356,-0.04058731,0.03090186,-0.02994523,-0.02603482,-0.07083608,0.01410699,-0.01921531,-0.05326847,0.02489713,-0.01682949,-0.00664936,-0.07839602,-0.05261739,-0.01463338,0.07319552,-0.05825461,0.01239422,-0.00214346,-0.05107643,-0.03191112,-0.04103911,0.04254869,0.00011591,-0.03352092,0.01092288,-0.02409114,-0.02032724,-0.05090494,-0.02347547,0.0582298,0.01852066,-0.04337242,-0.01938733,-0.06569772,0.00611693,0.06584487,-0.02523553,0.04118051,-0.01418451,0.01365268,-0.05604093,-0.02355361,-0.00954756,0.06546911,-0.01021008,0.05605315,0.006149,0.006788,-0.02231858,-0.00707057,0.05878089,-0.04436892,-0.01455572,-0.06374992,-0.03442268,-0.03669446,0.00879644,-0.00536383,-0.02968449,-0.02105893,0.01250279,-0.0324312,0.08217598,0.04456155,0.00624064,-0.07537363,0.03235938,0.00909179,0.04392495,-0.04178408,-0.03095553,0.01318122,0.01240678,0.05112232,-0.06098741,0.0183612,0.07949948,0.00624682,-0.0092233,-0.02204182,0.02084625,-0.00814852,-0.02243355,-0.00015042,0.01835965,-0.02553282,0.03133548,0.01063954,0.02454903,-0.01382236,-0.04829734,-0.00563313,0.01148345,0.03999389,-0.03267121,-0.01826529,-0.07891732,-0.02250373,0.05384139,-0.02382488,0.05376236,0.00889155,0.01293696,0.01399315,0.06905544,-0.00596501,-0.01085284,-0.0619614,0.01335781,0.01830976,0.00571681,-0.02706658,-0.09149108,0.05492687,-0.02027444,0.00429055,-0.01690554,0.05929453,-0.02673308,-0.00616883,-0.03496508,-0.01416791,0.0711564,-0.03774365,-0.00339259,-0.0245181,-0.06305222,-0.02004172,0.0663926,0.04806184,-0.05699747,-0.00846694,0.01765968,0.02800882,-0.04750553,0.04004614,-0.02307188,0.00538505,0.0337333,0.00629746,-0.04642215,-0.08063494,-0.02402989,-0.05123078,0.08533925,-0.00657021,0.01396901,0.00545104,-0.05175073,0.01719473,0.00929645,0.01159847,0.05391215,0.03490199,-0.00151815,0.04041681,-0.01213455,0.01050105,-0.04124628,-0.01323669,0.05527291,-0.02985539,0.06559657,-0.0040186,0.00167164,-0.08957507,-0.00294116,-0.01001128,-0.00975476,0.0329964,0.02449832,-0.0233407,0.00795204,0.05154553,0.00448986,-0.03928225,-0.00698914,-0.03033434,0.02393874,0.0572071,-0.02795238,0.01627717,-0.04515858,0.03737639,-0.06068293,-0.00680389,-0.01764461,0.01942283,0.03800061,0.01606092,0.05891145,0.04322701,0.00875358,-0.0111168,0.00944379,0.03883135,-0.00047725,0.0149487,0.03990714,0.03358899,-0.01992113,-0.01991734,0.03829337,0.01251312,0.00339977,-0.01176257,-0.05589262,-0.01473812,-0.03638913,0.01920125,0.00761878,0.00622838,0.08400007,-0.01161039,-0.07289625,0.01009734,0.00910959,-0.082721,0.03393435,-0.04877864,0.01843668,-0.00240996,0.02149718,-0.01312602,-0.03457477,0.03038746,0.01221731,-0.01221801,-0.03439813,0.06402431,-0.05388873,-0.06027176,-0.05812041,-0.02652858,0.00543109,0.0273478,0.03693466,-0.03070034,-0.02057106,-0.01801006,0.0186636,0.04502778,-0.03206185,-0.04540221,0.02772672,0.02264637,0.0210272,-0.01784739,0.01900357,-0.03073262,0.04284671,0.00125948,0.00910336,-0.04821131,-0.03514229,-0.00274836,0.02099912,-0.02038365,0.02714569,0.01642407,-0.00049079,-0.02968449,-0.03855409,-0.02705012,-0.03599892,-0.02938741,-0.01777934,0.02674347,0.00318447,0.01564221,0.00954867,0.02456867,-0.01248343,-0.00616385,0.03205801,0.00822246,-0.01072011,-0.04044698,0.03564328,0.01507056,0.00715588,-0.0460818,-0.01211659,-0.00907831,-0.02449223,0.03636933,-0.0136332,0.00086118,-0.00614224,0.01467508,-0.03581506,-0.0083647,-0.01058366,-0.03337799,-0.02056239,0.01954676,-0.02487781,0.02866952,0.00874909,0.02823318,-0.02061865,-0.00988212,0.01876784,-0.0065046,0.00363722,-0.01185248,0.00653312,0.0352147,0.04015951,0.02878108,-0.01000991,-0.0638784,0.00733008,0.0386588,-0.04996014,0.03125281,0.00587443,0.00710148,0.03426072,-0.06567018,0.02812436,0.00907249,0.05978401,0.01901102,0.07979417,-0.10592868,-0.00772466,-0.04994954,0.08462471,-0.01152827,0.04478953,0.01317684,-0.03445925,0.0005118,0.02251263,0.0513253,-0.02410849,-0.04181915,-0.01741659,0.01674492,0.00988081,-0.04331295,-0.03619301,-0.03684286,-0.00339823,-0.10217009,-0.05628735,-0.0107675,0.01700073,-0.04609834,0.06135814,-0.00503003,-0.02272362,-0.02302637,0.02857646,-0.00822746,-0.02994347,0.0153112,-0.01394989,7.1e-7,0.04746889,0.01178129,0.08091708,-0.03836666,-0.01317292,-0.03793351,0.01895032,-0.00527167,0.00457275],"last_embed":{"tokens":813,"hash":"6ky0uz"}}},"last_read":{"hash":"6ky0uz","at":1750993403712},"class_name":"SmartSource","outlinks":[{"title":"正则表达式","target":"正则表达式","line":27},{"title":"awk","target":"awk","line":29},{"title":"sed","target":"sed","line":29}],"metadata":{"aliases":["Global Regular Expression Print"],"tags":["工具/数据处理","操作系统/文件系统"],"工具界面":["命令行"],"系统平台":["Linux发行版"],"发布时间":null,"开发者":null,"邮箱":null,"官网":null,"♥star":null,"文档更新日期":"2024-02-27 16:22","类型":null,"开发栈":null,"编程语言":null,"架构平台":null,"工具类型":["数据处理"]},"blocks":{"#---frontmatter---":[1,23],"#简介":[24,33],"#简介#{1}":[25,25],"#简介#{2}":[26,28],"#简介#{3}":[29,32],"#简介#{4}":[33,33],"#常用参数":[34,48],"#常用参数#{1}":[36,48],"#使用技巧":[49,88],"#使用技巧#常用查询方式":[51,74],"#使用技巧#常用查询方式#{1}":[52,55],"#使用技巧#常用查询方式#{2}":[56,56],"#使用技巧#常用查询方式#{3}":[57,62],"#使用技巧#常用查询方式#{4}":[63,63],"#使用技巧#常用查询方式#{5}":[64,68],"#使用技巧#常用查询方式#{6}":[69,69],"#使用技巧#常用查询方式#{7}":[70,70],"#使用技巧#常用查询方式#{8}":[71,71],"#使用技巧#常用查询方式#{9}":[72,73],"#使用技巧#常用查询方式#{10}":[74,74],"#使用技巧#数据过滤":[75,88],"#使用技巧#数据过滤#{1}":[76,79],"#使用技巧#数据过滤#{2}":[80,80],"#使用技巧#数据过滤#{3}":[81,87],"#使用技巧#数据过滤#{4}":[88,88]},"last_import":{"mtime":1729570301065,"size":2961,"at":1749024987540,"hash":"6ky0uz"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md","last_embed":{"hash":"6ky0uz","at":1750993403712}},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#---frontmatter---","lines":[1,23],"size":213,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#简介": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#简介","lines":[24,33],"size":171,"outlinks":[{"title":"正则表达式","target":"正则表达式","line":4},{"title":"awk","target":"awk","line":6},{"title":"sed","target":"sed","line":6}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#简介#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#简介#{1}","lines":[25,25],"size":28,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#简介#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#简介#{2}","lines":[26,28],"size":78,"outlinks":[{"title":"正则表达式","target":"正则表达式","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#简介#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#简介#{3}","lines":[29,32],"size":54,"outlinks":[{"title":"awk","target":"awk","line":1},{"title":"sed","target":"sed","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#简介#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#简介#{4}","lines":[33,33],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#常用参数": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#常用参数","lines":[34,48],"size":453,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#常用参数#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#常用参数#{1}","lines":[36,48],"size":445,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#使用技巧": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#使用技巧","lines":[49,88],"size":947,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#使用技巧#常用查询方式": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#使用技巧#常用查询方式","lines":[51,74],"size":687,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#使用技巧#常用查询方式#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#使用技巧#常用查询方式#{1}","lines":[52,55],"size":67,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#使用技巧#常用查询方式#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#使用技巧#常用查询方式#{2}","lines":[56,56],"size":28,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#使用技巧#常用查询方式#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#使用技巧#常用查询方式#{3}","lines":[57,62],"size":61,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#使用技巧#常用查询方式#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#使用技巧#常用查询方式#{4}","lines":[63,63],"size":40,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#使用技巧#常用查询方式#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#使用技巧#常用查询方式#{5}","lines":[64,68],"size":90,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#使用技巧#常用查询方式#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#使用技巧#常用查询方式#{6}","lines":[69,69],"size":106,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#使用技巧#常用查询方式#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#使用技巧#常用查询方式#{7}","lines":[70,70],"size":43,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#使用技巧#常用查询方式#{8}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#使用技巧#常用查询方式#{8}","lines":[71,71],"size":137,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#使用技巧#常用查询方式#{9}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#使用技巧#常用查询方式#{9}","lines":[72,73],"size":92,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#使用技巧#常用查询方式#{10}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#使用技巧#常用查询方式#{10}","lines":[74,74],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#使用技巧#数据过滤": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#使用技巧#数据过滤","lines":[75,88],"size":251,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#使用技巧#数据过滤#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#使用技巧#数据过滤#{1}","lines":[76,79],"size":97,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#使用技巧#数据过滤#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#使用技巧#数据过滤#{2}","lines":[80,80],"size":21,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#使用技巧#数据过滤#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#使用技巧#数据过滤#{3}","lines":[81,87],"size":92,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#使用技巧#数据过滤#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md#使用技巧#数据过滤#{4}","lines":[88,88],"size":30,"outlinks":[],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.05105343,-0.00295328,0.0036657,-0.04205765,0.03783983,-0.05784354,-0.01427677,0.0295327,0.03987896,-0.02077059,0.01424952,-0.02864787,0.05172927,0.02921687,0.01496604,-0.02711667,-0.01112895,0.03795557,-0.01829925,-0.00855374,0.10775201,-0.0275027,-0.00479112,-0.07372158,-0.00596656,0.05398575,0.00072088,0.00576469,0.00367415,-0.18315773,-0.01557636,0.03741232,0.02224145,0.02190524,-0.02477257,-0.03106014,0.00889034,0.03278522,-0.02350884,0.06360239,-0.00852988,-0.00722807,-0.0098362,-0.04321348,-0.02159635,-0.05137813,-0.04351546,-0.02120023,-0.01250482,-0.03866075,-0.03607344,-0.02895129,-0.02429555,-0.03127629,-0.04367311,-0.04018538,0.04383823,-0.01341862,0.02530672,-0.04752484,0.01273046,0.00587068,-0.23663925,0.06117576,0.00583875,-0.02363847,-0.01593881,-0.00053334,0.0028796,0.06385782,-0.05999497,-0.00554659,-0.03005577,0.07943922,0.02771629,0.02602781,0.00893779,-0.05791009,-0.03455707,-0.04591185,-0.02058544,0.03451231,-0.06730057,-0.03222247,-0.01020546,0.02912937,-0.01777859,-0.01803802,0.01746061,0.00090795,-0.00250752,-0.04572903,0.00421652,0.02693077,0.02646053,0.02194845,0.03581264,0.06588728,-0.04990645,0.11584897,-0.0560141,0.00685696,-0.01787067,-0.05196398,0.01752799,-0.00848128,-0.02377485,-0.00550201,0.02122879,-0.01203108,-0.04656572,-0.02380838,0.11188867,-0.03188564,0.02776689,-0.00240754,0.0039009,-0.02731384,0.01006498,-0.00683402,-0.03860318,-0.03321862,0.02829703,-0.01829979,0.01864319,-0.01732193,0.00538335,0.07045791,0.03214666,0.06076269,0.07669735,-0.0434427,-0.04713268,-0.01599238,-0.02475611,0.00396618,-0.02978997,0.02624595,-0.01243215,-0.02163117,-0.00486231,-0.07029859,-0.0572114,-0.04505762,-0.05725738,0.10348445,-0.07527734,0.01256665,0.07013942,-0.02773824,-0.02480998,0.03774435,-0.03285568,-0.01298292,-0.02253468,0.00728056,0.08966123,0.15331602,-0.07327301,-0.01615199,-0.00020262,0.04326193,-0.06623127,0.17599772,-0.00382857,-0.03347311,-0.02704276,-0.01841053,0.00660927,-0.02421935,0.08066752,-0.02021547,0.06191127,0.00141666,0.03593024,-0.03075808,-0.00597878,0.02432659,0.00004957,0.04777455,0.0808173,-0.00512478,-0.07063742,0.03812728,0.01918291,-0.12153234,-0.03752953,-0.00228937,-0.03801116,-0.03704379,-0.12909406,0.04867816,-0.04562781,-0.00296207,0.0013198,-0.09397763,0.00513508,-0.00310043,0.06181633,-0.06107274,0.13655719,0.03408698,-0.00911035,-0.01232257,-0.06465012,-0.0360574,-0.02154585,-0.01221449,-0.00399004,0.0017802,0.01237819,0.0216234,0.00583063,0.02003573,0.00718779,0.03317137,-0.00239017,0.00899536,0.03189297,0.01571749,0.01720223,-0.05229444,-0.06022929,-0.21328035,-0.01984417,0.04671376,-0.01832597,0.02508582,0.00197976,0.02471469,0.01331038,0.09395272,0.10351506,0.03987373,0.01280016,-0.07358499,0.02663338,-0.00221531,-0.00652205,0.00749185,-0.01674191,0.00486959,0.0163449,0.0039398,0.02874662,-0.00327425,-0.01004191,0.05363743,-0.00698929,0.14124712,0.07439836,0.021172,-0.0022064,0.01216791,0.02897757,0.0260678,-0.1012236,0.02995375,0.06608029,-0.04619609,-0.04643707,0.01463941,-0.04622387,0.03074699,0.01267409,-0.02694691,-0.03741856,0.01730615,-0.00930939,-0.00488619,-0.02712593,0.0002661,0.06279155,0.00519853,-0.0034888,0.03453415,-0.04240032,0.00648812,-0.05595755,-0.04651172,-0.04214855,-0.01578197,0.0243213,0.04933241,-0.01784551,0.01568342,-0.00833863,0.00617913,-0.0081906,-0.0144728,-0.03199259,-0.05262714,-0.01669584,-0.00360066,0.14592648,0.02355831,-0.05903789,0.0366139,0.00397878,0.02395068,-0.01819181,-0.02988121,-0.0277717,0.08628485,-0.00633603,0.0407659,-0.00057704,-0.00115216,0.03051523,0.02263776,-0.03979541,0.10067714,-0.08189691,-0.05203752,-0.02054022,-0.0428841,0.02791733,0.06875198,-0.04188079,-0.29579833,0.04403735,-0.02385948,0.02338846,0.03475174,0.04758986,0.03900923,-0.00703675,-0.06415514,0.01711651,-0.04593742,0.0585253,-0.03661209,-0.03026854,-0.01979738,-0.01445453,0.06308527,-0.00420204,0.06623847,-0.01815914,0.0194671,0.05458311,0.19647171,-0.03378221,0.03443946,-0.02094751,-0.00100295,0.00651118,0.02794866,0.0609674,0.02423994,-0.02595612,0.05043845,-0.04404983,0.06884111,0.10507087,-0.04878091,0.0306588,0.02303717,-0.00981241,-0.02237699,0.05619469,-0.02742012,0.04147474,0.04194484,-0.0065843,-0.01169215,-0.02127658,-0.02082028,0.07731014,0.02397747,0.03783101,-0.0283522,-0.02771153,0.01727105,0.05567805,0.00643255,-0.02772929,-0.0662349,0.01730117,0.00309146,0.01959598,0.1041293,0.14824766,0.05209554],"last_embed":{"hash":"a463e60ec2401ad7438671c84d972b1eb99a7206a2813238e6601458f64d5e13","tokens":442}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.08084057,-0.00646502,0.00557234,0.00389144,-0.04176816,0.04248578,0.0173569,-0.02516938,-0.05186383,-0.02803784,0.06528362,0.03908327,-0.02775004,0.06849251,0.00977212,0.03331291,-0.035909,0.05616748,-0.03441692,-0.06017156,-0.01275525,-0.02352243,0.01133223,0.05682446,-0.00781239,-0.03931807,0.0268812,-0.02805516,0.01820191,0.06422482,0.0484863,0.04768804,0.01285211,0.01379915,-0.00133871,-0.02277524,0.00403791,-0.00198442,-0.03257661,0.00477847,-0.01976983,0.02038491,-0.01824438,0.03892782,-0.04379057,-0.00875518,-0.02311321,-0.05845684,-0.01182064,0.00787546,0.01037079,-0.0588149,0.03987678,0.04892788,-0.00682127,-0.00626532,-0.05463079,-0.09238346,-0.02471341,-0.04012016,0.03397337,-0.04547136,0.04601898,0.03977962,-0.03934791,0.06577441,0.06136171,-0.00249608,-0.04917676,-0.010369,0.05829565,-0.01126873,0.03569273,-0.04246638,0.01149164,-0.02290748,0.02220508,-0.01160352,-0.05850849,-0.04765039,-0.05273072,0.00459752,0.02102864,0.01725451,0.02113593,-0.01620646,-0.01339265,-0.0515435,-0.02213531,-0.00584665,-0.00634924,-0.00065058,-0.01637543,-0.00975841,0.05775196,0.04439351,0.05339684,0.00813422,0.00170175,-0.00587914,-0.02753315,0.03595036,-0.10293175,0.00562802,-0.04647759,-0.03742301,-0.06946475,0.0182294,0.00690285,0.0134846,-0.01252582,0.01148418,-0.02108204,-0.01497263,-0.01408331,0.01246635,-0.05334575,0.02723653,0.03665176,0.08539946,-0.00279122,0.00389548,0.02107348,0.00315345,-0.02539811,0.01294072,0.01623368,-0.02410012,-0.00189932,-0.05396521,0.0095266,0.02670217,-0.01543467,0.02755475,0.02379488,0.03723306,0.05585803,-0.04667092,-0.01860934,-0.00318452,0.02916434,-0.00834087,0.06097753,-0.03762385,-0.0048144,-0.03569354,-0.03443681,-0.02681129,-0.0482981,0.04056925,0.05447087,-0.00119325,0.03695484,0.00457033,0.02420712,0.0122672,0.03562025,-0.02253126,0.00368683,-0.05046133,-0.01454432,0.03872284,-0.03216485,0.04790076,0.104447,-0.03115244,-0.03982764,-0.03503847,0.06531456,-0.03830423,0.02836672,0.00301206,-0.00906894,-0.02140089,0.00114112,0.00617273,0.01583343,0.04187817,0.01130501,0.00297638,0.04395236,0.0313083,0.0359123,0.05844101,-0.01887636,0.04080935,-0.04596559,0.06567468,-0.04001554,0.01357228,0.0139927,0.01927619,0.02349973,0.00125825,0.07883721,0.02604903,0.0025143,-0.05619732,0.02059105,0.02283297,0.00846961,-0.02203826,-0.03838558,-0.03031446,0.04654641,0.04300864,-0.0370938,0.00605898,0.00672524,0.01932289,-0.02075663,0.02141223,0.01389457,0.03272492,-0.02164377,-0.03586055,-0.02783111,-0.03291561,0.0109449,0.00053593,0.05431917,-0.01976769,0.0104984,0.0055711,-0.04267329,-0.033326,0.02199283,0.03234879,0.00362981,0.02339989,0.01423493,0.06318472,-0.00107148,-0.05047685,-0.02511831,0.06305443,-0.00345065,0.03264219,0.03506102,-0.03627443,0.01682673,0.0520371,0.0301941,0.0479267,-0.05708228,-0.00742994,-0.00786028,0.00967307,0.00580938,0.07704865,0.07946991,0.01690924,-0.02872412,0.00375214,0.034897,-0.06484595,-0.02061814,0.0531361,0.04838229,-0.00154888,-0.00831935,-0.01953878,-0.03540895,0.08457046,0.04249541,-0.04570288,-0.02499291,0.02885154,0.03714364,-0.03499607,-0.00154033,0.0089205,0.01702075,0.0294297,-0.02777153,-0.0060688,0.02287486,-0.00469932,0.09797558,-0.00743928,0.03351344,-0.0330302,-0.0232602,-0.02540387,-0.05014585,0.02015784,0.01399827,-0.04710549,0.07024416,0.03778595,-0.0093873,-0.02940382,-0.04950179,0.02154041,-0.02674028,-0.05757184,-0.02417708,0.01333758,0.01739524,-0.01951833,-0.00905695,0.03123704,-0.00870974,-0.00980758,-0.00451982,0.03466423,0.00235586,0.02772459,-0.00341691,-0.04482488,0.01141658,-0.02725719,0.04480701,0.02030768,-0.0405739,0.12638722,0.03102649,0.01989639,0.00920462,-0.04914469,-0.10311559,0.00429373,-0.05388441,-0.00460615,0.02005198,-0.08331364,0.05431946,-0.01490451,-0.02232319,0.04021046,-0.0453104,0.018253,-0.06115105,-0.05477498,0.05593444,0.00959893,-0.00326388,-0.02340039,0.05578598,0.00230055,0.08157141,-0.0367497,-0.02647152,-0.01996483,0.00208765,0.03258903,-0.01720274,0.04131553,-0.04901841,0.0028816,0.039142,-0.00639481,-0.00878953,0.03366005,0.00243261,-0.00038946,-0.00452861,0.02089297,-0.03221627,-0.02861245,-0.07699573,-0.0169407,-0.0319229,-0.0288118,0.00151889,-0.02397425,0.0120995,0.0538748,-0.00877119,-0.05757403,-0.02267268,-0.05255742,0.02237317,0.00686553,-0.02811884,-0.00752776,-0.02657473,0.0107016,-0.00736698,-0.02858549,-0.02609923,-0.04660003,0.02597776,0.00344256,-0.02836112,0.03101312,-0.00890281,0.00561679,-0.00584116,-0.04737307,-0.01048891,0.01157954,-0.05631877,0.01368697,0.09289858,0.02021112,0.02499731,0.02030062,-0.01833356,-0.04058731,0.03090186,-0.02994523,-0.02603482,-0.07083608,0.01410699,-0.01921531,-0.05326847,0.02489713,-0.01682949,-0.00664936,-0.07839602,-0.05261739,-0.01463338,0.07319552,-0.05825461,0.01239422,-0.00214346,-0.05107643,-0.03191112,-0.04103911,0.04254869,0.00011591,-0.03352092,0.01092288,-0.02409114,-0.02032724,-0.05090494,-0.02347547,0.0582298,0.01852066,-0.04337242,-0.01938733,-0.06569772,0.00611693,0.06584487,-0.02523553,0.04118051,-0.01418451,0.01365268,-0.05604093,-0.02355361,-0.00954756,0.06546911,-0.01021008,0.05605315,0.006149,0.006788,-0.02231858,-0.00707057,0.05878089,-0.04436892,-0.01455572,-0.06374992,-0.03442268,-0.03669446,0.00879644,-0.00536383,-0.02968449,-0.02105893,0.01250279,-0.0324312,0.08217598,0.04456155,0.00624064,-0.07537363,0.03235938,0.00909179,0.04392495,-0.04178408,-0.03095553,0.01318122,0.01240678,0.05112232,-0.06098741,0.0183612,0.07949948,0.00624682,-0.0092233,-0.02204182,0.02084625,-0.00814852,-0.02243355,-0.00015042,0.01835965,-0.02553282,0.03133548,0.01063954,0.02454903,-0.01382236,-0.04829734,-0.00563313,0.01148345,0.03999389,-0.03267121,-0.01826529,-0.07891732,-0.02250373,0.05384139,-0.02382488,0.05376236,0.00889155,0.01293696,0.01399315,0.06905544,-0.00596501,-0.01085284,-0.0619614,0.01335781,0.01830976,0.00571681,-0.02706658,-0.09149108,0.05492687,-0.02027444,0.00429055,-0.01690554,0.05929453,-0.02673308,-0.00616883,-0.03496508,-0.01416791,0.0711564,-0.03774365,-0.00339259,-0.0245181,-0.06305222,-0.02004172,0.0663926,0.04806184,-0.05699747,-0.00846694,0.01765968,0.02800882,-0.04750553,0.04004614,-0.02307188,0.00538505,0.0337333,0.00629746,-0.04642215,-0.08063494,-0.02402989,-0.05123078,0.08533925,-0.00657021,0.01396901,0.00545104,-0.05175073,0.01719473,0.00929645,0.01159847,0.05391215,0.03490199,-0.00151815,0.04041681,-0.01213455,0.01050105,-0.04124628,-0.01323669,0.05527291,-0.02985539,0.06559657,-0.0040186,0.00167164,-0.08957507,-0.00294116,-0.01001128,-0.00975476,0.0329964,0.02449832,-0.0233407,0.00795204,0.05154553,0.00448986,-0.03928225,-0.00698914,-0.03033434,0.02393874,0.0572071,-0.02795238,0.01627717,-0.04515858,0.03737639,-0.06068293,-0.00680389,-0.01764461,0.01942283,0.03800061,0.01606092,0.05891145,0.04322701,0.00875358,-0.0111168,0.00944379,0.03883135,-0.00047725,0.0149487,0.03990714,0.03358899,-0.01992113,-0.01991734,0.03829337,0.01251312,0.00339977,-0.01176257,-0.05589262,-0.01473812,-0.03638913,0.01920125,0.00761878,0.00622838,0.08400007,-0.01161039,-0.07289625,0.01009734,0.00910959,-0.082721,0.03393435,-0.04877864,0.01843668,-0.00240996,0.02149718,-0.01312602,-0.03457477,0.03038746,0.01221731,-0.01221801,-0.03439813,0.06402431,-0.05388873,-0.06027176,-0.05812041,-0.02652858,0.00543109,0.0273478,0.03693466,-0.03070034,-0.02057106,-0.01801006,0.0186636,0.04502778,-0.03206185,-0.04540221,0.02772672,0.02264637,0.0210272,-0.01784739,0.01900357,-0.03073262,0.04284671,0.00125948,0.00910336,-0.04821131,-0.03514229,-0.00274836,0.02099912,-0.02038365,0.02714569,0.01642407,-0.00049079,-0.02968449,-0.03855409,-0.02705012,-0.03599892,-0.02938741,-0.01777934,0.02674347,0.00318447,0.01564221,0.00954867,0.02456867,-0.01248343,-0.00616385,0.03205801,0.00822246,-0.01072011,-0.04044698,0.03564328,0.01507056,0.00715588,-0.0460818,-0.01211659,-0.00907831,-0.02449223,0.03636933,-0.0136332,0.00086118,-0.00614224,0.01467508,-0.03581506,-0.0083647,-0.01058366,-0.03337799,-0.02056239,0.01954676,-0.02487781,0.02866952,0.00874909,0.02823318,-0.02061865,-0.00988212,0.01876784,-0.0065046,0.00363722,-0.01185248,0.00653312,0.0352147,0.04015951,0.02878108,-0.01000991,-0.0638784,0.00733008,0.0386588,-0.04996014,0.03125281,0.00587443,0.00710148,0.03426072,-0.06567018,0.02812436,0.00907249,0.05978401,0.01901102,0.07979417,-0.10592868,-0.00772466,-0.04994954,0.08462471,-0.01152827,0.04478953,0.01317684,-0.03445925,0.0005118,0.02251263,0.0513253,-0.02410849,-0.04181915,-0.01741659,0.01674492,0.00988081,-0.04331295,-0.03619301,-0.03684286,-0.00339823,-0.10217009,-0.05628735,-0.0107675,0.01700073,-0.04609834,0.06135814,-0.00503003,-0.02272362,-0.02302637,0.02857646,-0.00822746,-0.02994347,0.0153112,-0.01394989,7.1e-7,0.04746889,0.01178129,0.08091708,-0.03836666,-0.01317292,-0.03793351,0.01895032,-0.00527167,0.00457275],"last_embed":{"tokens":813,"hash":"6ky0uz"}}},"last_read":{"hash":"6ky0uz","at":1751079989297},"class_name":"SmartSource","outlinks":[{"title":"正则表达式","target":"正则表达式","line":27},{"title":"awk","target":"awk","line":29},{"title":"sed","target":"sed","line":29}],"metadata":{"aliases":["Global Regular Expression Print"],"tags":["工具/数据处理","操作系统/文件系统"],"工具界面":["命令行"],"系统平台":["Linux发行版"],"发布时间":null,"开发者":null,"邮箱":null,"官网":null,"♥star":null,"文档更新日期":"2024-02-27 16:22","类型":null,"开发栈":null,"编程语言":null,"架构平台":null,"工具类型":["数据处理"]},"blocks":{"#---frontmatter---":[1,23],"#简介":[24,33],"#简介#{1}":[25,25],"#简介#{2}":[26,28],"#简介#{3}":[29,32],"#简介#{4}":[33,33],"#常用参数":[34,48],"#常用参数#{1}":[36,48],"#使用技巧":[49,88],"#使用技巧#常用查询方式":[51,74],"#使用技巧#常用查询方式#{1}":[52,55],"#使用技巧#常用查询方式#{2}":[56,56],"#使用技巧#常用查询方式#{3}":[57,62],"#使用技巧#常用查询方式#{4}":[63,63],"#使用技巧#常用查询方式#{5}":[64,68],"#使用技巧#常用查询方式#{6}":[69,69],"#使用技巧#常用查询方式#{7}":[70,70],"#使用技巧#常用查询方式#{8}":[71,71],"#使用技巧#常用查询方式#{9}":[72,73],"#使用技巧#常用查询方式#{10}":[74,74],"#使用技巧#数据过滤":[75,88],"#使用技巧#数据过滤#{1}":[76,79],"#使用技巧#数据过滤#{2}":[80,80],"#使用技巧#数据过滤#{3}":[81,87],"#使用技巧#数据过滤#{4}":[88,88]},"last_import":{"mtime":1729570301065,"size":2961,"at":1749024987540,"hash":"6ky0uz"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/数据处理工具/grep.md","last_embed":{"hash":"6ky0uz","at":1751079989297}},