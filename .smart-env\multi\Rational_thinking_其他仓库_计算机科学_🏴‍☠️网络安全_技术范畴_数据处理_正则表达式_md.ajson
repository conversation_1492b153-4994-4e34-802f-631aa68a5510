"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md","last_embed":{"hash":"b0g3gy","at":1750993395185},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08388279,-0.01542185,-0.01710912,-0.04223506,0.0163874,-0.00327926,-0.00564028,0.03111003,0.05510193,-0.01834314,0.00102901,-0.04262774,0.06737566,0.02992057,0.07070322,0.02138679,-0.01327496,0.07448065,0.01361034,-0.01247035,0.09367356,-0.00865906,-0.01056392,-0.0592685,-0.01335617,0.04342286,0.02760069,-0.02184499,-0.00544074,-0.18749052,-0.00947071,0.02028155,0.04804475,0.00787257,0.00235113,-0.0441929,0.01834353,0.07104044,-0.01596594,0.04903884,-0.00774325,0.01741534,0.01241374,-0.01735547,-0.0286368,-0.05408061,0.00941961,-0.01541584,-0.00703702,-0.03480574,-0.02011818,-0.07091691,-0.04294333,0.01881068,-0.06930163,0.00718763,0.04024045,-0.00726264,0.04414426,-0.0221084,0.00062583,0.0155294,-0.20991573,0.06236512,-0.00496096,-0.02340926,-0.00555747,-0.01415703,0.0009644,0.08124276,-0.04051842,-0.00931416,-0.0423455,0.07586405,0.02763409,0.0329074,-0.01026351,-0.05475784,-0.01753335,-0.022363,-0.03940357,-0.0090535,-0.01418902,0.01806147,0.02028704,0.03315207,-0.00063279,-0.0384476,0.03633592,0.02610554,0.02725687,-0.06241966,0.01663627,0.03227326,-0.00124478,-0.00876875,0.02398342,0.04824919,-0.04128463,0.13103795,-0.05845413,0.013382,-0.01262826,-0.05045319,-0.01241266,-0.01062796,-0.00645233,0.00500765,0.0162566,-0.04886974,-0.07691558,0.00641106,0.06831165,-0.01784994,0.00462701,0.02091219,0.02152733,-0.04157566,0.00041583,-0.01628811,-0.02072391,-0.01376009,0.03833891,-0.01335463,0.00048647,-0.04262716,0.01294993,0.07081003,0.01526292,0.06821372,0.12282597,-0.02282102,-0.05958614,-0.02377871,-0.00998643,-0.01855469,-0.04262847,0.0319044,-0.02606937,-0.03151945,-0.01269148,-0.0753153,-0.05246828,-0.04383191,-0.09683875,0.10451008,-0.07493192,-0.02799682,0.04685627,-0.05406401,0.0103086,0.03573711,-0.02236313,0.02145514,0.00479218,-0.00917906,0.09057575,0.12325168,-0.03843355,-0.01969188,-0.00785842,0.0493965,-0.08738455,0.18037768,0.03444008,-0.05607634,-0.0339541,-0.04371798,0.00435835,-0.02767087,0.02202167,-0.01751173,0.01794537,0.04173542,0.03626018,-0.00028036,-0.01430312,-0.02172148,0.00003339,0.04979933,0.05154469,-0.00565316,-0.09463399,0.08118391,-0.01826194,-0.08310377,-0.02793331,-0.04867849,-0.01556095,-0.05864011,-0.11455804,0.04225473,-0.04793931,0.01159062,-0.06107956,-0.08166374,0.03315866,-0.01214879,0.05976911,-0.06815694,0.11426737,0.01963387,-0.01924423,0.00602555,-0.02091041,-0.0323486,0.01841063,-0.01557261,-0.00440964,0.0433487,-0.01497255,0.01373301,0.02163765,-0.00088475,0.01649707,0.04448858,0.02317774,-0.00638238,0.03913569,0.02937231,-0.02078521,-0.01311431,-0.07323361,-0.19106142,-0.02678748,0.02965268,-0.01339277,0.00988372,-0.00878808,0.03092045,0.00470548,0.08410195,0.10871692,0.03364964,0.04075565,-0.09487954,-0.01009169,0.01141372,-0.01317312,0.01250668,-0.01829272,-0.02423712,0.0159331,0.02002993,0.07009713,-0.01046497,0.01292557,0.05374663,-0.0390502,0.13051356,0.06291436,0.00950014,0.03769553,0.01479107,0.01364277,0.05394068,-0.08216068,0.02467168,0.0180625,-0.05128191,-0.08237415,-0.00267625,-0.03208655,0.02896457,0.02567607,-0.05212939,-0.04661488,0.01211912,-0.02721956,-0.02331385,-0.02733344,0.00768913,0.06399086,-0.01970953,-0.00371062,0.00815465,0.01076333,-0.02758746,-0.02284885,-0.06677659,-0.03324071,-0.03058411,0.02283756,0.00410901,-0.0137419,0.05893578,-0.01783123,-0.00830397,0.03733104,0.00929767,-0.0677653,-0.05774733,-0.00829791,-0.07356239,0.14066535,-0.00349034,-0.06232683,0.02515597,0.02680622,-0.00029895,-0.01050055,0.0235428,-0.00718597,0.05757197,-0.02112075,0.06300726,0.01701417,0.02129958,0.01067887,0.02834694,-0.04051512,0.09549975,-0.09425217,-0.02696977,-0.01210137,-0.0423621,0.04888607,0.09034596,-0.04543932,-0.27812064,-0.00130536,0.00222548,0.00600216,0.02816091,0.03563079,0.06274876,-0.04571434,-0.06683591,0.04033704,-0.04824445,0.04876078,-0.00169604,-0.02997117,-0.02162661,-0.04697838,0.05301412,-0.03843927,0.08658466,-0.04044469,0.01728173,0.05393726,0.19329949,0.00715879,0.04434355,-0.00831053,-0.00167089,0.01876256,0.02480949,0.05118316,0.03346274,-0.07710803,0.08219844,-0.00395775,0.06437375,0.06450047,-0.06707612,0.00902199,-0.00275473,0.00043931,-0.01341658,0.03589763,-0.07151989,0.02372959,0.05845626,0.01317279,-0.00987894,-0.04669877,-0.00800074,0.02939416,0.02078735,0.05911659,-0.03012762,-0.01862129,0.01156678,0.04656947,0.00067859,-0.03095997,-0.05038087,0.0052271,-0.00934815,0.03345275,0.07420506,0.16999552,0.04667317],"last_embed":{"hash":"bc520491018caaba7b64599113ccacc6106102016003bd0b30a306551de0b68c","tokens":346}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.03068093,0.03204521,-0.02287473,0.01556337,-0.0248197,0.00883416,0.01278356,-0.00184099,-0.00844574,-0.04194016,-0.00817192,0.04075667,0.00508651,0.0194363,0.00475479,0.03300615,-0.02193103,0.07392928,-0.02064798,-0.03971538,0.0133689,-0.01262124,0.02567771,0.06706588,-0.04892135,-0.05390766,0.00081545,-0.05706588,0.02735366,0.05899282,0.06856823,0.02785541,0.03310365,0.01173468,0.00756613,-0.01497722,0.04141342,0.03977064,0.00782473,-0.04108483,-0.02768109,0.01820913,-0.00266793,0.00169547,-0.05705,0.01352651,0.00205254,-0.0648147,0.03221148,0.04629322,-0.00847596,-0.05215839,0.05081201,0.0438762,-0.0224414,0.0033625,-0.03599636,-0.0531038,0.01715489,0.00172134,0.03074987,0.01356609,0.0591896,0.01364561,-0.01189304,0.06137194,0.04846064,-0.02362237,-0.06729753,0.01838861,0.0241092,0.01868832,0.03734697,-0.040239,0.01085711,-0.03913552,0.01611797,-0.03215925,0.03641236,-0.04547425,-0.00946284,-0.00675098,0.03357907,-0.00993959,0.07345343,-0.02295674,-0.02564443,0.02238027,-0.02983686,-0.01321813,0.01873018,0.02500336,0.05591273,0.02534692,0.03116205,0.03851153,-0.00550934,0.01765569,-0.01788188,-0.01260378,0.02944421,0.02495003,-0.084136,0.0346639,-0.06134064,-0.02317744,-0.05370083,0.02425848,-0.0070318,0.04728853,0.0190595,0.04003214,0.02116493,-0.04597486,-0.09740309,0.01637926,0.01584716,0.03683494,0.04486946,0.12383088,0.05750075,0.02960825,0.04559456,0.03753015,-0.01419459,-0.05432127,-0.00192546,-0.06856868,0.04642417,-0.01931491,0.00185971,-0.02894044,0.012944,-0.04339817,0.02127685,0.00658802,0.07825962,-0.05918203,0.01268158,-0.02764729,0.04873519,0.02018251,0.05086095,-0.05362329,0.00890735,-0.00589223,-0.02977479,-0.06635069,0.01287624,-0.00930092,0.00161693,-0.00414226,-0.01597813,-0.02243673,-0.00535314,0.02501176,0.02738669,-0.00942764,-0.00044535,-0.0105447,0.02306369,0.03137297,0.0057612,0.0331714,0.094848,-0.0006284,0.03583782,0.00003928,0.02077001,-0.04356709,0.05280287,0.01310597,-0.00903101,0.02834605,0.0013596,0.00400713,0.00984144,-0.00113954,-0.01372579,0.00601521,0.02351932,-0.01488833,0.00705355,0.06553206,-0.01452508,0.0325578,-0.01545303,0.0505245,0.00138678,-0.01899037,-0.00731544,0.0302475,0.05967822,0.00698254,0.02983095,0.04472719,0.03266899,-0.02258852,0.03028654,0.02189869,-0.01320297,0.03710072,-0.03956493,-0.04941279,-0.02848542,0.04094327,0.01605877,-0.04004043,-0.00791761,0.02324766,-0.00357954,0.04121909,0.029984,0.04829133,-0.0572903,-0.00438973,-0.0582586,-0.01172658,0.02161584,-0.0087298,0.0168454,-0.02852507,0.04000567,-0.00680696,-0.01069512,-0.02411176,0.02765116,0.02075364,0.03435287,0.07280914,0.0056226,0.00988292,-0.00638715,-0.05232759,-0.05078136,0.00418259,0.01308136,-0.00317845,0.00157001,-0.00965914,-0.00539876,-0.02280274,-0.0076766,0.02764577,-0.06648728,0.00520837,-0.03995317,-0.0173164,-0.01025003,0.05459302,0.07681109,0.02081825,-0.03660149,0.04189989,0.02887858,-0.02933166,-0.06040524,0.03789558,0.01617519,0.00656533,0.00384795,0.01232874,-0.01637904,0.05943144,-0.00288625,-0.02835241,0.01409317,0.00312523,0.04752975,0.01464711,-0.05343999,-0.00659041,-0.00228071,0.03602589,-0.05835579,0.01871555,0.03033799,0.01045509,0.01918948,-0.02586656,0.02256975,-0.01546051,0.02294145,-0.02723004,-0.0658231,0.05174985,-0.02763014,-0.01293599,0.01065505,0.02392583,0.00308955,-0.0153835,-0.07448872,0.03972466,-0.02087013,-0.05626026,0.01727732,0.00321052,0.03985507,-0.04268204,-0.00356107,0.05200681,-0.01313058,-0.01587298,-0.03759877,-0.00403619,0.00239364,0.06407085,-0.01680094,-0.03883282,0.00759108,-0.04297344,0.0030015,0.03717042,-0.03625413,0.10317941,0.02428397,0.01843135,-0.04138641,0.00316282,-0.06233925,-0.0056659,-0.04369667,-0.00808063,0.03405446,-0.07357354,0.06351604,-0.02229247,-0.03092623,0.05126481,-0.04468341,0.01782901,-0.00803902,-0.04233437,0.06634516,0.02525292,0.02209716,-0.00543981,0.04403212,-0.03056412,0.05381046,-0.04727625,0.00535176,-0.01098927,-0.012681,0.00234636,0.01995123,0.02371091,0.00443549,-0.02681118,0.0266895,-0.03115319,-0.05726639,0.00536789,-0.01729448,-0.01890503,-0.04137853,0.01283623,-0.01812386,-0.03557942,-0.03608827,-0.04529057,-0.04536236,-0.00864549,-0.01151268,-0.02324143,0.0251991,0.05552235,0.02353653,-0.02442752,0.04261074,-0.04735937,0.01597995,-0.03201593,-0.01656356,-0.00450755,-0.08554559,-0.00661493,-0.03383369,-0.02101102,-0.03026096,-0.05676444,-0.03206506,-0.00061689,-0.0688781,-0.00643819,-0.00806237,-0.00864969,-0.0012386,-0.05511921,-0.0222973,0.00497229,-0.04542148,-0.00705362,0.01796177,-0.00359237,0.02168626,0.02232275,-0.05302938,-0.0427271,0.03349461,-0.02527309,-0.04324746,-0.06881118,0.03036626,-0.01331311,-0.05856181,0.05550477,-0.00205683,0.03016463,-0.02867672,0.02208509,-0.0355686,0.00322351,-0.00499768,-0.02203279,-0.01545628,-0.08557846,-0.01622698,-0.00872888,0.03358157,0.01358021,-0.01713719,-0.02207627,-0.01844436,-0.04206292,-0.0077239,0.0212613,-0.00751789,-0.03295982,-0.04837527,-0.02533335,-0.0271334,-0.03561872,0.03669083,0.00660613,0.0370698,0.00700326,-0.01070519,-0.02174405,-0.04655448,-0.01189994,0.03370727,-0.01574043,0.06439944,-0.00213358,-0.00997584,0.02249219,-0.00623815,0.04515809,-0.05118211,-0.07987747,-0.06379527,-0.09357243,0.00609094,0.01778373,-0.00610858,-0.04789502,0.01270369,-0.05336208,-0.00000789,0.03626257,0.05176476,0.00920089,-0.02972566,-0.02120765,0.02276805,0.00653371,-0.03442189,-0.00118858,-0.03301001,-0.01101474,0.03030368,-0.04021204,0.02732769,0.07323721,0.01390418,0.02796793,-0.0749546,-0.00187615,0.00102717,-0.04048702,0.02940826,-0.01162628,-0.01156517,0.05576042,0.00363716,0.07047191,0.01578976,-0.03832025,-0.04171398,0.03695134,0.03353364,-0.00559339,0.01430199,-0.0183711,-0.02956595,0.0177686,-0.01669884,0.04734845,-0.00573456,0.03466466,-0.06152004,0.06624743,0.04817356,0.00432335,-0.02530123,0.03493873,-0.00321943,-0.01008473,-0.05171709,-0.06179736,0.05250066,-0.01320604,0.03183918,0.04554587,0.02077921,0.02381473,-0.00766846,-0.02887119,-0.08265223,0.01347576,-0.05866802,0.04472899,-0.01458904,-0.08726908,0.0027646,0.0086236,0.05676316,-0.01998292,0.03437749,-0.03382294,0.02803368,-0.00781698,0.01719759,0.01433574,-0.02355217,0.0338651,-0.00334452,0.04591376,-0.03903427,-0.03645619,-0.05681201,0.0943623,-0.0277191,0.03668933,0.02934801,-0.06742617,-0.00792264,-0.04655973,0.00586184,-0.02630129,0.04554763,-0.01075523,0.05765403,0.02535532,0.03938968,0.03303251,0.00475802,0.07980546,0.00607817,-0.01528177,0.0463515,-0.01025173,-0.04444107,0.02657361,0.00927583,-0.05063363,-0.01521743,0.05867531,-0.00895793,-0.02627414,0.06229362,0.07421747,-0.02238507,-0.0263963,-0.00306898,0.01963132,0.04621307,-0.02072947,0.02439325,-0.06713028,0.05874271,-0.0567874,-0.0234937,-0.01158361,0.04528224,0.01721045,-0.02324057,0.02530834,0.05896503,-0.01412948,-0.02974107,0.0294941,0.01791885,-0.02153199,-0.00254938,0.03877992,0.07652365,-0.03435781,-0.05601332,0.01633359,0.00812034,0.00334445,0.0391425,0.00471339,-0.00776171,-0.07609186,-0.00328591,0.0018468,0.00281689,0.08442852,-0.02611139,-0.0597902,-0.00469579,0.01433677,-0.03966755,-0.01184014,-0.01543252,0.02813974,-0.00562577,0.03325181,0.00717058,-0.05684285,0.00697423,0.0178456,-0.00102365,-0.03345334,0.03109407,-0.07987368,-0.03361378,-0.10107405,0.01356966,-0.0190045,0.05188216,0.01830033,-0.01672591,-0.04207088,-0.00679434,0.02629472,0.05118299,-0.00839594,-0.07923722,0.12799087,0.00772729,0.04497432,-0.02345528,-0.00537282,-0.06042537,0.05600249,-0.05355492,0.01733931,-0.01893712,-0.00657723,-0.02691956,0.01980523,-0.05105709,0.02092501,-0.0167132,0.01024116,-0.00367585,0.00281251,0.01046231,0.01792774,0.00977399,0.00024878,0.0371362,-0.01765577,-0.01801037,-0.00206358,-0.01802561,0.00962023,-0.02752317,-0.00021916,0.0218441,-0.00275078,-0.04610324,0.02631322,-0.01754524,0.03621568,-0.11189214,-0.03303116,0.02801882,0.00917869,0.06622504,0.01021396,0.00223275,-0.00881919,0.02035315,-0.02470502,-0.00396225,0.03232303,-0.00205493,-0.00148888,0.02122635,-0.00266362,0.02155432,0.00710502,0.02312014,0.00134498,0.02010722,0.01361093,0.0000097,-0.01362228,-0.06301869,0.01706057,0.01406039,0.02587978,0.09472937,-0.00227242,-0.03968128,0.0077664,0.00273285,-0.04757487,0.00231159,-0.04558164,0.01277252,0.03966661,-0.0187128,-0.00565211,-0.03770866,0.01842101,0.04561719,0.0449254,-0.04318405,-0.01228186,-0.01464662,0.07680321,0.0321296,-0.0270798,0.01475439,-0.06973997,0.02137663,0.00054828,0.05863567,-0.00200551,-0.01774666,-0.03941547,0.00638623,0.03424948,-0.02917956,-0.02536743,-0.02990317,-0.04790402,-0.04902728,-0.08788065,-0.03461625,-0.01195524,-0.08119306,0.03962718,-0.01513221,-0.0112737,0.01815717,-0.02719958,0.04314721,-0.00033174,-0.0199169,-0.06392907,8.4e-7,0.05184718,-0.01758483,0.01234958,-0.06328037,-0.00693076,-0.02505992,0.01585745,-0.07471492,-0.00644094],"last_embed":{"tokens":1025,"hash":"b0g3gy"}}},"last_read":{"hash":"b0g3gy","at":1750993395185},"class_name":"SmartSource","outlinks":[{"title":"- /.","target":"0?[1-9]|[12][0-9]|3[01]","line":48},{"title":"- /.","target":"19|20","line":48},{"title":"- /.","target":"0?[1-9]|[12][0-9]|3[01]","line":49},{"title":"- /.","target":"0?[1-9]|1[012]","line":49},{"title":"a-zA-Z0-9","target":"a-zA-Z0-9","line":72}],"metadata":{"aliases":["Regular Expressions","regex"],"tags":null,"cssclasses":["editor-full"]},"blocks":{"#---frontmatter---":[1,8],"#简介":[9,15],"#简介#{1}":[10,15],"#元字符":[16,36],"#元字符#{1}":[18,36],"#常用的正则表达式":[37,90],"#常用的正则表达式#{1}":[39,39],"#常用的正则表达式#{2}":[40,40],"#常用的正则表达式#{3}":[41,41],"#常用的正则表达式#{4}":[42,42],"#常用的正则表达式#{5}":[43,43],"#常用的正则表达式#{6}":[44,44],"#常用的正则表达式#{7}":[45,45],"#常用的正则表达式#{8}":[46,46],"#常用的正则表达式#{9}":[47,47],"#常用的正则表达式#{10}":[48,48],"#常用的正则表达式#{11}":[49,49],"#常用的正则表达式#{12}":[50,51],"#常用的正则表达式#电话号码匹配":[52,57],"#常用的正则表达式#电话号码匹配#{1}":[53,57],"#常用的正则表达式#大小写字母匹配":[58,64],"#常用的正则表达式#大小写字母匹配#{1}":[59,64],"#常用的正则表达式#电子邮件匹配":[65,69],"#常用的正则表达式#电子邮件匹配#{1}":[66,69],"#常用的正则表达式#网站匹配":[70,75],"#常用的正则表达式#网站匹配#{1}":[71,75],"#常用的正则表达式#电话号码匹配#{2}":[76,82],"#常用的正则表达式#电话号码匹配#{2}#{1}":[77,82],"#常用的正则表达式#IPv4地址匹配":[83,90],"#常用的正则表达式#IPv4地址匹配#{1}":[85,90]},"last_import":{"mtime":1733037749031,"size":2907,"at":1748488128912,"hash":"b0g3gy"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#元字符": {"path":null,"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.02274186,0.04642082,-0.02034398,0.03901208,-0.00986257,0.00519856,0.0060637,0.00779079,-0.01959576,-0.04011595,-0.03365844,0.02796871,-0.0009749,0.01512172,-0.00787189,0.02500157,-0.01625005,0.06352493,-0.01596746,-0.02598636,0.01495784,-0.01973161,0.02562016,0.03249278,-0.04050282,-0.07487784,-0.00413544,-0.07056929,0.02989177,0.0600543,0.06867123,0.00789388,0.02265115,0.01983003,0.00829485,-0.00797092,0.02139628,0.04871657,0.03446067,-0.02931958,-0.007448,-0.01826295,0.02117291,0.00329159,-0.07960378,0.01290563,0.01472275,-0.07076084,0.04509869,0.04644058,0.00087602,-0.06387152,0.05176745,0.04103918,-0.03252897,0.00583145,-0.04364892,-0.0095937,0.02189542,0.01888263,0.03330449,0.02183827,0.0630043,0.00859169,0.00674628,0.05746872,0.03700778,-0.01387322,-0.09158269,0.01141759,0.02523601,0.00721313,0.04968591,-0.03798802,0.00265143,-0.0496494,0.0180922,-0.03138867,0.03943549,-0.07405598,0.0064133,-0.02766749,0.05233306,-0.00845925,0.07467528,-0.03250877,-0.04703583,0.02744597,-0.0221305,-0.0073498,0.01100052,0.0355537,0.03930448,0.05005213,0.02988527,0.04587519,-0.02247119,0.02524671,-0.02209747,0.0262024,0.00967883,0.02278449,-0.08294218,0.03663546,-0.07636828,-0.0015167,-0.04345941,0.04460623,0.00075936,0.05028607,0.01376747,0.03993288,0.02443851,-0.04178342,-0.07770539,0.01254242,0.01307006,0.07238647,0.02174553,0.11191391,0.04299125,-0.00554856,0.02543668,0.07335401,0.00006549,-0.04648433,-0.00888951,-0.06010104,0.04266013,-0.01670964,-0.01817627,-0.01109157,0.00601047,-0.0573703,0.02895693,0.00328025,0.08259454,-0.04848043,0.0074592,-0.04630095,0.04545295,0.02296751,0.03968043,-0.06611213,-0.02691354,-0.01601071,-0.00157679,-0.04409508,0.03782395,-0.01343637,0.00273647,0.00762359,0.01126647,-0.01451346,0.00528542,0.02226718,0.00924491,-0.01295557,-0.01343429,0.00137777,0.02818462,0.02582285,0.02750143,0.02582414,0.09565684,-0.00066631,0.05956399,0.00654035,0.02158327,-0.04219542,0.04327945,0.00273897,-0.00614557,0.02147179,-0.01183438,0.00229109,0.00268202,-0.00478085,-0.01374075,0.01821288,0.02666632,0.01139752,0.01689627,0.05309859,-0.0210383,0.02390987,-0.02800827,0.04303345,-0.04036065,-0.0076081,-0.01271365,0.04691969,0.06935723,0.01893417,0.01833537,0.03416462,0.01984928,-0.00897407,0.02966491,0.03157221,-0.00555711,0.0431592,-0.04012693,-0.03450443,-0.02555093,0.00662178,0.03516807,-0.02919047,-0.01650286,0.01900288,-0.0034981,0.0570457,0.04976914,0.04567485,-0.02683282,0.04211951,-0.05362422,-0.01506832,0.04500765,-0.00651906,0.01960927,-0.04032983,0.01961108,-0.01738305,-0.02599717,-0.02605016,0.0071455,0.01371595,0.03227791,0.07253831,-0.00404873,0.01405755,0.00917944,-0.03379272,-0.05227208,0.01391024,0.0009392,-0.02779004,-0.01014368,0.01210248,0.00583834,-0.01919148,0.0032606,0.0221507,-0.02898036,0.02118274,-0.04025922,-0.02863706,-0.03753532,0.03515923,0.05433512,0.02465019,-0.0238521,0.01211695,0.02750637,-0.01492701,-0.05394985,0.00575541,-0.0064419,0.02954756,0.00914233,0.01509597,0.00075599,0.05964383,0.02393704,-0.03225807,0.02551029,0.01688919,0.07962293,0.03112577,-0.04736113,0.00087616,0.0092167,0.03055524,-0.07198929,0.0175042,0.02183161,0.01518162,0.02442525,-0.03676494,0.00254806,0.00479288,0.0076128,-0.0172553,-0.08676089,0.04559128,-0.01318899,-0.02215373,-0.02185784,0.00819779,0.01448546,-0.02274766,-0.05961862,0.02446871,-0.00963953,-0.04438327,-0.00065675,0.01008745,0.04456722,-0.04424253,0.00736611,0.05125877,-0.01189195,-0.03695339,-0.03644913,-0.03600653,0.00148479,0.07673986,-0.05236775,-0.03275359,0.00584779,-0.05285264,0.00843481,0.02997913,-0.03959388,0.07585379,0.01093722,0.01244901,-0.03541623,0.01061883,-0.04037447,-0.0242068,-0.0306159,-0.00121588,0.04013852,-0.0479748,0.08555193,-0.03721949,-0.04743751,0.06091805,-0.03420116,0.02838828,0.00265201,-0.03565839,0.08248519,0.00171716,0.00148221,0.01755701,0.02559856,-0.04785542,0.03991517,-0.04517147,0.000897,-0.01830991,-0.0025314,0.02768117,0.00650033,0.01976223,0.01448563,-0.00246273,0.01109396,-0.0432479,-0.01786182,-0.00626569,-0.03112133,-0.02331606,-0.03982041,0.01454222,0.0017614,-0.04594877,-0.01764195,-0.06036956,-0.04512721,-0.00306954,0.0149687,-0.03441073,0.00738683,0.03301869,0.04133337,-0.00349407,0.04468967,-0.04866081,-0.01100479,-0.01359337,-0.01886026,0.00174219,-0.09587827,-0.00710607,-0.05059558,-0.0151913,-0.019982,-0.02281342,-0.05665512,0.012612,-0.05347337,0.00442367,-0.0040491,-0.01992002,-0.01303471,-0.07651508,-0.03035099,0.0138709,-0.04723999,-0.00048551,-0.00328959,-0.03062853,0.01456763,0.00451131,-0.04234764,-0.03401545,0.03283614,-0.01701611,-0.0637866,-0.06522891,0.01353232,0.00323113,-0.04096546,0.05342082,-0.00933636,0.03978791,-0.02281231,0.03783057,0.00211338,-0.01244317,-0.00974683,-0.02991584,-0.00990614,-0.07309234,-0.04635185,-0.02223081,0.01139744,-0.0161583,0.00354257,-0.00753857,-0.01864387,-0.03459108,-0.01582616,0.03871639,-0.01696927,-0.0408931,-0.06506234,0.00551916,-0.01919044,-0.02834961,0.02960089,0.02057275,0.03873157,-0.01550681,-0.01510625,-0.00197559,-0.04398221,-0.02897456,0.02583906,-0.02772896,0.06330286,0.00924597,0.02666531,0.02456234,-0.02969093,0.0125608,-0.0537797,-0.05237702,-0.03869824,-0.06533955,0.00765287,0.00675974,-0.02003168,-0.04913567,0.02483463,-0.04311597,-0.01948497,0.05851672,0.06409424,0.02968567,-0.02118123,-0.02727491,0.01261493,-0.01433171,-0.03709698,0.01215285,-0.05709812,-0.00049496,0.02284661,-0.04503429,0.02254068,0.06632318,0.01105433,0.03448426,-0.05972504,-0.01678239,0.02188103,-0.05802185,0.04585494,-0.0076842,-0.00762031,0.04289988,-0.00639231,0.05415176,0.04031242,-0.04007358,-0.0292569,0.03335003,0.01705003,0.00730958,0.02805434,0.00232898,-0.03918382,-0.00806785,0.00181512,0.01748256,-0.00636823,0.02064011,-0.05057244,0.06384011,0.05108494,0.00131403,-0.02001169,0.03933649,0.00777303,0.00770815,-0.06056688,-0.06376603,0.07078132,-0.01320163,0.00057428,0.03567133,0.00060798,0.02205144,-0.00758272,-0.02887197,-0.10185479,0.0278963,-0.05699181,0.04602449,-0.02521026,-0.07542937,0.00678228,0.00070314,0.04194742,-0.00458945,0.02293237,-0.03319397,0.01102241,-0.01210304,0.00668606,0.01403582,-0.03826756,0.02645748,-0.02755008,0.02602284,-0.04071525,-0.03606534,-0.07276532,0.09365077,-0.02632909,0.03155951,0.00742386,-0.07110613,-0.02388964,-0.05632259,0.00196331,-0.01372875,0.05770258,-0.00633868,0.02430547,0.02697194,0.04983138,0.03285384,-0.00803888,0.05192653,-0.02495856,-0.00443358,0.0340107,-0.02063049,-0.03156806,0.04103326,0.01080409,-0.04045416,-0.02751563,0.06893001,0.02864723,-0.04057137,0.05798256,0.08137874,0.0014988,-0.02940935,0.00195233,0.02490409,0.03657201,-0.01928696,0.01431606,-0.0715327,0.05109224,-0.03415548,-0.02834366,-0.0073294,0.04854639,0.00518213,-0.01277297,0.0064365,0.04722525,-0.03713961,-0.02311855,0.05101546,0.00670785,-0.0179757,-0.01713243,0.04852657,0.05624151,-0.0313301,-0.06894078,0.01741111,0.00535041,-0.00733892,0.05674405,0.01295818,-0.01404559,-0.07293496,0.00120314,-0.00583268,-0.00302669,0.05766195,-0.02408176,-0.02458592,-0.00594497,0.02035168,0.00179846,-0.0068193,-0.01382624,0.01283951,0.00583063,0.03110914,0.02451395,-0.06413436,-0.0080001,0.02224667,-0.00496682,-0.0524742,0.01604256,-0.04511527,-0.03409486,-0.09182722,0.02577018,-0.03105303,0.05993937,0.03700161,-0.01832736,-0.02016871,-0.00272145,0.02233774,0.05397771,0.00390942,-0.08260043,0.14694118,-0.00310295,0.06280238,0.00130354,0.00844805,-0.06231365,0.1008487,-0.06622717,-0.01030205,-0.01407657,0.00170948,-0.01536841,0.0287464,-0.04031095,0.01086876,-0.0255435,0.02328169,-0.0077231,0.00313989,0.02082123,0.01543105,-0.00543623,-0.01542761,0.04802964,-0.00409788,-0.01941202,0.00255648,-0.0174094,0.01375457,-0.04509694,-0.00900177,0.0042895,-0.00182772,-0.03863461,0.03572249,-0.01118234,0.02589044,-0.10166837,-0.04080572,0.01518426,0.01610573,0.07057648,0.01381662,0.00817409,-0.00372185,0.00241163,-0.00355564,-0.01119251,0.05324847,-0.01068497,-0.02022576,0.02288311,0.00001874,0.04198337,0.01991085,0.03235194,-0.00423376,0.02726439,-0.00842028,0.01769389,-0.00061543,-0.06331021,0.04039625,-0.00945433,0.01176542,0.09210838,0.00701744,-0.03909796,0.01427571,-0.01689293,-0.05811952,-0.01018997,-0.01716637,0.00630058,0.03268994,-0.00753637,0.01806911,-0.0455457,0.00444331,0.03954014,0.05265406,-0.01972069,-0.01438606,0.01042417,0.07977957,0.01075666,-0.02371642,0.02765886,-0.06770799,0.02728437,-0.00831722,0.06439085,-0.04680015,-0.0080287,-0.03796132,-0.00017578,0.01542293,-0.02494288,-0.03426155,-0.01022836,-0.0302574,-0.06613842,-0.05502449,-0.0691075,-0.00600413,-0.04603638,0.0365806,-0.017895,0.00856865,0.00396006,-0.02134811,0.02664437,0.02431156,-0.02123791,-0.06053127,7e-7,0.06238764,-0.02327381,0.03824983,-0.06646882,-0.01446667,-0.0170853,0.02090338,-0.07023466,0.00300288],"last_embed":{"hash":"zxwuc","tokens":269}}},"text":null,"length":0,"last_read":{"hash":"zxwuc","at":1749002749335},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#元字符","lines":[16,36],"size":1036,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#元字符#{1}": {"path":null,"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.02850777,0.0407977,-0.01612691,0.04519352,-0.01001975,0.01259863,0.00881913,0.01331683,-0.023985,-0.04486204,-0.02681664,0.03215056,0.00251185,0.0109801,-0.00484313,0.0286639,-0.01533629,0.06778076,-0.01937036,-0.02715577,0.0140645,-0.02303572,0.02533731,0.03663571,-0.03553643,-0.07189474,-0.00544048,-0.07337555,0.02894061,0.05711114,0.06715737,0.01205471,0.02017587,0.02494597,0.01064516,-0.0080634,0.01225371,0.04006092,0.03557641,-0.03049466,-0.01096009,-0.02343258,0.02399656,-0.00306602,-0.07160293,0.00828741,0.01772759,-0.06443792,0.04417734,0.05263399,0.00633458,-0.0660026,0.05137693,0.04926381,-0.02992192,-0.00050035,-0.04410443,-0.00968378,0.01807716,0.01071283,0.03215812,0.01698261,0.05982267,0.00575553,0.00501093,0.0613382,0.03771076,-0.01832065,-0.08899267,0.01048138,0.02222169,0.00916518,0.0540356,-0.03658671,0.00730472,-0.05123582,0.02670055,-0.02901942,0.04128351,-0.07460898,0.0031727,-0.02600012,0.05213051,-0.01399163,0.07353237,-0.03460608,-0.04075156,0.0197986,-0.01960423,-0.00296604,0.00866421,0.0293688,0.04551252,0.04763129,0.02818357,0.05611478,-0.02550901,0.02780331,-0.01972528,0.02526751,0.01049077,0.02182558,-0.08276123,0.03826816,-0.07157004,-0.00379702,-0.04510098,0.05670566,0.00439309,0.0524396,0.00827897,0.04662181,0.02484887,-0.0428025,-0.07732866,0.01504061,0.00813264,0.06329639,0.0210071,0.11575005,0.04497738,0.00049067,0.0234092,0.06508677,0.00617381,-0.04874995,-0.00473069,-0.05995164,0.04337009,-0.01437524,-0.01901052,-0.00779372,-0.00047885,-0.05218711,0.02771331,0.00468844,0.07362819,-0.05283176,0.00510134,-0.048162,0.05017244,0.02018401,0.03967975,-0.06819564,-0.01672051,-0.01841627,-0.00196013,-0.05242684,0.03543491,-0.01370301,-0.00100286,-0.00271379,0.00614548,-0.01201982,0.01079791,0.02735808,0.01075054,-0.00769397,-0.00248601,-0.00490954,0.02554158,0.02655728,0.02936458,0.02340111,0.09012411,-0.00309546,0.05458998,0.00924681,0.0257345,-0.04667755,0.03842031,0.00598242,-0.00660769,0.024963,-0.00818058,0.00509576,-0.00364735,0.00160026,-0.01246468,0.00868997,0.02803781,0.01242593,0.01697551,0.05210655,-0.02089493,0.0220771,-0.02554122,0.05011366,-0.04462133,-0.01527097,-0.0098258,0.0407883,0.06851142,0.01870557,0.01811559,0.02886477,0.02353323,-0.0059961,0.0267906,0.02898861,-0.00254268,0.04484076,-0.03788557,-0.03201196,-0.0233567,0.0068528,0.03312144,-0.0289861,-0.01338139,0.02075843,-0.00320201,0.04868175,0.0418658,0.03987042,-0.02661193,0.0394393,-0.04724197,-0.01546334,0.03962431,-0.00847897,0.02605132,-0.03915492,0.0259423,-0.01696505,-0.02524363,-0.0225866,0.00233675,0.01331259,0.02711196,0.0733002,-0.0025082,0.02083344,0.01119094,-0.037862,-0.05420817,0.01909591,0.0097832,-0.02463099,-0.01011513,0.01739512,0.00473271,-0.0203415,-0.00201797,0.02880489,-0.02645148,0.021319,-0.04286143,-0.03207772,-0.03855444,0.04001295,0.05425635,0.02035604,-0.01871124,0.0154797,0.032883,-0.01372938,-0.0579238,0.0031287,0.00145951,0.02771111,0.00998419,0.0216128,-0.00086549,0.05752154,0.0206531,-0.02801134,0.03094641,0.01912451,0.07546877,0.03146383,-0.04638904,0.0002156,0.00745658,0.02978553,-0.06577566,0.01567092,0.0233954,0.0176367,0.02704599,-0.02611795,0.00456964,-0.00080379,0.00527437,-0.01961995,-0.08374652,0.04348071,-0.01518587,-0.01485043,-0.0198765,0.01214267,0.02144479,-0.02717467,-0.06733177,0.02351353,-0.01216396,-0.04307574,-0.00075451,0.00000435,0.04651324,-0.0562708,0.00247356,0.0565994,-0.01344499,-0.03394445,-0.04450892,-0.03889252,0.0017489,0.07650024,-0.05303765,-0.03138126,0.00372586,-0.05576825,0.00679559,0.03351212,-0.04429785,0.07773563,0.00935973,0.00780982,-0.03918182,0.01050241,-0.04258464,-0.02703135,-0.03079732,-0.00740875,0.04302253,-0.04100038,0.07811113,-0.03922315,-0.03910894,0.06070318,-0.03875354,0.0231824,0.00593301,-0.03732163,0.08449034,0.00682273,0.00154939,0.01134536,0.03133455,-0.04691499,0.03617159,-0.04831071,-0.00255998,-0.02575741,-0.00279437,0.02122628,0.00165281,0.00895568,0.01021713,-0.00009181,0.00528867,-0.04317877,-0.01170808,-0.01045512,-0.03371647,-0.01569356,-0.0449272,0.01537353,0.00293599,-0.04890718,-0.0158633,-0.06486595,-0.04438049,-0.00409712,0.01084062,-0.03357138,0.00872317,0.03498123,0.03488353,0.00021815,0.04634616,-0.05333042,-0.00755987,-0.01393092,-0.01797773,0.00086466,-0.09540321,-0.00774715,-0.05319759,-0.02144211,-0.01569119,-0.02318861,-0.06382492,0.01309759,-0.04971526,0.00787935,-0.00421334,-0.02738415,-0.00588987,-0.08411708,-0.02766488,0.01002468,-0.04580989,-0.0008934,-0.00118912,-0.03133043,0.0081679,0.00274316,-0.03840166,-0.03758763,0.03513837,-0.01466204,-0.06222023,-0.06774959,0.01428696,0.00147753,-0.04063271,0.04923883,-0.01508546,0.0400793,-0.02879219,0.04453836,-0.00277645,-0.01693289,-0.00435729,-0.02851841,-0.00824808,-0.07473465,-0.04197196,-0.02284402,0.00890242,-0.01187526,0.00916381,-0.01044443,-0.01767995,-0.03044911,-0.01172085,0.03622568,-0.01243246,-0.04820898,-0.06379406,0.00191964,-0.01678037,-0.02961119,0.03281948,0.02149359,0.03758583,-0.01854188,-0.0141261,0.00183512,-0.03749701,-0.0302356,0.03247583,-0.0309605,0.06561574,0.01618051,0.02312152,0.02114752,-0.02984552,0.01248201,-0.06257658,-0.05336551,-0.03811316,-0.07257007,0.00807431,0.01314064,-0.02328438,-0.04755448,0.01672745,-0.04113394,-0.022308,0.06187968,0.05559599,0.02786275,-0.02483738,-0.02420206,0.01181833,-0.01204085,-0.04005383,0.01406876,-0.04491869,-0.00178925,0.03404576,-0.04603483,0.02301339,0.06989533,0.01099675,0.03288398,-0.05992001,-0.01900575,0.02565782,-0.05909076,0.04332329,-0.0115407,-0.00713815,0.04931647,-0.00360581,0.05577954,0.04238511,-0.04492771,-0.0277415,0.03341911,0.02231235,0.00916665,0.02613846,0.00189755,-0.03026262,-0.00824875,-0.00081553,0.01100792,-0.00486125,0.01764249,-0.04860495,0.06436995,0.05580535,0.00228953,-0.02248066,0.03839619,0.01201163,0.00522784,-0.0576819,-0.06366983,0.07582597,-0.01265564,0.00597546,0.03616426,-0.00612723,0.02407161,-0.00856169,-0.02657706,-0.09906589,0.02713416,-0.06348411,0.04532532,-0.02369155,-0.07916829,0.00150984,-0.000147,0.04574987,-0.00802721,0.02048768,-0.03484364,0.01346435,-0.0147762,0.0026792,0.0162225,-0.03536138,0.02622548,-0.01714458,0.02648644,-0.04418384,-0.03540016,-0.06298448,0.09120698,-0.02487779,0.03876194,0.00702335,-0.07698258,-0.02536903,-0.05739501,0.0021787,-0.01700433,0.05100695,-0.0082759,0.02785889,0.02452217,0.05102215,0.02525355,-0.00623154,0.05246437,-0.0191727,-0.00287778,0.03291547,-0.01936531,-0.0197789,0.0433657,0.00886118,-0.04381368,-0.03008348,0.069368,0.02547266,-0.03811731,0.06136507,0.0785395,0.00163738,-0.03261705,-0.00041545,0.02535831,0.0285196,-0.02161686,0.01943587,-0.07277928,0.04751395,-0.03437654,-0.02854729,-0.00338831,0.04634804,0.00842877,-0.01420765,0.00418441,0.04325665,-0.03277231,-0.02401708,0.05113021,0.00261756,-0.02446517,-0.01706614,0.05008035,0.05668275,-0.03111288,-0.07578108,0.01071047,0.00706561,-0.00801594,0.05291868,0.00691933,-0.01142899,-0.07244361,0.00076887,-0.01273339,-0.00426802,0.05505912,-0.02379434,-0.02444702,-0.00057519,0.01983882,0.0051841,-0.00926324,-0.01414733,0.01569469,0.00463414,0.02954557,0.02123082,-0.06291907,-0.00277379,0.02600227,-0.00665206,-0.05180914,0.02113935,-0.04453691,-0.03337179,-0.09922954,0.02203399,-0.02712036,0.0616226,0.03991,-0.02080048,-0.01565119,-0.00451572,0.02531732,0.051661,-0.00308443,-0.07677019,0.14864467,-0.00589361,0.06814884,-0.00349755,0.0096051,-0.06278886,0.10493681,-0.06304868,-0.00801289,-0.00805766,-0.00912388,-0.01118917,0.02860579,-0.04640394,0.01082941,-0.02012876,0.02311837,-0.01354775,-0.00108478,0.03027839,0.01543511,-0.00261315,-0.02019184,0.04236329,-0.00241949,-0.02160242,0.010363,-0.02365498,0.01391502,-0.04260122,-0.00200706,0.00738797,0.00274941,-0.03209504,0.03587224,-0.00600242,0.02161372,-0.10107613,-0.03569812,0.01401946,0.0182499,0.06584875,0.0101097,0.00687122,-0.00726256,-0.00544967,-0.01140607,-0.00734052,0.0447205,-0.01314314,-0.01760087,0.01918411,0.0016383,0.04026538,0.02055374,0.03188686,-0.00020835,0.02949807,-0.00899473,0.01473729,0.0032284,-0.0695387,0.04502911,-0.0084769,0.00665655,0.09662016,0.00174565,-0.03517771,0.01739824,-0.01932214,-0.06189782,-0.00535875,-0.02419267,0.00307633,0.03263273,-0.00623264,0.02125257,-0.04786874,0.00613219,0.03515352,0.04803383,-0.01464513,-0.01572017,0.01246033,0.0804688,0.00744618,-0.02569889,0.02830854,-0.06449537,0.02788745,-0.00876324,0.06302543,-0.05004627,-0.00644977,-0.04181201,-0.00306958,0.01996298,-0.02430522,-0.03202575,-0.01393089,-0.0233809,-0.07048822,-0.05363582,-0.0666092,-0.00857352,-0.04192804,0.04697499,-0.01449522,0.0008488,0.01085532,-0.01474325,0.0236064,0.02691864,-0.02305384,-0.05093704,6.7e-7,0.05554987,-0.02398697,0.04198827,-0.06519635,-0.0135279,-0.01756584,0.01945339,-0.06469321,0.00652916],"last_embed":{"hash":"erzr8z","tokens":269}}},"text":null,"length":0,"last_read":{"hash":"erzr8z","at":1749002749378},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#元字符#{1}","lines":[18,36],"size":1029,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#---frontmatter---","lines":[1,8],"size":85,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#简介": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#简介","lines":[9,15],"size":100,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#简介#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#简介#{1}","lines":[10,15],"size":95,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式","lines":[37,90],"size":913,"outlinks":[{"title":"- /.","target":"0?[1-9]|[12][0-9]|3[01]","line":12},{"title":"- /.","target":"19|20","line":12},{"title":"- /.","target":"0?[1-9]|[12][0-9]|3[01]","line":13},{"title":"- /.","target":"0?[1-9]|1[012]","line":13},{"title":"a-zA-Z0-9","target":"a-zA-Z0-9","line":36}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#{1}","lines":[39,39],"size":1,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#{2}","lines":[40,40],"size":11,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#{3}","lines":[41,41],"size":12,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#{4}","lines":[42,42],"size":12,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#{5}","lines":[43,43],"size":22,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#{6}","lines":[44,44],"size":23,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#{7}","lines":[45,45],"size":28,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#{8}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#{8}","lines":[46,46],"size":62,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#{9}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#{9}","lines":[47,47],"size":41,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#{10}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#{10}","lines":[48,48],"size":88,"outlinks":[{"title":"- /.","target":"0?[1-9]|[12][0-9]|3[01]","line":1},{"title":"- /.","target":"19|20","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#{11}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#{11}","lines":[49,49],"size":88,"outlinks":[{"title":"- /.","target":"0?[1-9]|[12][0-9]|3[01]","line":1},{"title":"- /.","target":"0?[1-9]|1[012]","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#{12}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#{12}","lines":[50,51],"size":32,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#电话号码匹配": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#电话号码匹配","lines":[52,57],"size":38,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#电话号码匹配#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#电话号码匹配#{1}","lines":[53,57],"size":28,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#大小写字母匹配": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#大小写字母匹配","lines":[58,64],"size":58,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#大小写字母匹配#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#大小写字母匹配#{1}","lines":[59,64],"size":47,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#电子邮件匹配": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#电子邮件匹配","lines":[65,69],"size":74,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#电子邮件匹配#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#电子邮件匹配#{1}","lines":[66,69],"size":64,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#网站匹配": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#网站匹配","lines":[70,75],"size":121,"outlinks":[{"title":"a-zA-Z0-9","target":"a-zA-Z0-9","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#网站匹配#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#网站匹配#{1}","lines":[71,75],"size":113,"outlinks":[{"title":"a-zA-Z0-9","target":"a-zA-Z0-9","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#电话号码匹配#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#电话号码匹配#{2}","lines":[76,82],"size":49,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#电话号码匹配#{2}#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#电话号码匹配#{2}#{1}","lines":[77,82],"size":39,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#IPv4地址匹配": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#IPv4地址匹配","lines":[83,90],"size":124,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#IPv4地址匹配#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md#常用的正则表达式#IPv4地址匹配#{1}","lines":[85,90],"size":111,"outlinks":[],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md","last_embed":{"hash":"b0g3gy","at":1751079981347},"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08388279,-0.01542185,-0.01710912,-0.04223506,0.0163874,-0.00327926,-0.00564028,0.03111003,0.05510193,-0.01834314,0.00102901,-0.04262774,0.06737566,0.02992057,0.07070322,0.02138679,-0.01327496,0.07448065,0.01361034,-0.01247035,0.09367356,-0.00865906,-0.01056392,-0.0592685,-0.01335617,0.04342286,0.02760069,-0.02184499,-0.00544074,-0.18749052,-0.00947071,0.02028155,0.04804475,0.00787257,0.00235113,-0.0441929,0.01834353,0.07104044,-0.01596594,0.04903884,-0.00774325,0.01741534,0.01241374,-0.01735547,-0.0286368,-0.05408061,0.00941961,-0.01541584,-0.00703702,-0.03480574,-0.02011818,-0.07091691,-0.04294333,0.01881068,-0.06930163,0.00718763,0.04024045,-0.00726264,0.04414426,-0.0221084,0.00062583,0.0155294,-0.20991573,0.06236512,-0.00496096,-0.02340926,-0.00555747,-0.01415703,0.0009644,0.08124276,-0.04051842,-0.00931416,-0.0423455,0.07586405,0.02763409,0.0329074,-0.01026351,-0.05475784,-0.01753335,-0.022363,-0.03940357,-0.0090535,-0.01418902,0.01806147,0.02028704,0.03315207,-0.00063279,-0.0384476,0.03633592,0.02610554,0.02725687,-0.06241966,0.01663627,0.03227326,-0.00124478,-0.00876875,0.02398342,0.04824919,-0.04128463,0.13103795,-0.05845413,0.013382,-0.01262826,-0.05045319,-0.01241266,-0.01062796,-0.00645233,0.00500765,0.0162566,-0.04886974,-0.07691558,0.00641106,0.06831165,-0.01784994,0.00462701,0.02091219,0.02152733,-0.04157566,0.00041583,-0.01628811,-0.02072391,-0.01376009,0.03833891,-0.01335463,0.00048647,-0.04262716,0.01294993,0.07081003,0.01526292,0.06821372,0.12282597,-0.02282102,-0.05958614,-0.02377871,-0.00998643,-0.01855469,-0.04262847,0.0319044,-0.02606937,-0.03151945,-0.01269148,-0.0753153,-0.05246828,-0.04383191,-0.09683875,0.10451008,-0.07493192,-0.02799682,0.04685627,-0.05406401,0.0103086,0.03573711,-0.02236313,0.02145514,0.00479218,-0.00917906,0.09057575,0.12325168,-0.03843355,-0.01969188,-0.00785842,0.0493965,-0.08738455,0.18037768,0.03444008,-0.05607634,-0.0339541,-0.04371798,0.00435835,-0.02767087,0.02202167,-0.01751173,0.01794537,0.04173542,0.03626018,-0.00028036,-0.01430312,-0.02172148,0.00003339,0.04979933,0.05154469,-0.00565316,-0.09463399,0.08118391,-0.01826194,-0.08310377,-0.02793331,-0.04867849,-0.01556095,-0.05864011,-0.11455804,0.04225473,-0.04793931,0.01159062,-0.06107956,-0.08166374,0.03315866,-0.01214879,0.05976911,-0.06815694,0.11426737,0.01963387,-0.01924423,0.00602555,-0.02091041,-0.0323486,0.01841063,-0.01557261,-0.00440964,0.0433487,-0.01497255,0.01373301,0.02163765,-0.00088475,0.01649707,0.04448858,0.02317774,-0.00638238,0.03913569,0.02937231,-0.02078521,-0.01311431,-0.07323361,-0.19106142,-0.02678748,0.02965268,-0.01339277,0.00988372,-0.00878808,0.03092045,0.00470548,0.08410195,0.10871692,0.03364964,0.04075565,-0.09487954,-0.01009169,0.01141372,-0.01317312,0.01250668,-0.01829272,-0.02423712,0.0159331,0.02002993,0.07009713,-0.01046497,0.01292557,0.05374663,-0.0390502,0.13051356,0.06291436,0.00950014,0.03769553,0.01479107,0.01364277,0.05394068,-0.08216068,0.02467168,0.0180625,-0.05128191,-0.08237415,-0.00267625,-0.03208655,0.02896457,0.02567607,-0.05212939,-0.04661488,0.01211912,-0.02721956,-0.02331385,-0.02733344,0.00768913,0.06399086,-0.01970953,-0.00371062,0.00815465,0.01076333,-0.02758746,-0.02284885,-0.06677659,-0.03324071,-0.03058411,0.02283756,0.00410901,-0.0137419,0.05893578,-0.01783123,-0.00830397,0.03733104,0.00929767,-0.0677653,-0.05774733,-0.00829791,-0.07356239,0.14066535,-0.00349034,-0.06232683,0.02515597,0.02680622,-0.00029895,-0.01050055,0.0235428,-0.00718597,0.05757197,-0.02112075,0.06300726,0.01701417,0.02129958,0.01067887,0.02834694,-0.04051512,0.09549975,-0.09425217,-0.02696977,-0.01210137,-0.0423621,0.04888607,0.09034596,-0.04543932,-0.27812064,-0.00130536,0.00222548,0.00600216,0.02816091,0.03563079,0.06274876,-0.04571434,-0.06683591,0.04033704,-0.04824445,0.04876078,-0.00169604,-0.02997117,-0.02162661,-0.04697838,0.05301412,-0.03843927,0.08658466,-0.04044469,0.01728173,0.05393726,0.19329949,0.00715879,0.04434355,-0.00831053,-0.00167089,0.01876256,0.02480949,0.05118316,0.03346274,-0.07710803,0.08219844,-0.00395775,0.06437375,0.06450047,-0.06707612,0.00902199,-0.00275473,0.00043931,-0.01341658,0.03589763,-0.07151989,0.02372959,0.05845626,0.01317279,-0.00987894,-0.04669877,-0.00800074,0.02939416,0.02078735,0.05911659,-0.03012762,-0.01862129,0.01156678,0.04656947,0.00067859,-0.03095997,-0.05038087,0.0052271,-0.00934815,0.03345275,0.07420506,0.16999552,0.04667317],"last_embed":{"hash":"bc520491018caaba7b64599113ccacc6106102016003bd0b30a306551de0b68c","tokens":346}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.03068093,0.03204521,-0.02287473,0.01556337,-0.0248197,0.00883416,0.01278356,-0.00184099,-0.00844574,-0.04194016,-0.00817192,0.04075667,0.00508651,0.0194363,0.00475479,0.03300615,-0.02193103,0.07392928,-0.02064798,-0.03971538,0.0133689,-0.01262124,0.02567771,0.06706588,-0.04892135,-0.05390766,0.00081545,-0.05706588,0.02735366,0.05899282,0.06856823,0.02785541,0.03310365,0.01173468,0.00756613,-0.01497722,0.04141342,0.03977064,0.00782473,-0.04108483,-0.02768109,0.01820913,-0.00266793,0.00169547,-0.05705,0.01352651,0.00205254,-0.0648147,0.03221148,0.04629322,-0.00847596,-0.05215839,0.05081201,0.0438762,-0.0224414,0.0033625,-0.03599636,-0.0531038,0.01715489,0.00172134,0.03074987,0.01356609,0.0591896,0.01364561,-0.01189304,0.06137194,0.04846064,-0.02362237,-0.06729753,0.01838861,0.0241092,0.01868832,0.03734697,-0.040239,0.01085711,-0.03913552,0.01611797,-0.03215925,0.03641236,-0.04547425,-0.00946284,-0.00675098,0.03357907,-0.00993959,0.07345343,-0.02295674,-0.02564443,0.02238027,-0.02983686,-0.01321813,0.01873018,0.02500336,0.05591273,0.02534692,0.03116205,0.03851153,-0.00550934,0.01765569,-0.01788188,-0.01260378,0.02944421,0.02495003,-0.084136,0.0346639,-0.06134064,-0.02317744,-0.05370083,0.02425848,-0.0070318,0.04728853,0.0190595,0.04003214,0.02116493,-0.04597486,-0.09740309,0.01637926,0.01584716,0.03683494,0.04486946,0.12383088,0.05750075,0.02960825,0.04559456,0.03753015,-0.01419459,-0.05432127,-0.00192546,-0.06856868,0.04642417,-0.01931491,0.00185971,-0.02894044,0.012944,-0.04339817,0.02127685,0.00658802,0.07825962,-0.05918203,0.01268158,-0.02764729,0.04873519,0.02018251,0.05086095,-0.05362329,0.00890735,-0.00589223,-0.02977479,-0.06635069,0.01287624,-0.00930092,0.00161693,-0.00414226,-0.01597813,-0.02243673,-0.00535314,0.02501176,0.02738669,-0.00942764,-0.00044535,-0.0105447,0.02306369,0.03137297,0.0057612,0.0331714,0.094848,-0.0006284,0.03583782,0.00003928,0.02077001,-0.04356709,0.05280287,0.01310597,-0.00903101,0.02834605,0.0013596,0.00400713,0.00984144,-0.00113954,-0.01372579,0.00601521,0.02351932,-0.01488833,0.00705355,0.06553206,-0.01452508,0.0325578,-0.01545303,0.0505245,0.00138678,-0.01899037,-0.00731544,0.0302475,0.05967822,0.00698254,0.02983095,0.04472719,0.03266899,-0.02258852,0.03028654,0.02189869,-0.01320297,0.03710072,-0.03956493,-0.04941279,-0.02848542,0.04094327,0.01605877,-0.04004043,-0.00791761,0.02324766,-0.00357954,0.04121909,0.029984,0.04829133,-0.0572903,-0.00438973,-0.0582586,-0.01172658,0.02161584,-0.0087298,0.0168454,-0.02852507,0.04000567,-0.00680696,-0.01069512,-0.02411176,0.02765116,0.02075364,0.03435287,0.07280914,0.0056226,0.00988292,-0.00638715,-0.05232759,-0.05078136,0.00418259,0.01308136,-0.00317845,0.00157001,-0.00965914,-0.00539876,-0.02280274,-0.0076766,0.02764577,-0.06648728,0.00520837,-0.03995317,-0.0173164,-0.01025003,0.05459302,0.07681109,0.02081825,-0.03660149,0.04189989,0.02887858,-0.02933166,-0.06040524,0.03789558,0.01617519,0.00656533,0.00384795,0.01232874,-0.01637904,0.05943144,-0.00288625,-0.02835241,0.01409317,0.00312523,0.04752975,0.01464711,-0.05343999,-0.00659041,-0.00228071,0.03602589,-0.05835579,0.01871555,0.03033799,0.01045509,0.01918948,-0.02586656,0.02256975,-0.01546051,0.02294145,-0.02723004,-0.0658231,0.05174985,-0.02763014,-0.01293599,0.01065505,0.02392583,0.00308955,-0.0153835,-0.07448872,0.03972466,-0.02087013,-0.05626026,0.01727732,0.00321052,0.03985507,-0.04268204,-0.00356107,0.05200681,-0.01313058,-0.01587298,-0.03759877,-0.00403619,0.00239364,0.06407085,-0.01680094,-0.03883282,0.00759108,-0.04297344,0.0030015,0.03717042,-0.03625413,0.10317941,0.02428397,0.01843135,-0.04138641,0.00316282,-0.06233925,-0.0056659,-0.04369667,-0.00808063,0.03405446,-0.07357354,0.06351604,-0.02229247,-0.03092623,0.05126481,-0.04468341,0.01782901,-0.00803902,-0.04233437,0.06634516,0.02525292,0.02209716,-0.00543981,0.04403212,-0.03056412,0.05381046,-0.04727625,0.00535176,-0.01098927,-0.012681,0.00234636,0.01995123,0.02371091,0.00443549,-0.02681118,0.0266895,-0.03115319,-0.05726639,0.00536789,-0.01729448,-0.01890503,-0.04137853,0.01283623,-0.01812386,-0.03557942,-0.03608827,-0.04529057,-0.04536236,-0.00864549,-0.01151268,-0.02324143,0.0251991,0.05552235,0.02353653,-0.02442752,0.04261074,-0.04735937,0.01597995,-0.03201593,-0.01656356,-0.00450755,-0.08554559,-0.00661493,-0.03383369,-0.02101102,-0.03026096,-0.05676444,-0.03206506,-0.00061689,-0.0688781,-0.00643819,-0.00806237,-0.00864969,-0.0012386,-0.05511921,-0.0222973,0.00497229,-0.04542148,-0.00705362,0.01796177,-0.00359237,0.02168626,0.02232275,-0.05302938,-0.0427271,0.03349461,-0.02527309,-0.04324746,-0.06881118,0.03036626,-0.01331311,-0.05856181,0.05550477,-0.00205683,0.03016463,-0.02867672,0.02208509,-0.0355686,0.00322351,-0.00499768,-0.02203279,-0.01545628,-0.08557846,-0.01622698,-0.00872888,0.03358157,0.01358021,-0.01713719,-0.02207627,-0.01844436,-0.04206292,-0.0077239,0.0212613,-0.00751789,-0.03295982,-0.04837527,-0.02533335,-0.0271334,-0.03561872,0.03669083,0.00660613,0.0370698,0.00700326,-0.01070519,-0.02174405,-0.04655448,-0.01189994,0.03370727,-0.01574043,0.06439944,-0.00213358,-0.00997584,0.02249219,-0.00623815,0.04515809,-0.05118211,-0.07987747,-0.06379527,-0.09357243,0.00609094,0.01778373,-0.00610858,-0.04789502,0.01270369,-0.05336208,-0.00000789,0.03626257,0.05176476,0.00920089,-0.02972566,-0.02120765,0.02276805,0.00653371,-0.03442189,-0.00118858,-0.03301001,-0.01101474,0.03030368,-0.04021204,0.02732769,0.07323721,0.01390418,0.02796793,-0.0749546,-0.00187615,0.00102717,-0.04048702,0.02940826,-0.01162628,-0.01156517,0.05576042,0.00363716,0.07047191,0.01578976,-0.03832025,-0.04171398,0.03695134,0.03353364,-0.00559339,0.01430199,-0.0183711,-0.02956595,0.0177686,-0.01669884,0.04734845,-0.00573456,0.03466466,-0.06152004,0.06624743,0.04817356,0.00432335,-0.02530123,0.03493873,-0.00321943,-0.01008473,-0.05171709,-0.06179736,0.05250066,-0.01320604,0.03183918,0.04554587,0.02077921,0.02381473,-0.00766846,-0.02887119,-0.08265223,0.01347576,-0.05866802,0.04472899,-0.01458904,-0.08726908,0.0027646,0.0086236,0.05676316,-0.01998292,0.03437749,-0.03382294,0.02803368,-0.00781698,0.01719759,0.01433574,-0.02355217,0.0338651,-0.00334452,0.04591376,-0.03903427,-0.03645619,-0.05681201,0.0943623,-0.0277191,0.03668933,0.02934801,-0.06742617,-0.00792264,-0.04655973,0.00586184,-0.02630129,0.04554763,-0.01075523,0.05765403,0.02535532,0.03938968,0.03303251,0.00475802,0.07980546,0.00607817,-0.01528177,0.0463515,-0.01025173,-0.04444107,0.02657361,0.00927583,-0.05063363,-0.01521743,0.05867531,-0.00895793,-0.02627414,0.06229362,0.07421747,-0.02238507,-0.0263963,-0.00306898,0.01963132,0.04621307,-0.02072947,0.02439325,-0.06713028,0.05874271,-0.0567874,-0.0234937,-0.01158361,0.04528224,0.01721045,-0.02324057,0.02530834,0.05896503,-0.01412948,-0.02974107,0.0294941,0.01791885,-0.02153199,-0.00254938,0.03877992,0.07652365,-0.03435781,-0.05601332,0.01633359,0.00812034,0.00334445,0.0391425,0.00471339,-0.00776171,-0.07609186,-0.00328591,0.0018468,0.00281689,0.08442852,-0.02611139,-0.0597902,-0.00469579,0.01433677,-0.03966755,-0.01184014,-0.01543252,0.02813974,-0.00562577,0.03325181,0.00717058,-0.05684285,0.00697423,0.0178456,-0.00102365,-0.03345334,0.03109407,-0.07987368,-0.03361378,-0.10107405,0.01356966,-0.0190045,0.05188216,0.01830033,-0.01672591,-0.04207088,-0.00679434,0.02629472,0.05118299,-0.00839594,-0.07923722,0.12799087,0.00772729,0.04497432,-0.02345528,-0.00537282,-0.06042537,0.05600249,-0.05355492,0.01733931,-0.01893712,-0.00657723,-0.02691956,0.01980523,-0.05105709,0.02092501,-0.0167132,0.01024116,-0.00367585,0.00281251,0.01046231,0.01792774,0.00977399,0.00024878,0.0371362,-0.01765577,-0.01801037,-0.00206358,-0.01802561,0.00962023,-0.02752317,-0.00021916,0.0218441,-0.00275078,-0.04610324,0.02631322,-0.01754524,0.03621568,-0.11189214,-0.03303116,0.02801882,0.00917869,0.06622504,0.01021396,0.00223275,-0.00881919,0.02035315,-0.02470502,-0.00396225,0.03232303,-0.00205493,-0.00148888,0.02122635,-0.00266362,0.02155432,0.00710502,0.02312014,0.00134498,0.02010722,0.01361093,0.0000097,-0.01362228,-0.06301869,0.01706057,0.01406039,0.02587978,0.09472937,-0.00227242,-0.03968128,0.0077664,0.00273285,-0.04757487,0.00231159,-0.04558164,0.01277252,0.03966661,-0.0187128,-0.00565211,-0.03770866,0.01842101,0.04561719,0.0449254,-0.04318405,-0.01228186,-0.01464662,0.07680321,0.0321296,-0.0270798,0.01475439,-0.06973997,0.02137663,0.00054828,0.05863567,-0.00200551,-0.01774666,-0.03941547,0.00638623,0.03424948,-0.02917956,-0.02536743,-0.02990317,-0.04790402,-0.04902728,-0.08788065,-0.03461625,-0.01195524,-0.08119306,0.03962718,-0.01513221,-0.0112737,0.01815717,-0.02719958,0.04314721,-0.00033174,-0.0199169,-0.06392907,8.4e-7,0.05184718,-0.01758483,0.01234958,-0.06328037,-0.00693076,-0.02505992,0.01585745,-0.07471492,-0.00644094],"last_embed":{"tokens":1025,"hash":"b0g3gy"}}},"last_read":{"hash":"b0g3gy","at":1751079981347},"class_name":"SmartSource","outlinks":[{"title":"- /.","target":"0?[1-9]|[12][0-9]|3[01]","line":48},{"title":"- /.","target":"19|20","line":48},{"title":"- /.","target":"0?[1-9]|[12][0-9]|3[01]","line":49},{"title":"- /.","target":"0?[1-9]|1[012]","line":49},{"title":"a-zA-Z0-9","target":"a-zA-Z0-9","line":72}],"metadata":{"aliases":["Regular Expressions","regex"],"tags":null,"cssclasses":["editor-full"]},"blocks":{"#---frontmatter---":[1,8],"#简介":[9,15],"#简介#{1}":[10,15],"#元字符":[16,36],"#元字符#{1}":[18,36],"#常用的正则表达式":[37,90],"#常用的正则表达式#{1}":[39,39],"#常用的正则表达式#{2}":[40,40],"#常用的正则表达式#{3}":[41,41],"#常用的正则表达式#{4}":[42,42],"#常用的正则表达式#{5}":[43,43],"#常用的正则表达式#{6}":[44,44],"#常用的正则表达式#{7}":[45,45],"#常用的正则表达式#{8}":[46,46],"#常用的正则表达式#{9}":[47,47],"#常用的正则表达式#{10}":[48,48],"#常用的正则表达式#{11}":[49,49],"#常用的正则表达式#{12}":[50,51],"#常用的正则表达式#电话号码匹配":[52,57],"#常用的正则表达式#电话号码匹配#{1}":[53,57],"#常用的正则表达式#大小写字母匹配":[58,64],"#常用的正则表达式#大小写字母匹配#{1}":[59,64],"#常用的正则表达式#电子邮件匹配":[65,69],"#常用的正则表达式#电子邮件匹配#{1}":[66,69],"#常用的正则表达式#网站匹配":[70,75],"#常用的正则表达式#网站匹配#{1}":[71,75],"#常用的正则表达式#电话号码匹配#{2}":[76,82],"#常用的正则表达式#电话号码匹配#{2}#{1}":[77,82],"#常用的正则表达式#IPv4地址匹配":[83,90],"#常用的正则表达式#IPv4地址匹配#{1}":[85,90]},"last_import":{"mtime":1733037749031,"size":2907,"at":1748488128912,"hash":"b0g3gy"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/数据处理/正则表达式.md"},