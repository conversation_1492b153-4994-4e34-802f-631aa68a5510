"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08766069,-0.01134524,0.01410643,-0.01986586,-0.00267199,0.01631452,-0.04769528,0.03860858,0.05526187,0.02276582,-0.01726162,-0.0468584,0.04922675,0.07862788,0.05273273,-0.00190688,-0.02732927,0.0509655,0.00940664,0.02222554,0.09474611,-0.0296759,-0.02361311,-0.08328513,-0.0396268,0.0293904,0.0476357,-0.02417355,-0.02257133,-0.16741367,-0.0080131,-0.03479403,0.03617789,0.0185938,-0.01851944,-0.02625665,0.04338941,0.0650847,0.00434075,0.06965973,-0.00119323,0.01415725,0.0315633,-0.01421773,-0.031679,-0.07494855,-0.06356841,-0.01917976,0.00631077,-0.01931509,-0.02485645,-0.06412055,-0.02802,-0.01234083,-0.0513211,0.03846388,0.00362694,0.00963008,0.0205008,-0.03044073,0.0051709,0.01067596,-0.19751543,0.09081339,-0.00860532,0.01513183,-0.02871708,-0.02293986,0.01757474,0.02435246,-0.04945005,0.00596293,-0.04094231,0.05788882,0.04702114,-0.00511157,0.01613777,-0.08435112,-0.0268023,-0.0572575,-0.01487525,0.01424595,-0.02836042,0.02749966,0.02657868,0.07935603,0.01856053,-0.03192298,0.01530035,0.01681697,0.03824778,-0.07394927,0.0251986,-0.00431152,-0.0161505,-0.03860889,0.03611616,0.05781536,-0.05448566,0.11628222,-0.03579129,0.03292787,0.02603248,-0.04947935,-0.01317905,-0.04698838,-0.02775036,-0.04116227,-0.01561422,-0.03954526,-0.07318422,-0.03763006,0.05402623,-0.02548948,0.00222534,0.03198766,0.00232424,-0.0365699,0.03448362,-0.01876843,0.01723476,0.00249142,0.07372738,0.00686137,-0.02275087,0.0185367,0.00368786,0.04972653,-0.0308147,0.0326409,0.07211191,-0.01434238,-0.04269094,-0.02301265,-0.03696433,0.00755119,-0.03025093,0.04092301,-0.00843429,-0.04507559,0.01397799,-0.0570345,-0.01404087,-0.06527298,-0.06685007,0.07323622,-0.06197504,0.00824092,0.03594137,-0.0349067,0.00840013,0.03844135,-0.02381645,-0.0295107,-0.03970495,0.03710749,0.05523846,0.08718757,-0.01252841,-0.05586444,-0.01218321,0.04414407,-0.08412309,0.18695198,0.04240623,-0.08768416,-0.02007728,0.01365661,0.00329926,-0.00510182,0.00759888,-0.01232247,0.00154143,-0.01319874,0.07797893,-0.02027399,-0.0064516,-0.01757595,0.04211279,0.02095348,0.09631548,-0.04396256,-0.11756127,0.09634975,0.0286824,-0.10567503,-0.03121321,-0.05452846,-0.00034103,-0.04760951,-0.06168843,-0.00708834,-0.02594081,0.02205709,-0.03332032,-0.04192246,0.01792176,-0.00080079,0.04524585,-0.06303677,0.124999,0.04919589,-0.03514897,-0.02643419,-0.01288809,-0.0198745,0.05546629,0.01307768,0.02971864,0.03962398,0.00065038,0.02764719,0.00829946,-0.01812259,0.01834463,0.02766567,0.01988125,0.02789608,0.04412318,0.05064629,-0.04574226,0.00256461,-0.05529813,-0.22747456,-0.04804463,0.00153867,-0.01384299,0.00261618,-0.02082727,0.02535247,0.0195002,0.10402039,0.09694135,0.10896902,0.05569047,-0.07018397,-0.02840796,-0.01623283,0.00257462,0.03241377,-0.06207223,-0.01076908,-0.02253948,0.01203385,0.05161968,0.00817262,-0.02862009,0.04907926,-0.05760036,0.12462313,0.0477502,0.04372466,0.03711875,0.02856615,-0.01666062,0.04004782,-0.09605002,0.01472971,0.0742285,-0.02002933,-0.04874482,-0.00853171,-0.00594889,-0.01389008,-0.01257804,-0.03753796,-0.05142841,-0.0169019,-0.02018069,-0.05778557,-0.04939838,0.01195107,0.05496607,-0.04539563,-0.01570825,-0.00248546,0.04125797,-0.05146442,0.0121069,-0.07275515,-0.03497765,-0.01411507,0.04537421,0.00852158,-0.00970231,-0.00687952,-0.00085134,-0.00987614,0.01875262,0.02041527,-0.04364318,-0.04538102,0.01735828,-0.04515913,0.13698009,-0.00237946,-0.02692969,0.01063664,0.00387096,-0.01177306,-0.02265481,-0.00463808,0.0446072,0.04902243,0.02144204,0.03696759,0.03705266,0.01944933,0.03794857,0.00527056,-0.02783187,0.07696371,-0.0608932,-0.07175711,-0.03505233,-0.05902981,0.05194386,0.07271526,-0.05073414,-0.30423152,-0.00365304,-0.03491054,0.00218162,0.02772535,0.00440135,0.03939426,-0.01958182,-0.03261219,0.06125262,-0.0140086,0.05427907,0.00941597,-0.0032975,-0.03771552,-0.02496307,0.03669487,-0.01060924,0.06593224,-0.00393873,0.05125444,0.02704317,0.20681113,0.0466303,0.03846293,0.01250819,-0.02143946,0.05252085,0.04026585,0.0505241,0.01370039,-0.03464473,0.03485829,-0.03830599,0.01318392,0.05854568,-0.07600136,0.03251471,0.04137474,0.00086188,-0.07022461,0.03535637,-0.06071956,0.02182766,0.08110816,0.02456924,0.0237229,-0.02666198,0.01705025,0.01203904,-0.00263451,0.08192877,-0.05825632,-0.04095059,0.03326455,0.03567886,-0.02232476,-0.01996926,-0.03188971,-0.03713955,0.0290322,0.01788592,0.08265711,0.11929982,0.0484647],"last_embed":{"hash":"c455a207dfb089f7cc59e1139c6d8e231d0c269dc8206087e170c7021fa73b5b","tokens":451}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.04641368,0.00402836,-0.02759994,-0.01979963,-0.01047124,0.0297705,0.04862652,-0.04552205,-0.00959321,-0.00516382,-0.02518007,0.00923101,-0.00461926,-0.01723321,0.02901532,-0.00778936,-0.05451843,-0.03013575,0.05893471,0.00342142,0.00407357,0.00430415,0.02069908,0.02736148,-0.02213473,-0.05491296,0.01488641,-0.07848615,-0.0005969,0.03417132,0.06137597,0.0235882,0.01801459,-0.01862192,-0.0339928,-0.0076577,0.00733321,0.01303326,0.02189528,0.03441364,-0.01317631,0.05009503,-0.03978971,0.02683997,-0.09091485,0.01892079,-0.03261797,-0.049069,0.02169336,-0.01457626,-0.01211743,-0.02668662,0.01305798,0.04186605,0.01360651,0.00673131,0.00105309,-0.05164393,-0.00507201,-0.00896967,0.00466984,0.03290299,0.06448398,0.00746252,0.00932874,0.00747103,-0.00850123,-0.00374658,0.03095542,-0.0061681,-0.072569,-0.02324303,0.04267984,0.02287183,0.0464421,0.00683825,0.02843522,-0.00210162,-0.03256693,0.02539699,-0.11146862,-0.00494526,-0.06231945,0.08198866,0.00649917,-0.02445349,-0.02250837,-0.10416453,-0.00259227,-0.00274907,0.0351435,0.02852035,0.03513378,0.01797399,0.00536298,-0.05766172,-0.02454139,0.0381395,0.00937683,0.00767528,0.00668528,0.01226694,-0.01992072,0.00728083,-0.05184096,-0.07123525,-0.05226965,0.03498208,-0.01721444,-0.00356911,-0.00174144,0.04217271,-0.04265793,-0.04273203,-0.02575572,0.0088482,-0.00689057,-0.03039633,0.04716733,-0.0035617,0.02142248,0.02793076,-0.01835898,-0.02858443,-0.00824365,0.02082152,-0.0454286,-0.01001072,0.0414737,-0.01990462,-0.09660462,0.03536481,-0.02687411,-0.01666049,0.02377409,-0.0301241,0.03428895,-0.09152649,0.04636694,-0.03442474,0.0447331,-0.01103701,-0.03691655,-0.00108766,-0.01455007,0.01092793,-0.08004548,-0.0539061,0.01651252,0.01446084,0.01226005,0.02248559,-0.02755135,-0.04152083,0.03691148,0.00395679,0.08009494,0.0707328,-0.00824558,-0.03060618,-0.01603258,0.02571078,-0.0021717,-0.02455658,0.01430879,-0.00019652,-0.01538114,0.02464215,0.0477349,-0.0179693,-0.01175835,0.00009145,0.0005347,0.02836585,0.00851292,-0.01867871,0.03561405,0.00148263,-0.11709505,-0.05106917,0.03276902,0.01346189,0.01605017,-0.00461901,0.01345967,0.02881821,0.07315359,0.03939265,0.01613505,-0.00971962,0.05385247,0.02774544,0.01850466,0.00098206,0.02286824,0.02771634,-0.02783589,-0.02876598,0.01914121,0.01868133,-0.05254733,0.06401077,-0.05075898,-0.03066728,0.01953447,-0.035578,-0.04391689,-0.03883365,-0.02677327,0.07892241,0.0233342,-0.02193032,0.01764112,-0.01318074,-0.04234063,0.01604513,-0.03411751,0.07832703,0.01646467,-0.023106,0.00927526,-0.06109726,-0.00921955,-0.00789363,0.04120439,-0.03957967,-0.03596268,0.00963426,0.07482426,0.06484003,0.02217049,0.07674743,-0.0311268,-0.03276861,-0.01641613,-0.04071433,0.07182582,0.00280957,-0.0093846,0.04008543,-0.00470319,0.01389348,-0.01409723,-0.03863378,-0.02284058,-0.03708404,-0.0649303,0.08053394,-0.04266518,0.03123382,0.05672177,0.03702395,0.02805537,0.08184422,0.02526439,-0.02797483,-0.06223094,0.02320941,0.03016546,-0.04312257,0.00322557,-0.03681185,-0.02574148,-0.01280911,-0.00726552,-0.00241519,-0.01785023,0.00516501,-0.04779503,0.09062919,0.02108657,-0.02496067,0.06977185,-0.00872886,0.0079942,0.01861332,-0.00457721,-0.03708054,-0.06245644,-0.01789246,0.0220762,-0.01869793,0.00066406,-0.01067801,-0.06246788,0.04913035,0.00359037,0.01109099,0.0386305,-0.01655519,0.02052872,-0.00542657,-0.04878131,0.01205132,-0.06533888,-0.02355026,0.01956414,0.00477498,-0.00603268,-0.04845221,0.04103747,0.03713373,0.02877958,-0.01860451,-0.00165694,-0.00002342,0.00240424,0.04205339,-0.02646606,-0.02881758,0.00171487,-0.03117489,0.03311275,-0.01816775,0.02869991,0.07800561,0.07776207,-0.00421539,-0.01479172,0.03502302,-0.00257913,-0.0385069,-0.00073506,-0.00693764,0.05746098,-0.09847122,-0.03587154,0.01233559,0.04414929,0.05989819,-0.05388511,0.06085669,-0.0050975,-0.02121153,-0.01934999,-0.00731986,0.00066789,-0.06448859,0.03262389,-0.09240492,0.03742528,-0.08816005,-0.00816234,-0.0214117,-0.01029073,0.02866666,-0.03596091,0.00801517,-0.02191467,-0.03353023,0.02876795,-0.00558087,-0.0543552,0.01880753,-0.01474879,0.012174,-0.02764293,-0.00471707,-0.04067998,0.04705772,-0.04118197,-0.03156576,-0.00875622,-0.05119133,-0.0246853,0.00675689,0.0627887,0.02667141,-0.06466497,-0.05297576,0.05172024,-0.00901296,0.00070032,0.0259442,0.02844917,-0.00907862,-0.01065434,0.01478229,-0.01836187,-0.00446415,-0.02824914,-0.04525255,-0.0278336,-0.01817745,-0.06614557,-0.01796228,0.00567189,-0.01540899,0.05285618,-0.03870691,-0.00299438,0.02053014,0.00103985,-0.00719186,0.048195,0.00177381,0.04135815,-0.01806797,-0.0079488,-0.01339451,0.02550947,0.00754318,0.02435145,-0.00309597,-0.02067928,0.04163304,-0.07162971,0.04578754,-0.02146798,0.00792513,-0.00939985,0.07370348,-0.0303455,0.01442008,-0.01256131,0.06304663,0.04122672,-0.05900012,0.02007235,-0.00406684,0.03585783,0.00588897,0.0376893,0.06316461,0.00611624,-0.0116487,-0.02083943,0.00718604,-0.00697851,-0.00959602,-0.08101923,-0.03745646,-0.05641504,-0.01207733,-0.02832893,0.00366811,0.04833475,-0.04136775,0.01787765,-0.00548097,-0.04615949,-0.01013737,-0.06107681,0.00455214,0.05093122,0.01565957,-0.02367913,-0.06479388,0.0046017,-0.01019819,0.03728133,-0.0100742,-0.07187442,-0.06464351,0.0307531,0.01680804,0.01093194,-0.00673926,0.03211384,0.02903437,-0.05972567,0.02218818,0.02930519,0.00142911,0.00744837,0.01353666,0.0032209,0.00643359,0.00393591,0.03926465,0.00944624,0.00681614,-0.0041509,-0.0207411,0.03959411,-0.00037027,-0.02395004,0.00371198,-0.06116652,-0.00376829,0.0095254,-0.02262165,0.01025565,-0.02987843,-0.05487235,-0.01275087,-0.04071701,-0.00169368,0.00090735,-0.04425735,-0.01452919,0.07780372,-0.00493603,0.02715105,0.01968741,0.00735507,0.07615576,0.04553179,0.00187539,0.08941165,0.03321523,0.05261242,-0.02209796,0.03225425,0.04474801,0.00972586,-0.02192794,-0.00155291,0.0393863,-0.03645547,0.01361088,0.01052471,0.07278299,0.04169567,0.02389852,0.00545086,-0.03464696,-0.04918558,0.00630237,-0.04183541,-0.01287237,-0.00171982,-0.04719039,0.01803072,0.02802453,0.02585412,-0.06928352,0.0360306,0.03687913,0.07956287,-0.06024031,0.01043328,-0.00572978,-0.02409872,0.02793559,-0.01106706,0.04604028,-0.00248951,-0.05780734,0.03552239,0.0407623,0.0541184,-0.00198637,0.02883694,-0.03491603,-0.02387291,0.02758306,-0.0219848,0.03886328,-0.03498468,0.01620732,-0.03894686,-0.02068552,-0.0351523,-0.02912841,0.02176964,0.02100225,-0.04370872,0.04182661,0.10207641,0.03416383,0.04330907,-0.00600117,-0.02545611,-0.00583341,0.01053846,-0.03448716,-0.0098794,0.03412294,0.05377228,-0.05923259,-0.01250445,0.05442732,0.05316796,-0.07163507,0.00893,0.02990702,0.03302845,0.0462597,-0.0039094,0.08544799,-0.01431937,0.06963224,-0.02779369,0.02959331,-0.00105614,-0.00388217,0.02387996,0.02338918,0.00539936,0.0548453,-0.05812071,-0.03105139,-0.01331978,-0.00079836,0.00088323,-0.02841572,0.03039365,-0.00229482,-0.02519193,0.00780122,0.00198489,0.00004112,0.02991616,0.01393988,-0.07315325,-0.03233683,-0.05784322,0.04049077,0.05108206,-0.01476782,0.09682368,-0.00033862,-0.04532525,-0.00043707,-0.04831452,-0.02769761,-0.01328709,-0.0329022,-0.00831499,-0.02452251,-0.06869133,-0.0272221,0.02446832,-0.00361548,0.07294944,-0.00692748,0.01937469,0.0209882,-0.05729454,-0.01520191,0.01220134,0.00641162,0.04865215,0.01528763,0.02153764,-0.0229976,-0.02816947,-0.02957343,0.01324854,0.04126903,-0.00473731,-0.01207448,0.03507278,0.0318847,-0.04231989,-0.01840491,0.00089041,-0.00165688,0.00687238,0.00341881,-0.03495903,0.01594423,0.01136746,-0.03346685,0.01094706,0.02192794,0.00224608,0.00719292,0.02318005,0.00162571,-0.02311604,0.01358538,0.00194828,-0.04043271,0.0415894,0.0277019,0.00691305,-0.03888166,-0.00471588,-0.0595081,-0.05697263,0.00614204,0.0286093,-0.00821854,0.01773341,-0.0273381,0.02550595,-0.02500113,-0.00728346,-0.04349537,-0.0201976,-0.01443509,0.04172457,0.03747624,0.05410144,0.00681694,-0.02269234,0.00668283,0.02596965,-0.00153019,-0.00617088,-0.0182368,0.01392548,0.03718908,-0.06606469,-0.00869677,-0.00404515,-0.01014846,0.01676231,-0.01638601,0.01970247,-0.05891332,0.00853621,-0.06238099,-0.0319635,0.0580143,0.02876135,0.06332824,0.03724592,-0.06765775,-0.05048564,-0.00505125,-0.01237067,0.03466846,-0.0559138,0.02473421,0.06578635,-0.03782047,-0.03172743,-0.03376249,0.00823383,0.03620988,0.01614797,0.00111386,-0.04814984,-0.04971705,0.01329706,-0.02497527,0.00618343,-0.01590177,-0.0968658,0.00465445,0.03507978,0.04648044,-0.04658138,-0.0344496,-0.00857182,0.04524651,-0.04477926,-0.00935851,-0.01040563,-0.02481916,-0.02636446,-0.06567313,0.0402127,0.02394827,0.03036146,0.09003226,0.02724737,-0.02173358,0.00460231,0.05065627,0.07827469,0.01008697,0.01481038,-0.01912479,-0.05462078,7.4e-7,-0.03167421,0.01096673,0.02438961,-0.018062,-0.00602117,-0.05961515,-0.04162319,-0.00369145,0.00064513],"last_embed":{"tokens":884,"hash":"7tpnp2"}}},"last_read":{"hash":"7tpnp2","at":1750993404797},"class_name":"SmartSource","outlinks":[{"title":"sql注入","target":"sql注入","line":18},{"title":"反射型XSS攻击(Reflected XSS)","target":"#反射型XSS攻击(Reflected XSS)","line":27},{"title":"存储型XSS (Stored XSS)","target":"#存储型XSS (Stored XSS)","line":28},{"title":"HTTP(S)协议","target":"HTTP(S)协议","line":43},{"title":"DOM","target":"DOM","line":54},{"title":"反射型XSS攻击(Reflected XSS)","target":"#反射型XSS攻击(Reflected XSS)","line":60},{"title":"肉鸡","target":"Bot-Zombie","line":72},{"title":"DDoS","target":"DDoS","line":72},{"title":"DOM","target":"DOM","line":81},{"title":"DOM","target":"DOM","line":84}],"metadata":{"aliases":["cross site script","跨站脚本攻击"],"tags":["网络安全/web安全/XSS攻击"],"发布时间":"2007-01-01","类型":["漏洞"],"编程语言":["JavaScript"],"文档更新日期":"2024-01-12","cssclasses":["editor-full"]},"blocks":{"#---frontmatter---":[1,15],"#简介":[16,23],"#简介#{1}":[17,19],"#简介#{2}":[20,21],"#简介#{3}":[22,23],"#XSS的类型":[24,32],"#XSS的类型#{1}":[25,25],"#XSS的类型#{2}":[26,29],"#XSS的类型#{3}":[30,32],"#反射型XSS攻击(Reflected XSS)":[33,57],"#反射型XSS攻击(Reflected XSS)#{1}":[34,38],"#反射型XSS攻击(Reflected XSS)#非持久性的特点":[39,48],"#反射型XSS攻击(Reflected XSS)#非持久性的特点#{1}":[41,43],"#反射型XSS攻击(Reflected XSS)#非持久性的特点#{2}":[44,44],"#反射型XSS攻击(Reflected XSS)#非持久性的特点#{3}":[45,46],"#反射型XSS攻击(Reflected XSS)#非持久性的特点#{4}":[47,48],"#反射型XSS攻击(Reflected XSS)#防御非持久性的方式":[49,57],"#反射型XSS攻击(Reflected XSS)#防御非持久性的方式#{1}":[51,52],"#反射型XSS攻击(Reflected XSS)#防御非持久性的方式#{2}":[53,53],"#反射型XSS攻击(Reflected XSS)#防御非持久性的方式#{3}":[54,56],"#反射型XSS攻击(Reflected XSS)#防御非持久性的方式#{4}":[57,57],"#存储型XSS (Stored XSS)":[58,82],"#存储型XSS (Stored XSS)#{1}":[59,66],"#存储型XSS (Stored XSS)#持久性的特点":[67,75],"#存储型XSS (Stored XSS)#持久性的特点#{1}":[69,70],"#存储型XSS (Stored XSS)#持久性的特点#{2}":[71,72],"#存储型XSS (Stored XSS)#持久性的特点#{3}":[73,75],"#存储型XSS (Stored XSS)#防御持久性的方式":[76,82],"#存储型XSS (Stored XSS)#防御持久性的方式#{1}":[78,79],"#存储型XSS (Stored XSS)#防御持久性的方式#{2}":[80,80],"#存储型XSS (Stored XSS)#防御持久性的方式#{3}":[81,82],"#DOM-based型":[83,107],"#DOM-based型#{1}":[84,88],"#DOM-based型#常用的的HTML标签":[89,96],"#DOM-based型#常用的的HTML标签#{1}":[91,96],"#DOM-based型#常用的Javascript方法":[97,107],"#DOM-based型#常用的Javascript方法#{1}":[99,107]},"last_import":{"mtime":1740108944640,"size":3540,"at":1749024987587,"hash":"7tpnp2"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md","last_embed":{"hash":"7tpnp2","at":1750993404797}},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#---frontmatter---","lines":[1,15],"size":172,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#简介": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#简介","lines":[16,23],"size":141,"outlinks":[{"title":"sql注入","target":"sql注入","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#简介#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#简介#{1}","lines":[17,19],"size":95,"outlinks":[{"title":"sql注入","target":"sql注入","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#简介#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#简介#{2}","lines":[20,21],"size":34,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#简介#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#简介#{3}","lines":[22,23],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#XSS的类型": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#XSS的类型","lines":[24,32],"size":139,"outlinks":[{"title":"反射型XSS攻击(Reflected XSS)","target":"#反射型XSS攻击(Reflected XSS)","line":4},{"title":"存储型XSS (Stored XSS)","target":"#存储型XSS (Stored XSS)","line":5}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#XSS的类型#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#XSS的类型#{1}","lines":[25,25],"size":11,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#XSS的类型#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#XSS的类型#{2}","lines":[26,29],"size":112,"outlinks":[{"title":"反射型XSS攻击(Reflected XSS)","target":"#反射型XSS攻击(Reflected XSS)","line":2},{"title":"存储型XSS (Stored XSS)","target":"#存储型XSS (Stored XSS)","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#XSS的类型#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#XSS的类型#{3}","lines":[30,32],"size":5,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#反射型XSS攻击(Reflected XSS)": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#反射型XSS攻击(Reflected XSS)","lines":[33,57],"size":398,"outlinks":[{"title":"HTTP(S)协议","target":"HTTP(S)协议","line":11},{"title":"DOM","target":"DOM","line":22}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#反射型XSS攻击(Reflected XSS)#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#反射型XSS攻击(Reflected XSS)#{1}","lines":[34,38],"size":121,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#反射型XSS攻击(Reflected XSS)#非持久性的特点": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#反射型XSS攻击(Reflected XSS)#非持久性的特点","lines":[39,48],"size":130,"outlinks":[{"title":"HTTP(S)协议","target":"HTTP(S)协议","line":5}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#反射型XSS攻击(Reflected XSS)#非持久性的特点#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#反射型XSS攻击(Reflected XSS)#非持久性的特点#{1}","lines":[41,43],"size":60,"outlinks":[{"title":"HTTP(S)协议","target":"HTTP(S)协议","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#反射型XSS攻击(Reflected XSS)#非持久性的特点#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#反射型XSS攻击(Reflected XSS)#非持久性的特点#{2}","lines":[44,44],"size":18,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#反射型XSS攻击(Reflected XSS)#非持久性的特点#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#反射型XSS攻击(Reflected XSS)#非持久性的特点#{3}","lines":[45,46],"size":23,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#反射型XSS攻击(Reflected XSS)#非持久性的特点#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#反射型XSS攻击(Reflected XSS)#非持久性的特点#{4}","lines":[47,48],"size":13,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#反射型XSS攻击(Reflected XSS)#防御非持久性的方式": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#反射型XSS攻击(Reflected XSS)#防御非持久性的方式","lines":[49,57],"size":119,"outlinks":[{"title":"DOM","target":"DOM","line":6}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#反射型XSS攻击(Reflected XSS)#防御非持久性的方式#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#反射型XSS攻击(Reflected XSS)#防御非持久性的方式#{1}","lines":[51,52],"size":45,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#反射型XSS攻击(Reflected XSS)#防御非持久性的方式#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#反射型XSS攻击(Reflected XSS)#防御非持久性的方式#{2}","lines":[53,53],"size":18,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#反射型XSS攻击(Reflected XSS)#防御非持久性的方式#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#反射型XSS攻击(Reflected XSS)#防御非持久性的方式#{3}","lines":[54,56],"size":35,"outlinks":[{"title":"DOM","target":"DOM","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#反射型XSS攻击(Reflected XSS)#防御非持久性的方式#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#反射型XSS攻击(Reflected XSS)#防御非持久性的方式#{4}","lines":[57,57],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#存储型XSS (Stored XSS)": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#存储型XSS (Stored XSS)","lines":[58,82],"size":448,"outlinks":[{"title":"反射型XSS攻击(Reflected XSS)","target":"#反射型XSS攻击(Reflected XSS)","line":3},{"title":"肉鸡","target":"Bot-Zombie","line":15},{"title":"DDoS","target":"DDoS","line":15},{"title":"DOM","target":"DOM","line":24}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#存储型XSS (Stored XSS)#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#存储型XSS (Stored XSS)#{1}","lines":[59,66],"size":219,"outlinks":[{"title":"反射型XSS攻击(Reflected XSS)","target":"#反射型XSS攻击(Reflected XSS)","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#存储型XSS (Stored XSS)#持久性的特点": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#存储型XSS (Stored XSS)#持久性的特点","lines":[67,75],"size":93,"outlinks":[{"title":"肉鸡","target":"Bot-Zombie","line":6},{"title":"DDoS","target":"DDoS","line":6}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#存储型XSS (Stored XSS)#持久性的特点#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#存储型XSS (Stored XSS)#持久性的特点#{1}","lines":[69,70],"size":22,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#存储型XSS (Stored XSS)#持久性的特点#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#存储型XSS (Stored XSS)#持久性的特点#{2}","lines":[71,72],"size":46,"outlinks":[{"title":"肉鸡","target":"Bot-Zombie","line":2},{"title":"DDoS","target":"DDoS","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#存储型XSS (Stored XSS)#持久性的特点#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#存储型XSS (Stored XSS)#持久性的特点#{3}","lines":[73,75],"size":12,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#存储型XSS (Stored XSS)#防御持久性的方式": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#存储型XSS (Stored XSS)#防御持久性的方式","lines":[76,82],"size":112,"outlinks":[{"title":"DOM","target":"DOM","line":6}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#存储型XSS (Stored XSS)#防御持久性的方式#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#存储型XSS (Stored XSS)#防御持久性的方式#{1}","lines":[78,79],"size":33,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#存储型XSS (Stored XSS)#防御持久性的方式#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#存储型XSS (Stored XSS)#防御持久性的方式#{2}","lines":[80,80],"size":22,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#存储型XSS (Stored XSS)#防御持久性的方式#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#存储型XSS (Stored XSS)#防御持久性的方式#{3}","lines":[81,82],"size":41,"outlinks":[{"title":"DOM","target":"DOM","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#DOM-based型": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#DOM-based型","lines":[83,107],"size":641,"outlinks":[{"title":"DOM","target":"DOM","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#DOM-based型#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#DOM-based型#{1}","lines":[84,88],"size":111,"outlinks":[{"title":"DOM","target":"DOM","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#DOM-based型#常用的的HTML标签": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#DOM-based型#常用的的HTML标签","lines":[89,96],"size":142,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#DOM-based型#常用的的HTML标签#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#DOM-based型#常用的的HTML标签#{1}","lines":[91,96],"size":126,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#DOM-based型#常用的Javascript方法": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#DOM-based型#常用的Javascript方法","lines":[97,107],"size":373,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#DOM-based型#常用的Javascript方法#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md#DOM-based型#常用的Javascript方法#{1}","lines":[99,107],"size":352,"outlinks":[],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08766069,-0.01134524,0.01410643,-0.01986586,-0.00267199,0.01631452,-0.04769528,0.03860858,0.05526187,0.02276582,-0.01726162,-0.0468584,0.04922675,0.07862788,0.05273273,-0.00190688,-0.02732927,0.0509655,0.00940664,0.02222554,0.09474611,-0.0296759,-0.02361311,-0.08328513,-0.0396268,0.0293904,0.0476357,-0.02417355,-0.02257133,-0.16741367,-0.0080131,-0.03479403,0.03617789,0.0185938,-0.01851944,-0.02625665,0.04338941,0.0650847,0.00434075,0.06965973,-0.00119323,0.01415725,0.0315633,-0.01421773,-0.031679,-0.07494855,-0.06356841,-0.01917976,0.00631077,-0.01931509,-0.02485645,-0.06412055,-0.02802,-0.01234083,-0.0513211,0.03846388,0.00362694,0.00963008,0.0205008,-0.03044073,0.0051709,0.01067596,-0.19751543,0.09081339,-0.00860532,0.01513183,-0.02871708,-0.02293986,0.01757474,0.02435246,-0.04945005,0.00596293,-0.04094231,0.05788882,0.04702114,-0.00511157,0.01613777,-0.08435112,-0.0268023,-0.0572575,-0.01487525,0.01424595,-0.02836042,0.02749966,0.02657868,0.07935603,0.01856053,-0.03192298,0.01530035,0.01681697,0.03824778,-0.07394927,0.0251986,-0.00431152,-0.0161505,-0.03860889,0.03611616,0.05781536,-0.05448566,0.11628222,-0.03579129,0.03292787,0.02603248,-0.04947935,-0.01317905,-0.04698838,-0.02775036,-0.04116227,-0.01561422,-0.03954526,-0.07318422,-0.03763006,0.05402623,-0.02548948,0.00222534,0.03198766,0.00232424,-0.0365699,0.03448362,-0.01876843,0.01723476,0.00249142,0.07372738,0.00686137,-0.02275087,0.0185367,0.00368786,0.04972653,-0.0308147,0.0326409,0.07211191,-0.01434238,-0.04269094,-0.02301265,-0.03696433,0.00755119,-0.03025093,0.04092301,-0.00843429,-0.04507559,0.01397799,-0.0570345,-0.01404087,-0.06527298,-0.06685007,0.07323622,-0.06197504,0.00824092,0.03594137,-0.0349067,0.00840013,0.03844135,-0.02381645,-0.0295107,-0.03970495,0.03710749,0.05523846,0.08718757,-0.01252841,-0.05586444,-0.01218321,0.04414407,-0.08412309,0.18695198,0.04240623,-0.08768416,-0.02007728,0.01365661,0.00329926,-0.00510182,0.00759888,-0.01232247,0.00154143,-0.01319874,0.07797893,-0.02027399,-0.0064516,-0.01757595,0.04211279,0.02095348,0.09631548,-0.04396256,-0.11756127,0.09634975,0.0286824,-0.10567503,-0.03121321,-0.05452846,-0.00034103,-0.04760951,-0.06168843,-0.00708834,-0.02594081,0.02205709,-0.03332032,-0.04192246,0.01792176,-0.00080079,0.04524585,-0.06303677,0.124999,0.04919589,-0.03514897,-0.02643419,-0.01288809,-0.0198745,0.05546629,0.01307768,0.02971864,0.03962398,0.00065038,0.02764719,0.00829946,-0.01812259,0.01834463,0.02766567,0.01988125,0.02789608,0.04412318,0.05064629,-0.04574226,0.00256461,-0.05529813,-0.22747456,-0.04804463,0.00153867,-0.01384299,0.00261618,-0.02082727,0.02535247,0.0195002,0.10402039,0.09694135,0.10896902,0.05569047,-0.07018397,-0.02840796,-0.01623283,0.00257462,0.03241377,-0.06207223,-0.01076908,-0.02253948,0.01203385,0.05161968,0.00817262,-0.02862009,0.04907926,-0.05760036,0.12462313,0.0477502,0.04372466,0.03711875,0.02856615,-0.01666062,0.04004782,-0.09605002,0.01472971,0.0742285,-0.02002933,-0.04874482,-0.00853171,-0.00594889,-0.01389008,-0.01257804,-0.03753796,-0.05142841,-0.0169019,-0.02018069,-0.05778557,-0.04939838,0.01195107,0.05496607,-0.04539563,-0.01570825,-0.00248546,0.04125797,-0.05146442,0.0121069,-0.07275515,-0.03497765,-0.01411507,0.04537421,0.00852158,-0.00970231,-0.00687952,-0.00085134,-0.00987614,0.01875262,0.02041527,-0.04364318,-0.04538102,0.01735828,-0.04515913,0.13698009,-0.00237946,-0.02692969,0.01063664,0.00387096,-0.01177306,-0.02265481,-0.00463808,0.0446072,0.04902243,0.02144204,0.03696759,0.03705266,0.01944933,0.03794857,0.00527056,-0.02783187,0.07696371,-0.0608932,-0.07175711,-0.03505233,-0.05902981,0.05194386,0.07271526,-0.05073414,-0.30423152,-0.00365304,-0.03491054,0.00218162,0.02772535,0.00440135,0.03939426,-0.01958182,-0.03261219,0.06125262,-0.0140086,0.05427907,0.00941597,-0.0032975,-0.03771552,-0.02496307,0.03669487,-0.01060924,0.06593224,-0.00393873,0.05125444,0.02704317,0.20681113,0.0466303,0.03846293,0.01250819,-0.02143946,0.05252085,0.04026585,0.0505241,0.01370039,-0.03464473,0.03485829,-0.03830599,0.01318392,0.05854568,-0.07600136,0.03251471,0.04137474,0.00086188,-0.07022461,0.03535637,-0.06071956,0.02182766,0.08110816,0.02456924,0.0237229,-0.02666198,0.01705025,0.01203904,-0.00263451,0.08192877,-0.05825632,-0.04095059,0.03326455,0.03567886,-0.02232476,-0.01996926,-0.03188971,-0.03713955,0.0290322,0.01788592,0.08265711,0.11929982,0.0484647],"last_embed":{"hash":"c455a207dfb089f7cc59e1139c6d8e231d0c269dc8206087e170c7021fa73b5b","tokens":451}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.04641368,0.00402836,-0.02759994,-0.01979963,-0.01047124,0.0297705,0.04862652,-0.04552205,-0.00959321,-0.00516382,-0.02518007,0.00923101,-0.00461926,-0.01723321,0.02901532,-0.00778936,-0.05451843,-0.03013575,0.05893471,0.00342142,0.00407357,0.00430415,0.02069908,0.02736148,-0.02213473,-0.05491296,0.01488641,-0.07848615,-0.0005969,0.03417132,0.06137597,0.0235882,0.01801459,-0.01862192,-0.0339928,-0.0076577,0.00733321,0.01303326,0.02189528,0.03441364,-0.01317631,0.05009503,-0.03978971,0.02683997,-0.09091485,0.01892079,-0.03261797,-0.049069,0.02169336,-0.01457626,-0.01211743,-0.02668662,0.01305798,0.04186605,0.01360651,0.00673131,0.00105309,-0.05164393,-0.00507201,-0.00896967,0.00466984,0.03290299,0.06448398,0.00746252,0.00932874,0.00747103,-0.00850123,-0.00374658,0.03095542,-0.0061681,-0.072569,-0.02324303,0.04267984,0.02287183,0.0464421,0.00683825,0.02843522,-0.00210162,-0.03256693,0.02539699,-0.11146862,-0.00494526,-0.06231945,0.08198866,0.00649917,-0.02445349,-0.02250837,-0.10416453,-0.00259227,-0.00274907,0.0351435,0.02852035,0.03513378,0.01797399,0.00536298,-0.05766172,-0.02454139,0.0381395,0.00937683,0.00767528,0.00668528,0.01226694,-0.01992072,0.00728083,-0.05184096,-0.07123525,-0.05226965,0.03498208,-0.01721444,-0.00356911,-0.00174144,0.04217271,-0.04265793,-0.04273203,-0.02575572,0.0088482,-0.00689057,-0.03039633,0.04716733,-0.0035617,0.02142248,0.02793076,-0.01835898,-0.02858443,-0.00824365,0.02082152,-0.0454286,-0.01001072,0.0414737,-0.01990462,-0.09660462,0.03536481,-0.02687411,-0.01666049,0.02377409,-0.0301241,0.03428895,-0.09152649,0.04636694,-0.03442474,0.0447331,-0.01103701,-0.03691655,-0.00108766,-0.01455007,0.01092793,-0.08004548,-0.0539061,0.01651252,0.01446084,0.01226005,0.02248559,-0.02755135,-0.04152083,0.03691148,0.00395679,0.08009494,0.0707328,-0.00824558,-0.03060618,-0.01603258,0.02571078,-0.0021717,-0.02455658,0.01430879,-0.00019652,-0.01538114,0.02464215,0.0477349,-0.0179693,-0.01175835,0.00009145,0.0005347,0.02836585,0.00851292,-0.01867871,0.03561405,0.00148263,-0.11709505,-0.05106917,0.03276902,0.01346189,0.01605017,-0.00461901,0.01345967,0.02881821,0.07315359,0.03939265,0.01613505,-0.00971962,0.05385247,0.02774544,0.01850466,0.00098206,0.02286824,0.02771634,-0.02783589,-0.02876598,0.01914121,0.01868133,-0.05254733,0.06401077,-0.05075898,-0.03066728,0.01953447,-0.035578,-0.04391689,-0.03883365,-0.02677327,0.07892241,0.0233342,-0.02193032,0.01764112,-0.01318074,-0.04234063,0.01604513,-0.03411751,0.07832703,0.01646467,-0.023106,0.00927526,-0.06109726,-0.00921955,-0.00789363,0.04120439,-0.03957967,-0.03596268,0.00963426,0.07482426,0.06484003,0.02217049,0.07674743,-0.0311268,-0.03276861,-0.01641613,-0.04071433,0.07182582,0.00280957,-0.0093846,0.04008543,-0.00470319,0.01389348,-0.01409723,-0.03863378,-0.02284058,-0.03708404,-0.0649303,0.08053394,-0.04266518,0.03123382,0.05672177,0.03702395,0.02805537,0.08184422,0.02526439,-0.02797483,-0.06223094,0.02320941,0.03016546,-0.04312257,0.00322557,-0.03681185,-0.02574148,-0.01280911,-0.00726552,-0.00241519,-0.01785023,0.00516501,-0.04779503,0.09062919,0.02108657,-0.02496067,0.06977185,-0.00872886,0.0079942,0.01861332,-0.00457721,-0.03708054,-0.06245644,-0.01789246,0.0220762,-0.01869793,0.00066406,-0.01067801,-0.06246788,0.04913035,0.00359037,0.01109099,0.0386305,-0.01655519,0.02052872,-0.00542657,-0.04878131,0.01205132,-0.06533888,-0.02355026,0.01956414,0.00477498,-0.00603268,-0.04845221,0.04103747,0.03713373,0.02877958,-0.01860451,-0.00165694,-0.00002342,0.00240424,0.04205339,-0.02646606,-0.02881758,0.00171487,-0.03117489,0.03311275,-0.01816775,0.02869991,0.07800561,0.07776207,-0.00421539,-0.01479172,0.03502302,-0.00257913,-0.0385069,-0.00073506,-0.00693764,0.05746098,-0.09847122,-0.03587154,0.01233559,0.04414929,0.05989819,-0.05388511,0.06085669,-0.0050975,-0.02121153,-0.01934999,-0.00731986,0.00066789,-0.06448859,0.03262389,-0.09240492,0.03742528,-0.08816005,-0.00816234,-0.0214117,-0.01029073,0.02866666,-0.03596091,0.00801517,-0.02191467,-0.03353023,0.02876795,-0.00558087,-0.0543552,0.01880753,-0.01474879,0.012174,-0.02764293,-0.00471707,-0.04067998,0.04705772,-0.04118197,-0.03156576,-0.00875622,-0.05119133,-0.0246853,0.00675689,0.0627887,0.02667141,-0.06466497,-0.05297576,0.05172024,-0.00901296,0.00070032,0.0259442,0.02844917,-0.00907862,-0.01065434,0.01478229,-0.01836187,-0.00446415,-0.02824914,-0.04525255,-0.0278336,-0.01817745,-0.06614557,-0.01796228,0.00567189,-0.01540899,0.05285618,-0.03870691,-0.00299438,0.02053014,0.00103985,-0.00719186,0.048195,0.00177381,0.04135815,-0.01806797,-0.0079488,-0.01339451,0.02550947,0.00754318,0.02435145,-0.00309597,-0.02067928,0.04163304,-0.07162971,0.04578754,-0.02146798,0.00792513,-0.00939985,0.07370348,-0.0303455,0.01442008,-0.01256131,0.06304663,0.04122672,-0.05900012,0.02007235,-0.00406684,0.03585783,0.00588897,0.0376893,0.06316461,0.00611624,-0.0116487,-0.02083943,0.00718604,-0.00697851,-0.00959602,-0.08101923,-0.03745646,-0.05641504,-0.01207733,-0.02832893,0.00366811,0.04833475,-0.04136775,0.01787765,-0.00548097,-0.04615949,-0.01013737,-0.06107681,0.00455214,0.05093122,0.01565957,-0.02367913,-0.06479388,0.0046017,-0.01019819,0.03728133,-0.0100742,-0.07187442,-0.06464351,0.0307531,0.01680804,0.01093194,-0.00673926,0.03211384,0.02903437,-0.05972567,0.02218818,0.02930519,0.00142911,0.00744837,0.01353666,0.0032209,0.00643359,0.00393591,0.03926465,0.00944624,0.00681614,-0.0041509,-0.0207411,0.03959411,-0.00037027,-0.02395004,0.00371198,-0.06116652,-0.00376829,0.0095254,-0.02262165,0.01025565,-0.02987843,-0.05487235,-0.01275087,-0.04071701,-0.00169368,0.00090735,-0.04425735,-0.01452919,0.07780372,-0.00493603,0.02715105,0.01968741,0.00735507,0.07615576,0.04553179,0.00187539,0.08941165,0.03321523,0.05261242,-0.02209796,0.03225425,0.04474801,0.00972586,-0.02192794,-0.00155291,0.0393863,-0.03645547,0.01361088,0.01052471,0.07278299,0.04169567,0.02389852,0.00545086,-0.03464696,-0.04918558,0.00630237,-0.04183541,-0.01287237,-0.00171982,-0.04719039,0.01803072,0.02802453,0.02585412,-0.06928352,0.0360306,0.03687913,0.07956287,-0.06024031,0.01043328,-0.00572978,-0.02409872,0.02793559,-0.01106706,0.04604028,-0.00248951,-0.05780734,0.03552239,0.0407623,0.0541184,-0.00198637,0.02883694,-0.03491603,-0.02387291,0.02758306,-0.0219848,0.03886328,-0.03498468,0.01620732,-0.03894686,-0.02068552,-0.0351523,-0.02912841,0.02176964,0.02100225,-0.04370872,0.04182661,0.10207641,0.03416383,0.04330907,-0.00600117,-0.02545611,-0.00583341,0.01053846,-0.03448716,-0.0098794,0.03412294,0.05377228,-0.05923259,-0.01250445,0.05442732,0.05316796,-0.07163507,0.00893,0.02990702,0.03302845,0.0462597,-0.0039094,0.08544799,-0.01431937,0.06963224,-0.02779369,0.02959331,-0.00105614,-0.00388217,0.02387996,0.02338918,0.00539936,0.0548453,-0.05812071,-0.03105139,-0.01331978,-0.00079836,0.00088323,-0.02841572,0.03039365,-0.00229482,-0.02519193,0.00780122,0.00198489,0.00004112,0.02991616,0.01393988,-0.07315325,-0.03233683,-0.05784322,0.04049077,0.05108206,-0.01476782,0.09682368,-0.00033862,-0.04532525,-0.00043707,-0.04831452,-0.02769761,-0.01328709,-0.0329022,-0.00831499,-0.02452251,-0.06869133,-0.0272221,0.02446832,-0.00361548,0.07294944,-0.00692748,0.01937469,0.0209882,-0.05729454,-0.01520191,0.01220134,0.00641162,0.04865215,0.01528763,0.02153764,-0.0229976,-0.02816947,-0.02957343,0.01324854,0.04126903,-0.00473731,-0.01207448,0.03507278,0.0318847,-0.04231989,-0.01840491,0.00089041,-0.00165688,0.00687238,0.00341881,-0.03495903,0.01594423,0.01136746,-0.03346685,0.01094706,0.02192794,0.00224608,0.00719292,0.02318005,0.00162571,-0.02311604,0.01358538,0.00194828,-0.04043271,0.0415894,0.0277019,0.00691305,-0.03888166,-0.00471588,-0.0595081,-0.05697263,0.00614204,0.0286093,-0.00821854,0.01773341,-0.0273381,0.02550595,-0.02500113,-0.00728346,-0.04349537,-0.0201976,-0.01443509,0.04172457,0.03747624,0.05410144,0.00681694,-0.02269234,0.00668283,0.02596965,-0.00153019,-0.00617088,-0.0182368,0.01392548,0.03718908,-0.06606469,-0.00869677,-0.00404515,-0.01014846,0.01676231,-0.01638601,0.01970247,-0.05891332,0.00853621,-0.06238099,-0.0319635,0.0580143,0.02876135,0.06332824,0.03724592,-0.06765775,-0.05048564,-0.00505125,-0.01237067,0.03466846,-0.0559138,0.02473421,0.06578635,-0.03782047,-0.03172743,-0.03376249,0.00823383,0.03620988,0.01614797,0.00111386,-0.04814984,-0.04971705,0.01329706,-0.02497527,0.00618343,-0.01590177,-0.0968658,0.00465445,0.03507978,0.04648044,-0.04658138,-0.0344496,-0.00857182,0.04524651,-0.04477926,-0.00935851,-0.01040563,-0.02481916,-0.02636446,-0.06567313,0.0402127,0.02394827,0.03036146,0.09003226,0.02724737,-0.02173358,0.00460231,0.05065627,0.07827469,0.01008697,0.01481038,-0.01912479,-0.05462078,7.4e-7,-0.03167421,0.01096673,0.02438961,-0.018062,-0.00602117,-0.05961515,-0.04162319,-0.00369145,0.00064513],"last_embed":{"tokens":884,"hash":"7tpnp2"}}},"last_read":{"hash":"7tpnp2","at":1751079989705},"class_name":"SmartSource","outlinks":[{"title":"sql注入","target":"sql注入","line":18},{"title":"反射型XSS攻击(Reflected XSS)","target":"#反射型XSS攻击(Reflected XSS)","line":27},{"title":"存储型XSS (Stored XSS)","target":"#存储型XSS (Stored XSS)","line":28},{"title":"HTTP(S)协议","target":"HTTP(S)协议","line":43},{"title":"DOM","target":"DOM","line":54},{"title":"反射型XSS攻击(Reflected XSS)","target":"#反射型XSS攻击(Reflected XSS)","line":60},{"title":"肉鸡","target":"Bot-Zombie","line":72},{"title":"DDoS","target":"DDoS","line":72},{"title":"DOM","target":"DOM","line":81},{"title":"DOM","target":"DOM","line":84}],"metadata":{"aliases":["cross site script","跨站脚本攻击"],"tags":["网络安全/web安全/XSS攻击"],"发布时间":"2007-01-01","类型":["漏洞"],"编程语言":["JavaScript"],"文档更新日期":"2024-01-12","cssclasses":["editor-full"]},"blocks":{"#---frontmatter---":[1,15],"#简介":[16,23],"#简介#{1}":[17,19],"#简介#{2}":[20,21],"#简介#{3}":[22,23],"#XSS的类型":[24,32],"#XSS的类型#{1}":[25,25],"#XSS的类型#{2}":[26,29],"#XSS的类型#{3}":[30,32],"#反射型XSS攻击(Reflected XSS)":[33,57],"#反射型XSS攻击(Reflected XSS)#{1}":[34,38],"#反射型XSS攻击(Reflected XSS)#非持久性的特点":[39,48],"#反射型XSS攻击(Reflected XSS)#非持久性的特点#{1}":[41,43],"#反射型XSS攻击(Reflected XSS)#非持久性的特点#{2}":[44,44],"#反射型XSS攻击(Reflected XSS)#非持久性的特点#{3}":[45,46],"#反射型XSS攻击(Reflected XSS)#非持久性的特点#{4}":[47,48],"#反射型XSS攻击(Reflected XSS)#防御非持久性的方式":[49,57],"#反射型XSS攻击(Reflected XSS)#防御非持久性的方式#{1}":[51,52],"#反射型XSS攻击(Reflected XSS)#防御非持久性的方式#{2}":[53,53],"#反射型XSS攻击(Reflected XSS)#防御非持久性的方式#{3}":[54,56],"#反射型XSS攻击(Reflected XSS)#防御非持久性的方式#{4}":[57,57],"#存储型XSS (Stored XSS)":[58,82],"#存储型XSS (Stored XSS)#{1}":[59,66],"#存储型XSS (Stored XSS)#持久性的特点":[67,75],"#存储型XSS (Stored XSS)#持久性的特点#{1}":[69,70],"#存储型XSS (Stored XSS)#持久性的特点#{2}":[71,72],"#存储型XSS (Stored XSS)#持久性的特点#{3}":[73,75],"#存储型XSS (Stored XSS)#防御持久性的方式":[76,82],"#存储型XSS (Stored XSS)#防御持久性的方式#{1}":[78,79],"#存储型XSS (Stored XSS)#防御持久性的方式#{2}":[80,80],"#存储型XSS (Stored XSS)#防御持久性的方式#{3}":[81,82],"#DOM-based型":[83,107],"#DOM-based型#{1}":[84,88],"#DOM-based型#常用的的HTML标签":[89,96],"#DOM-based型#常用的的HTML标签#{1}":[91,96],"#DOM-based型#常用的Javascript方法":[97,107],"#DOM-based型#常用的Javascript方法#{1}":[99,107]},"last_import":{"mtime":1740108944640,"size":3540,"at":1749024987587,"hash":"7tpnp2"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/XSS攻击.md","last_embed":{"hash":"7tpnp2","at":1751079989705}},