"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/条件竞争漏洞.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/条件竞争漏洞.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07492787,0.03806281,-0.02941818,-0.01612687,-0.01339363,0.00092639,-0.0009378,0.02247603,0.03679767,0.02048321,0.04381178,-0.09065581,0.08939249,0.05772661,0.01315828,0.02402616,0.00814129,0.07394785,-0.02096732,0.02381716,0.02947272,-0.05047158,-0.00531257,-0.07488126,-0.01670511,0.0194859,0.01881906,0.02324876,-0.00664862,-0.18331601,0.00263656,0.04499582,0.01613676,0.03911693,0.01816622,-0.02550467,-0.01816149,0.05828685,0.01396068,0.01688814,0.0357581,0.03333176,-0.04136417,-0.04456415,-0.01323503,-0.06102186,-0.05899747,-0.02368581,0.02708529,-0.04931569,-0.0351162,-0.02504192,-0.03255826,-0.00688565,-0.02005692,-0.00408821,0.03372078,0.00802577,0.00729889,-0.00521085,0.02182773,0.03601494,-0.19485047,0.04468527,0.02224815,-0.01212958,-0.01330364,-0.00834526,0.00026833,0.03135465,-0.03301662,0.00587306,-0.01073308,0.05118213,0.04766224,0.00413369,0.00765441,-0.03533737,-0.04931348,-0.05162298,-0.01735062,0.02974757,-0.05266271,-0.02279747,0.03185791,0.04580683,-0.02566493,-0.01405913,-0.01134243,0.03807131,-0.01150473,-0.05724173,0.04659425,0.05131222,0.00347588,0.03192572,0.04061823,0.04105618,-0.093619,0.14261679,-0.04974253,-0.02606651,-0.0308745,-0.05182105,-0.00052903,-0.02260049,-0.01425393,-0.04644742,-0.0062911,-0.01503796,-0.00263353,0.00703958,0.06780311,-0.00727812,0.03274877,0.03032797,-0.00916,-0.01549318,-0.03588362,-0.02381278,-0.03507462,0.04039139,0.06845336,-0.02850777,0.00748037,-0.01831299,0.04607727,0.05850855,0.03187748,-0.0159625,0.0454823,-0.03607822,-0.05763498,-0.00001402,-0.03886948,-0.02538558,-0.0668996,0.01762439,0.01163402,-0.05653863,-0.02415142,-0.05139995,0.04366165,-0.08996908,-0.05622407,0.03117367,-0.10334832,-0.03886959,0.04394504,-0.07023484,-0.01552989,0.01641753,-0.02124811,-0.00466013,-0.00598572,-0.0229238,0.04274336,0.13821569,-0.02434305,-0.02759778,0.0121663,0.00629199,-0.08287839,0.1906814,0.01580748,-0.06405141,-0.05134041,0.01296669,-0.02854402,-0.01817641,0.03588213,-0.05325304,0.03518768,0.00351678,0.02492885,-0.01387185,-0.00093257,-0.01057702,-0.03010795,0.05166042,0.04831443,-0.01205085,-0.09586393,0.04116945,-0.00539527,-0.09139983,-0.07213615,-0.00966377,0.0574362,-0.04762056,-0.07926901,-0.0000904,-0.03451717,0.00343336,-0.04372775,-0.05319655,0.04449537,-0.03926047,0.01263191,-0.05521605,0.08367196,0.01036402,-0.0390179,0.01209946,-0.04223576,-0.0423332,-0.01922927,-0.07323825,-0.00094672,0.04861241,0.00881403,0.00041215,-0.07517427,0.00949691,-0.01443676,0.04315529,0.00455432,0.03359332,0.05908645,0.07930912,0.03149242,-0.00529672,-0.03398095,-0.23487543,-0.06243524,-0.03255893,-0.01318046,0.00633046,-0.03496815,0.04567578,-0.03155705,0.09081376,0.0345167,0.06978057,0.01331839,-0.05510208,-0.03077671,0.01587448,-0.01258691,0.01451759,-0.01608048,0.00764037,0.0085621,-0.01833819,0.044656,0.01790827,-0.01395999,0.00986477,-0.01635961,0.15270083,0.02373005,0.03503717,-0.02773018,0.01832759,0.04666305,-0.00593543,-0.07314961,0.06844484,0.02216059,-0.08762201,-0.00596105,-0.02547506,-0.04572942,0.01493606,0.00008257,-0.02898942,-0.07818159,-0.04782619,-0.00827887,0.02521213,0.01465225,-0.01511527,0.09376241,0.00647096,0.03959376,0.05771555,0.0669805,0.03250452,-0.07834454,-0.0841006,0.00608194,-0.01052384,0.02469045,0.05050867,-0.00707184,0.0625949,-0.0345247,0.009441,0.00940024,0.00774746,-0.04466252,-0.05527549,-0.02775075,-0.02534737,0.19263528,0.02658474,-0.02720913,0.05110764,0.02097514,0.0198141,-0.04900165,-0.02647344,-0.00953429,0.02542759,0.03091175,0.05246203,0.03050354,0.00881672,0.0282194,0.03538968,0.00048531,0.11947838,-0.04172311,-0.04660298,-0.01402778,-0.05805038,0.01659839,0.10563954,-0.01727244,-0.27312303,0.03196645,0.00280727,0.00885776,0.02211795,0.02068245,0.06122854,-0.00504893,-0.05979375,0.04451325,-0.07531197,0.07601081,0.00269492,-0.0534968,-0.03994365,-0.04261484,0.05167987,-0.00183604,0.07901098,-0.01748171,0.0124311,0.09006529,0.19980636,0.01699433,0.05199363,0.01624531,0.0262738,0.06192182,0.02494193,0.01767311,-0.01777002,-0.03947499,0.04222405,0.00317047,0.05604636,0.02057681,-0.01697188,0.02196702,0.04476801,0.02122845,-0.01785623,0.06092721,-0.07956705,0.04013322,0.09720276,0.04367728,-0.01245437,-0.01121662,-0.00054052,0.04817293,-0.00621625,-0.02150628,0.00726017,-0.03073844,0.04232415,0.04513614,0.02638145,-0.03612202,-0.03672394,-0.03783369,0.05348733,0.04725102,0.04649279,0.0819454,0.005765],"last_embed":{"hash":"8c48cddcbcde9923990a5ab77e8423b87c657ea8c2ead7b17b8ec63fb413bd85","tokens":504}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.02652007,0.02686921,-0.01239352,0.00688602,-0.00066985,0.02354191,0.03762998,-0.03770941,0.04491571,-0.00964346,0.02013815,-0.05873973,-0.02942127,-0.08953323,-0.00311668,-0.02981751,-0.00438737,0.02427508,0.04851197,-0.05076319,0.00475965,0.00932553,0.04580192,0.02968356,0.0271754,-0.03639352,-0.01559969,-0.02936519,0.03455595,0.00130478,0.08133257,-0.02181574,0.02024937,-0.04873566,-0.10849451,0.00793197,-0.01929413,-0.02610889,0.00856295,0.02266191,-0.05540557,0.00796068,-0.01210354,0.03328875,-0.03912876,-0.03824538,-0.00604236,0.02496146,0.0065777,0.02103111,0.02405418,-0.03265801,-0.01915616,0.01337007,-0.02016739,0.05288185,-0.02915548,-0.07907781,-0.03245083,0.02127294,0.06843069,-0.06344101,0.0738226,0.01125066,0.00912282,0.00198836,0.02316488,-0.01201361,-0.01249568,0.02289324,-0.04103116,0.04086579,-0.00591669,0.01802793,0.05952201,-0.01462198,0.03204552,0.05003812,0.01033005,0.01065413,-0.06604481,0.04610839,0.0113319,0.01780201,0.03238444,-0.01797733,0.03930232,-0.05403034,-0.02339282,0.01572549,-0.00088148,-0.01112136,-0.00204848,-0.01896728,0.00438504,-0.09399902,0.0229667,0.03457751,0.01360663,-0.02508877,0.02391632,-0.00587949,-0.04339739,0.03350153,-0.05377091,-0.05979595,-0.02919062,-0.04224226,-0.05281686,0.03215466,-0.01369368,-0.00713809,0.033166,-0.0267392,-0.06697065,0.0415959,0.00183834,-0.01559835,-0.00271177,0.01756575,0.06134185,0.03170826,-0.01020795,-0.05896742,-0.03682232,0.02883707,0.01362458,-0.01822808,0.05449437,0.01785984,0.01230733,-0.00269586,-0.02390494,-0.03985389,0.02976106,0.02285761,0.02688389,-0.03040909,0.05297582,-0.04042454,-0.00668816,0.00649192,-0.02413307,0.02292015,-0.0012653,0.0661498,-0.0426112,0.02158624,-0.03205465,-0.00220384,0.08579645,0.05916787,-0.02363703,-0.04154741,-0.01507965,-0.00775242,0.01944492,-0.00685641,0.01845547,-0.00470541,-0.00049704,0.00158004,0.04300081,0.00543747,0.0364279,0.00328211,-0.02743027,0.04588643,-0.01952082,-0.00443784,0.0348052,0.03374576,-0.00923451,0.01586324,0.07341442,-0.05321898,0.04316454,0.02817545,-0.06859051,0.01021839,0.01574565,-0.04083101,-0.0019213,-0.02901876,-0.00087737,0.02524685,0.00093386,-0.05598134,0.01505324,0.02802007,0.03395123,-0.0024955,-0.02945664,0.02643055,-0.02539976,0.05573333,0.01645984,-0.01163623,-0.02196878,0.03804582,0.00016048,-0.0204779,-0.02694414,-0.11303383,0.0817576,0.0086091,-0.01036441,-0.01893444,-0.0672829,0.00989492,-0.00652899,-0.00087125,0.01992709,-0.01914197,-0.01565949,-0.03221846,-0.05815078,0.04577647,0.03429844,-0.04733625,0.03473982,-0.02141644,-0.03868356,0.00227388,-0.03159291,-0.06803376,-0.04454676,0.01315608,-0.00145728,-0.00678623,0.01189245,0.06171342,-0.04443428,-0.02595125,-0.02226198,0.03983727,0.0141285,0.05721052,0.03377502,0.02594839,-0.01345159,0.03752411,0.02568114,0.02341752,0.00078991,-0.05104258,-0.06056169,0.05043265,-0.00537548,0.0576617,-0.01831924,-0.02505451,-0.03788267,-0.00051896,0.01961991,-0.00049074,-0.00315393,-0.02816948,0.02324883,-0.06310067,-0.0083478,0.04620241,-0.04131516,0.00147977,-0.0410251,-0.01949314,-0.00506364,0.01665758,-0.08322234,0.07448775,-0.00821518,-0.0175649,0.04657635,0.02022677,0.01075365,-0.01200495,-0.04436028,0.03835714,-0.10885826,-0.00173051,-0.02528067,0.02220858,-0.00180541,0.00624797,-0.005535,0.0446322,-0.02295897,-0.01023801,-0.0381659,-0.0233156,0.03954232,-0.00664011,-0.04609196,0.03898066,-0.07702152,-0.07772316,0.01085117,0.02070143,0.01307456,-0.04135786,-0.02415519,-0.03378814,0.00660245,-0.04217055,-0.00587303,0.01823395,0.00156086,0.06011068,-0.00608149,-0.01840182,0.00528436,-0.03052323,0.00498795,-0.0109431,0.02674797,0.05533998,0.02030672,0.04710997,-0.05430438,0.02126064,-0.04728652,0.02174929,-0.02869549,-0.05945584,-0.0377535,-0.08060271,-0.03309655,0.00358225,0.02871845,0.05570243,-0.01498261,0.00436665,0.00474499,-0.02055403,-0.00221663,0.01955354,-0.03735944,-0.06495637,0.00739694,-0.03090521,0.00896848,-0.05685955,0.02930436,-0.003025,-0.00765357,0.01965166,0.00122735,0.02714905,-0.00313955,-0.01330794,-0.03200299,0.02160409,-0.04738413,-0.00947676,-0.00120741,-0.05764899,-0.02378296,-0.04329985,0.02504578,0.01874888,0.01049201,-0.03090728,-0.03276253,-0.01408413,0.01224806,0.04138976,0.03476393,0.05470146,-0.01373076,-0.00234532,0.03482017,-0.02090572,0.00620332,-0.00326715,0.01690771,-0.00678168,-0.00196365,0.01616195,0.05974542,0.00297742,0.00351325,0.0019742,-0.01602094,-0.02026984,-0.03675802,0.02325508,0.04297096,-0.05659815,-0.02475273,-0.05179838,-0.05649817,-0.00671935,-0.01890423,0.0298627,0.0420381,0.04674558,-0.00261218,-0.01977853,-0.05248263,-0.03559877,0.00872139,-0.00308303,0.02431747,0.02446135,0.0007512,0.0397108,0.00199273,0.02863141,-0.05032187,-0.00592984,0.0486375,-0.00236719,0.04076154,-0.01268835,0.00559694,-0.00291824,0.01355324,-0.0683345,-0.01499033,0.04357455,0.02998889,-0.06493732,0.03591046,-0.00533769,0.01550308,-0.01845052,-0.01145255,-0.0054853,-0.00850546,-0.0025612,-0.01387841,-0.01608252,-0.0766607,-0.01789781,-0.03522924,-0.00100958,0.03758463,-0.04030661,0.02848033,-0.0026247,-0.05239924,-0.00189058,0.03986291,0.00495753,0.00806057,0.02129751,-0.02468198,-0.06319189,0.02097315,-0.01132529,-0.02350214,-0.00491876,-0.00283891,0.00298317,0.03531762,0.03573194,0.0512936,-0.04822652,-0.00736266,-0.03633757,-0.04118722,-0.05603467,0.0622955,-0.03526758,0.03260932,0.08298204,-0.00821338,-0.00451875,-0.04387357,0.01810052,-0.01408982,-0.00758405,0.02264474,0.01756727,0.0530438,0.06479198,-0.0263703,0.03266132,-0.01575313,-0.01811168,0.05822465,0.00553742,0.05331091,-0.00896675,-0.00699566,0.00108844,0.00432967,0.05453609,0.03631781,-0.01988513,0.02235316,0.06688623,0.05184583,0.05008702,0.00372491,-0.0638946,-0.00797563,0.05868131,0.0010971,0.06153519,-0.02848934,0.00279774,-0.03326863,0.06701266,0.01392405,0.02500923,-0.04956823,-0.00508726,0.00539266,-0.01016268,-0.03396214,-0.01609169,0.08204685,0.08908863,-0.02559688,-0.02791721,-0.02759005,0.02632977,-0.02221526,0.00294742,-0.0144841,0.01700844,-0.01385844,0.02387068,0.04608624,-0.00371092,-0.04591456,0.01800082,-0.00838691,0.00790396,-0.05886671,-0.0097982,0.00634761,0.00186082,0.03374243,-0.04933088,-0.00208281,-0.03351239,-0.00098968,0.06094127,0.07163388,0.00951305,-0.01792903,-0.01889792,-0.03900313,-0.02433581,0.0941124,-0.03174432,0.04690251,-0.06421629,-0.00223491,-0.0237108,0.03476489,-0.02815519,-0.10888071,-0.01129004,0.06358424,-0.06354216,0.01923434,0.08257364,0.01328815,0.02853145,0.0269006,0.04418226,-0.02793512,0.02207201,-0.0584808,0.00911666,0.02119609,0.047902,-0.06190007,-0.02118648,0.0412064,-0.02331196,-0.05231467,-0.00119167,0.04084583,-0.01202826,0.04138045,-0.02694657,0.00956213,0.00578686,0.08495244,0.06814725,-0.0206342,0.04983212,0.06015579,0.01491997,-0.01407388,0.00650283,0.05980966,-0.07405397,0.0179925,-0.06582417,-0.05364225,0.01542134,-0.01618109,0.03090399,0.00204067,-0.05115583,0.00389708,0.02959155,-0.00972428,-0.03620898,0.03642187,-0.09361373,-0.00019362,-0.03924817,0.01040216,0.06577197,0.03813722,0.04503879,-0.020515,0.00179407,0.00998537,-0.03585179,-0.03246945,-0.03743697,0.00354946,-0.01412042,-0.0676557,-0.08804837,-0.06298702,-0.01337435,0.00076413,0.10919798,0.04512252,0.00288382,0.02640643,0.00410484,-0.02615226,-0.01965642,-0.00132379,0.05579244,0.0295547,0.02184663,-0.01604792,0.02112629,0.00683959,-0.00059327,0.06149106,0.06583627,0.06320082,0.02428506,0.02320311,-0.01012572,-0.03741683,0.02584411,0.02481275,0.03025105,-0.01192234,0.00119147,-0.02431886,-0.0071839,-0.01138413,0.03149931,0.00826343,-0.00843803,0.0075649,0.09992067,0.01183292,-0.03078143,0.01174779,-0.02743809,0.00212325,-0.00803063,0.01552169,-0.05324965,-0.01766737,-0.00944567,-0.00553082,0.04940065,-0.02426896,0.01981662,0.039071,0.00582416,0.0106316,0.02110311,0.00729242,-0.00495751,-0.04107177,0.03417929,-0.03524819,0.0124858,0.04141956,-0.02489445,0.0368764,0.00870875,-0.0153981,0.01097538,-0.07026628,0.05250072,-0.01234449,-0.03149773,-0.04101904,-0.07966208,0.04859522,-0.05270436,0.02806403,0.03764077,-0.06958217,-0.02157602,-0.03712723,-0.00523847,0.00190316,-0.00662004,0.01433052,0.0434065,0.05331123,0.05090737,-0.03757467,-0.03067019,-0.03270398,-0.01576176,0.05215865,0.00074354,-0.00713464,0.03882303,-0.0869199,0.01823547,-0.02386597,-0.03039069,-0.0018018,0.02859487,-0.06598375,-0.04726192,-0.04883331,0.02348867,-0.00910643,0.0016256,-0.00605275,-0.07332136,-0.00650405,0.00360623,0.03466991,-0.03027099,-0.00536005,0.03690559,0.05698806,-0.02964016,-0.00927849,0.01247067,-0.00961095,-0.01084241,-0.01714379,0.01803912,0.06293271,-0.00193046,0.01581564,0.00985603,-0.01369387,0.06941742,-0.01862941,0.07319272,0.01643082,0.01637907,-0.03350865,-0.00095569,7e-7,0.01478843,-0.01549626,0.03870038,0.00001305,-0.0143887,-0.02864061,-0.0675868,-0.04435765,0.04130324],"last_embed":{"tokens":303,"hash":"1c89kfe"}}},"last_read":{"hash":"1c89kfe","at":1750993410129},"class_name":"SmartSource","outlinks":[{"title":"内存","target":"内存","line":10}],"metadata":{"aliases":["Race Condition Vulnerability"],"英文":"Race Condition Vulnerability","tags":["网络安全/漏洞"]},"blocks":{"#---frontmatter---":[1,7],"#简介":[8,23],"#简介#{1}":[9,11],"#简介#{2}":[12,12],"#简介#{3}":[13,13],"#简介#{4}":[14,14],"#简介#{5}":[15,17],"#简介#{6}":[18,23],"#漏洞原理":[24,29],"#漏洞原理#{1}":[26,26],"#漏洞原理#{2}":[27,27],"#漏洞原理#{3}":[28,29]},"last_import":{"mtime":1747536242445,"size":1343,"at":1749024987637,"hash":"1c89kfe"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/条件竞争漏洞.md","last_embed":{"hash":"1c89kfe","at":1750993410129}},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/条件竞争漏洞.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/条件竞争漏洞.md#---frontmatter---","lines":[1,7],"size":100,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/条件竞争漏洞.md#简介": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/条件竞争漏洞.md#简介","lines":[8,23],"size":386,"outlinks":[{"title":"内存","target":"内存","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/条件竞争漏洞.md#简介#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/条件竞争漏洞.md#简介#{1}","lines":[9,11],"size":105,"outlinks":[{"title":"内存","target":"内存","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/条件竞争漏洞.md#简介#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/条件竞争漏洞.md#简介#{2}","lines":[12,12],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/条件竞争漏洞.md#简介#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/条件竞争漏洞.md#简介#{3}","lines":[13,13],"size":35,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/条件竞争漏洞.md#简介#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/条件竞争漏洞.md#简介#{4}","lines":[14,14],"size":30,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/条件竞争漏洞.md#简介#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/条件竞争漏洞.md#简介#{5}","lines":[15,17],"size":74,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/条件竞争漏洞.md#简介#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/条件竞争漏洞.md#简介#{6}","lines":[18,23],"size":129,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/条件竞争漏洞.md#漏洞原理": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/条件竞争漏洞.md#漏洞原理","lines":[24,29],"size":89,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/条件竞争漏洞.md#漏洞原理#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/条件竞争漏洞.md#漏洞原理#{1}","lines":[26,26],"size":29,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/条件竞争漏洞.md#漏洞原理#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/条件竞争漏洞.md#漏洞原理#{2}","lines":[27,27],"size":21,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/条件竞争漏洞.md#漏洞原理#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/条件竞争漏洞.md#漏洞原理#{3}","lines":[28,29],"size":28,"outlinks":[],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/条件竞争漏洞.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/条件竞争漏洞.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07492787,0.03806281,-0.02941818,-0.01612687,-0.01339363,0.00092639,-0.0009378,0.02247603,0.03679767,0.02048321,0.04381178,-0.09065581,0.08939249,0.05772661,0.01315828,0.02402616,0.00814129,0.07394785,-0.02096732,0.02381716,0.02947272,-0.05047158,-0.00531257,-0.07488126,-0.01670511,0.0194859,0.01881906,0.02324876,-0.00664862,-0.18331601,0.00263656,0.04499582,0.01613676,0.03911693,0.01816622,-0.02550467,-0.01816149,0.05828685,0.01396068,0.01688814,0.0357581,0.03333176,-0.04136417,-0.04456415,-0.01323503,-0.06102186,-0.05899747,-0.02368581,0.02708529,-0.04931569,-0.0351162,-0.02504192,-0.03255826,-0.00688565,-0.02005692,-0.00408821,0.03372078,0.00802577,0.00729889,-0.00521085,0.02182773,0.03601494,-0.19485047,0.04468527,0.02224815,-0.01212958,-0.01330364,-0.00834526,0.00026833,0.03135465,-0.03301662,0.00587306,-0.01073308,0.05118213,0.04766224,0.00413369,0.00765441,-0.03533737,-0.04931348,-0.05162298,-0.01735062,0.02974757,-0.05266271,-0.02279747,0.03185791,0.04580683,-0.02566493,-0.01405913,-0.01134243,0.03807131,-0.01150473,-0.05724173,0.04659425,0.05131222,0.00347588,0.03192572,0.04061823,0.04105618,-0.093619,0.14261679,-0.04974253,-0.02606651,-0.0308745,-0.05182105,-0.00052903,-0.02260049,-0.01425393,-0.04644742,-0.0062911,-0.01503796,-0.00263353,0.00703958,0.06780311,-0.00727812,0.03274877,0.03032797,-0.00916,-0.01549318,-0.03588362,-0.02381278,-0.03507462,0.04039139,0.06845336,-0.02850777,0.00748037,-0.01831299,0.04607727,0.05850855,0.03187748,-0.0159625,0.0454823,-0.03607822,-0.05763498,-0.00001402,-0.03886948,-0.02538558,-0.0668996,0.01762439,0.01163402,-0.05653863,-0.02415142,-0.05139995,0.04366165,-0.08996908,-0.05622407,0.03117367,-0.10334832,-0.03886959,0.04394504,-0.07023484,-0.01552989,0.01641753,-0.02124811,-0.00466013,-0.00598572,-0.0229238,0.04274336,0.13821569,-0.02434305,-0.02759778,0.0121663,0.00629199,-0.08287839,0.1906814,0.01580748,-0.06405141,-0.05134041,0.01296669,-0.02854402,-0.01817641,0.03588213,-0.05325304,0.03518768,0.00351678,0.02492885,-0.01387185,-0.00093257,-0.01057702,-0.03010795,0.05166042,0.04831443,-0.01205085,-0.09586393,0.04116945,-0.00539527,-0.09139983,-0.07213615,-0.00966377,0.0574362,-0.04762056,-0.07926901,-0.0000904,-0.03451717,0.00343336,-0.04372775,-0.05319655,0.04449537,-0.03926047,0.01263191,-0.05521605,0.08367196,0.01036402,-0.0390179,0.01209946,-0.04223576,-0.0423332,-0.01922927,-0.07323825,-0.00094672,0.04861241,0.00881403,0.00041215,-0.07517427,0.00949691,-0.01443676,0.04315529,0.00455432,0.03359332,0.05908645,0.07930912,0.03149242,-0.00529672,-0.03398095,-0.23487543,-0.06243524,-0.03255893,-0.01318046,0.00633046,-0.03496815,0.04567578,-0.03155705,0.09081376,0.0345167,0.06978057,0.01331839,-0.05510208,-0.03077671,0.01587448,-0.01258691,0.01451759,-0.01608048,0.00764037,0.0085621,-0.01833819,0.044656,0.01790827,-0.01395999,0.00986477,-0.01635961,0.15270083,0.02373005,0.03503717,-0.02773018,0.01832759,0.04666305,-0.00593543,-0.07314961,0.06844484,0.02216059,-0.08762201,-0.00596105,-0.02547506,-0.04572942,0.01493606,0.00008257,-0.02898942,-0.07818159,-0.04782619,-0.00827887,0.02521213,0.01465225,-0.01511527,0.09376241,0.00647096,0.03959376,0.05771555,0.0669805,0.03250452,-0.07834454,-0.0841006,0.00608194,-0.01052384,0.02469045,0.05050867,-0.00707184,0.0625949,-0.0345247,0.009441,0.00940024,0.00774746,-0.04466252,-0.05527549,-0.02775075,-0.02534737,0.19263528,0.02658474,-0.02720913,0.05110764,0.02097514,0.0198141,-0.04900165,-0.02647344,-0.00953429,0.02542759,0.03091175,0.05246203,0.03050354,0.00881672,0.0282194,0.03538968,0.00048531,0.11947838,-0.04172311,-0.04660298,-0.01402778,-0.05805038,0.01659839,0.10563954,-0.01727244,-0.27312303,0.03196645,0.00280727,0.00885776,0.02211795,0.02068245,0.06122854,-0.00504893,-0.05979375,0.04451325,-0.07531197,0.07601081,0.00269492,-0.0534968,-0.03994365,-0.04261484,0.05167987,-0.00183604,0.07901098,-0.01748171,0.0124311,0.09006529,0.19980636,0.01699433,0.05199363,0.01624531,0.0262738,0.06192182,0.02494193,0.01767311,-0.01777002,-0.03947499,0.04222405,0.00317047,0.05604636,0.02057681,-0.01697188,0.02196702,0.04476801,0.02122845,-0.01785623,0.06092721,-0.07956705,0.04013322,0.09720276,0.04367728,-0.01245437,-0.01121662,-0.00054052,0.04817293,-0.00621625,-0.02150628,0.00726017,-0.03073844,0.04232415,0.04513614,0.02638145,-0.03612202,-0.03672394,-0.03783369,0.05348733,0.04725102,0.04649279,0.0819454,0.005765],"last_embed":{"hash":"8c48cddcbcde9923990a5ab77e8423b87c657ea8c2ead7b17b8ec63fb413bd85","tokens":504}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.02652007,0.02686921,-0.01239352,0.00688602,-0.00066985,0.02354191,0.03762998,-0.03770941,0.04491571,-0.00964346,0.02013815,-0.05873973,-0.02942127,-0.08953323,-0.00311668,-0.02981751,-0.00438737,0.02427508,0.04851197,-0.05076319,0.00475965,0.00932553,0.04580192,0.02968356,0.0271754,-0.03639352,-0.01559969,-0.02936519,0.03455595,0.00130478,0.08133257,-0.02181574,0.02024937,-0.04873566,-0.10849451,0.00793197,-0.01929413,-0.02610889,0.00856295,0.02266191,-0.05540557,0.00796068,-0.01210354,0.03328875,-0.03912876,-0.03824538,-0.00604236,0.02496146,0.0065777,0.02103111,0.02405418,-0.03265801,-0.01915616,0.01337007,-0.02016739,0.05288185,-0.02915548,-0.07907781,-0.03245083,0.02127294,0.06843069,-0.06344101,0.0738226,0.01125066,0.00912282,0.00198836,0.02316488,-0.01201361,-0.01249568,0.02289324,-0.04103116,0.04086579,-0.00591669,0.01802793,0.05952201,-0.01462198,0.03204552,0.05003812,0.01033005,0.01065413,-0.06604481,0.04610839,0.0113319,0.01780201,0.03238444,-0.01797733,0.03930232,-0.05403034,-0.02339282,0.01572549,-0.00088148,-0.01112136,-0.00204848,-0.01896728,0.00438504,-0.09399902,0.0229667,0.03457751,0.01360663,-0.02508877,0.02391632,-0.00587949,-0.04339739,0.03350153,-0.05377091,-0.05979595,-0.02919062,-0.04224226,-0.05281686,0.03215466,-0.01369368,-0.00713809,0.033166,-0.0267392,-0.06697065,0.0415959,0.00183834,-0.01559835,-0.00271177,0.01756575,0.06134185,0.03170826,-0.01020795,-0.05896742,-0.03682232,0.02883707,0.01362458,-0.01822808,0.05449437,0.01785984,0.01230733,-0.00269586,-0.02390494,-0.03985389,0.02976106,0.02285761,0.02688389,-0.03040909,0.05297582,-0.04042454,-0.00668816,0.00649192,-0.02413307,0.02292015,-0.0012653,0.0661498,-0.0426112,0.02158624,-0.03205465,-0.00220384,0.08579645,0.05916787,-0.02363703,-0.04154741,-0.01507965,-0.00775242,0.01944492,-0.00685641,0.01845547,-0.00470541,-0.00049704,0.00158004,0.04300081,0.00543747,0.0364279,0.00328211,-0.02743027,0.04588643,-0.01952082,-0.00443784,0.0348052,0.03374576,-0.00923451,0.01586324,0.07341442,-0.05321898,0.04316454,0.02817545,-0.06859051,0.01021839,0.01574565,-0.04083101,-0.0019213,-0.02901876,-0.00087737,0.02524685,0.00093386,-0.05598134,0.01505324,0.02802007,0.03395123,-0.0024955,-0.02945664,0.02643055,-0.02539976,0.05573333,0.01645984,-0.01163623,-0.02196878,0.03804582,0.00016048,-0.0204779,-0.02694414,-0.11303383,0.0817576,0.0086091,-0.01036441,-0.01893444,-0.0672829,0.00989492,-0.00652899,-0.00087125,0.01992709,-0.01914197,-0.01565949,-0.03221846,-0.05815078,0.04577647,0.03429844,-0.04733625,0.03473982,-0.02141644,-0.03868356,0.00227388,-0.03159291,-0.06803376,-0.04454676,0.01315608,-0.00145728,-0.00678623,0.01189245,0.06171342,-0.04443428,-0.02595125,-0.02226198,0.03983727,0.0141285,0.05721052,0.03377502,0.02594839,-0.01345159,0.03752411,0.02568114,0.02341752,0.00078991,-0.05104258,-0.06056169,0.05043265,-0.00537548,0.0576617,-0.01831924,-0.02505451,-0.03788267,-0.00051896,0.01961991,-0.00049074,-0.00315393,-0.02816948,0.02324883,-0.06310067,-0.0083478,0.04620241,-0.04131516,0.00147977,-0.0410251,-0.01949314,-0.00506364,0.01665758,-0.08322234,0.07448775,-0.00821518,-0.0175649,0.04657635,0.02022677,0.01075365,-0.01200495,-0.04436028,0.03835714,-0.10885826,-0.00173051,-0.02528067,0.02220858,-0.00180541,0.00624797,-0.005535,0.0446322,-0.02295897,-0.01023801,-0.0381659,-0.0233156,0.03954232,-0.00664011,-0.04609196,0.03898066,-0.07702152,-0.07772316,0.01085117,0.02070143,0.01307456,-0.04135786,-0.02415519,-0.03378814,0.00660245,-0.04217055,-0.00587303,0.01823395,0.00156086,0.06011068,-0.00608149,-0.01840182,0.00528436,-0.03052323,0.00498795,-0.0109431,0.02674797,0.05533998,0.02030672,0.04710997,-0.05430438,0.02126064,-0.04728652,0.02174929,-0.02869549,-0.05945584,-0.0377535,-0.08060271,-0.03309655,0.00358225,0.02871845,0.05570243,-0.01498261,0.00436665,0.00474499,-0.02055403,-0.00221663,0.01955354,-0.03735944,-0.06495637,0.00739694,-0.03090521,0.00896848,-0.05685955,0.02930436,-0.003025,-0.00765357,0.01965166,0.00122735,0.02714905,-0.00313955,-0.01330794,-0.03200299,0.02160409,-0.04738413,-0.00947676,-0.00120741,-0.05764899,-0.02378296,-0.04329985,0.02504578,0.01874888,0.01049201,-0.03090728,-0.03276253,-0.01408413,0.01224806,0.04138976,0.03476393,0.05470146,-0.01373076,-0.00234532,0.03482017,-0.02090572,0.00620332,-0.00326715,0.01690771,-0.00678168,-0.00196365,0.01616195,0.05974542,0.00297742,0.00351325,0.0019742,-0.01602094,-0.02026984,-0.03675802,0.02325508,0.04297096,-0.05659815,-0.02475273,-0.05179838,-0.05649817,-0.00671935,-0.01890423,0.0298627,0.0420381,0.04674558,-0.00261218,-0.01977853,-0.05248263,-0.03559877,0.00872139,-0.00308303,0.02431747,0.02446135,0.0007512,0.0397108,0.00199273,0.02863141,-0.05032187,-0.00592984,0.0486375,-0.00236719,0.04076154,-0.01268835,0.00559694,-0.00291824,0.01355324,-0.0683345,-0.01499033,0.04357455,0.02998889,-0.06493732,0.03591046,-0.00533769,0.01550308,-0.01845052,-0.01145255,-0.0054853,-0.00850546,-0.0025612,-0.01387841,-0.01608252,-0.0766607,-0.01789781,-0.03522924,-0.00100958,0.03758463,-0.04030661,0.02848033,-0.0026247,-0.05239924,-0.00189058,0.03986291,0.00495753,0.00806057,0.02129751,-0.02468198,-0.06319189,0.02097315,-0.01132529,-0.02350214,-0.00491876,-0.00283891,0.00298317,0.03531762,0.03573194,0.0512936,-0.04822652,-0.00736266,-0.03633757,-0.04118722,-0.05603467,0.0622955,-0.03526758,0.03260932,0.08298204,-0.00821338,-0.00451875,-0.04387357,0.01810052,-0.01408982,-0.00758405,0.02264474,0.01756727,0.0530438,0.06479198,-0.0263703,0.03266132,-0.01575313,-0.01811168,0.05822465,0.00553742,0.05331091,-0.00896675,-0.00699566,0.00108844,0.00432967,0.05453609,0.03631781,-0.01988513,0.02235316,0.06688623,0.05184583,0.05008702,0.00372491,-0.0638946,-0.00797563,0.05868131,0.0010971,0.06153519,-0.02848934,0.00279774,-0.03326863,0.06701266,0.01392405,0.02500923,-0.04956823,-0.00508726,0.00539266,-0.01016268,-0.03396214,-0.01609169,0.08204685,0.08908863,-0.02559688,-0.02791721,-0.02759005,0.02632977,-0.02221526,0.00294742,-0.0144841,0.01700844,-0.01385844,0.02387068,0.04608624,-0.00371092,-0.04591456,0.01800082,-0.00838691,0.00790396,-0.05886671,-0.0097982,0.00634761,0.00186082,0.03374243,-0.04933088,-0.00208281,-0.03351239,-0.00098968,0.06094127,0.07163388,0.00951305,-0.01792903,-0.01889792,-0.03900313,-0.02433581,0.0941124,-0.03174432,0.04690251,-0.06421629,-0.00223491,-0.0237108,0.03476489,-0.02815519,-0.10888071,-0.01129004,0.06358424,-0.06354216,0.01923434,0.08257364,0.01328815,0.02853145,0.0269006,0.04418226,-0.02793512,0.02207201,-0.0584808,0.00911666,0.02119609,0.047902,-0.06190007,-0.02118648,0.0412064,-0.02331196,-0.05231467,-0.00119167,0.04084583,-0.01202826,0.04138045,-0.02694657,0.00956213,0.00578686,0.08495244,0.06814725,-0.0206342,0.04983212,0.06015579,0.01491997,-0.01407388,0.00650283,0.05980966,-0.07405397,0.0179925,-0.06582417,-0.05364225,0.01542134,-0.01618109,0.03090399,0.00204067,-0.05115583,0.00389708,0.02959155,-0.00972428,-0.03620898,0.03642187,-0.09361373,-0.00019362,-0.03924817,0.01040216,0.06577197,0.03813722,0.04503879,-0.020515,0.00179407,0.00998537,-0.03585179,-0.03246945,-0.03743697,0.00354946,-0.01412042,-0.0676557,-0.08804837,-0.06298702,-0.01337435,0.00076413,0.10919798,0.04512252,0.00288382,0.02640643,0.00410484,-0.02615226,-0.01965642,-0.00132379,0.05579244,0.0295547,0.02184663,-0.01604792,0.02112629,0.00683959,-0.00059327,0.06149106,0.06583627,0.06320082,0.02428506,0.02320311,-0.01012572,-0.03741683,0.02584411,0.02481275,0.03025105,-0.01192234,0.00119147,-0.02431886,-0.0071839,-0.01138413,0.03149931,0.00826343,-0.00843803,0.0075649,0.09992067,0.01183292,-0.03078143,0.01174779,-0.02743809,0.00212325,-0.00803063,0.01552169,-0.05324965,-0.01766737,-0.00944567,-0.00553082,0.04940065,-0.02426896,0.01981662,0.039071,0.00582416,0.0106316,0.02110311,0.00729242,-0.00495751,-0.04107177,0.03417929,-0.03524819,0.0124858,0.04141956,-0.02489445,0.0368764,0.00870875,-0.0153981,0.01097538,-0.07026628,0.05250072,-0.01234449,-0.03149773,-0.04101904,-0.07966208,0.04859522,-0.05270436,0.02806403,0.03764077,-0.06958217,-0.02157602,-0.03712723,-0.00523847,0.00190316,-0.00662004,0.01433052,0.0434065,0.05331123,0.05090737,-0.03757467,-0.03067019,-0.03270398,-0.01576176,0.05215865,0.00074354,-0.00713464,0.03882303,-0.0869199,0.01823547,-0.02386597,-0.03039069,-0.0018018,0.02859487,-0.06598375,-0.04726192,-0.04883331,0.02348867,-0.00910643,0.0016256,-0.00605275,-0.07332136,-0.00650405,0.00360623,0.03466991,-0.03027099,-0.00536005,0.03690559,0.05698806,-0.02964016,-0.00927849,0.01247067,-0.00961095,-0.01084241,-0.01714379,0.01803912,0.06293271,-0.00193046,0.01581564,0.00985603,-0.01369387,0.06941742,-0.01862941,0.07319272,0.01643082,0.01637907,-0.03350865,-0.00095569,7e-7,0.01478843,-0.01549626,0.03870038,0.00001305,-0.0143887,-0.02864061,-0.0675868,-0.04435765,0.04130324],"last_embed":{"tokens":303,"hash":"1c89kfe"}}},"last_read":{"hash":"1c89kfe","at":1751079995990},"class_name":"SmartSource","outlinks":[{"title":"内存","target":"内存","line":10}],"metadata":{"aliases":["Race Condition Vulnerability"],"英文":"Race Condition Vulnerability","tags":["网络安全/漏洞"]},"blocks":{"#---frontmatter---":[1,7],"#简介":[8,23],"#简介#{1}":[9,11],"#简介#{2}":[12,12],"#简介#{3}":[13,13],"#简介#{4}":[14,14],"#简介#{5}":[15,17],"#简介#{6}":[18,23],"#漏洞原理":[24,29],"#漏洞原理#{1}":[26,26],"#漏洞原理#{2}":[27,27],"#漏洞原理#{3}":[28,29]},"last_import":{"mtime":1747536242445,"size":1343,"at":1749024987637,"hash":"1c89kfe"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/漏洞/漏洞类型/条件竞争漏洞.md","last_embed":{"hash":"1c89kfe","at":1751079995990}},