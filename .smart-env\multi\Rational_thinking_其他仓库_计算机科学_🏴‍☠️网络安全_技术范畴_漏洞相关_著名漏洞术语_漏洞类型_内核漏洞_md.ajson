"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08641038,-0.01147018,0.01538746,-0.04498573,-0.00511847,-0.029908,-0.01172448,0.04445242,0.07385953,-0.00495248,-0.01722306,-0.05664203,0.03963515,0.04747397,0.08337288,0.01920881,-0.01229348,0.04860479,-0.02269854,-0.02794174,0.06967381,-0.0540745,-0.02314281,-0.06670938,-0.01714483,0.03063511,0.0370228,-0.02173204,-0.01991093,-0.15740791,0.01056984,0.0081984,0.02986073,0.03031204,-0.02505395,-0.04387314,0.01177804,0.055145,-0.00368316,0.02983718,0.01391518,0.02071612,-0.01219477,-0.02994927,-0.02901115,-0.05755708,-0.04964529,-0.019504,-0.00643307,-0.03062863,-0.04044107,-0.02898482,-0.06120814,-0.01361495,-0.04846971,-0.01541599,0.03337659,-0.01074896,0.08740021,-0.0118896,0.02048022,0.01306722,-0.2228092,0.05228478,0.01496358,-0.04681947,0.00828891,-0.04077718,0.00788688,0.05126577,-0.03154027,0.04156727,-0.01852969,0.05500842,0.06512947,-0.00510794,0.01362675,-0.04159614,-0.04912531,-0.04930859,-0.0049755,0.05680474,-0.03941016,-0.01213107,0.00428859,0.04595467,-0.04068599,-0.00689938,0.01227382,0.01075186,-0.02930008,-0.06656129,-0.01003641,0.03381017,0.02595232,0.02496245,0.01838301,0.04408789,-0.09591383,0.1193606,-0.04747657,0.0005228,-0.02636973,-0.06305354,-0.00279249,-0.03882734,-0.00540543,-0.0298378,-0.01215372,-0.00543724,-0.06323797,-0.01436887,0.08563398,-0.03984557,0.07071325,0.00606576,0.01964323,-0.02973221,-0.0057203,-0.0458992,-0.02020053,0.03365497,0.05446136,-0.02860457,-0.00853274,-0.03165798,0.02478296,0.06811707,0.01112372,0.02496826,0.0541476,-0.05006851,-0.06675242,-0.0417835,-0.04017438,0.00561323,-0.05504529,0.04012882,0.00416435,-0.02950543,-0.03660588,-0.02565447,0.01788012,-0.11897206,-0.04808605,0.1090075,-0.05998195,-0.03194957,0.04110719,-0.0109816,0.01328414,0.05335886,-0.03439524,-0.01999857,-0.01881685,-0.00998726,0.09091289,0.14697441,-0.03857223,-0.02215044,0.0014285,0.01335875,-0.08839787,0.18246745,0.01209787,-0.04969284,-0.03603796,0.01519869,0.040344,-0.00716937,0.02435739,-0.03077781,0.05297661,-0.00273375,0.06039138,-0.00964535,-0.02919915,-0.01423192,-0.01499975,0.05053566,0.10030401,-0.03532088,-0.07296741,0.02604691,0.01524058,-0.09363775,-0.03385723,-0.05548789,-0.0049069,-0.05646953,-0.13538791,0.04759813,-0.02959883,0.00912874,-0.05600086,-0.07638932,0.02609114,0.03139849,0.05125001,-0.03564479,0.07976037,0.04133965,-0.02697208,0.03044122,-0.04635134,-0.04233517,-0.01927458,-0.01387778,0.02255694,0.02898272,0.00179074,0.00406419,-0.03117098,0.01423278,-0.00681766,0.02594193,0.0127524,0.01971745,0.03872059,0.05508889,0.01156042,-0.02724167,-0.08178226,-0.22304036,-0.02752665,-0.00268047,-0.0140183,-0.01634443,-0.01165011,0.06162106,-0.00274705,0.08459844,0.04982061,0.06621899,0.02923677,-0.07327503,-0.01456439,0.0303052,0.00889546,0.00501651,-0.01084844,-0.0238588,-0.01529131,0.01111847,0.06217856,-0.00176964,-0.03764651,0.02441594,-0.03981892,0.11996417,0.02879199,0.04041389,0.01795115,0.0339542,0.05026423,0.02350051,-0.08748841,0.00620537,0.04785728,-0.03829907,-0.04895132,-0.02361898,-0.02591152,0.00614372,-0.00768412,-0.03661405,-0.08779961,0.00178984,-0.02035185,-0.00550279,0.01733827,0.03231649,0.05738517,-0.01927805,-0.00062754,0.00747631,-0.01031311,0.0077519,-0.02716952,-0.03999805,-0.01699125,-0.00849307,0.02992162,0.04246273,-0.00774216,0.02579784,0.00793854,0.0061259,-0.01307168,-0.00873016,0.00478183,-0.05953054,-0.01576235,-0.01830485,0.17055203,0.00175304,-0.05415082,0.04745559,0.03609778,-0.00319996,-0.01267654,-0.00507408,0.00010451,0.0402723,0.01452363,0.04234517,-0.00912064,-0.00647178,0.01319124,0.02180728,-0.01271668,0.08498736,-0.08313683,-0.07340957,-0.00192342,-0.04847824,0.02022272,0.10981499,-0.02747934,-0.28334841,0.03812531,-0.00776733,-0.01718873,0.08427498,0.04827091,0.03592538,-0.00376363,-0.05796225,0.01405155,-0.04553892,0.07147627,-0.01270336,-0.02353838,-0.01911587,-0.01797793,0.0688787,0.00009068,0.05844338,-0.02287259,0.01108356,0.04901499,0.20440075,-0.01491646,0.04746914,0.00211286,0.01098818,0.05234742,0.02678373,0.032711,0.03629896,-0.06264009,0.02212033,-0.03544256,0.03652454,0.05616448,-0.04223771,0.01870658,0.06213564,0.02755422,-0.01326744,0.07092161,-0.07862341,0.02549098,0.08118177,0.01803149,0.02274271,-0.04845094,0.00036183,0.0547076,0.02784411,0.04269101,0.00267917,0.01387348,0.05380601,0.03436226,0.00897063,-0.04286394,-0.07089537,-0.019337,0.02578593,0.03522338,0.08725555,0.1076185,0.07271516],"last_embed":{"hash":"067f8f78b1c77ccd74fe57834e9d90a0b13e8b5f818bfad40dcf30d0110b730b","tokens":465}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.04393384,0.00839292,-0.00778957,0.02202913,-0.01210221,0.01510802,0.05636985,-0.02548672,0.00447676,-0.0140099,-0.0443634,-0.00137892,-0.07070726,-0.07225204,-0.01224583,0.00550491,0.03251331,0.04795779,0.02267039,0.00654898,-0.02001152,0.03426712,0.04853172,0.03267295,0.02476964,-0.03246582,0.0248386,-0.04527462,0.03423374,0.04671099,0.09718641,0.0088871,-0.0085591,-0.03402323,-0.08517492,0.00960256,0.03908081,-0.05394663,-0.01811904,0.05226029,-0.01363044,0.02463753,0.00147897,0.03334925,-0.08980708,-0.00803465,0.02589523,-0.00420696,-0.00854547,0.04235535,0.05009465,-0.07817601,0.01244667,0.03965726,-0.02830173,0.04560228,-0.05198439,-0.05585586,-0.02704277,0.00506701,0.01742799,0.00340905,0.09079864,0.03494736,0.02544341,0.04526643,-0.03650708,-0.04024619,0.01151372,0.00382628,-0.0497316,0.01476716,0.00125512,-0.01230864,0.09492451,-0.00950347,0.03401755,0.02240992,-0.02682474,-0.00943734,-0.10143843,-0.01332737,-0.01181939,0.07323306,0.04417921,-0.05039956,0.02798845,-0.01942252,-0.00843783,-0.01788848,-0.01692137,0.04421191,-0.00215787,0.00745118,0.04503815,-0.07855293,0.00426916,-0.00261281,-0.02341148,-0.03939778,-0.00342482,-0.00203429,-0.07777441,0.00376759,-0.04875528,-0.0664094,-0.05193979,0.04028028,-0.03156481,-0.02478791,0.01265592,0.0169035,-0.02574857,-0.01645011,-0.07523426,0.04312128,-0.03420969,-0.02588553,0.03028855,0.05233011,0.01989428,0.04246381,0.02344825,-0.0522467,-0.01082415,-0.00061581,-0.01645374,-0.02855355,0.03818665,-0.03583285,0.01900578,0.04046715,-0.04273066,-0.04719422,-0.03062623,0.0259554,0.04649401,-0.03084687,0.04701567,-0.03122485,0.01390374,-0.02634759,0.00607355,0.01114336,-0.0362475,0.07864832,-0.03111383,-0.0333066,-0.03612684,0.04785892,0.0280405,0.02629811,0.03656754,-0.05467877,0.02967551,-0.02662282,0.02682122,0.05878347,-0.01904942,-0.02752563,-0.02448664,0.03108096,0.04993399,0.02682561,0.01771204,-0.02249432,-0.01598461,0.03226908,-0.00494515,-0.00087091,0.00565115,0.02573389,0.00510526,0.01155075,-0.00298571,-0.04008,0.01169959,0.01524126,-0.02015283,-0.01348,0.02171233,-0.03908717,-0.0020308,-0.00306087,0.00085681,0.05689847,-0.01877662,-0.00760333,0.06629086,0.01893038,0.03473441,0.02733168,0.00061202,0.01338754,0.02697016,0.02023192,-0.02141113,-0.03978139,-0.03742055,0.03945643,0.03040026,-0.06326694,0.01469936,-0.05372581,0.05403719,-0.00805612,0.01513172,-0.05866826,-0.07622945,0.06303586,0.01171087,0.01900124,-0.00258272,0.02020388,-0.01952594,0.08117085,-0.04369358,0.04023166,0.04696145,-0.00915297,0.00765374,-0.0229252,-0.06719358,0.00623006,-0.03440911,-0.01352207,0.01448436,0.01717103,0.01728426,-0.03904384,0.03078444,0.03840151,-0.06140536,-0.00522393,-0.01861509,0.0297133,0.03774358,0.05137392,0.02297325,0.0042498,0.03131023,0.02405529,-0.01392466,-0.00889943,-0.00100801,-0.04220814,-0.06850253,0.03060129,-0.00202547,0.077172,-0.00687097,-0.02985234,-0.01033974,0.04209277,0.0358047,-0.01997595,-0.00555963,-0.02412944,0.00552291,-0.01978403,0.0117665,-0.00440566,-0.03783785,0.01371659,-0.02347637,-0.02800838,-0.0399089,0.02892226,-0.04633801,0.04408915,0.01428062,-0.03075662,0.07422718,-0.00684989,-0.03500787,0.02465209,-0.0412874,0.02176542,-0.05822216,-0.01378629,-0.04579541,-0.0364172,-0.00815278,0.00585104,-0.04134175,0.0223918,0.01293979,-0.01233401,0.01610442,-0.00448553,0.03593035,0.00703755,-0.05316188,0.05904967,-0.07489512,-0.024284,-0.00367522,0.00356997,-0.03533378,-0.04571478,0.00219234,0.04939215,0.03529311,-0.01166734,-0.01435781,0.0168004,0.004055,0.0436018,-0.03848225,-0.04717148,0.00383961,-0.05113709,0.02561877,-0.03976132,0.03466603,0.08143617,0.01299108,0.01209496,-0.03852573,-0.00891517,-0.08033915,0.04561849,0.00795357,-0.00975971,0.02472248,-0.06764451,0.0361026,0.00926969,0.04808805,0.06095877,-0.01953876,0.01864089,0.011154,-0.02356671,0.03704621,0.00088397,-0.02551372,-0.04301813,0.03299155,-0.02112399,0.04050899,-0.04563463,0.02271298,-0.03413384,0.01083618,0.07247137,-0.00640943,-0.00721435,-0.01602994,-0.0243643,0.01161848,-0.0346491,-0.02627966,-0.02974181,0.03745785,-0.00990851,-0.03466132,-0.00539425,-0.00630499,0.01992099,-0.00710564,-0.04379334,-0.03222048,-0.00324476,-0.0452019,0.03545731,0.04135104,0.05758606,-0.05640738,-0.0677687,0.01195419,0.02330197,-0.02828876,0.01389736,0.02796035,-0.01324655,0.00433311,0.01075892,0.00746856,-0.0488769,-0.00506182,-0.04094698,-0.03329688,-0.04367782,-0.07204086,-0.05881642,-0.01094131,-0.03882527,-0.08478753,-0.03627836,-0.02465899,0.03946967,-0.04219878,0.02509858,-0.00026929,0.02500128,0.04656283,0.0045309,-0.01578515,-0.01482585,0.00774435,-0.02346668,0.03840912,0.03106134,0.00003172,0.05932136,-0.0250094,0.03608798,-0.04141454,-0.05094888,0.0178292,0.0659825,0.09406868,0.00879411,-0.001905,0.01073196,-0.01006799,-0.05630707,0.02223875,0.08176034,0.01336728,-0.05044169,0.02846688,0.05945719,0.02994554,0.00504579,-0.00818481,-0.00356467,-0.00175068,0.03107196,-0.01222782,-0.03546628,-0.05873853,-0.02423463,-0.03689743,-0.01143621,0.06919175,-0.06342544,-0.01836918,0.07856715,-0.05362595,-0.01778556,0.02999767,0.00117743,0.05404491,0.04913116,-0.02481845,-0.08343446,-0.00345222,0.01071645,-0.00943368,0.01250962,-0.06214062,0.00866364,0.03610701,0.00729469,0.01219135,0.00857688,-0.0277071,-0.00722589,-0.01459629,-0.02998735,0.00845453,-0.00858534,0.01501486,0.02556098,0.04377755,-0.00298694,-0.08861507,0.00296506,-0.00651569,-0.02032677,-0.01686702,0.01571704,0.01874153,0.04353672,-0.01167253,0.02011198,-0.0134873,0.02867638,0.03710068,0.04582249,0.02696631,0.00787674,0.03763125,0.06031742,-0.04190125,0.02749673,0.06772413,-0.00854906,-0.01613788,0.03496348,0.0220489,0.03478739,0.02006653,-0.02336247,-0.01187599,0.04578342,0.00926582,0.00981907,-0.04138917,0.02484745,-0.00133521,-0.00289433,0.00457819,0.02753626,-0.03096966,-0.0001953,0.04106826,-0.0231776,-0.02904912,-0.01175686,0.08778153,0.05933774,-0.04876518,-0.07049475,-0.03946655,0.01568953,-0.0054548,0.03290568,-0.05937812,0.02899931,-0.05892136,-0.00222565,0.00760067,-0.03964286,-0.04316691,0.00540808,-0.01282879,-0.01986006,-0.06307619,-0.00579998,0.00376646,-0.01381256,0.03388257,-0.0597327,0.01442625,-0.04808458,-0.0162143,0.01980916,0.06604049,-0.03291231,0.00947062,0.02773932,-0.03802266,-0.00944118,0.00036731,-0.00395758,0.07142242,-0.01981204,-0.00577774,0.01688572,0.01625531,0.0044757,-0.04699896,-0.0078577,0.02755629,-0.04445111,-0.0270276,0.04455457,0.0114786,0.0666211,-0.07540277,0.03842149,-0.06804742,-0.00556334,-0.02918531,0.02861894,0.01748491,0.04545543,-0.01906496,-0.02277296,0.06250906,0.00084214,-0.05512311,0.0020543,0.02464306,-0.03749016,0.03709883,-0.00766336,-0.0042254,0.0159296,0.04181537,0.00916457,-0.02639744,0.01678527,-0.01359715,0.01775604,0.01580353,-0.0072924,-0.01107866,-0.00442112,-0.0090989,-0.0235103,-0.01175934,-0.00944442,-0.04947472,0.01779919,0.0250848,-0.0294799,0.01020013,0.02526418,-0.00980289,-0.03195212,0.00534036,-0.11551247,-0.05678226,-0.02998206,0.04759149,0.051513,0.00945689,-0.0112157,0.02261897,-0.02025606,0.02503949,-0.03259019,0.01488433,-0.0164728,-0.02820079,0.02467933,-0.01064659,-0.06515031,-0.05285283,-0.00147526,-0.01420568,0.07555582,-0.00516588,0.01218297,0.06877063,0.01318785,-0.03974178,-0.04988261,0.01825737,0.06749629,-0.01034753,-0.00090673,-0.04914178,0.00375556,-0.00142043,-0.02312651,0.06554241,0.04703435,0.02722062,0.00720652,0.02431115,-0.05761585,0.00404744,0.00902573,0.0565339,-0.0414404,0.00749739,-0.0697249,-0.00193428,0.00186507,-0.03297988,0.00129194,-0.00430968,-0.00310656,0.02355485,0.07353166,-0.03608505,-0.03614165,-0.04357616,-0.02486235,-0.01095083,-0.00222999,0.0813997,0.00849228,-0.04930861,0.04877322,-0.05071925,-0.01296345,-0.02216375,0.00661952,-0.03114922,0.02051485,0.00833971,0.06016258,-0.02007484,-0.05113852,-0.05758122,0.01815582,-0.01890432,-0.01209061,0.05098597,-0.03577532,-0.01186854,-0.02017351,-0.02220344,0.033116,-0.06610825,0.03474414,0.00673519,-0.02377106,-0.03692953,-0.05486399,0.04908925,-0.05563159,0.01764507,0.01694855,-0.03988595,-0.03506213,-0.01267848,0.00605149,-0.05184145,-0.02110803,0.01019058,-0.01155332,0.03341264,0.01107066,-0.05500676,0.02791323,-0.01549898,0.0617702,0.01713239,-0.03866522,-0.00850043,0.07824265,-0.08871824,0.00194972,-0.04410969,-0.00517487,-0.00058188,0.05038602,-0.03853416,-0.05095518,-0.0197947,-0.03322733,0.02379017,-0.00062331,0.01077508,-0.0784816,0.03751164,-0.00440558,0.0150204,-0.00236177,0.00752569,0.00402351,0.0445335,-0.00601102,-0.01237981,0.00915321,-0.01978248,-0.0292065,-0.03933611,0.03890827,0.0588726,0.01433069,0.03710818,0.01508361,-0.03836625,0.04878529,-0.01740638,0.10256288,0.00805625,-0.00471584,-0.02766707,-0.04590338,8.5e-7,0.03637297,-0.00777787,0.01404735,0.00634046,-0.01151211,0.00597338,-0.01802057,-0.03854864,0.05475771],"last_embed":{"tokens":613,"hash":"oyiytq"}}},"last_read":{"hash":"oyiytq","at":1750993410231},"class_name":"SmartSource","outlinks":[{"title":"内核","target":"Kernel","line":12},{"title":"内核","target":"Kernel","line":14},{"title":"Linux","target":"Linux","line":14},{"title":"windows系统","target":"windows系统","line":14},{"title":"windows系统","target":"windows系统","line":16},{"title":"windows系统","target":"windows系统","line":16},{"title":"内核","target":"Kernel","line":19},{"title":"内存","target":"内存","line":29},{"title":"缓冲区溢出","target":"缓冲区溢出","line":30},{"title":"内核","target":"Kernel","line":31},{"title":"内核","target":"Kernel","line":31},{"title":"内核","target":"Kernel","line":33},{"title":"内核","target":"Kernel","line":33},{"title":"内核","target":"Kernel","line":35}],"metadata":{"aliases":null,"tags":["网络安全/漏洞"]},"blocks":{"#---frontmatter---":[1,5],"#简介":[6,10],"#简介#{1}":[7,7],"#简介#{2}":[8,9],"#简介#{3}":[10,10],"#基于提权方面的考虑":[11,23],"#基于提权方面的考虑#{1}":[12,14],"#基于提权方面的考虑#{2}":[15,15],"#基于提权方面的考虑#{3}":[16,17],"#基于提权方面的考虑#{4}":[18,18],"#基于提权方面的考虑#{5}":[19,21],"#基于提权方面的考虑#{6}":[22,23],"#内核漏洞的主要类型":[24,53],"#内核漏洞的主要类型#{1}":[26,27],"#内核漏洞的主要类型#{2}":[28,29],"#内核漏洞的主要类型#{3}":[30,31],"#内核漏洞的主要类型#{4}":[32,33],"#内核漏洞的主要类型#{5}":[34,36],"#内核漏洞的主要类型#{6}":[37,39],"#内核漏洞的主要类型#{7}":[40,40],"#内核漏洞的主要类型#相关的内核漏洞":[41,53],"#内核漏洞的主要类型#相关的内核漏洞#{1}":[42,53]},"last_import":{"mtime":1747536242696,"size":2462,"at":1749024987637,"hash":"oyiytq"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md","last_embed":{"hash":"oyiytq","at":1750993410231}},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#---frontmatter---","lines":[1,5],"size":35,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#简介": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#简介","lines":[6,10],"size":82,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#简介#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#简介#{1}","lines":[7,7],"size":36,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#简介#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#简介#{2}","lines":[8,9],"size":36,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#简介#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#简介#{3}","lines":[10,10],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#基于提权方面的考虑": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#基于提权方面的考虑","lines":[11,23],"size":342,"outlinks":[{"title":"内核","target":"Kernel","line":2},{"title":"内核","target":"Kernel","line":4},{"title":"Linux","target":"Linux","line":4},{"title":"windows系统","target":"windows系统","line":4},{"title":"windows系统","target":"windows系统","line":6},{"title":"windows系统","target":"windows系统","line":6},{"title":"内核","target":"Kernel","line":9}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#基于提权方面的考虑#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#基于提权方面的考虑#{1}","lines":[12,14],"size":145,"outlinks":[{"title":"内核","target":"Kernel","line":1},{"title":"内核","target":"Kernel","line":3},{"title":"Linux","target":"Linux","line":3},{"title":"windows系统","target":"windows系统","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#基于提权方面的考虑#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#基于提权方面的考虑#{2}","lines":[15,15],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#基于提权方面的考虑#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#基于提权方面的考虑#{3}","lines":[16,17],"size":71,"outlinks":[{"title":"windows系统","target":"windows系统","line":1},{"title":"windows系统","target":"windows系统","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#基于提权方面的考虑#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#基于提权方面的考虑#{4}","lines":[18,18],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#基于提权方面的考虑#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#基于提权方面的考虑#{5}","lines":[19,21],"size":99,"outlinks":[{"title":"内核","target":"Kernel","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#基于提权方面的考虑#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#基于提权方面的考虑#{6}","lines":[22,23],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#内核漏洞的主要类型": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#内核漏洞的主要类型","lines":[24,53],"size":836,"outlinks":[{"title":"内存","target":"内存","line":6},{"title":"缓冲区溢出","target":"缓冲区溢出","line":7},{"title":"内核","target":"Kernel","line":8},{"title":"内核","target":"Kernel","line":8},{"title":"内核","target":"Kernel","line":10},{"title":"内核","target":"Kernel","line":10},{"title":"内核","target":"Kernel","line":12}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#内核漏洞的主要类型#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#内核漏洞的主要类型#{1}","lines":[26,27],"size":84,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#内核漏洞的主要类型#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#内核漏洞的主要类型#{2}","lines":[28,29],"size":71,"outlinks":[{"title":"内存","target":"内存","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#内核漏洞的主要类型#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#内核漏洞的主要类型#{3}","lines":[30,31],"size":97,"outlinks":[{"title":"缓冲区溢出","target":"缓冲区溢出","line":1},{"title":"内核","target":"Kernel","line":2},{"title":"内核","target":"Kernel","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#内核漏洞的主要类型#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#内核漏洞的主要类型#{4}","lines":[32,33],"size":93,"outlinks":[{"title":"内核","target":"Kernel","line":2},{"title":"内核","target":"Kernel","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#内核漏洞的主要类型#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#内核漏洞的主要类型#{5}","lines":[34,36],"size":107,"outlinks":[{"title":"内核","target":"Kernel","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#内核漏洞的主要类型#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#内核漏洞的主要类型#{6}","lines":[37,39],"size":69,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#内核漏洞的主要类型#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#内核漏洞的主要类型#{7}","lines":[40,40],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#内核漏洞的主要类型#相关的内核漏洞": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#内核漏洞的主要类型#相关的内核漏洞","lines":[41,53],"size":292,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#内核漏洞的主要类型#相关的内核漏洞#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md#内核漏洞的主要类型#相关的内核漏洞#{1}","lines":[42,53],"size":281,"outlinks":[],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.08641038,-0.01147018,0.01538746,-0.04498573,-0.00511847,-0.029908,-0.01172448,0.04445242,0.07385953,-0.00495248,-0.01722306,-0.05664203,0.03963515,0.04747397,0.08337288,0.01920881,-0.01229348,0.04860479,-0.02269854,-0.02794174,0.06967381,-0.0540745,-0.02314281,-0.06670938,-0.01714483,0.03063511,0.0370228,-0.02173204,-0.01991093,-0.15740791,0.01056984,0.0081984,0.02986073,0.03031204,-0.02505395,-0.04387314,0.01177804,0.055145,-0.00368316,0.02983718,0.01391518,0.02071612,-0.01219477,-0.02994927,-0.02901115,-0.05755708,-0.04964529,-0.019504,-0.00643307,-0.03062863,-0.04044107,-0.02898482,-0.06120814,-0.01361495,-0.04846971,-0.01541599,0.03337659,-0.01074896,0.08740021,-0.0118896,0.02048022,0.01306722,-0.2228092,0.05228478,0.01496358,-0.04681947,0.00828891,-0.04077718,0.00788688,0.05126577,-0.03154027,0.04156727,-0.01852969,0.05500842,0.06512947,-0.00510794,0.01362675,-0.04159614,-0.04912531,-0.04930859,-0.0049755,0.05680474,-0.03941016,-0.01213107,0.00428859,0.04595467,-0.04068599,-0.00689938,0.01227382,0.01075186,-0.02930008,-0.06656129,-0.01003641,0.03381017,0.02595232,0.02496245,0.01838301,0.04408789,-0.09591383,0.1193606,-0.04747657,0.0005228,-0.02636973,-0.06305354,-0.00279249,-0.03882734,-0.00540543,-0.0298378,-0.01215372,-0.00543724,-0.06323797,-0.01436887,0.08563398,-0.03984557,0.07071325,0.00606576,0.01964323,-0.02973221,-0.0057203,-0.0458992,-0.02020053,0.03365497,0.05446136,-0.02860457,-0.00853274,-0.03165798,0.02478296,0.06811707,0.01112372,0.02496826,0.0541476,-0.05006851,-0.06675242,-0.0417835,-0.04017438,0.00561323,-0.05504529,0.04012882,0.00416435,-0.02950543,-0.03660588,-0.02565447,0.01788012,-0.11897206,-0.04808605,0.1090075,-0.05998195,-0.03194957,0.04110719,-0.0109816,0.01328414,0.05335886,-0.03439524,-0.01999857,-0.01881685,-0.00998726,0.09091289,0.14697441,-0.03857223,-0.02215044,0.0014285,0.01335875,-0.08839787,0.18246745,0.01209787,-0.04969284,-0.03603796,0.01519869,0.040344,-0.00716937,0.02435739,-0.03077781,0.05297661,-0.00273375,0.06039138,-0.00964535,-0.02919915,-0.01423192,-0.01499975,0.05053566,0.10030401,-0.03532088,-0.07296741,0.02604691,0.01524058,-0.09363775,-0.03385723,-0.05548789,-0.0049069,-0.05646953,-0.13538791,0.04759813,-0.02959883,0.00912874,-0.05600086,-0.07638932,0.02609114,0.03139849,0.05125001,-0.03564479,0.07976037,0.04133965,-0.02697208,0.03044122,-0.04635134,-0.04233517,-0.01927458,-0.01387778,0.02255694,0.02898272,0.00179074,0.00406419,-0.03117098,0.01423278,-0.00681766,0.02594193,0.0127524,0.01971745,0.03872059,0.05508889,0.01156042,-0.02724167,-0.08178226,-0.22304036,-0.02752665,-0.00268047,-0.0140183,-0.01634443,-0.01165011,0.06162106,-0.00274705,0.08459844,0.04982061,0.06621899,0.02923677,-0.07327503,-0.01456439,0.0303052,0.00889546,0.00501651,-0.01084844,-0.0238588,-0.01529131,0.01111847,0.06217856,-0.00176964,-0.03764651,0.02441594,-0.03981892,0.11996417,0.02879199,0.04041389,0.01795115,0.0339542,0.05026423,0.02350051,-0.08748841,0.00620537,0.04785728,-0.03829907,-0.04895132,-0.02361898,-0.02591152,0.00614372,-0.00768412,-0.03661405,-0.08779961,0.00178984,-0.02035185,-0.00550279,0.01733827,0.03231649,0.05738517,-0.01927805,-0.00062754,0.00747631,-0.01031311,0.0077519,-0.02716952,-0.03999805,-0.01699125,-0.00849307,0.02992162,0.04246273,-0.00774216,0.02579784,0.00793854,0.0061259,-0.01307168,-0.00873016,0.00478183,-0.05953054,-0.01576235,-0.01830485,0.17055203,0.00175304,-0.05415082,0.04745559,0.03609778,-0.00319996,-0.01267654,-0.00507408,0.00010451,0.0402723,0.01452363,0.04234517,-0.00912064,-0.00647178,0.01319124,0.02180728,-0.01271668,0.08498736,-0.08313683,-0.07340957,-0.00192342,-0.04847824,0.02022272,0.10981499,-0.02747934,-0.28334841,0.03812531,-0.00776733,-0.01718873,0.08427498,0.04827091,0.03592538,-0.00376363,-0.05796225,0.01405155,-0.04553892,0.07147627,-0.01270336,-0.02353838,-0.01911587,-0.01797793,0.0688787,0.00009068,0.05844338,-0.02287259,0.01108356,0.04901499,0.20440075,-0.01491646,0.04746914,0.00211286,0.01098818,0.05234742,0.02678373,0.032711,0.03629896,-0.06264009,0.02212033,-0.03544256,0.03652454,0.05616448,-0.04223771,0.01870658,0.06213564,0.02755422,-0.01326744,0.07092161,-0.07862341,0.02549098,0.08118177,0.01803149,0.02274271,-0.04845094,0.00036183,0.0547076,0.02784411,0.04269101,0.00267917,0.01387348,0.05380601,0.03436226,0.00897063,-0.04286394,-0.07089537,-0.019337,0.02578593,0.03522338,0.08725555,0.1076185,0.07271516],"last_embed":{"hash":"067f8f78b1c77ccd74fe57834e9d90a0b13e8b5f818bfad40dcf30d0110b730b","tokens":465}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.04393384,0.00839292,-0.00778957,0.02202913,-0.01210221,0.01510802,0.05636985,-0.02548672,0.00447676,-0.0140099,-0.0443634,-0.00137892,-0.07070726,-0.07225204,-0.01224583,0.00550491,0.03251331,0.04795779,0.02267039,0.00654898,-0.02001152,0.03426712,0.04853172,0.03267295,0.02476964,-0.03246582,0.0248386,-0.04527462,0.03423374,0.04671099,0.09718641,0.0088871,-0.0085591,-0.03402323,-0.08517492,0.00960256,0.03908081,-0.05394663,-0.01811904,0.05226029,-0.01363044,0.02463753,0.00147897,0.03334925,-0.08980708,-0.00803465,0.02589523,-0.00420696,-0.00854547,0.04235535,0.05009465,-0.07817601,0.01244667,0.03965726,-0.02830173,0.04560228,-0.05198439,-0.05585586,-0.02704277,0.00506701,0.01742799,0.00340905,0.09079864,0.03494736,0.02544341,0.04526643,-0.03650708,-0.04024619,0.01151372,0.00382628,-0.0497316,0.01476716,0.00125512,-0.01230864,0.09492451,-0.00950347,0.03401755,0.02240992,-0.02682474,-0.00943734,-0.10143843,-0.01332737,-0.01181939,0.07323306,0.04417921,-0.05039956,0.02798845,-0.01942252,-0.00843783,-0.01788848,-0.01692137,0.04421191,-0.00215787,0.00745118,0.04503815,-0.07855293,0.00426916,-0.00261281,-0.02341148,-0.03939778,-0.00342482,-0.00203429,-0.07777441,0.00376759,-0.04875528,-0.0664094,-0.05193979,0.04028028,-0.03156481,-0.02478791,0.01265592,0.0169035,-0.02574857,-0.01645011,-0.07523426,0.04312128,-0.03420969,-0.02588553,0.03028855,0.05233011,0.01989428,0.04246381,0.02344825,-0.0522467,-0.01082415,-0.00061581,-0.01645374,-0.02855355,0.03818665,-0.03583285,0.01900578,0.04046715,-0.04273066,-0.04719422,-0.03062623,0.0259554,0.04649401,-0.03084687,0.04701567,-0.03122485,0.01390374,-0.02634759,0.00607355,0.01114336,-0.0362475,0.07864832,-0.03111383,-0.0333066,-0.03612684,0.04785892,0.0280405,0.02629811,0.03656754,-0.05467877,0.02967551,-0.02662282,0.02682122,0.05878347,-0.01904942,-0.02752563,-0.02448664,0.03108096,0.04993399,0.02682561,0.01771204,-0.02249432,-0.01598461,0.03226908,-0.00494515,-0.00087091,0.00565115,0.02573389,0.00510526,0.01155075,-0.00298571,-0.04008,0.01169959,0.01524126,-0.02015283,-0.01348,0.02171233,-0.03908717,-0.0020308,-0.00306087,0.00085681,0.05689847,-0.01877662,-0.00760333,0.06629086,0.01893038,0.03473441,0.02733168,0.00061202,0.01338754,0.02697016,0.02023192,-0.02141113,-0.03978139,-0.03742055,0.03945643,0.03040026,-0.06326694,0.01469936,-0.05372581,0.05403719,-0.00805612,0.01513172,-0.05866826,-0.07622945,0.06303586,0.01171087,0.01900124,-0.00258272,0.02020388,-0.01952594,0.08117085,-0.04369358,0.04023166,0.04696145,-0.00915297,0.00765374,-0.0229252,-0.06719358,0.00623006,-0.03440911,-0.01352207,0.01448436,0.01717103,0.01728426,-0.03904384,0.03078444,0.03840151,-0.06140536,-0.00522393,-0.01861509,0.0297133,0.03774358,0.05137392,0.02297325,0.0042498,0.03131023,0.02405529,-0.01392466,-0.00889943,-0.00100801,-0.04220814,-0.06850253,0.03060129,-0.00202547,0.077172,-0.00687097,-0.02985234,-0.01033974,0.04209277,0.0358047,-0.01997595,-0.00555963,-0.02412944,0.00552291,-0.01978403,0.0117665,-0.00440566,-0.03783785,0.01371659,-0.02347637,-0.02800838,-0.0399089,0.02892226,-0.04633801,0.04408915,0.01428062,-0.03075662,0.07422718,-0.00684989,-0.03500787,0.02465209,-0.0412874,0.02176542,-0.05822216,-0.01378629,-0.04579541,-0.0364172,-0.00815278,0.00585104,-0.04134175,0.0223918,0.01293979,-0.01233401,0.01610442,-0.00448553,0.03593035,0.00703755,-0.05316188,0.05904967,-0.07489512,-0.024284,-0.00367522,0.00356997,-0.03533378,-0.04571478,0.00219234,0.04939215,0.03529311,-0.01166734,-0.01435781,0.0168004,0.004055,0.0436018,-0.03848225,-0.04717148,0.00383961,-0.05113709,0.02561877,-0.03976132,0.03466603,0.08143617,0.01299108,0.01209496,-0.03852573,-0.00891517,-0.08033915,0.04561849,0.00795357,-0.00975971,0.02472248,-0.06764451,0.0361026,0.00926969,0.04808805,0.06095877,-0.01953876,0.01864089,0.011154,-0.02356671,0.03704621,0.00088397,-0.02551372,-0.04301813,0.03299155,-0.02112399,0.04050899,-0.04563463,0.02271298,-0.03413384,0.01083618,0.07247137,-0.00640943,-0.00721435,-0.01602994,-0.0243643,0.01161848,-0.0346491,-0.02627966,-0.02974181,0.03745785,-0.00990851,-0.03466132,-0.00539425,-0.00630499,0.01992099,-0.00710564,-0.04379334,-0.03222048,-0.00324476,-0.0452019,0.03545731,0.04135104,0.05758606,-0.05640738,-0.0677687,0.01195419,0.02330197,-0.02828876,0.01389736,0.02796035,-0.01324655,0.00433311,0.01075892,0.00746856,-0.0488769,-0.00506182,-0.04094698,-0.03329688,-0.04367782,-0.07204086,-0.05881642,-0.01094131,-0.03882527,-0.08478753,-0.03627836,-0.02465899,0.03946967,-0.04219878,0.02509858,-0.00026929,0.02500128,0.04656283,0.0045309,-0.01578515,-0.01482585,0.00774435,-0.02346668,0.03840912,0.03106134,0.00003172,0.05932136,-0.0250094,0.03608798,-0.04141454,-0.05094888,0.0178292,0.0659825,0.09406868,0.00879411,-0.001905,0.01073196,-0.01006799,-0.05630707,0.02223875,0.08176034,0.01336728,-0.05044169,0.02846688,0.05945719,0.02994554,0.00504579,-0.00818481,-0.00356467,-0.00175068,0.03107196,-0.01222782,-0.03546628,-0.05873853,-0.02423463,-0.03689743,-0.01143621,0.06919175,-0.06342544,-0.01836918,0.07856715,-0.05362595,-0.01778556,0.02999767,0.00117743,0.05404491,0.04913116,-0.02481845,-0.08343446,-0.00345222,0.01071645,-0.00943368,0.01250962,-0.06214062,0.00866364,0.03610701,0.00729469,0.01219135,0.00857688,-0.0277071,-0.00722589,-0.01459629,-0.02998735,0.00845453,-0.00858534,0.01501486,0.02556098,0.04377755,-0.00298694,-0.08861507,0.00296506,-0.00651569,-0.02032677,-0.01686702,0.01571704,0.01874153,0.04353672,-0.01167253,0.02011198,-0.0134873,0.02867638,0.03710068,0.04582249,0.02696631,0.00787674,0.03763125,0.06031742,-0.04190125,0.02749673,0.06772413,-0.00854906,-0.01613788,0.03496348,0.0220489,0.03478739,0.02006653,-0.02336247,-0.01187599,0.04578342,0.00926582,0.00981907,-0.04138917,0.02484745,-0.00133521,-0.00289433,0.00457819,0.02753626,-0.03096966,-0.0001953,0.04106826,-0.0231776,-0.02904912,-0.01175686,0.08778153,0.05933774,-0.04876518,-0.07049475,-0.03946655,0.01568953,-0.0054548,0.03290568,-0.05937812,0.02899931,-0.05892136,-0.00222565,0.00760067,-0.03964286,-0.04316691,0.00540808,-0.01282879,-0.01986006,-0.06307619,-0.00579998,0.00376646,-0.01381256,0.03388257,-0.0597327,0.01442625,-0.04808458,-0.0162143,0.01980916,0.06604049,-0.03291231,0.00947062,0.02773932,-0.03802266,-0.00944118,0.00036731,-0.00395758,0.07142242,-0.01981204,-0.00577774,0.01688572,0.01625531,0.0044757,-0.04699896,-0.0078577,0.02755629,-0.04445111,-0.0270276,0.04455457,0.0114786,0.0666211,-0.07540277,0.03842149,-0.06804742,-0.00556334,-0.02918531,0.02861894,0.01748491,0.04545543,-0.01906496,-0.02277296,0.06250906,0.00084214,-0.05512311,0.0020543,0.02464306,-0.03749016,0.03709883,-0.00766336,-0.0042254,0.0159296,0.04181537,0.00916457,-0.02639744,0.01678527,-0.01359715,0.01775604,0.01580353,-0.0072924,-0.01107866,-0.00442112,-0.0090989,-0.0235103,-0.01175934,-0.00944442,-0.04947472,0.01779919,0.0250848,-0.0294799,0.01020013,0.02526418,-0.00980289,-0.03195212,0.00534036,-0.11551247,-0.05678226,-0.02998206,0.04759149,0.051513,0.00945689,-0.0112157,0.02261897,-0.02025606,0.02503949,-0.03259019,0.01488433,-0.0164728,-0.02820079,0.02467933,-0.01064659,-0.06515031,-0.05285283,-0.00147526,-0.01420568,0.07555582,-0.00516588,0.01218297,0.06877063,0.01318785,-0.03974178,-0.04988261,0.01825737,0.06749629,-0.01034753,-0.00090673,-0.04914178,0.00375556,-0.00142043,-0.02312651,0.06554241,0.04703435,0.02722062,0.00720652,0.02431115,-0.05761585,0.00404744,0.00902573,0.0565339,-0.0414404,0.00749739,-0.0697249,-0.00193428,0.00186507,-0.03297988,0.00129194,-0.00430968,-0.00310656,0.02355485,0.07353166,-0.03608505,-0.03614165,-0.04357616,-0.02486235,-0.01095083,-0.00222999,0.0813997,0.00849228,-0.04930861,0.04877322,-0.05071925,-0.01296345,-0.02216375,0.00661952,-0.03114922,0.02051485,0.00833971,0.06016258,-0.02007484,-0.05113852,-0.05758122,0.01815582,-0.01890432,-0.01209061,0.05098597,-0.03577532,-0.01186854,-0.02017351,-0.02220344,0.033116,-0.06610825,0.03474414,0.00673519,-0.02377106,-0.03692953,-0.05486399,0.04908925,-0.05563159,0.01764507,0.01694855,-0.03988595,-0.03506213,-0.01267848,0.00605149,-0.05184145,-0.02110803,0.01019058,-0.01155332,0.03341264,0.01107066,-0.05500676,0.02791323,-0.01549898,0.0617702,0.01713239,-0.03866522,-0.00850043,0.07824265,-0.08871824,0.00194972,-0.04410969,-0.00517487,-0.00058188,0.05038602,-0.03853416,-0.05095518,-0.0197947,-0.03322733,0.02379017,-0.00062331,0.01077508,-0.0784816,0.03751164,-0.00440558,0.0150204,-0.00236177,0.00752569,0.00402351,0.0445335,-0.00601102,-0.01237981,0.00915321,-0.01978248,-0.0292065,-0.03933611,0.03890827,0.0588726,0.01433069,0.03710818,0.01508361,-0.03836625,0.04878529,-0.01740638,0.10256288,0.00805625,-0.00471584,-0.02766707,-0.04590338,8.5e-7,0.03637297,-0.00777787,0.01404735,0.00634046,-0.01151211,0.00597338,-0.01802057,-0.03854864,0.05475771],"last_embed":{"tokens":613,"hash":"oyiytq"}}},"last_read":{"hash":"oyiytq","at":1751079996119},"class_name":"SmartSource","outlinks":[{"title":"内核","target":"Kernel","line":12},{"title":"内核","target":"Kernel","line":14},{"title":"Linux","target":"Linux","line":14},{"title":"windows系统","target":"windows系统","line":14},{"title":"windows系统","target":"windows系统","line":16},{"title":"windows系统","target":"windows系统","line":16},{"title":"内核","target":"Kernel","line":19},{"title":"内存","target":"内存","line":29},{"title":"缓冲区溢出","target":"缓冲区溢出","line":30},{"title":"内核","target":"Kernel","line":31},{"title":"内核","target":"Kernel","line":31},{"title":"内核","target":"Kernel","line":33},{"title":"内核","target":"Kernel","line":33},{"title":"内核","target":"Kernel","line":35}],"metadata":{"aliases":null,"tags":["网络安全/漏洞"]},"blocks":{"#---frontmatter---":[1,5],"#简介":[6,10],"#简介#{1}":[7,7],"#简介#{2}":[8,9],"#简介#{3}":[10,10],"#基于提权方面的考虑":[11,23],"#基于提权方面的考虑#{1}":[12,14],"#基于提权方面的考虑#{2}":[15,15],"#基于提权方面的考虑#{3}":[16,17],"#基于提权方面的考虑#{4}":[18,18],"#基于提权方面的考虑#{5}":[19,21],"#基于提权方面的考虑#{6}":[22,23],"#内核漏洞的主要类型":[24,53],"#内核漏洞的主要类型#{1}":[26,27],"#内核漏洞的主要类型#{2}":[28,29],"#内核漏洞的主要类型#{3}":[30,31],"#内核漏洞的主要类型#{4}":[32,33],"#内核漏洞的主要类型#{5}":[34,36],"#内核漏洞的主要类型#{6}":[37,39],"#内核漏洞的主要类型#{7}":[40,40],"#内核漏洞的主要类型#相关的内核漏洞":[41,53],"#内核漏洞的主要类型#相关的内核漏洞#{1}":[42,53]},"last_import":{"mtime":1747536242696,"size":2462,"at":1749024987637,"hash":"oyiytq"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/内核漏洞.md","last_embed":{"hash":"oyiytq","at":1751079996119}},