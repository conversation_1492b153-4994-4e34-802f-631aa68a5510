"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07812562,-0.03636123,-0.02127724,-0.06209531,0.02166435,-0.03727525,-0.01073551,0.06407018,0.0386518,-0.00003168,0.0137583,-0.05684697,0.02837543,0.06593029,0.06224327,0.01415448,-0.00512498,0.06634507,-0.00071243,0.03386038,0.09473725,-0.01837642,0.00689406,-0.08771443,0.00833162,0.03742732,0.02513763,0.00625972,-0.02437725,-0.17825991,0.0136041,-0.00337275,0.02532552,0.02491341,0.02116656,-0.01989922,0.0101378,0.03603557,-0.03124084,0.00530729,0.02763668,0.00272243,0.00046292,-0.02464532,-0.05833906,-0.11158293,-0.03122369,-0.01099607,0.01664223,-0.0614652,-0.03193048,-0.07566752,-0.03295406,-0.01064015,-0.01153033,-0.01588228,0.01319433,0.0441776,0.04792453,0.00488086,0.0377881,0.0197856,-0.20148037,0.05023342,0.01572482,0.01071918,0.00430164,-0.02195246,0.00663513,-0.02476851,-0.01169932,0.03918337,-0.05050372,0.03379416,0.05738692,0.05280736,-0.01093612,-0.04605187,-0.00278622,-0.05367221,-0.02325497,0.04347768,-0.00373388,0.01418195,-0.00719063,0.03387315,-0.00891077,0.0161004,0.02751058,-0.01255086,-0.02610861,-0.03602223,0.04903582,0.02577249,-0.02792197,0.05773067,0.04346034,0.01889857,-0.05855408,0.12364048,-0.06426165,-0.00922707,0.00293696,-0.04400668,0.01907929,-0.02780432,-0.03073681,-0.08596615,0.03501156,-0.03511867,-0.04415675,-0.00124959,0.07161038,-0.01090323,0.03115174,0.00857784,0.00705228,-0.00341192,-0.02919278,0.01950868,-0.05349799,0.01337538,0.08798948,-0.00030838,-0.00931415,-0.00570954,0.02620656,0.05602419,0.04321222,-0.00262375,0.03428222,-0.00277651,-0.03441424,-0.03890571,-0.01335706,0.03254751,-0.03783676,0.00606173,0.00541431,-0.00579183,-0.0145298,-0.04519094,-0.02028465,-0.083314,-0.02557155,0.04428179,-0.03657334,0.00154677,0.02271323,-0.04577371,0.00246816,0.06796784,-0.01324848,-0.02945483,-0.00655801,-0.00651501,0.05800324,0.1562248,-0.05357187,-0.03492638,0.01211043,0.01907292,-0.05412497,0.1998153,0.01644456,-0.04655078,-0.0456444,0.01574359,-0.02553865,-0.02729043,0.00883855,-0.00696237,0.03744972,-0.02180207,0.02012135,-0.05982293,-0.01128694,-0.04549663,-0.0123388,0.05956849,0.05831633,-0.01457956,-0.08011524,0.03553367,0.00541452,-0.09855808,-0.08820089,-0.0295629,0.0031202,-0.05776661,-0.12791269,0.00536197,-0.02815936,0.00017779,-0.01359415,-0.05745159,0.02957383,0.00660311,0.03844539,-0.07636449,0.15550418,0.04959806,-0.0226829,0.03943138,-0.02668944,-0.03447633,-0.00316618,-0.04800167,-0.03467916,0.0351158,-0.02234388,0.00931838,-0.0406102,0.01604367,-0.05426602,0.09247047,0.03702188,0.03862416,0.03885703,0.08263167,0.0241581,-0.01162708,-0.07796904,-0.24009517,-0.0630075,-0.00459597,-0.03843327,-0.00272728,-0.00336631,0.04128448,-0.01927217,0.08640046,0.05086721,0.05818009,0.05817536,-0.05505973,-0.03695872,0.02332793,-0.00555878,0.01433953,-0.0248169,-0.04318729,0.02109672,-0.00305436,0.03348542,-0.00263167,0.00548678,0.02699767,-0.0184774,0.13364059,0.00973307,0.03293685,-0.02055695,0.04282468,0.06633548,0.02643591,-0.09913947,0.02476158,0.03622152,-0.03739285,-0.02466929,-0.00105759,-0.02786302,-0.01811051,0.02689253,-0.01724301,-0.10891943,-0.00723022,-0.03206498,-0.02562452,0.01561653,0.02650157,0.06881413,-0.00449114,0.00847494,0.03147709,0.01075426,0.0179706,-0.05240117,-0.02847592,-0.00224968,0.01739027,0.04505751,0.03420903,0.00949126,0.02433061,-0.01909884,0.00335679,-0.06310967,-0.00043953,-0.02374064,-0.00733468,-0.0756828,-0.07208974,0.19009231,-0.00252619,0.00410157,0.04866687,0.00972836,-0.02239938,-0.04152886,-0.01058129,-0.00656276,0.04610057,0.04215178,0.03850626,0.01408421,-0.02026881,0.0484176,-0.00121192,-0.01160084,0.07313037,-0.0538183,-0.04884095,-0.0239803,-0.09876295,0.00861341,0.09094622,-0.00635172,-0.27518192,0.01190196,-0.01232874,0.00299553,0.04736987,0.01022551,0.06349616,-0.02833789,-0.04863226,0.02959714,-0.05059819,0.05719479,-0.01007111,-0.06362691,0.01752472,-0.00019482,0.03880351,0.03905598,0.0606929,-0.05239129,0.01869461,0.09367767,0.21710142,0.04558231,0.00609891,0.0431124,0.00839511,0.0904306,0.02832094,0.00230602,-0.0035169,-0.04009409,0.05596274,0.000236,0.02295433,-0.00433762,-0.02509749,-0.01863856,0.04710112,0.00016486,-0.04220875,0.0549503,-0.04614448,0.06058666,0.08473402,0.00213394,-0.02622876,-0.02953162,0.02791441,0.02289439,0.00429874,-0.01226913,0.01471208,-0.01215098,0.01865541,0.04593462,0.0191636,-0.03754511,-0.06107171,-0.00277388,0.02557149,0.03268354,0.0484633,0.04170229,0.02332202],"last_embed":{"hash":"909a14d7beec3b68dc2f1d21c0f5ba7f099ca353e2e716aedace9b7b18f2b044","tokens":412}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.0605938,0.04687835,-0.00683432,0.00133127,-0.0117562,-0.01816038,0.06685812,-0.03658928,0.01022273,-0.01756719,-0.006208,-0.01990677,-0.02285628,-0.01292851,-0.05801592,0.04510941,0.01742898,0.04073948,0.04561749,-0.00810129,0.00335188,0.02813538,0.05126566,0.0489807,-0.01403518,-0.0262706,0.01300023,-0.04969492,0.02170867,0.03955124,0.04716959,-0.05256065,-0.00716546,-0.02683718,-0.07069579,0.02146472,0.03192372,-0.02846186,-0.03461616,0.02268673,-0.00940289,-0.02004508,-0.04391277,0.01270029,-0.10551517,-0.02444486,0.0450837,-0.01675669,0.00415918,0.01697012,0.01191333,-0.05956686,-0.00268966,0.04585459,0.00099277,0.03276982,-0.0359952,-0.04646933,-0.01658636,-0.00205754,0.01354944,0.04380148,0.08159384,0.02428913,-0.01566591,0.02875278,0.00669384,-0.03327052,0.00172492,-0.01345832,-0.08818144,0.00437686,-0.00975251,0.01845044,0.07724077,-0.04252681,0.04860214,0.01212147,-0.03307029,0.00221584,-0.10411441,0.01391608,-0.00509808,0.04512125,0.03627793,-0.02041466,0.00425233,-0.0267258,0.03484576,0.00581789,0.03098275,0.03246936,-0.0010346,0.00762872,0.01493359,-0.08055083,-0.01195218,0.01485319,-0.01434235,-0.04388086,0.03883273,-0.01032561,-0.05638241,-0.0076534,-0.03241513,-0.02482201,-0.00094751,0.00700011,-0.02693866,-0.00457582,-0.02973029,0.02259558,-0.0186177,-0.04887431,-0.08905932,0.03239382,-0.03928358,0.00665325,0.03286203,0.08622203,0.04025502,0.02021477,0.0126928,-0.01381687,-0.04586859,0.00734369,0.00595967,-0.02864214,0.0256698,-0.02967187,-0.0048619,0.0030538,-0.01180305,-0.02397156,-0.01070856,0.00082072,0.02253993,-0.04708271,0.0198289,-0.03464956,0.03844563,-0.02629254,0.00315455,-0.01277013,-0.02010438,0.06577182,-0.04259189,-0.057946,-0.0119645,0.04479548,-0.01658884,-0.00070927,-0.0380882,-0.05288912,0.01625635,0.02285221,0.0332467,0.06635134,0.00843669,-0.00291495,-0.0071528,-0.00257953,0.04542733,0.04319802,0.03962749,0.02383934,0.04285936,0.01408718,-0.05686,-0.00121441,-0.01850441,0.01339532,0.02275272,0.04794612,0.05413298,-0.04817061,0.07223742,0.01891023,-0.0559932,-0.02927741,-0.0102847,-0.0284695,-0.00080419,0.02753629,0.0163335,0.02386601,-0.00113147,0.03092656,0.0133974,-0.00113843,0.02279229,-0.00475598,0.00592734,0.00438172,0.04178133,0.01134313,-0.0127457,-0.04797491,-0.03138401,0.02017684,0.02935739,-0.03222977,-0.0453283,-0.04068922,0.05468766,0.01329055,-0.01820862,-0.01966747,-0.02676997,0.03168165,0.05733637,0.01855968,0.01848385,-0.01027732,-0.05649765,0.0693541,-0.06348602,0.09375585,0.02418518,-0.01349963,-0.00858774,-0.10342237,-0.02937675,-0.02697498,0.01613696,-0.0582513,-0.02347469,0.03417491,0.02819482,0.00567511,-0.00918402,0.04693934,-0.02888283,-0.01507981,-0.03723304,-0.00260125,-0.0113616,0.03892186,0.03418313,0.02450075,-0.01023233,0.00478297,0.00509805,-0.03178853,0.01101364,-0.00496605,-0.06493957,0.00699098,-0.00629365,0.00659531,0.02155336,-0.00872013,0.00778748,0.08343649,0.0076266,-0.02733667,-0.02834976,-0.04326587,0.02025938,-0.02248752,0.00583426,0.04750186,-0.04602808,0.01434339,-0.02583943,-0.03260226,-0.01839101,0.02491798,-0.0595685,0.08040116,-0.03494728,-0.0439484,0.0753781,0.01773703,-0.00904587,-0.00457944,-0.07043656,-0.01988553,-0.05142219,-0.03856205,-0.04646173,-0.0536154,0.01591697,0.03264141,-0.07673872,0.03014551,0.00543065,0.017108,0.05144564,-0.03500583,0.04879666,0.00203012,-0.05103976,0.04694208,-0.04715778,-0.03523583,0.0185725,0.0397351,0.00115982,-0.05634853,-0.03365349,0.07029031,0.02084439,-0.03429768,0.00176721,0.01303379,0.00409599,0.02134916,-0.02063906,-0.03643373,0.01986348,-0.04809496,0.02684254,0.00912148,0.04686016,0.07080584,0.00085898,-0.0103146,-0.01337711,-0.00184252,-0.04509666,-0.00888178,0.03044272,0.01925019,0.03227906,-0.06305782,0.03545033,-0.00661549,0.02601832,0.04281368,-0.03873459,0.00708534,0.00852282,-0.0125358,0.03919275,-0.01107061,-0.02902588,-0.06465652,0.03104646,-0.05682289,0.02209818,-0.05514782,0.0135888,-0.03844707,-0.01639427,0.01594315,0.00027506,0.01499031,-0.04775489,-0.03909538,-0.01397017,-0.03631928,-0.04475924,-0.00320603,0.03953297,-0.01977302,-0.01917268,0.02151905,0.01798073,0.00719347,-0.00103228,-0.04321624,-0.0540408,-0.05075847,-0.03514342,0.03189909,0.0798085,0.03490041,-0.06545305,-0.02561542,0.0293547,0.02943545,-0.04793363,-0.0100024,0.04896491,-0.00812223,0.01189227,-0.00308764,0.03524535,-0.0165282,0.01150585,-0.02593821,-0.02955822,-0.02740939,-0.11263339,-0.04148334,0.04641872,-0.0466943,-0.02745154,-0.05208974,-0.05988551,0.01386609,-0.04219684,0.05141056,0.009656,0.01452376,0.05199666,-0.01315279,-0.02847068,-0.01170896,0.02764547,0.03293062,0.00535685,0.02175703,-0.01064541,0.05885814,-0.01734001,0.06686821,-0.02258829,-0.02832938,0.00260308,0.05155246,0.00260902,-0.00258543,0.0077203,0.02737127,-0.01559572,-0.04798394,0.02173214,0.05908844,0.01734863,-0.04579104,0.04134998,0.03676183,0.02864903,-0.03189264,-0.06180235,0.03377793,0.00162385,0.02396328,-0.02348932,0.02627121,-0.0942032,-0.05905928,-0.02592431,-0.02161708,0.08798584,0.02354675,0.00445456,0.02255645,-0.03497078,-0.02528702,-0.0052938,-0.03404532,0.07036214,0.05946713,-0.01243876,-0.02304561,-0.00288948,0.06755278,-0.01631747,-0.04290672,-0.06721755,-0.02598467,0.03379843,0.02000109,0.02740276,-0.04612442,-0.0141673,-0.00363735,-0.06275005,-0.01830686,0.04282592,-0.00958318,0.01136204,-0.00873201,0.01174153,-0.03016712,-0.0578231,-0.00080133,0.00558532,-0.03394983,0.00156351,-0.01065117,0.07990927,0.04088388,-0.03639128,-0.00733076,-0.05533164,-0.02335287,-0.01342808,0.04467833,0.0229665,0.01680943,-0.00379945,0.04902438,-0.03046446,0.00192632,0.0220388,-0.01780548,-0.03215621,0.07061272,0.03948706,0.01655749,0.02341819,-0.01123479,0.01147525,0.04925403,0.00060942,0.04359918,0.00969913,0.04879702,-0.02885098,-0.0077166,0.04474712,0.04279149,-0.04225077,0.02007038,0.06262249,-0.01805164,-0.02653023,-0.08195223,0.08462113,0.03051376,-0.03289557,-0.05920732,-0.03507368,0.02640633,-0.03436967,0.01258248,0.0007155,0.01617385,-0.06853729,-0.00072931,0.03239753,-0.02421737,-0.02329788,0.06629463,0.03900705,0.03453993,-0.04362718,-0.00389563,0.01297543,-0.01770622,0.03239689,-0.04850624,0.04218021,-0.0076872,-0.0110507,0.04570417,0.09684315,0.0157249,0.00750131,0.02535798,-0.01915224,0.00002266,0.02671663,-0.00663302,0.06498204,-0.00117138,-0.00020207,0.04571383,0.03356745,-0.00823937,-0.02015737,0.01885772,0.01230682,-0.03653046,-0.04033989,0.05680362,0.00900295,0.04643645,-0.03675516,0.02522873,-0.00242268,0.00087269,-0.04858439,0.02044369,0.01750256,0.03195681,-0.04141834,-0.00427962,-0.00267247,0.03343308,-0.06650099,-0.02131479,0.01220624,-0.00498069,0.05480671,-0.07938524,0.0263503,-0.02048775,0.05537537,-0.00256279,-0.01632216,0.01005542,0.00909906,0.05650183,0.01836015,0.00060954,-0.00556928,-0.04959016,-0.02816264,-0.02479824,-0.01909856,0.04356752,-0.00771023,0.02242279,0.03174052,-0.03905712,0.0094334,-0.02300327,-0.01060226,-0.00691506,-0.0021332,-0.08238056,-0.0257459,-0.07001116,0.00073153,0.04082036,-0.00033552,0.05253101,0.0256303,0.01917319,0.00849392,-0.04933649,0.00382588,-0.0111436,-0.01205414,-0.03856781,-0.00024492,-0.05017735,-0.03016537,-0.04805256,-0.02266281,0.01360826,0.00663863,0.00439156,0.04488305,-0.05391617,-0.07001817,-0.0267512,0.04467165,0.0370898,-0.01186503,0.03726443,-0.07233507,-0.05169658,-0.03507467,-0.03947217,0.01379298,0.02973484,0.0078274,0.04756887,0.02508624,-0.0158249,-0.00540651,0.01247069,0.03263141,-0.01963484,-0.0014813,-0.02006182,-0.00982543,-0.00818524,-0.04446287,0.03871214,-0.03055588,0.01866894,0.00138331,0.05127821,-0.04852546,-0.02079412,-0.01578854,-0.02830909,-0.06212029,0.05174435,0.0419728,0.01251176,-0.02936723,-0.00218551,-0.09649477,-0.00087816,-0.01811864,0.01342551,0.01390899,0.03298628,0.02455116,0.03022834,0.01990713,-0.02858344,-0.09196609,0.05446625,-0.00817176,-0.02372979,0.0555923,-0.02436426,0.02530163,0.01680809,-0.04793112,0.000825,-0.03730216,0.03301842,-0.02203962,-0.01593821,0.06188288,-0.05030227,0.06734429,-0.03695999,0.02330637,0.04036328,-0.01470517,0.01938087,-0.0342401,0.01464678,-0.01985229,0.00446019,0.02158917,0.00650436,0.11198653,-0.04615266,-0.06621802,-0.00667232,-0.02496847,0.03428909,0.01364371,-0.03212943,-0.00866156,0.02512674,-0.0479105,-0.00214166,-0.04226252,-0.04901357,0.05674211,0.02276123,-0.0093526,-0.01175327,-0.02441847,-0.00673936,0.00876198,-0.00542765,-0.04460094,-0.09989185,0.00516911,0.04755382,0.03656646,0.00289982,-0.03459216,-0.00117233,0.0429735,-0.00903646,-0.01319131,0.01072267,-0.02506237,0.00724341,-0.00595752,0.05990357,-0.00686334,0.00812589,-0.00068357,0.02780898,0.00020092,0.03947467,-0.01071734,0.05769562,0.01803746,0.02657603,-0.05425519,-0.03069217,0.00000108,-0.007729,-0.02590023,0.02660734,-0.00021574,-0.01903012,-0.00665121,-0.04099252,-0.05967892,0.04808004],"last_embed":{"tokens":1303,"hash":"1i88o0l"}}},"last_read":{"hash":"1i88o0l","at":1750993410307},"class_name":"SmartSource","outlinks":[{"title":"Jenkins","target":"Jenkins","line":11},{"title":"反序列化","target":"反序列化","line":15},{"title":"反序列化","target":"反序列化","line":16},{"title":"反序列化","target":"反序列化","line":17},{"title":"session","target":"session","line":34},{"title":"token","target":"token","line":34},{"title":"反序列化","target":"反序列化","line":35},{"title":"序列化","target":"序列化","line":35},{"title":"json","target":"json","line":36},{"title":"序列化","target":"序列化","line":37},{"title":"json","target":"json","line":38},{"title":"序列化","target":"序列化","line":40},{"title":"Java","target":"Java","line":43},{"title":"反序列化","target":"反序列化","line":48},{"title":"序列化","target":"序列化","line":49},{"title":"反序列化","target":"反序列化","line":52},{"title":"PHP","target":"PHP","line":60},{"title":"Python","target":"Python","line":60},{"title":"序列化","target":"序列化","line":64},{"title":"反序列化","target":"反序列化","line":65},{"title":"#Python的反序列化实验","target":"#Python的反序列化实验","line":66},{"title":"反射（reflection）机制","target":"https://xz.aliyun.com/t/7029/","line":68},{"title":"Java","target":"Java","line":68},{"title":"变量","target":"变量","line":71},{"title":"Java","target":"Java","line":73},{"title":"序列化","target":"序列化","line":99},{"title":"序列化","target":"序列化","line":129}],"metadata":{"tags":["网络安全/漏洞/Web安全"],"英文":"Deserialization Vulnerability","aliases":["Deserialization Vulnerability"]},"blocks":{"#---frontmatter---":[1,7],"#简介":[8,19],"#简介#{1}":[9,13],"#简介#{2}":[14,14],"#简介#{3}":[15,17],"#简介#{4}":[18,19],"#相关漏洞":[20,25],"#相关漏洞#{1}":[21,21],"#相关漏洞#{2}":[22,24],"#相关漏洞#{3}":[25,25],"#常用工具":[26,30],"#常用工具#{1}":[27,27],"#常用工具#{2}":[28,28],"#常用工具#{3}":[29,29],"#常用工具#{4}":[30,30],"#漏洞环境":[31,77],"#漏洞环境#可能出现的位置":[33,45],"#漏洞环境#可能出现的位置#{1}":[34,34],"#漏洞环境#可能出现的位置#{2}":[35,36],"#漏洞环境#可能出现的位置#{3}":[37,38],"#漏洞环境#可能出现的位置#{4}":[39,39],"#漏洞环境#可能出现的位置#{5}":[40,40],"#漏洞环境#可能出现的位置#{6}":[41,42],"#漏洞环境#可能出现的位置#{7}":[43,44],"#漏洞环境#可能出现的位置#{8}":[45,45],"#漏洞环境#漏洞成因/环节":[46,58],"#漏洞环境#漏洞成因/环节#{1}":[47,53],"#漏洞环境#漏洞成因/环节#{2}":[54,54],"#漏洞环境#漏洞成因/环节#{3}":[55,57],"#漏洞环境#漏洞成因/环节#{4}":[58,58],"#漏洞环境#漏洞原理":[59,77],"#漏洞环境#漏洞原理#{1}":[60,65],"#漏洞环境#漏洞原理#{2}":[66,66],"#漏洞环境#漏洞原理#{3}":[67,67],"#漏洞环境#漏洞原理#{4}":[68,75],"#漏洞环境#漏洞原理#{5}":[76,77],"#实际测试":[78,141],"#实际测试#Python的反序列化实验":[80,141],"#实际测试#Python的反序列化实验#序列化与反序列化":[82,104],"#实际测试#Python的反序列化实验#序列化与反序列化#{1}":[83,98],"#实际测试#Python的反序列化实验#序列化与反序列化#{2}":[99,100],"#实际测试#Python的反序列化实验#序列化与反序列化#{3}":[101,103],"#实际测试#Python的反序列化实验#序列化与反序列化#{4}":[104,104],"#实际测试#Python的反序列化实验#系统命令序列化传输":[105,141],"#实际测试#Python的反序列化实验#系统命令序列化传输#{1}":[106,120],"#实际测试#Python的反序列化实验#系统命令序列化传输#{2}":[121,121],"#实际测试#Python的反序列化实验#系统命令序列化传输#{3}":[122,127],"#实际测试#Python的反序列化实验#系统命令序列化传输#{4}":[128,129],"#实际测试#Python的反序列化实验#系统命令序列化传输#{5}":[130,138],"#实际测试#Python的反序列化实验#系统命令序列化传输#{6}":[139,141]},"last_import":{"mtime":1718639835046,"size":5028,"at":1748488129023,"hash":"1i88o0l"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md","last_embed":{"hash":"1i88o0l","at":1750993410307}},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.11572103,0.00653519,-0.02433526,-0.05135904,0.00879812,-0.00896876,-0.01490544,0.03190955,0.05885334,-0.01327876,-0.01607878,-0.06823328,0.06117346,0.06404126,0.0793427,0.00340161,-0.00478718,-0.00561657,-0.06366094,0.03893937,0.05763964,-0.0461344,-0.0184893,-0.09852643,0.01528004,0.03965786,0.02029387,0.01685966,-0.01956178,-0.14774486,0.04197739,-0.03579254,0.04583132,0.0286574,-0.03427589,0.02058823,0.02485063,0.06714625,-0.02581996,0.01324453,0.02379312,0.04650602,0.03185969,-0.02591286,-0.0358911,-0.06429377,-0.04956452,-0.01694532,0.02653671,-0.04491739,-0.03357086,-0.03028236,-0.00293298,-0.03266615,-0.02508968,0.01599235,-0.02020142,0.03182645,-0.0059393,0.0170524,0.01935481,0.04434093,-0.21134177,0.032798,0.01741598,-0.05046275,-0.02961852,0.00070724,0.00947441,0.03667023,-0.06905915,0.04438889,-0.00564448,0.04875766,0.03501119,-0.0349636,0.03976907,-0.0129167,0.03116478,-0.01939782,-0.07393379,0.04058405,-0.01721921,-0.03789873,-0.01274978,0.00911315,-0.03282361,0.01577739,0.01266465,0.0073293,0.00376741,-0.02942356,-0.03845331,0.04963381,-0.01759074,0.0713781,-0.01294409,0.04982001,-0.08028721,0.10900045,-0.04551045,0.02611444,0.02538598,-0.05818466,-0.01044015,-0.06907284,-0.00292309,-0.04895349,0.02150594,-0.03311014,-0.03493199,-0.02498,0.04257359,-0.00363216,0.0087668,0.02580401,0.04679129,0.00216486,-0.03319624,0.02830118,-0.00094672,-0.00082919,0.04823131,-0.02273041,-0.01749215,-0.04676373,0.00443217,0.07342293,0.02066259,0.01788165,0.04128986,-0.03292802,-0.04890893,-0.0343375,-0.02652355,0.00564638,-0.02745665,-0.02502489,0.0208105,-0.03141792,-0.00845296,-0.06858766,-0.02348621,-0.13149142,-0.07445159,0.04797271,-0.01211641,-0.02506959,0.04395925,-0.02811914,-0.03922542,0.08277068,-0.04499657,-0.00762837,0.00088623,-0.00855309,0.07329322,0.10961597,-0.00546191,-0.01571513,-0.00335683,0.0075196,-0.07103225,0.19632794,-0.01144547,-0.05918314,0.00537864,-0.00859351,-0.00447458,-0.03981355,0.004508,0.05850309,0.01155087,0.00871954,0.10522372,-0.04076797,-0.02573309,-0.04186611,0.02415708,0.02550822,0.05890161,-0.04844196,-0.06434317,0.03743947,0.02020347,-0.04172704,-0.00151395,-0.00772521,0.01856587,-0.07337493,-0.09760242,0.00553543,-0.03171834,-0.00082694,-0.04942818,-0.10317686,0.01881376,0.02846177,0.02325007,-0.0445465,0.11327953,0.03809029,0.00183297,0.03681992,-0.01581894,-0.01860761,0.03046725,0.0300843,0.01818855,0.02952974,-0.01752586,-0.00413019,-0.03643255,0.0162952,-0.03486938,0.02994327,0.010112,0.00407104,0.03641186,0.06990857,-0.0201367,-0.04821857,-0.079211,-0.21053904,-0.03988933,0.01513763,0.0019959,0.0064559,-0.02899467,0.03751039,-0.03476807,0.10070384,0.07442983,0.06066976,0.01895719,-0.10011967,-0.04388944,0.00179293,0.01766378,-0.01259122,-0.00402857,-0.01809363,-0.00169119,0.00060492,0.04683888,0.00929494,-0.02909472,0.03191385,-0.01823468,0.14365415,0.01080271,0.01469486,0.02967418,0.01498828,0.00008956,0.00486895,-0.07594386,0.01993744,0.04292822,-0.0584179,0.02214799,-0.00577693,0.00010832,-0.0028871,0.04613305,-0.04185187,-0.10376808,-0.03337324,-0.04740356,-0.02017667,-0.02649744,0.04202729,0.02383942,-0.00361678,0.02034775,0.01612092,0.00951693,-0.03837479,-0.04953389,-0.02371137,-0.03631395,-0.01365403,0.0357035,0.03747996,-0.01870935,0.00951429,0.03087727,-0.01013417,-0.04037623,0.00527555,0.01733489,-0.07933573,0.01987428,-0.03853245,0.15418044,-0.00733384,-0.00769224,0.0806465,0.00934889,0.00093003,-0.05969703,0.01935497,0.01805648,0.01798149,0.06109697,0.05513931,0.01751792,-0.0270135,0.06086146,0.00418553,-0.01273922,0.03956239,-0.07465157,-0.06798368,-0.01760395,-0.0613471,0.05230227,0.03045353,-0.00478443,-0.28527963,0.05807856,-0.04902,0.021556,0.01200289,0.04238921,0.06484881,0.00218638,-0.05755189,0.08081124,-0.03441703,0.0733217,0.04379687,-0.05132615,0.01402858,-0.02478415,0.03691064,-0.00136762,0.09253645,-0.0257702,0.05062167,0.07276263,0.21207428,0.00675528,0.05943893,0.05866746,0.02080596,0.05664231,0.11482647,0.02015717,0.02710008,-0.0349341,0.06641232,-0.00260767,0.06252666,-0.02313337,-0.04754387,-0.03403397,0.03751966,-0.01626856,-0.02820297,0.058421,-0.06576695,0.00502063,0.09593861,0.04537436,-0.03319143,-0.07448656,-0.03224723,0.04389576,-0.00715641,-0.03433972,-0.017926,-0.01590379,0.02604046,0.05050767,0.01433546,-0.00395451,-0.0479834,-0.02950175,-0.00330011,0.00711433,0.10046797,0.10069986,0.01595828],"last_embed":{"hash":"556afe35668dfaafac0c00f5d9cf4e12736f2e448bba8ef9696417a97aebd2dd","tokens":447}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.06215987,0.02015541,0.00457076,0.02083996,-0.0037715,0.01013671,0.07859838,-0.02070141,0.01015316,-0.01707004,-0.01678846,-0.00664018,-0.05104284,-0.00828177,-0.0390703,0.02292977,0.01942128,0.04445423,0.02185484,-0.01510459,-0.00850166,0.02278077,0.05629122,0.04511865,0.0106781,-0.02123806,0.00764805,-0.01921383,0.03277684,0.0505377,0.06389935,-0.03642804,0.0055251,-0.02999425,-0.07810573,0.02022498,0.0335086,-0.03318431,-0.03381899,0.02799553,-0.03955257,-0.00752912,-0.02120717,0.01634742,-0.10699963,-0.00077873,0.02677866,-0.02073443,0.00350101,0.01853643,0.00382654,-0.05939379,-0.00551758,0.03040971,-0.00655196,0.0577772,-0.02663629,-0.03726991,-0.01986707,0.012842,0.01049923,0.01493073,0.08386638,0.02135557,0.00382254,0.02659222,0.02527573,-0.03639948,-0.00430109,-0.01241964,-0.09516118,-0.00082175,0.0024852,-0.00763474,0.06112553,-0.06276374,0.06058114,0.0111768,-0.01699989,0.01078417,-0.11044211,0.01323786,-0.00481552,0.07228876,0.03671537,-0.04375243,0.01812618,-0.02418009,0.01689134,0.00626361,0.02527933,0.03721649,-0.00914173,-0.00786861,0.02810088,-0.07535227,-0.00961214,0.00398019,-0.02838464,-0.05537585,0.06103422,-0.00281391,-0.06982442,-0.00358858,-0.04302092,-0.05171752,-0.02585102,0.02222872,-0.03673825,-0.01391466,-0.01376396,0.00543164,-0.02110242,-0.04388968,-0.07790278,0.06233004,-0.03767503,-0.01070943,0.03855709,0.10359968,0.05681005,0.02820335,0.01393822,-0.02363576,-0.03720298,0.00920172,-0.00464625,-0.04210985,0.04530698,-0.01064769,0.01166285,0.01116679,-0.00807727,-0.00950547,-0.00467136,0.00403981,0.02338396,-0.0396707,0.04496633,-0.03945744,0.02711358,-0.02840664,-0.00768757,0.00636811,0.01153931,0.06835063,-0.01833585,-0.03577129,-0.03488531,0.05070525,-0.00864304,0.00330603,-0.02182987,-0.05131911,0.01081483,0.02263272,0.02905211,0.04539479,0.00607543,0.01528769,-0.01926546,-0.02128098,0.04504318,0.04843046,0.0243631,-0.00458027,0.04912011,0.02303015,-0.04897678,-0.0091731,-0.02641486,0.0246651,0.02657663,0.04324247,0.05322791,-0.02229225,0.03784524,0.04441952,-0.03822213,-0.01992544,-0.01018503,-0.04403006,0.00698369,0.02163739,0.02390467,0.01806833,-0.0270716,0.01562799,0.04006691,-0.01260847,0.0219894,-0.01071149,0.00411392,0.00870428,0.02610452,0.01188932,-0.00275101,-0.03682251,-0.03540765,0.03083138,-0.0044208,-0.03777666,-0.01568728,-0.04089375,0.02578867,0.01269529,-0.02279854,-0.0172424,-0.02040899,0.0667467,0.03408067,0.01895317,0.01538722,-0.00207047,-0.06050295,0.05162517,-0.05916991,0.07268022,0.02838209,0.00797379,-0.00199184,-0.08915714,-0.06176563,-0.01040924,0.01055,-0.05157363,-0.00892054,0.01769882,0.04995605,-0.00388566,0.01218063,0.0754364,-0.04461918,-0.01145993,-0.05146931,0.00306385,-0.0066215,0.04334388,0.03235487,0.0080508,-0.00584215,0.00995,-0.01034081,-0.01297414,-0.00239324,-0.00182497,-0.04023372,0.00365961,-0.00283951,0.04027215,0.00616678,0.00029733,-0.00990014,0.09333006,0.00848447,-0.04100092,-0.04848515,-0.0189625,0.01289711,-0.03443092,0.01562246,0.03142154,-0.04804638,0.02676643,-0.01641373,-0.03616028,-0.03375949,0.01683572,-0.06050383,0.06092422,-0.04730364,-0.03362656,0.06886541,0.00258465,-0.01577364,0.01238385,-0.0748127,-0.02057145,-0.05673375,-0.04555581,-0.02695811,-0.03127263,0.0189367,0.02039504,-0.05469856,0.0390426,0.00967233,0.00825802,0.01409522,-0.03512767,0.03462257,0.00700731,-0.04177515,0.05995719,-0.05127491,-0.04072006,0.01343037,0.03599115,0.00085851,-0.06026345,-0.03146518,0.06049925,0.03102371,-0.01559286,-0.00476207,0.0096001,0.00455508,0.01099567,-0.02300788,-0.06169515,0.01080318,-0.04125221,0.03264449,0.00216493,0.03744372,0.0729252,0.00605615,0.00766165,-0.02058643,-0.00064591,-0.04669988,0.01171885,0.02850177,0.00351975,0.03670276,-0.07407957,0.04930313,0.00599424,0.05778567,0.0270518,-0.0388645,0.02195817,0.0034825,0.00342026,0.04628075,-0.01080498,-0.04676334,-0.06592156,0.02604051,-0.05613277,0.04397816,-0.05732809,0.02458813,-0.05677436,-0.01086174,0.01913943,-0.0093821,0.00590415,-0.07349798,-0.0395462,0.01700973,-0.0379687,-0.0392401,-0.00714088,0.03620788,-0.01116554,-0.01126664,-0.00256281,-0.01339227,0.01990322,-0.00165948,-0.05256994,-0.05269735,-0.00384874,-0.0460835,0.03243778,0.07147299,0.06597653,-0.05565067,-0.03917284,0.01024795,0.02094923,-0.04011108,-0.01116051,0.0308455,-0.01172964,-0.02536986,0.0097722,0.00202771,-0.03056529,0.02310571,-0.0215771,-0.02166115,-0.03101729,-0.11418372,-0.04047153,0.03380135,-0.02707832,-0.06226336,-0.05621373,-0.04367828,0.03027564,-0.07082364,0.04013895,0.01943662,0.00716588,0.04978514,-0.00823797,-0.0394739,-0.01586846,0.02735493,-0.01201725,0.01632646,0.00930613,-0.01838931,0.05678937,-0.03667878,0.06950328,-0.01584157,-0.04244527,-0.00855357,0.04502547,0.01821237,-0.00147902,0.01732397,0.01779306,0.00739278,-0.03209393,0.02248963,0.06668966,0.02128003,-0.03149304,0.04065418,0.03506735,0.04109988,-0.01452069,-0.04563919,0.03489649,0.00795173,-0.0134921,-0.02061182,0.03051165,-0.08347589,-0.05677808,-0.02853017,-0.03733528,0.0712893,-0.00834576,0.00995507,0.0160119,-0.03831771,-0.03919791,0.00906408,-0.03420499,0.07052309,0.05670322,-0.00174506,-0.04040439,-0.00790669,0.07172276,-0.00319297,-0.03002618,-0.06549728,-0.02828481,0.01865747,-0.00837548,0.01368483,-0.01967394,-0.03138161,-0.02057667,-0.05355785,-0.02230937,0.04413112,-0.00300907,0.00815091,-0.01115075,-0.00279488,-0.03960044,-0.06845151,-0.01103352,0.0203174,-0.01982957,0.00629257,-0.01290757,0.07366077,0.02884677,-0.00995482,0.00894352,-0.0687086,-0.03857873,0.01990812,0.05993064,0.01857921,-0.00411083,0.00119486,0.06974837,-0.04588901,-0.00739178,0.02957353,-0.02564909,-0.03894518,0.05470842,0.03844094,-0.01325684,0.02856961,-0.00907882,-0.00994544,0.05719928,-0.00540009,0.03988911,-0.0077034,0.06716545,-0.04755468,0.00226008,0.03200379,0.03802871,-0.0396187,0.03980513,0.07951412,-0.02660505,-0.02721193,-0.07000551,0.08342858,0.02096681,-0.03216652,-0.07270104,-0.03885366,0.00814858,-0.00636394,-0.00956702,-0.01036131,0.02535388,-0.04578013,-0.0043631,0.02096772,-0.03813872,-0.0154052,0.04946436,0.05024935,0.00301638,-0.05184866,-0.02409283,-0.00092862,-0.01130517,0.04133727,-0.04313596,0.04150546,-0.01489972,-0.01708907,0.06171195,0.10000622,0.00005189,0.01012826,0.03592014,-0.03580593,-0.00491207,0.01168883,-0.01100517,0.07030727,-0.00714571,0.01356035,0.04984004,0.03259602,-0.01733906,-0.0388023,0.01225327,0.03334646,-0.06264117,-0.02680198,0.06468751,-0.00234707,0.03914722,-0.05175878,0.01105108,-0.00925881,0.00874174,-0.04445517,0.02134925,0.01724241,0.03608518,-0.04618887,-0.03026935,0.01897754,0.01937062,-0.05860018,0.00290806,0.00483214,-0.01351496,0.05163278,-0.07639082,0.0240411,0.00350365,0.04129552,0.00156265,-0.02859031,0.00980077,-0.00093636,0.04271713,0.02499781,-0.00323835,0.01584063,-0.06122786,-0.01088221,-0.01563589,-0.03579392,0.00532313,-0.02500315,0.04529715,0.04749366,-0.03613042,-0.00192652,-0.01498475,-0.001922,-0.00252209,0.00529549,-0.09973723,-0.02257446,-0.06283861,0.01662195,0.04115554,0.0052016,0.02260317,-0.00044084,0.00252396,0.01111777,-0.04948325,0.00013111,0.00057748,-0.00755533,-0.03023305,-0.02189635,-0.04928152,-0.03792102,-0.02417806,-0.00021348,0.04871889,0.00727362,0.01475498,0.04765851,-0.07334334,-0.06129552,-0.03261383,0.04205976,0.04836009,-0.00891769,0.04683102,-0.07282393,-0.05266722,-0.04230381,-0.03354471,0.02138948,0.03845884,0.00574062,0.06606817,0.02068975,-0.02430484,-0.01284825,0.01022242,0.03385032,-0.03168836,-0.00609821,-0.02400497,-0.00443603,-0.01135944,-0.05157284,0.03099999,-0.03973303,0.0182988,0.00954202,0.05453989,-0.05137531,-0.04058565,0.00565561,-0.01728566,-0.02845713,0.04814439,0.03178041,0.02551454,-0.03208975,0.0091979,-0.07090118,-0.01079532,-0.01224846,0.01967113,0.0232342,0.02979993,0.03044758,0.04393651,0.01587979,-0.02692848,-0.08751153,0.05296349,0.00399268,-0.00812794,0.07553279,-0.02196011,0.02183899,0.00290623,-0.02687904,-0.00948816,-0.02066671,0.01090479,-0.00055647,-0.00384884,0.04917572,-0.03746165,0.05970984,-0.01150862,0.01248121,0.03946066,-0.0115958,0.02251807,-0.03017871,0.00564434,-0.04110703,-0.00492945,0.01361849,-0.00413869,0.08963663,-0.05427675,-0.04948433,0.0183878,-0.03217635,0.014227,0.01801009,-0.06460261,-0.02080285,0.05339596,-0.01518264,-0.00317844,-0.04881308,-0.03393229,0.0304573,0.02344015,-0.04414696,-0.01615713,-0.02113718,0.00841917,0.00993463,0.00674784,-0.04043792,-0.08202346,0.01112584,0.04989655,0.03623097,-0.01028873,-0.02497336,-0.00374031,0.04781848,-0.01144134,-0.00115985,0.00084874,-0.02489801,-0.02734524,-0.01117966,0.02993632,-0.00072341,0.00949727,-0.02243406,0.04775104,-0.01452722,0.05496839,-0.01078064,0.05644712,0.04021378,0.02363788,-0.04397633,-0.00631066,0.00000106,0.01484452,-0.03665135,0.0094019,-0.00079101,-0.00635741,-0.00843987,-0.04035344,-0.0642438,0.03779405],"last_embed":{"hash":"1u635ni","tokens":537}}},"text":null,"length":0,"last_read":{"hash":"1u635ni","at":1748397845363},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境","lines":[31,77],"size":1003,"outlinks":[{"title":"session","target":"session","line":4},{"title":"token","target":"token","line":4},{"title":"反序列化","target":"反序列化","line":5},{"title":"序列化","target":"序列化","line":5},{"title":"json","target":"json","line":6},{"title":"序列化","target":"序列化","line":7},{"title":"json","target":"json","line":8},{"title":"序列化","target":"序列化","line":10},{"title":"Java","target":"Java","line":13},{"title":"反序列化","target":"反序列化","line":18},{"title":"序列化","target":"序列化","line":19},{"title":"反序列化","target":"反序列化","line":22},{"title":"PHP","target":"PHP","line":30},{"title":"Python","target":"Python","line":30},{"title":"序列化","target":"序列化","line":34},{"title":"反序列化","target":"反序列化","line":35},{"title":"#Python的反序列化实验","target":"#Python的反序列化实验","line":36},{"title":"反射（reflection）机制","target":"https://xz.aliyun.com/t/7029/","line":38},{"title":"Java","target":"Java","line":38},{"title":"变量","target":"变量","line":41},{"title":"Java","target":"Java","line":43}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#实际测试": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.1260484,-0.00291002,-0.01869081,-0.04101758,-0.02298717,-0.00164972,-0.0128682,0.01528415,0.07031959,0.01277318,0.00065272,-0.11742014,0.04124004,0.04261542,0.06299368,-0.02258089,-0.03152676,-0.00263504,-0.05832726,0.00360176,0.10069013,-0.03550887,-0.02983123,-0.08905229,-0.0012044,0.0460578,0.0198963,0.01161973,-0.02517035,-0.16615732,0.06566638,-0.00696149,0.02758127,-0.0120944,0.0143081,0.01400258,0.03849756,0.03631371,-0.00732907,0.0529597,0.03177118,0.02820171,0.03393142,-0.04102997,-0.02567049,-0.06277187,-0.08268631,-0.0032875,0.01698745,-0.03489897,-0.02158412,-0.01712292,-0.05762141,-0.02199251,-0.01502612,-0.0023608,0.01386079,0.00745777,0.04000486,-0.01780812,0.02009103,0.0062391,-0.19455704,0.08320773,0.01362685,-0.02758282,-0.02649341,0.00579757,0.02823826,0.02206475,-0.09974544,0.02512866,0.01617357,0.06769531,0.03472266,-0.03847184,0.04035769,-0.0258237,-0.03091125,-0.05094323,-0.05001274,0.04705237,-0.0387906,-0.00396136,0.00087497,0.01308095,-0.00377021,-0.03493026,0.02920876,-0.04570572,0.03629923,-0.07709675,-0.04255151,0.0543489,-0.03115819,0.03024488,0.02867575,0.03995143,-0.10602077,0.11743287,-0.05101985,0.01803671,-0.01752367,-0.02562435,-0.01009422,-0.03727415,-0.017415,-0.03524309,-0.05718111,-0.03764369,-0.00991215,-0.06333642,0.07197684,-0.01754026,-0.00151597,0.04030242,-0.00035936,-0.0207441,-0.03655137,0.00757658,0.01694831,-0.02180499,0.08579107,-0.0285563,-0.03561105,0.00947669,-0.00218588,0.08568133,-0.03095812,0.02293969,0.06107632,-0.03536234,-0.0579143,-0.0061137,-0.01375816,0.00625319,-0.02892363,0.02029708,0.03559222,-0.01437586,-0.00562489,-0.08709081,0.01605223,-0.09331424,-0.0815213,0.03952978,-0.0134423,0.01141575,0.0227431,-0.02120517,0.01328821,0.02973094,-0.02136141,0.00639452,-0.03183907,0.01272212,0.08296689,0.07157687,-0.03306767,-0.03886844,-0.01678785,-0.01098154,-0.0738797,0.12276975,-0.00572341,-0.06296871,-0.05041355,-0.00522277,0.02194091,-0.02832623,0.05409849,0.00376616,-0.02124376,-0.01737675,0.05699607,-0.02977335,-0.03415713,-0.01221418,0.10279533,0.0077029,0.07904828,-0.04763183,-0.06432648,0.02528905,0.02467758,-0.04930866,0.02146825,-0.04414378,-0.01258505,-0.04262276,-0.05897355,0.03199881,-0.03338953,-0.03522476,-0.05560795,-0.047257,-0.00039663,0.02178242,0.0395271,-0.02264716,0.10757653,0.02527042,-0.05583624,0.0035119,-0.01243163,-0.00649145,0.01910483,0.00503279,0.08518136,0.02183742,0.01702983,0.00520699,0.04192884,-0.01427934,-0.05137346,0.01098167,0.02508639,0.02685728,0.02528864,-0.01795183,0.03528631,-0.06369539,-0.06167445,-0.20219171,-0.00564921,0.03923507,-0.03931919,0.0452936,-0.021015,0.02721199,-0.01944097,0.06340253,0.10212878,0.07826243,0.00860277,-0.05214766,0.01095228,-0.0004787,0.02031353,-0.00216652,-0.00784376,-0.04898523,0.02163104,-0.03498634,0.00885515,0.00776466,-0.02874469,0.05233398,-0.05063562,0.14960714,0.00165515,0.05957209,0.04226793,0.010143,0.01584792,-0.00693263,-0.10368381,0.02786847,0.0730749,-0.02558053,0.0136984,-0.0085394,-0.01756579,0.06674657,0.03700783,-0.05698732,-0.05387852,-0.00156266,-0.01192293,-0.02983478,0.01146397,-0.01191014,0.07340929,0.00708445,0.05249675,-0.03716095,-0.02388195,-0.02001311,-0.07522877,-0.03109424,-0.03215548,-0.00095102,0.04662297,0.01338472,-0.06525842,-0.01736137,0.05210443,0.01883278,-0.03663461,-0.00206888,-0.04530913,-0.01733897,0.04146495,0.00843304,0.12360467,0.05109652,-0.05343256,0.01175094,0.04071175,0.02928204,0.00297225,0.01241504,-0.00460626,0.02714727,0.04752886,0.02064579,0.01679841,0.02499008,-0.00529541,-0.0135198,0.0148404,0.06747073,-0.06892551,-0.05964528,-0.02013629,-0.01682124,0.08015796,0.09430667,-0.03020534,-0.2973817,0.04336235,-0.03381712,0.03354976,0.05490914,0.03086408,0.07767707,0.06186087,-0.03440709,0.02308478,-0.08028711,-0.0118723,0.01092956,-0.01160641,-0.01339862,-0.01495725,0.04741641,-0.03917628,0.06950538,-0.0304236,0.00292833,0.06003227,0.17997055,-0.00311822,0.07059488,0.00184335,0.02110412,0.05472353,0.07714125,0.06452239,0.02407781,-0.0559466,0.03335637,-0.0365258,0.07413341,0.02925003,-0.06904099,-0.02201225,0.04216803,-0.02497545,-0.01075374,0.06088181,-0.10815734,0.00312372,0.10873464,-0.0023913,-0.0151434,-0.10869618,-0.00510984,0.06402139,0.02826177,0.039686,-0.02194501,-0.01661757,0.02723753,0.05133864,0.03064129,-0.0013897,-0.04241325,-0.02173555,0.02719759,-0.01614772,0.12931824,0.10948578,0.01665883],"last_embed":{"hash":"a587b12f62a2b7b76928d3d92c1da754abb6cd2c772c86c99b3a0dfc8c4fcc53","tokens":432}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.10049605,-0.00245773,0.01468161,0.01506671,-0.03881512,0.02679985,0.05827126,-0.03337008,-0.01138567,-0.03138858,-0.00096084,0.02748311,0.03974095,-0.01150527,-0.03486818,0.04910057,-0.01114219,0.06067478,-0.00665194,0.00098926,0.0125614,-0.00976064,0.05003876,0.03035828,-0.01408529,-0.01161109,0.04628404,-0.0230293,0.02786789,0.03445252,0.0374207,-0.0656972,-0.00286552,-0.00571943,-0.0252605,0.03837915,0.04251424,0.00240058,-0.0613879,0.00795196,0.01353163,0.00810056,-0.04478737,0.0002761,-0.13313955,0.03345793,-0.01246515,-0.05765716,-0.02693649,0.0132574,-0.01641726,-0.0592069,0.03026413,0.04697486,0.00297534,0.04332997,-0.01815434,-0.06551912,-0.01569929,0.02400349,-0.02480994,0.00344753,0.07641801,0.02686673,-0.01006503,0.03451021,0.04893076,-0.08390073,-0.02482907,-0.02547365,-0.05284096,-0.03241097,-0.00334615,0.01365601,0.00105357,-0.07959283,0.05981754,0.00554712,0.00451819,0.00048364,-0.08810602,0.00517249,-0.0298059,0.06709152,-0.01181528,-0.02779565,-0.00416878,-0.02531199,0.01657656,0.00894981,0.01744032,0.03509823,-0.0220162,0.0077509,0.03261819,-0.00368132,-0.00591781,0.01177032,0.02090861,-0.04408797,0.01892838,0.02095789,-0.07609362,-0.00820783,-0.02215666,-0.01975537,-0.02391282,0.04089041,-0.02314569,-0.01100358,-0.01895071,0.00105575,-0.03060634,-0.04162015,-0.0147326,0.00544528,-0.04359689,0.03104815,0.02939625,0.08285163,0.03573764,0.02739231,0.01407694,-0.00629688,-0.01498927,0.00812251,-0.02499818,-0.05035434,0.03873419,-0.04954982,0.00988224,-0.01172996,0.00388094,0.02371699,0.0279293,0.00003295,0.01239371,-0.02751797,-0.00700616,-0.01520915,0.017663,0.01780273,0.01486374,-0.01295357,0.03550754,0.00713373,-0.01567951,-0.04515928,-0.01463045,0.05734672,-0.01640642,-0.0070499,-0.02429009,-0.05825423,-0.05691462,0.0346009,0.0230545,0.03742881,0.01842688,-0.00184415,-0.02753279,-0.00232274,0.06638788,0.03162895,0.06029451,0.01055923,0.0725307,0.02150577,-0.02424264,-0.02391133,-0.00211888,0.04218343,0.04332924,0.03074452,0.04766668,0.01827034,0.06559672,0.09085528,-0.02045806,-0.03241195,-0.00903537,-0.03690458,0.01131701,0.03631042,0.0121743,0.01517521,-0.02487165,0.07235111,0.00882627,-0.0241856,0.01714684,-0.00878157,0.00586389,-0.04052087,0.02288816,0.00383108,-0.01688951,-0.00221676,-0.02113549,0.0025605,-0.00703349,0.00258653,-0.01538364,-0.05382273,0.05192806,0.02921246,-0.01275048,-0.04888275,0.01069192,0.07239478,0.05918726,-0.01259945,0.05140759,-0.00666493,-0.03254785,0.05742575,-0.03138493,0.06045724,0.01451415,-0.00249369,0.00902987,-0.07285699,-0.07735192,-0.02231744,-0.0154222,-0.00157909,-0.02787217,0.02236369,0.04335798,0.02203588,0.00724495,0.07772687,-0.02249125,-0.04555931,-0.03535487,-0.01025449,-0.02632151,0.02941112,0.03239201,-0.00151581,0.00888048,0.02165196,0.00858468,0.03773321,-0.02206463,0.00754264,-0.04957523,-0.01263998,-0.03808669,0.04104641,0.05163448,0.02197698,0.02348428,0.09302195,0.01350791,-0.04299836,-0.06476261,-0.00111648,-0.01338967,-0.02488519,0.01876243,0.00364554,-0.02264085,0.01974725,0.0050295,-0.00454902,0.0154086,0.0283611,-0.01814886,0.05750629,-0.09214577,-0.04088137,0.04335882,-0.00015443,-0.01633155,-0.0134995,-0.06147718,-0.03755961,0.02935417,-0.00856728,-0.04831723,-0.05030083,-0.00063893,0.02228959,-0.09049528,0.00471193,0.01754578,0.01600235,0.05620416,-0.02205155,0.02550301,0.00448151,-0.03605592,0.02582633,-0.01077725,-0.02238991,-0.01995005,0.02000378,0.03937427,-0.08702476,-0.05118468,0.0217288,0.03359976,-0.00602886,0.04969781,0.02169099,0.001999,0.00509699,-0.04963098,-0.02411523,-0.01059795,-0.01872407,0.03528555,0.03349536,-0.00821989,0.0489113,0.02406872,0.00786404,0.00314778,-0.02163209,-0.04869632,-0.01279949,-0.00600205,0.0397798,0.03672777,-0.08157149,0.04003459,0.0040938,0.00589016,0.01037144,-0.06979616,-0.01640845,-0.01551598,0.03606905,0.01074725,-0.00208907,-0.03643496,-0.08100072,0.04313379,-0.04218678,0.03349358,-0.05799635,0.00757674,-0.07379983,0.00787616,0.06812316,0.00668697,0.00998354,-0.04153169,-0.04219379,0.00417283,-0.02066819,0.00127731,0.01834444,0.01420047,0.00410366,0.01225843,0.01745942,-0.02869477,0.00945831,0.01797308,-0.02961384,-0.02506335,-0.02203739,-0.06106634,-0.02001528,0.03265963,0.08515207,-0.05171439,-0.0080783,0.00781953,0.01411783,-0.04889434,-0.0208833,0.01233473,-0.0086398,-0.01602512,0.01170159,-0.03115498,-0.01414281,-0.01800429,-0.06303246,-0.0304392,-0.04728033,-0.07025375,-0.01239751,-0.00029687,-0.01770366,-0.00640212,-0.03951995,-0.00478719,0.00632013,-0.05871273,0.02694105,0.01486824,-0.02069689,0.01989907,-0.01545791,-0.03221258,0.02545623,0.0631966,0.00990201,0.0165289,-0.0199941,-0.02599907,0.01854102,-0.02739174,0.079345,-0.02106836,-0.00457695,-0.04480933,0.08954627,-0.0177257,0.02491101,0.00324038,0.02969607,0.05272735,0.03220698,-0.00379734,0.02674272,0.04980977,-0.01093272,0.02947918,0.01696654,0.00843615,-0.01735771,0.00508686,0.01763261,0.02273491,0.00803693,0.0231481,0.00550413,-0.09462675,-0.05892525,0.01585357,-0.02691553,0.04522233,0.02038602,0.0293053,0.01904471,-0.01643193,-0.03309,-0.00820171,-0.06420407,0.01550452,0.04229381,0.02761222,0.0002395,-0.04913145,0.03598579,0.00539477,-0.01950959,-0.13853803,-0.05013307,0.00613421,-0.01382213,0.00606108,-0.02732312,-0.02112349,0.01900092,-0.12449035,0.04433769,0.00794108,0.03745855,0.02413447,-0.02914042,-0.01058191,-0.0710656,-0.01201449,-0.00139852,0.02740094,0.0267256,0.00365056,-0.02593931,0.10074653,0.00875435,-0.00173453,-0.01762402,-0.04006119,-0.01297637,0.0102758,0.04952983,0.00314561,-0.00661506,-0.02908171,0.05040534,0.00789986,-0.03861899,0.01617605,-0.00752146,0.01423158,0.05273307,0.01895959,-0.07488532,0.040877,0.01385636,-0.02247775,0.06171595,-0.01290766,0.03620898,0.01409605,0.06529937,-0.03071612,0.02880201,0.07056198,0.02740793,-0.05072559,0.01312942,0.07071666,-0.03760872,-0.00049209,-0.08825937,0.0586292,0.0275279,-0.013997,-0.04327727,0.0159178,-0.01197401,0.00386592,-0.02670081,-0.03240072,0.05454079,-0.01844921,-0.00507358,0.03500712,-0.00305594,-0.01682995,0.07141709,0.031332,0.02227228,-0.01236888,-0.01481486,-0.00534596,-0.01086216,0.00388509,-0.00685015,0.02706196,-0.00408645,-0.01884046,0.02046617,0.03826756,0.02619498,0.0177025,0.07160617,-0.03647332,0.00537969,0.01129832,-0.04077445,0.00233793,0.02818244,0.0120189,0.06986067,0.00296665,-0.03140951,-0.00741774,0.02477117,0.01502812,-0.04868829,-0.00837872,0.04121657,-0.02062004,0.00348309,-0.03276135,-0.01879272,-0.02151923,0.00641244,-0.04344864,-0.01387604,-0.03635225,0.04181685,-0.06122304,-0.01801326,0.0372531,-0.01046828,-0.05624731,0.00505396,0.00197405,-0.01439914,0.07504066,-0.03943786,0.0251405,0.00413194,0.02750583,-0.02303322,-0.08168842,-0.02596897,0.04114355,-0.00459411,0.04215892,0.03885683,0.0018292,-0.06648102,-0.01566948,0.03390391,-0.03583999,-0.00090384,0.02883074,0.05956873,0.0319678,-0.02447679,-0.00949175,-0.05033937,-0.0255996,-0.01899868,-0.02414973,-0.06376791,-0.01487084,-0.08384492,0.00644234,0.056963,0.04295787,0.04216512,-0.00685003,-0.01683887,0.03832404,-0.01613705,0.01100588,-0.00953024,0.01458036,0.00075121,-0.00455164,0.01108705,0.00767103,0.00968709,0.01019396,0.00565759,-0.02073679,-0.00860505,0.03232735,-0.03599146,-0.04932486,-0.00934617,0.06065385,-0.00364606,0.00979112,0.07798052,-0.06503014,-0.09685372,-0.04909797,-0.01132701,0.03201212,-0.02272514,-0.0106895,0.03913296,0.01570802,0.01404464,-0.01691493,-0.01587021,0.04404323,-0.06610081,-0.02019778,-0.02392305,-0.00993919,-0.01821072,-0.04460734,-0.01881528,-0.02204628,0.05059634,-0.0124214,0.0465612,-0.08623074,-0.05199162,-0.01628024,-0.01509312,-0.05195531,-0.00506216,0.00437034,0.03939017,-0.02533901,-0.01185575,-0.04924987,-0.03536385,0.02901087,-0.00631544,0.0345385,0.04796294,-0.013326,0.03272438,0.0357958,-0.00567529,-0.09054735,0.02478018,-0.00106175,-0.01446776,0.05404051,-0.0225297,-0.00221588,0.02456259,-0.05230447,-0.01203646,-0.01494229,0.06973147,0.00079257,0.01515095,0.08106739,-0.03529071,0.02098006,-0.04535715,0.03256517,0.01846808,0.01994466,0.0243549,-0.01214601,0.00722862,-0.0441079,0.00793793,-0.00588485,0.02541693,0.09071972,-0.04523817,-0.06491391,-0.02901741,-0.008179,-0.01279795,0.0188328,-0.00865573,-0.02620518,0.05884779,-0.02062079,0.00905131,-0.02968774,0.01756785,0.05689238,0.03186172,-0.04418022,-0.01056526,-0.00629137,0.01012652,-0.00626877,0.03679093,0.00711063,-0.056337,-0.00292514,0.09932431,-0.00117555,-0.03216738,-0.04093746,0.00379679,0.02749823,-0.02449217,0.00249388,0.00048134,-0.02049282,-0.02494279,-0.0078683,0.01062674,-0.03479724,-0.00309274,0.02378022,0.06629556,-0.02656628,0.05544143,-0.0419813,0.02929958,0.00276527,-0.0167452,-0.03675197,0.01195505,7.1e-7,-0.00193479,-0.06721437,0.04585675,0.0026292,-0.01721031,-0.02300866,-0.00573133,0.01812169,0.07615285],"last_embed":{"hash":"1vm96m9","tokens":595}}},"text":null,"length":0,"last_read":{"hash":"1vm96m9","at":1748397845408},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#实际测试","lines":[78,141],"size":1290,"outlinks":[{"title":"序列化","target":"序列化","line":22},{"title":"序列化","target":"序列化","line":52}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验": {"path":null,"embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.12591203,-0.00297475,-0.018411,-0.04090662,-0.02288298,-0.00179718,-0.01295187,0.01495605,0.07025208,0.01282358,0.00061479,-0.11729167,0.04112878,0.04252064,0.06303554,-0.02252921,-0.03139041,-0.0029062,-0.05827985,0.00384227,0.10052194,-0.03539032,-0.0298404,-0.08897764,-0.001175,0.04606991,0.01964458,0.01178367,-0.02545631,-0.16605814,0.06563383,-0.00702775,0.02760673,-0.01240593,0.0143041,0.01395865,0.03853482,0.03605659,-0.00723946,0.05278066,0.03199037,0.02833265,0.03375633,-0.04097853,-0.02595167,-0.06282684,-0.08276248,-0.00328308,0.01692098,-0.03485646,-0.02123448,-0.01703715,-0.05747603,-0.02192724,-0.01498981,-0.00261476,0.01368785,0.00745013,0.03995446,-0.01756155,0.02007439,0.0062102,-0.19459854,0.08360631,0.01329562,-0.02772092,-0.02632535,0.00563738,0.02800156,0.02188138,-0.09983038,0.02489606,0.01631,0.06750791,0.03471774,-0.03891503,0.0402601,-0.0257453,-0.03101602,-0.05083499,-0.04996699,0.04696003,-0.03881207,-0.0040733,0.00038486,0.01296012,-0.00343891,-0.03530567,0.02915402,-0.04553854,0.03626116,-0.07766664,-0.0422888,0.05436443,-0.03107004,0.03025944,0.02844393,0.04013405,-0.10595639,0.11733743,-0.05097284,0.0181121,-0.01786063,-0.02539426,-0.01019743,-0.03705384,-0.01753422,-0.03487,-0.05724449,-0.03776706,-0.00980463,-0.06343675,0.07222874,-0.0174375,-0.00156524,0.0404509,-0.00070405,-0.0207848,-0.03671501,0.00769213,0.01695941,-0.02211317,0.0859506,-0.02850213,-0.03588921,0.00996559,-0.00199102,0.0859455,-0.03113703,0.02288258,0.06088783,-0.03569048,-0.05768586,-0.00597905,-0.01359674,0.0063736,-0.02891858,0.02012267,0.03554368,-0.01443262,-0.00566024,-0.08701977,0.01603074,-0.09312952,-0.08120936,0.03955879,-0.01326694,0.01160893,0.02249372,-0.02121328,0.01329959,0.02985029,-0.02125645,0.00648196,-0.03200254,0.01282983,0.08294484,0.07114821,-0.03285378,-0.03885467,-0.01670691,-0.01112576,-0.07340702,0.12276705,-0.00589878,-0.06294694,-0.05066671,-0.00519748,0.02187094,-0.02804276,0.05390017,0.00366091,-0.02128731,-0.01745947,0.05682203,-0.02962103,-0.03402075,-0.01203621,0.10295091,0.00741193,0.07900245,-0.04768362,-0.06419569,0.02512338,0.02461105,-0.04940028,0.02148116,-0.04391554,-0.01274156,-0.04276197,-0.05887503,0.03226858,-0.03336095,-0.03537728,-0.05554713,-0.04701222,-0.00058683,0.02187549,0.03962387,-0.02271744,0.10752785,0.0252488,-0.05580564,0.00312381,-0.01216078,-0.00620512,0.01885363,0.00509309,0.08528115,0.021397,0.01677904,0.00503228,0.04226623,-0.014297,-0.05156768,0.01093285,0.0251024,0.02672749,0.02536685,-0.01814562,0.03523776,-0.06386404,-0.06137395,-0.20245221,-0.00518862,0.03909766,-0.03931845,0.04528724,-0.02111477,0.02710079,-0.0192702,0.06319777,0.1020251,0.07839013,0.00832793,-0.05198311,0.01110714,-0.00064731,0.02030924,-0.00216927,-0.00797454,-0.04874012,0.02142501,-0.03529922,0.00856078,0.00782801,-0.02835538,0.05244835,-0.05053725,0.14977297,0.00184702,0.05954881,0.04201577,0.0105032,0.01613303,-0.00690365,-0.10390695,0.02776768,0.07339008,-0.02543131,0.01378066,-0.00838184,-0.01756887,0.06663163,0.03718124,-0.05711831,-0.05379152,-0.00130166,-0.01172192,-0.02956049,0.01177686,-0.01215776,0.07330444,0.00713088,0.05255265,-0.03731562,-0.02387168,-0.01995385,-0.07549215,-0.03079063,-0.03217731,-0.00103246,0.04630052,0.01346281,-0.06541964,-0.01746667,0.0519884,0.01904611,-0.03677918,-0.0022644,-0.04529075,-0.0170739,0.04135505,0.00859129,0.12328952,0.05105617,-0.05339872,0.01154516,0.04088803,0.02915842,0.00316744,0.01214348,-0.00461782,0.02726812,0.04770845,0.02040318,0.01681941,0.02512678,-0.00526085,-0.01372317,0.01494524,0.06762961,-0.06887708,-0.05965873,-0.02023853,-0.01648152,0.08079728,0.09467126,-0.03018684,-0.29746726,0.04335635,-0.03394039,0.03375724,0.05484198,0.03092148,0.07774978,0.06206622,-0.03397227,0.02295423,-0.08032096,-0.01168806,0.01084664,-0.01158035,-0.01356808,-0.01490706,0.04743757,-0.03910405,0.06961954,-0.03064842,0.00272648,0.05980188,0.18002076,-0.00293564,0.07036674,0.00173597,0.02118196,0.05475132,0.07699368,0.0647684,0.02427381,-0.05589917,0.0334402,-0.03662755,0.07414056,0.02955762,-0.06893683,-0.02195968,0.04199771,-0.02515472,-0.01083314,0.06086347,-0.10837188,0.00306347,0.10880959,-0.00252684,-0.01512432,-0.10864781,-0.00536638,0.06394881,0.02853914,0.039683,-0.02194452,-0.01649537,0.02714819,0.05101414,0.03068194,-0.00120568,-0.04233221,-0.02167219,0.02735659,-0.01621804,0.12919579,0.10971908,0.0165793],"last_embed":{"hash":"1a641bc05fe348bad86abfb56b92f90353b2702e52185c7f08bdac2faa38d34f","tokens":432}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.10174771,-0.0044375,0.01638716,0.01333256,-0.03820334,0.02674731,0.05471493,-0.03378232,-0.01254436,-0.03237311,0.00213528,0.0303308,0.04590948,-0.01411717,-0.03517542,0.05031895,-0.01288806,0.05699216,-0.00681232,0.00265347,0.00939899,-0.01148864,0.04800203,0.032378,-0.01589433,-0.01181201,0.04690855,-0.02259369,0.03144473,0.03385638,0.03440546,-0.06570069,-0.00065083,-0.00440199,-0.01707329,0.03961321,0.04369931,0.00417362,-0.06110865,0.00418616,0.01409952,0.00899852,-0.04581844,-0.00053461,-0.13352107,0.03324994,-0.01463561,-0.06026933,-0.02714917,0.01345577,-0.01310021,-0.06090736,0.02881912,0.04574063,0.00157832,0.04099322,-0.01427856,-0.06793103,-0.01690624,0.02701523,-0.0250455,0.00440635,0.07469111,0.02707282,-0.01386817,0.03226453,0.0486208,-0.08362079,-0.02671952,-0.02577279,-0.048411,-0.03193609,-0.00097845,0.0119628,-0.00069741,-0.08200368,0.05939223,0.00400779,0.00371339,0.00229309,-0.0858351,0.00445755,-0.03112395,0.06399567,-0.0152242,-0.02710846,-0.00627394,-0.02471259,0.01886121,0.00899191,0.01689267,0.03426791,-0.02347362,0.0067777,0.03333964,0.00020667,-0.00414811,0.01077841,0.02223081,-0.04354988,0.01946694,0.02221296,-0.07708623,-0.01008959,-0.0269862,-0.01729351,-0.02686495,0.04078701,-0.02361215,-0.01298257,-0.01662307,0.00267288,-0.03263459,-0.03920746,-0.01261311,0.00048942,-0.04448812,0.03228647,0.02832026,0.08218606,0.0312107,0.02906701,0.01296495,-0.00389744,-0.01242991,0.00732072,-0.02316293,-0.05176678,0.03687549,-0.05200216,0.00788258,-0.01011403,0.00635975,0.02664061,0.02654493,0.00167618,0.01238177,-0.02617287,-0.01296008,-0.01390597,0.02110064,0.01984242,0.01401596,-0.01379617,0.03467643,0.00816612,-0.01405271,-0.04047063,-0.01335092,0.05659207,-0.01455729,-0.00826125,-0.02315031,-0.05623678,-0.05897842,0.03498184,0.02188779,0.03328307,0.01833008,-0.00190628,-0.02873281,0.00316507,0.06622186,0.02910366,0.05561693,0.00895147,0.07380004,0.02191548,-0.02334819,-0.0267751,-0.00184896,0.04361694,0.04394911,0.03094779,0.04532888,0.01985541,0.06459314,0.09372887,-0.0197807,-0.03102127,-0.01071193,-0.03577325,0.01464937,0.03510465,0.0096304,0.01569758,-0.02710542,0.07254485,0.00697187,-0.02353529,0.01670587,-0.00864395,0.0067755,-0.04198844,0.02170644,0.00510143,-0.01811567,-0.00313923,-0.02240414,-0.00021676,-0.00525158,0.0014912,-0.01632913,-0.05652378,0.05544217,0.02884338,-0.01684797,-0.04731126,0.01219494,0.0684934,0.05920898,-0.01374059,0.05024327,-0.00749075,-0.02927853,0.05860789,-0.02907157,0.05499801,0.01251655,-0.00393172,0.00958063,-0.06811041,-0.07614303,-0.02500483,-0.01421774,0.00113751,-0.02793624,0.02511231,0.04255427,0.02175355,0.00534929,0.07766492,-0.02316036,-0.04408742,-0.03475916,-0.01420179,-0.02935317,0.02949285,0.03535373,-0.00601697,0.01143998,0.02134163,0.00761714,0.03561789,-0.02411239,0.00934233,-0.04817216,-0.01381625,-0.03696797,0.04231899,0.05423174,0.02213046,0.02493444,0.09356748,0.01180723,-0.04065952,-0.06176137,0.00281093,-0.01177597,-0.02446284,0.01934144,0.00145998,-0.02385138,0.01853528,0.0044451,-0.00106573,0.01603704,0.03134118,-0.0134239,0.05474533,-0.08774126,-0.04261148,0.0425386,-0.00308046,-0.01489586,-0.01600674,-0.06028489,-0.03618436,0.03633112,-0.00868159,-0.04869257,-0.05367569,0.00380407,0.02285783,-0.09030677,0.00241497,0.01759522,0.016531,0.0566225,-0.01928154,0.02489911,0.00312204,-0.03160397,0.02113985,-0.00993289,-0.02545194,-0.01782974,0.02175395,0.0397371,-0.08773686,-0.05221843,0.01949278,0.03434117,-0.00770744,0.04782183,0.02113769,0.0019375,0.00392897,-0.04634714,-0.02037327,-0.01300655,-0.01654871,0.03754275,0.03386264,-0.01294036,0.04834327,0.02860166,0.00485725,0.00604947,-0.02422476,-0.04434997,-0.01051908,-0.00624229,0.04028644,0.03610471,-0.07879457,0.03824189,0.00791638,0.00457401,0.00806426,-0.06833922,-0.01804375,-0.02060518,0.03810681,0.00897408,-0.00100803,-0.03597886,-0.07980514,0.04439196,-0.04304883,0.02922179,-0.05684954,0.00727924,-0.07492574,0.0078581,0.06830902,0.00816988,0.00985565,-0.03976416,-0.04619096,0.00126306,-0.02101144,-0.00124433,0.01963989,0.01377752,0.00531108,0.01366441,0.01747825,-0.03116937,0.01019794,0.01628391,-0.02955052,-0.02262131,-0.02212099,-0.06173998,-0.02272437,0.02970238,0.0836781,-0.05252479,-0.00631443,0.0047117,0.01633744,-0.04291072,-0.02049924,0.01206769,-0.0082131,-0.01485328,0.00788739,-0.02948335,-0.01414713,-0.02009807,-0.0632419,-0.02293893,-0.04792736,-0.06985611,-0.01418933,-0.00285818,-0.016581,-0.00143397,-0.04030392,-0.0019931,0.00495232,-0.0557995,0.02700047,0.01372576,-0.01941594,0.01705214,-0.01524942,-0.03324002,0.02411848,0.06521129,0.0065033,0.01353057,-0.02331258,-0.02128505,0.01477348,-0.02601745,0.07662459,-0.02045734,-0.00443396,-0.04710275,0.09258527,-0.02114559,0.02631889,0.00116706,0.03075212,0.05579839,0.03319248,-0.00384049,0.02408316,0.04996685,-0.00948854,0.0259704,0.0148111,0.00684027,-0.01778547,0.00822707,0.02069563,0.02145932,0.00926249,0.02516912,0.00512442,-0.09343696,-0.06093901,0.01673716,-0.02588579,0.04517517,0.02161958,0.02871648,0.01988637,-0.01823547,-0.03248932,-0.00862658,-0.06491943,0.0162117,0.03856433,0.02924192,0.00962528,-0.04808757,0.03302526,0.00939307,-0.01590746,-0.14226493,-0.04743883,0.00316801,-0.01579688,0.00394047,-0.02667801,-0.02271088,0.02209011,-0.12499693,0.04449876,0.00504553,0.03833211,0.02204857,-0.02727978,-0.00950143,-0.06970266,-0.0127933,-0.00372712,0.02707374,0.02390886,0.00291399,-0.02817212,0.10014293,0.00777634,-0.00053016,-0.01864388,-0.04026162,-0.01255428,0.01009903,0.04861446,0.00355558,-0.00826932,-0.03221934,0.04722582,0.00823628,-0.03732432,0.01709204,-0.00966077,0.01995534,0.05007074,0.01619214,-0.07569502,0.03904438,0.0180657,-0.02816218,0.0616301,-0.01522652,0.03250198,0.0153813,0.06520429,-0.0340872,0.02922167,0.06915419,0.02686977,-0.05250552,0.01151265,0.07176578,-0.03889883,0.00320839,-0.08612029,0.05909989,0.02334146,-0.01576977,-0.04371113,0.01867151,-0.01373231,0.00343615,-0.02998426,-0.03393559,0.05687106,-0.01472921,-0.00373618,0.03777412,-0.0018365,-0.01609798,0.07036898,0.03105413,0.01838521,-0.00978712,-0.01417537,-0.0040137,-0.01362738,0.00293218,-0.00447817,0.02642121,-0.00488854,-0.01912476,0.02102442,0.03657467,0.02633583,0.01714168,0.06979606,-0.03543326,0.00453755,0.01207645,-0.04048153,0.00043165,0.02903907,0.01192199,0.06988418,0.00418685,-0.03256336,-0.00560731,0.02233602,0.01417127,-0.05001444,-0.01008859,0.03811338,-0.02368125,0.00122299,-0.03146038,-0.01468141,-0.02167166,0.00370682,-0.04385189,-0.0158337,-0.03588407,0.04540449,-0.06505845,-0.01727977,0.0392718,-0.01206921,-0.05558055,0.00725974,-0.00066693,-0.0145885,0.07546727,-0.03859115,0.02449314,0.00612629,0.02604487,-0.02601139,-0.0834009,-0.02793804,0.04916999,-0.00511376,0.04592467,0.0362465,0.00558137,-0.06539416,-0.01674983,0.03340076,-0.03156767,0.00396656,0.02903115,0.06127959,0.03325832,-0.02375556,-0.00704465,-0.05146024,-0.02608252,-0.01844888,-0.02708942,-0.06480223,-0.0143355,-0.08336634,0.00836907,0.05585688,0.0443015,0.04312465,-0.0064503,-0.01476614,0.04085683,-0.01626476,0.01130043,-0.0098306,0.01654165,0.00355514,-0.00635144,0.01295513,0.00925633,0.01314095,0.01532407,0.00373329,-0.02281289,-0.01013966,0.03038692,-0.036718,-0.04506802,-0.00910001,0.06156632,-0.00638075,0.01146675,0.07669862,-0.06243569,-0.10224885,-0.04908643,-0.0137428,0.03095512,-0.02633303,-0.01230721,0.04173537,0.01511716,0.01659371,-0.01944451,-0.01772965,0.04202444,-0.06934687,-0.01889741,-0.02163715,-0.01174579,-0.0202751,-0.04158296,-0.02413659,-0.01914357,0.05279002,-0.01462304,0.04476811,-0.08693137,-0.05080931,-0.01345785,-0.01208495,-0.05126368,-0.00861719,0.00350871,0.03912212,-0.02676247,-0.01586155,-0.04809463,-0.03235581,0.03369784,-0.00674738,0.03380962,0.04601655,-0.01430546,0.03530473,0.03717922,-0.00467261,-0.09166258,0.02432825,0.00206375,-0.01507986,0.05361234,-0.02300806,-0.00127134,0.02356211,-0.04707019,-0.01217002,-0.01588359,0.06953343,0.00155954,0.01513554,0.08257287,-0.03508019,0.01738073,-0.04252059,0.03287903,0.01655972,0.02102781,0.025664,-0.01384764,0.00572174,-0.04260151,0.00844375,-0.00992435,0.02827908,0.09048688,-0.04429853,-0.0662437,-0.0330821,-0.00812347,-0.01435628,0.0212184,-0.00245328,-0.0252844,0.05649738,-0.02161251,0.00656895,-0.03150894,0.01664781,0.05820452,0.0353182,-0.04249992,-0.00901123,-0.0052446,0.00683445,-0.00656378,0.03943267,0.01110962,-0.0561169,-0.00276241,0.10010427,-0.00357862,-0.03083587,-0.03956923,0.00473203,0.02676775,-0.02444222,0.00511795,-0.00066794,-0.02048367,-0.02302097,-0.00809768,0.01371513,-0.03528422,-0.00378464,0.02144075,0.06952278,-0.02680964,0.05522635,-0.04154674,0.02963124,0.00080163,-0.02172956,-0.03578223,0.01430583,7.1e-7,0.00153321,-0.06733963,0.04503989,0.00223662,-0.01643589,-0.02105901,-0.00137045,0.02226655,0.07865971],"last_embed":{"hash":"190ah76","tokens":595}}},"text":null,"length":0,"last_read":{"hash":"190ah76","at":1748397845457},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验","lines":[80,141],"size":1282,"outlinks":[{"title":"序列化","target":"序列化","line":20},{"title":"序列化","target":"序列化","line":50}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#---frontmatter---","lines":[1,7],"size":108,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#简介": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#简介","lines":[8,19],"size":311,"outlinks":[{"title":"Jenkins","target":"Jenkins","line":4},{"title":"反序列化","target":"反序列化","line":8},{"title":"反序列化","target":"反序列化","line":9},{"title":"反序列化","target":"反序列化","line":10}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#简介#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#简介#{1}","lines":[9,13],"size":172,"outlinks":[{"title":"Jenkins","target":"Jenkins","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#简介#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#简介#{2}","lines":[14,14],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#简介#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#简介#{3}","lines":[15,17],"size":124,"outlinks":[{"title":"反序列化","target":"反序列化","line":1},{"title":"反序列化","target":"反序列化","line":2},{"title":"反序列化","target":"反序列化","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#简介#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#简介#{4}","lines":[18,19],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#相关漏洞": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#相关漏洞","lines":[20,25],"size":54,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#相关漏洞#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#相关漏洞#{1}","lines":[21,21],"size":15,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#相关漏洞#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#相关漏洞#{2}","lines":[22,24],"size":27,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#相关漏洞#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#相关漏洞#{3}","lines":[25,25],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#常用工具": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#常用工具","lines":[26,30],"size":51,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#常用工具#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#常用工具#{1}","lines":[27,27],"size":15,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#常用工具#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#常用工具#{2}","lines":[28,28],"size":8,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#常用工具#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#常用工具#{3}","lines":[29,29],"size":15,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#常用工具#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#常用工具#{4}","lines":[30,30],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#可能出现的位置": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#可能出现的位置","lines":[33,45],"size":279,"outlinks":[{"title":"session","target":"session","line":2},{"title":"token","target":"token","line":2},{"title":"反序列化","target":"反序列化","line":3},{"title":"序列化","target":"序列化","line":3},{"title":"json","target":"json","line":4},{"title":"序列化","target":"序列化","line":5},{"title":"json","target":"json","line":6},{"title":"序列化","target":"序列化","line":8},{"title":"Java","target":"Java","line":11}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#可能出现的位置#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#可能出现的位置#{1}","lines":[34,34],"size":31,"outlinks":[{"title":"session","target":"session","line":1},{"title":"token","target":"token","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#可能出现的位置#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#可能出现的位置#{2}","lines":[35,36],"size":65,"outlinks":[{"title":"反序列化","target":"反序列化","line":1},{"title":"序列化","target":"序列化","line":1},{"title":"json","target":"json","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#可能出现的位置#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#可能出现的位置#{3}","lines":[37,38],"size":45,"outlinks":[{"title":"序列化","target":"序列化","line":1},{"title":"json","target":"json","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#可能出现的位置#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#可能出现的位置#{4}","lines":[39,39],"size":10,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#可能出现的位置#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#可能出现的位置#{5}","lines":[40,40],"size":33,"outlinks":[{"title":"序列化","target":"序列化","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#可能出现的位置#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#可能出现的位置#{6}","lines":[41,42],"size":45,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#可能出现的位置#{7}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#可能出现的位置#{7}","lines":[43,44],"size":29,"outlinks":[{"title":"Java","target":"Java","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#可能出现的位置#{8}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#可能出现的位置#{8}","lines":[45,45],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞成因/环节": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞成因/环节","lines":[46,58],"size":222,"outlinks":[{"title":"反序列化","target":"反序列化","line":3},{"title":"序列化","target":"序列化","line":4},{"title":"反序列化","target":"反序列化","line":7}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞成因/环节#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞成因/环节#{1}","lines":[47,53],"size":148,"outlinks":[{"title":"反序列化","target":"反序列化","line":2},{"title":"序列化","target":"序列化","line":3},{"title":"反序列化","target":"反序列化","line":6}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞成因/环节#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞成因/环节#{2}","lines":[54,54],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞成因/环节#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞成因/环节#{3}","lines":[55,57],"size":54,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞成因/环节#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞成因/环节#{4}","lines":[58,58],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞原理": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞原理","lines":[59,77],"size":492,"outlinks":[{"title":"PHP","target":"PHP","line":2},{"title":"Python","target":"Python","line":2},{"title":"序列化","target":"序列化","line":6},{"title":"反序列化","target":"反序列化","line":7},{"title":"#Python的反序列化实验","target":"#Python的反序列化实验","line":8},{"title":"反射（reflection）机制","target":"https://xz.aliyun.com/t/7029/","line":10},{"title":"Java","target":"Java","line":10},{"title":"变量","target":"变量","line":13},{"title":"Java","target":"Java","line":15}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞原理#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞原理#{1}","lines":[60,65],"size":174,"outlinks":[{"title":"PHP","target":"PHP","line":1},{"title":"Python","target":"Python","line":1},{"title":"序列化","target":"序列化","line":5},{"title":"反序列化","target":"反序列化","line":6}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞原理#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞原理#{2}","lines":[66,66],"size":28,"outlinks":[{"title":"#Python的反序列化实验","target":"#Python的反序列化实验","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞原理#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞原理#{3}","lines":[67,67],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞原理#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞原理#{4}","lines":[68,75],"size":271,"outlinks":[{"title":"反射（reflection）机制","target":"https://xz.aliyun.com/t/7029/","line":1},{"title":"Java","target":"Java","line":1},{"title":"变量","target":"变量","line":4},{"title":"Java","target":"Java","line":6}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞原理#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#漏洞环境#漏洞原理#{5}","lines":[76,77],"size":4,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#序列化与反序列化": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#序列化与反序列化","lines":[82,104],"size":485,"outlinks":[{"title":"序列化","target":"序列化","line":18}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#序列化与反序列化#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#序列化与反序列化#{1}","lines":[83,98],"size":344,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#序列化与反序列化#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#序列化与反序列化#{2}","lines":[99,100],"size":39,"outlinks":[{"title":"序列化","target":"序列化","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#序列化与反序列化#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#序列化与反序列化#{3}","lines":[101,103],"size":83,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#序列化与反序列化#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#序列化与反序列化#{4}","lines":[104,104],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#系统命令序列化传输": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#系统命令序列化传输","lines":[105,141],"size":778,"outlinks":[{"title":"序列化","target":"序列化","line":25}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#系统命令序列化传输#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#系统命令序列化传输#{1}","lines":[106,120],"size":254,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#系统命令序列化传输#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#系统命令序列化传输#{2}","lines":[121,121],"size":22,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#系统命令序列化传输#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#系统命令序列化传输#{3}","lines":[122,127],"size":140,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#系统命令序列化传输#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#系统命令序列化传输#{4}","lines":[128,129],"size":25,"outlinks":[{"title":"序列化","target":"序列化","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#系统命令序列化传输#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#系统命令序列化传输#{5}","lines":[130,138],"size":250,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#系统命令序列化传输#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md#实际测试#Python的反序列化实验#系统命令序列化传输#{6}","lines":[139,141],"size":68,"outlinks":[],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07812562,-0.03636123,-0.02127724,-0.06209531,0.02166435,-0.03727525,-0.01073551,0.06407018,0.0386518,-0.00003168,0.0137583,-0.05684697,0.02837543,0.06593029,0.06224327,0.01415448,-0.00512498,0.06634507,-0.00071243,0.03386038,0.09473725,-0.01837642,0.00689406,-0.08771443,0.00833162,0.03742732,0.02513763,0.00625972,-0.02437725,-0.17825991,0.0136041,-0.00337275,0.02532552,0.02491341,0.02116656,-0.01989922,0.0101378,0.03603557,-0.03124084,0.00530729,0.02763668,0.00272243,0.00046292,-0.02464532,-0.05833906,-0.11158293,-0.03122369,-0.01099607,0.01664223,-0.0614652,-0.03193048,-0.07566752,-0.03295406,-0.01064015,-0.01153033,-0.01588228,0.01319433,0.0441776,0.04792453,0.00488086,0.0377881,0.0197856,-0.20148037,0.05023342,0.01572482,0.01071918,0.00430164,-0.02195246,0.00663513,-0.02476851,-0.01169932,0.03918337,-0.05050372,0.03379416,0.05738692,0.05280736,-0.01093612,-0.04605187,-0.00278622,-0.05367221,-0.02325497,0.04347768,-0.00373388,0.01418195,-0.00719063,0.03387315,-0.00891077,0.0161004,0.02751058,-0.01255086,-0.02610861,-0.03602223,0.04903582,0.02577249,-0.02792197,0.05773067,0.04346034,0.01889857,-0.05855408,0.12364048,-0.06426165,-0.00922707,0.00293696,-0.04400668,0.01907929,-0.02780432,-0.03073681,-0.08596615,0.03501156,-0.03511867,-0.04415675,-0.00124959,0.07161038,-0.01090323,0.03115174,0.00857784,0.00705228,-0.00341192,-0.02919278,0.01950868,-0.05349799,0.01337538,0.08798948,-0.00030838,-0.00931415,-0.00570954,0.02620656,0.05602419,0.04321222,-0.00262375,0.03428222,-0.00277651,-0.03441424,-0.03890571,-0.01335706,0.03254751,-0.03783676,0.00606173,0.00541431,-0.00579183,-0.0145298,-0.04519094,-0.02028465,-0.083314,-0.02557155,0.04428179,-0.03657334,0.00154677,0.02271323,-0.04577371,0.00246816,0.06796784,-0.01324848,-0.02945483,-0.00655801,-0.00651501,0.05800324,0.1562248,-0.05357187,-0.03492638,0.01211043,0.01907292,-0.05412497,0.1998153,0.01644456,-0.04655078,-0.0456444,0.01574359,-0.02553865,-0.02729043,0.00883855,-0.00696237,0.03744972,-0.02180207,0.02012135,-0.05982293,-0.01128694,-0.04549663,-0.0123388,0.05956849,0.05831633,-0.01457956,-0.08011524,0.03553367,0.00541452,-0.09855808,-0.08820089,-0.0295629,0.0031202,-0.05776661,-0.12791269,0.00536197,-0.02815936,0.00017779,-0.01359415,-0.05745159,0.02957383,0.00660311,0.03844539,-0.07636449,0.15550418,0.04959806,-0.0226829,0.03943138,-0.02668944,-0.03447633,-0.00316618,-0.04800167,-0.03467916,0.0351158,-0.02234388,0.00931838,-0.0406102,0.01604367,-0.05426602,0.09247047,0.03702188,0.03862416,0.03885703,0.08263167,0.0241581,-0.01162708,-0.07796904,-0.24009517,-0.0630075,-0.00459597,-0.03843327,-0.00272728,-0.00336631,0.04128448,-0.01927217,0.08640046,0.05086721,0.05818009,0.05817536,-0.05505973,-0.03695872,0.02332793,-0.00555878,0.01433953,-0.0248169,-0.04318729,0.02109672,-0.00305436,0.03348542,-0.00263167,0.00548678,0.02699767,-0.0184774,0.13364059,0.00973307,0.03293685,-0.02055695,0.04282468,0.06633548,0.02643591,-0.09913947,0.02476158,0.03622152,-0.03739285,-0.02466929,-0.00105759,-0.02786302,-0.01811051,0.02689253,-0.01724301,-0.10891943,-0.00723022,-0.03206498,-0.02562452,0.01561653,0.02650157,0.06881413,-0.00449114,0.00847494,0.03147709,0.01075426,0.0179706,-0.05240117,-0.02847592,-0.00224968,0.01739027,0.04505751,0.03420903,0.00949126,0.02433061,-0.01909884,0.00335679,-0.06310967,-0.00043953,-0.02374064,-0.00733468,-0.0756828,-0.07208974,0.19009231,-0.00252619,0.00410157,0.04866687,0.00972836,-0.02239938,-0.04152886,-0.01058129,-0.00656276,0.04610057,0.04215178,0.03850626,0.01408421,-0.02026881,0.0484176,-0.00121192,-0.01160084,0.07313037,-0.0538183,-0.04884095,-0.0239803,-0.09876295,0.00861341,0.09094622,-0.00635172,-0.27518192,0.01190196,-0.01232874,0.00299553,0.04736987,0.01022551,0.06349616,-0.02833789,-0.04863226,0.02959714,-0.05059819,0.05719479,-0.01007111,-0.06362691,0.01752472,-0.00019482,0.03880351,0.03905598,0.0606929,-0.05239129,0.01869461,0.09367767,0.21710142,0.04558231,0.00609891,0.0431124,0.00839511,0.0904306,0.02832094,0.00230602,-0.0035169,-0.04009409,0.05596274,0.000236,0.02295433,-0.00433762,-0.02509749,-0.01863856,0.04710112,0.00016486,-0.04220875,0.0549503,-0.04614448,0.06058666,0.08473402,0.00213394,-0.02622876,-0.02953162,0.02791441,0.02289439,0.00429874,-0.01226913,0.01471208,-0.01215098,0.01865541,0.04593462,0.0191636,-0.03754511,-0.06107171,-0.00277388,0.02557149,0.03268354,0.0484633,0.04170229,0.02332202],"last_embed":{"hash":"909a14d7beec3b68dc2f1d21c0f5ba7f099ca353e2e716aedace9b7b18f2b044","tokens":412}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.0605938,0.04687835,-0.00683432,0.00133127,-0.0117562,-0.01816038,0.06685812,-0.03658928,0.01022273,-0.01756719,-0.006208,-0.01990677,-0.02285628,-0.01292851,-0.05801592,0.04510941,0.01742898,0.04073948,0.04561749,-0.00810129,0.00335188,0.02813538,0.05126566,0.0489807,-0.01403518,-0.0262706,0.01300023,-0.04969492,0.02170867,0.03955124,0.04716959,-0.05256065,-0.00716546,-0.02683718,-0.07069579,0.02146472,0.03192372,-0.02846186,-0.03461616,0.02268673,-0.00940289,-0.02004508,-0.04391277,0.01270029,-0.10551517,-0.02444486,0.0450837,-0.01675669,0.00415918,0.01697012,0.01191333,-0.05956686,-0.00268966,0.04585459,0.00099277,0.03276982,-0.0359952,-0.04646933,-0.01658636,-0.00205754,0.01354944,0.04380148,0.08159384,0.02428913,-0.01566591,0.02875278,0.00669384,-0.03327052,0.00172492,-0.01345832,-0.08818144,0.00437686,-0.00975251,0.01845044,0.07724077,-0.04252681,0.04860214,0.01212147,-0.03307029,0.00221584,-0.10411441,0.01391608,-0.00509808,0.04512125,0.03627793,-0.02041466,0.00425233,-0.0267258,0.03484576,0.00581789,0.03098275,0.03246936,-0.0010346,0.00762872,0.01493359,-0.08055083,-0.01195218,0.01485319,-0.01434235,-0.04388086,0.03883273,-0.01032561,-0.05638241,-0.0076534,-0.03241513,-0.02482201,-0.00094751,0.00700011,-0.02693866,-0.00457582,-0.02973029,0.02259558,-0.0186177,-0.04887431,-0.08905932,0.03239382,-0.03928358,0.00665325,0.03286203,0.08622203,0.04025502,0.02021477,0.0126928,-0.01381687,-0.04586859,0.00734369,0.00595967,-0.02864214,0.0256698,-0.02967187,-0.0048619,0.0030538,-0.01180305,-0.02397156,-0.01070856,0.00082072,0.02253993,-0.04708271,0.0198289,-0.03464956,0.03844563,-0.02629254,0.00315455,-0.01277013,-0.02010438,0.06577182,-0.04259189,-0.057946,-0.0119645,0.04479548,-0.01658884,-0.00070927,-0.0380882,-0.05288912,0.01625635,0.02285221,0.0332467,0.06635134,0.00843669,-0.00291495,-0.0071528,-0.00257953,0.04542733,0.04319802,0.03962749,0.02383934,0.04285936,0.01408718,-0.05686,-0.00121441,-0.01850441,0.01339532,0.02275272,0.04794612,0.05413298,-0.04817061,0.07223742,0.01891023,-0.0559932,-0.02927741,-0.0102847,-0.0284695,-0.00080419,0.02753629,0.0163335,0.02386601,-0.00113147,0.03092656,0.0133974,-0.00113843,0.02279229,-0.00475598,0.00592734,0.00438172,0.04178133,0.01134313,-0.0127457,-0.04797491,-0.03138401,0.02017684,0.02935739,-0.03222977,-0.0453283,-0.04068922,0.05468766,0.01329055,-0.01820862,-0.01966747,-0.02676997,0.03168165,0.05733637,0.01855968,0.01848385,-0.01027732,-0.05649765,0.0693541,-0.06348602,0.09375585,0.02418518,-0.01349963,-0.00858774,-0.10342237,-0.02937675,-0.02697498,0.01613696,-0.0582513,-0.02347469,0.03417491,0.02819482,0.00567511,-0.00918402,0.04693934,-0.02888283,-0.01507981,-0.03723304,-0.00260125,-0.0113616,0.03892186,0.03418313,0.02450075,-0.01023233,0.00478297,0.00509805,-0.03178853,0.01101364,-0.00496605,-0.06493957,0.00699098,-0.00629365,0.00659531,0.02155336,-0.00872013,0.00778748,0.08343649,0.0076266,-0.02733667,-0.02834976,-0.04326587,0.02025938,-0.02248752,0.00583426,0.04750186,-0.04602808,0.01434339,-0.02583943,-0.03260226,-0.01839101,0.02491798,-0.0595685,0.08040116,-0.03494728,-0.0439484,0.0753781,0.01773703,-0.00904587,-0.00457944,-0.07043656,-0.01988553,-0.05142219,-0.03856205,-0.04646173,-0.0536154,0.01591697,0.03264141,-0.07673872,0.03014551,0.00543065,0.017108,0.05144564,-0.03500583,0.04879666,0.00203012,-0.05103976,0.04694208,-0.04715778,-0.03523583,0.0185725,0.0397351,0.00115982,-0.05634853,-0.03365349,0.07029031,0.02084439,-0.03429768,0.00176721,0.01303379,0.00409599,0.02134916,-0.02063906,-0.03643373,0.01986348,-0.04809496,0.02684254,0.00912148,0.04686016,0.07080584,0.00085898,-0.0103146,-0.01337711,-0.00184252,-0.04509666,-0.00888178,0.03044272,0.01925019,0.03227906,-0.06305782,0.03545033,-0.00661549,0.02601832,0.04281368,-0.03873459,0.00708534,0.00852282,-0.0125358,0.03919275,-0.01107061,-0.02902588,-0.06465652,0.03104646,-0.05682289,0.02209818,-0.05514782,0.0135888,-0.03844707,-0.01639427,0.01594315,0.00027506,0.01499031,-0.04775489,-0.03909538,-0.01397017,-0.03631928,-0.04475924,-0.00320603,0.03953297,-0.01977302,-0.01917268,0.02151905,0.01798073,0.00719347,-0.00103228,-0.04321624,-0.0540408,-0.05075847,-0.03514342,0.03189909,0.0798085,0.03490041,-0.06545305,-0.02561542,0.0293547,0.02943545,-0.04793363,-0.0100024,0.04896491,-0.00812223,0.01189227,-0.00308764,0.03524535,-0.0165282,0.01150585,-0.02593821,-0.02955822,-0.02740939,-0.11263339,-0.04148334,0.04641872,-0.0466943,-0.02745154,-0.05208974,-0.05988551,0.01386609,-0.04219684,0.05141056,0.009656,0.01452376,0.05199666,-0.01315279,-0.02847068,-0.01170896,0.02764547,0.03293062,0.00535685,0.02175703,-0.01064541,0.05885814,-0.01734001,0.06686821,-0.02258829,-0.02832938,0.00260308,0.05155246,0.00260902,-0.00258543,0.0077203,0.02737127,-0.01559572,-0.04798394,0.02173214,0.05908844,0.01734863,-0.04579104,0.04134998,0.03676183,0.02864903,-0.03189264,-0.06180235,0.03377793,0.00162385,0.02396328,-0.02348932,0.02627121,-0.0942032,-0.05905928,-0.02592431,-0.02161708,0.08798584,0.02354675,0.00445456,0.02255645,-0.03497078,-0.02528702,-0.0052938,-0.03404532,0.07036214,0.05946713,-0.01243876,-0.02304561,-0.00288948,0.06755278,-0.01631747,-0.04290672,-0.06721755,-0.02598467,0.03379843,0.02000109,0.02740276,-0.04612442,-0.0141673,-0.00363735,-0.06275005,-0.01830686,0.04282592,-0.00958318,0.01136204,-0.00873201,0.01174153,-0.03016712,-0.0578231,-0.00080133,0.00558532,-0.03394983,0.00156351,-0.01065117,0.07990927,0.04088388,-0.03639128,-0.00733076,-0.05533164,-0.02335287,-0.01342808,0.04467833,0.0229665,0.01680943,-0.00379945,0.04902438,-0.03046446,0.00192632,0.0220388,-0.01780548,-0.03215621,0.07061272,0.03948706,0.01655749,0.02341819,-0.01123479,0.01147525,0.04925403,0.00060942,0.04359918,0.00969913,0.04879702,-0.02885098,-0.0077166,0.04474712,0.04279149,-0.04225077,0.02007038,0.06262249,-0.01805164,-0.02653023,-0.08195223,0.08462113,0.03051376,-0.03289557,-0.05920732,-0.03507368,0.02640633,-0.03436967,0.01258248,0.0007155,0.01617385,-0.06853729,-0.00072931,0.03239753,-0.02421737,-0.02329788,0.06629463,0.03900705,0.03453993,-0.04362718,-0.00389563,0.01297543,-0.01770622,0.03239689,-0.04850624,0.04218021,-0.0076872,-0.0110507,0.04570417,0.09684315,0.0157249,0.00750131,0.02535798,-0.01915224,0.00002266,0.02671663,-0.00663302,0.06498204,-0.00117138,-0.00020207,0.04571383,0.03356745,-0.00823937,-0.02015737,0.01885772,0.01230682,-0.03653046,-0.04033989,0.05680362,0.00900295,0.04643645,-0.03675516,0.02522873,-0.00242268,0.00087269,-0.04858439,0.02044369,0.01750256,0.03195681,-0.04141834,-0.00427962,-0.00267247,0.03343308,-0.06650099,-0.02131479,0.01220624,-0.00498069,0.05480671,-0.07938524,0.0263503,-0.02048775,0.05537537,-0.00256279,-0.01632216,0.01005542,0.00909906,0.05650183,0.01836015,0.00060954,-0.00556928,-0.04959016,-0.02816264,-0.02479824,-0.01909856,0.04356752,-0.00771023,0.02242279,0.03174052,-0.03905712,0.0094334,-0.02300327,-0.01060226,-0.00691506,-0.0021332,-0.08238056,-0.0257459,-0.07001116,0.00073153,0.04082036,-0.00033552,0.05253101,0.0256303,0.01917319,0.00849392,-0.04933649,0.00382588,-0.0111436,-0.01205414,-0.03856781,-0.00024492,-0.05017735,-0.03016537,-0.04805256,-0.02266281,0.01360826,0.00663863,0.00439156,0.04488305,-0.05391617,-0.07001817,-0.0267512,0.04467165,0.0370898,-0.01186503,0.03726443,-0.07233507,-0.05169658,-0.03507467,-0.03947217,0.01379298,0.02973484,0.0078274,0.04756887,0.02508624,-0.0158249,-0.00540651,0.01247069,0.03263141,-0.01963484,-0.0014813,-0.02006182,-0.00982543,-0.00818524,-0.04446287,0.03871214,-0.03055588,0.01866894,0.00138331,0.05127821,-0.04852546,-0.02079412,-0.01578854,-0.02830909,-0.06212029,0.05174435,0.0419728,0.01251176,-0.02936723,-0.00218551,-0.09649477,-0.00087816,-0.01811864,0.01342551,0.01390899,0.03298628,0.02455116,0.03022834,0.01990713,-0.02858344,-0.09196609,0.05446625,-0.00817176,-0.02372979,0.0555923,-0.02436426,0.02530163,0.01680809,-0.04793112,0.000825,-0.03730216,0.03301842,-0.02203962,-0.01593821,0.06188288,-0.05030227,0.06734429,-0.03695999,0.02330637,0.04036328,-0.01470517,0.01938087,-0.0342401,0.01464678,-0.01985229,0.00446019,0.02158917,0.00650436,0.11198653,-0.04615266,-0.06621802,-0.00667232,-0.02496847,0.03428909,0.01364371,-0.03212943,-0.00866156,0.02512674,-0.0479105,-0.00214166,-0.04226252,-0.04901357,0.05674211,0.02276123,-0.0093526,-0.01175327,-0.02441847,-0.00673936,0.00876198,-0.00542765,-0.04460094,-0.09989185,0.00516911,0.04755382,0.03656646,0.00289982,-0.03459216,-0.00117233,0.0429735,-0.00903646,-0.01319131,0.01072267,-0.02506237,0.00724341,-0.00595752,0.05990357,-0.00686334,0.00812589,-0.00068357,0.02780898,0.00020092,0.03947467,-0.01071734,0.05769562,0.01803746,0.02657603,-0.05425519,-0.03069217,0.00000108,-0.007729,-0.02590023,0.02660734,-0.00021574,-0.01903012,-0.00665121,-0.04099252,-0.05967892,0.04808004],"last_embed":{"tokens":1303,"hash":"1i88o0l"}}},"last_read":{"hash":"1i88o0l","at":1751079996203},"class_name":"SmartSource","outlinks":[{"title":"Jenkins","target":"Jenkins","line":11},{"title":"反序列化","target":"反序列化","line":15},{"title":"反序列化","target":"反序列化","line":16},{"title":"反序列化","target":"反序列化","line":17},{"title":"session","target":"session","line":34},{"title":"token","target":"token","line":34},{"title":"反序列化","target":"反序列化","line":35},{"title":"序列化","target":"序列化","line":35},{"title":"json","target":"json","line":36},{"title":"序列化","target":"序列化","line":37},{"title":"json","target":"json","line":38},{"title":"序列化","target":"序列化","line":40},{"title":"Java","target":"Java","line":43},{"title":"反序列化","target":"反序列化","line":48},{"title":"序列化","target":"序列化","line":49},{"title":"反序列化","target":"反序列化","line":52},{"title":"PHP","target":"PHP","line":60},{"title":"Python","target":"Python","line":60},{"title":"序列化","target":"序列化","line":64},{"title":"反序列化","target":"反序列化","line":65},{"title":"#Python的反序列化实验","target":"#Python的反序列化实验","line":66},{"title":"反射（reflection）机制","target":"https://xz.aliyun.com/t/7029/","line":68},{"title":"Java","target":"Java","line":68},{"title":"变量","target":"变量","line":71},{"title":"Java","target":"Java","line":73},{"title":"序列化","target":"序列化","line":99},{"title":"序列化","target":"序列化","line":129}],"metadata":{"tags":["网络安全/漏洞/Web安全"],"英文":"Deserialization Vulnerability","aliases":["Deserialization Vulnerability"]},"blocks":{"#---frontmatter---":[1,7],"#简介":[8,19],"#简介#{1}":[9,13],"#简介#{2}":[14,14],"#简介#{3}":[15,17],"#简介#{4}":[18,19],"#相关漏洞":[20,25],"#相关漏洞#{1}":[21,21],"#相关漏洞#{2}":[22,24],"#相关漏洞#{3}":[25,25],"#常用工具":[26,30],"#常用工具#{1}":[27,27],"#常用工具#{2}":[28,28],"#常用工具#{3}":[29,29],"#常用工具#{4}":[30,30],"#漏洞环境":[31,77],"#漏洞环境#可能出现的位置":[33,45],"#漏洞环境#可能出现的位置#{1}":[34,34],"#漏洞环境#可能出现的位置#{2}":[35,36],"#漏洞环境#可能出现的位置#{3}":[37,38],"#漏洞环境#可能出现的位置#{4}":[39,39],"#漏洞环境#可能出现的位置#{5}":[40,40],"#漏洞环境#可能出现的位置#{6}":[41,42],"#漏洞环境#可能出现的位置#{7}":[43,44],"#漏洞环境#可能出现的位置#{8}":[45,45],"#漏洞环境#漏洞成因/环节":[46,58],"#漏洞环境#漏洞成因/环节#{1}":[47,53],"#漏洞环境#漏洞成因/环节#{2}":[54,54],"#漏洞环境#漏洞成因/环节#{3}":[55,57],"#漏洞环境#漏洞成因/环节#{4}":[58,58],"#漏洞环境#漏洞原理":[59,77],"#漏洞环境#漏洞原理#{1}":[60,65],"#漏洞环境#漏洞原理#{2}":[66,66],"#漏洞环境#漏洞原理#{3}":[67,67],"#漏洞环境#漏洞原理#{4}":[68,75],"#漏洞环境#漏洞原理#{5}":[76,77],"#实际测试":[78,141],"#实际测试#Python的反序列化实验":[80,141],"#实际测试#Python的反序列化实验#序列化与反序列化":[82,104],"#实际测试#Python的反序列化实验#序列化与反序列化#{1}":[83,98],"#实际测试#Python的反序列化实验#序列化与反序列化#{2}":[99,100],"#实际测试#Python的反序列化实验#序列化与反序列化#{3}":[101,103],"#实际测试#Python的反序列化实验#序列化与反序列化#{4}":[104,104],"#实际测试#Python的反序列化实验#系统命令序列化传输":[105,141],"#实际测试#Python的反序列化实验#系统命令序列化传输#{1}":[106,120],"#实际测试#Python的反序列化实验#系统命令序列化传输#{2}":[121,121],"#实际测试#Python的反序列化实验#系统命令序列化传输#{3}":[122,127],"#实际测试#Python的反序列化实验#系统命令序列化传输#{4}":[128,129],"#实际测试#Python的反序列化实验#系统命令序列化传输#{5}":[130,138],"#实际测试#Python的反序列化实验#系统命令序列化传输#{6}":[139,141]},"last_import":{"mtime":1718639835046,"size":5028,"at":1748488129023,"hash":"1i88o0l"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/反序列化漏洞.md","last_embed":{"hash":"1i88o0l","at":1751079996203}},