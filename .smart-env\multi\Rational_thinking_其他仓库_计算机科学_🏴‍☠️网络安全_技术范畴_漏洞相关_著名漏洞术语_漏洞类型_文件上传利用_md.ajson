"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/文件上传利用.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/文件上传利用.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09831097,-0.02067675,-0.01905662,-0.04813225,0.04998057,-0.01116338,-0.04054281,0.00391706,0.03517023,0.00028111,0.04436733,-0.03953083,0.0509906,0.04267763,0.05130357,0.03224405,0.02879131,-0.02993623,-0.03805289,0.0118263,0.07522372,-0.0451665,0.00784391,-0.0562957,0.00626267,-0.01556579,0.00446547,-0.01212224,-0.02274792,-0.15284406,-0.00449093,-0.02504759,0.02713462,0.00876637,-0.01804535,-0.05741024,0.05072551,0.05097545,0.0072454,-0.00646101,-0.01567149,0.05340937,0.01077825,-0.03668923,-0.04222195,-0.03225042,-0.05530881,-0.03221707,0.02609701,-0.03832972,-0.04601081,-0.04918293,-0.04038092,-0.02695184,-0.05967668,-0.02731704,0.00361532,-0.00039006,0.02876311,0.0175296,0.01892713,0.02862445,-0.19941081,0.0641823,0.02486543,-0.03936825,-0.04422202,0.00435876,0.01292983,0.05588139,-0.10054675,0.05000313,-0.01508319,0.07125881,0.06185823,-0.0384633,0.05195477,-0.0201431,-0.06246974,-0.06114513,-0.02608356,0.04983043,-0.03843406,0.00874517,-0.00806143,0.02335005,-0.01704046,-0.02222469,-0.04065247,-0.00480727,0.00159489,-0.02529442,-0.02088656,0.03227548,-0.00490482,0.00175799,0.01281944,0.0648365,-0.05289939,0.11467559,-0.08723486,-0.00073936,0.00380506,-0.07001711,-0.01251348,-0.02918737,-0.00053416,-0.01426513,0.02973211,-0.01270832,-0.0456001,-0.03529997,0.05234628,-0.02458207,0.05393479,0.04637576,-0.00014092,-0.00475374,-0.04163766,-0.03860043,-0.01776334,0.00031659,0.07795209,-0.02859701,-0.03846722,-0.02974396,0.06141702,0.05743581,0.03544042,0.0295207,0.06362605,0.00317067,-0.06931904,-0.04530201,-0.01144161,-0.00227704,-0.03611481,0.01574424,-0.02535372,-0.03389859,-0.01852925,-0.06794877,-0.00760326,-0.09850623,-0.08425847,0.07660622,-0.05236944,-0.02565198,0.07601211,-0.05238154,-0.00239681,0.09225381,-0.03334429,-0.01670387,-0.00443139,-0.00917877,0.08345836,0.1290673,-0.03816814,-0.01797052,-0.00328998,0.02214075,-0.09072615,0.16632499,0.0078824,-0.07724846,-0.01942477,-0.00554936,0.04571138,-0.01900533,0.03376955,-0.01215495,0.04286639,-0.00944668,0.07447311,-0.00735005,0.00442994,-0.05350649,0.02601119,0.03519025,0.0988845,-0.02220044,-0.07547143,0.07710944,-0.0136286,-0.08116642,-0.05706327,0.00269383,0.02281714,-0.02273454,-0.09866598,0.05514959,-0.0356846,-0.01916011,-0.05564179,-0.06669572,0.03066027,0.02348301,0.02224903,-0.07332581,0.09706861,0.03282115,-0.01808222,0.01957502,-0.01786336,-0.01649112,0.0112561,-0.00081721,0.01870029,0.0115655,0.01175731,-0.02172882,-0.00982548,-0.00358024,-0.03154628,0.05522664,0.02618951,0.00207195,0.03057327,0.08132789,-0.00066847,-0.06692135,-0.03728954,-0.19562759,-0.00899599,0.00184663,-0.04552347,0.01157911,-0.01139512,0.04473056,0.03019625,0.09648241,0.09655056,0.06914603,0.01526337,-0.09218839,-0.0260381,0.01317993,-0.02772413,0.01747603,-0.01000087,-0.02756061,0.00760239,0.00785838,0.04404645,-0.02797548,-0.02128464,0.08133278,-0.03490426,0.12432501,0.05471717,0.00990534,0.04391562,0.05685932,0.05278208,-0.00900075,-0.1153926,0.03864585,0.00789734,-0.03466131,-0.0352615,-0.03335834,-0.02572298,0.02940657,0.00984129,-0.02504474,-0.06935126,-0.02035827,-0.01800772,-0.01714375,0.01695781,0.03888728,0.02539203,0.00208192,-0.00315638,0.00456091,0.00853826,-0.03426846,-0.05640664,-0.03858114,0.00350591,-0.00964316,0.01149822,0.07026133,-0.00519919,0.04448088,0.02863169,-0.016162,-0.03091031,-0.01491059,-0.02155648,-0.06953992,0.00164289,0.01110729,0.14278163,-0.01506859,-0.01738785,0.033629,0.01840717,0.0062667,-0.01019119,0.01145075,-0.01190259,0.02824736,0.03397956,0.04142607,0.02373721,-0.02615988,0.02540302,0.01562889,-0.02118059,0.08175851,-0.04957037,-0.04331714,-0.01412106,-0.0487098,0.01841,0.07581603,-0.01496978,-0.31508175,0.01894757,-0.01483336,0.01597779,0.05655668,0.02568622,0.06695726,0.0433967,-0.0168096,0.03978041,-0.06883922,0.03934122,-0.02280597,-0.04475959,-0.02015854,-0.04256989,0.03112714,0.01884121,0.09389576,-0.00936329,0.0302337,0.05190409,0.22108936,0.00176783,0.04803829,-0.0046684,-0.0010661,0.05076224,0.02693129,0.02342205,0.02086417,-0.04049329,0.03846869,-0.03714093,0.05598778,0.03514297,-0.04988711,0.03738868,0.00441097,0.01408111,-0.03894125,0.04173091,-0.08165355,0.03124369,0.07896673,0.03492297,-0.02038154,-0.02485431,-0.01661337,0.06060708,0.00074471,0.00673043,0.00665821,-0.01570486,0.0218586,0.03611004,0.03575153,-0.040445,-0.05971714,-0.01110385,0.05091498,0.01355224,0.10040601,0.12829971,0.07826735],"last_embed":{"hash":"0c3dd77631514e6dcd620057b55f3620267dcb89edc4aa9e2f8ac53cd08aa707","tokens":420}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.07216306,-0.00044139,-0.0216261,-0.02971359,0.00112929,-0.02616731,0.05449723,0.00971916,-0.02043255,-0.04133326,-0.04884024,-0.01909485,-0.09063927,-0.04978709,0.05674798,0.05991408,0.01077218,0.01451864,0.06506386,-0.04296267,-0.02449741,0.05022334,0.05848382,-0.00640141,-0.02063152,-0.00978073,0.00275024,-0.02544489,0.03453285,0.07431212,0.08743531,-0.02597458,0.02369484,0.00814077,-0.06385752,-0.05341913,0.02437128,-0.0414262,0.00403602,0.01959981,-0.03024999,0.01534156,-0.00678519,0.03992688,-0.05588186,-0.01990532,-0.02811739,-0.03648101,-0.00493475,-0.00158281,0.03252326,0.01158231,-0.01541503,0.01631781,0.00722394,0.05057783,-0.03295462,-0.03920291,-0.00083732,-0.00421448,0.02943356,-0.04270847,0.06610019,0.04057664,0.02372064,-0.0011139,-0.01268413,-0.00245846,-0.00953154,-0.02964068,-0.05531596,-0.03639191,-0.00309955,0.04678443,0.07002972,-0.00685721,0.05705476,-0.00185133,0.04249467,0.01863876,-0.06278881,0.01620254,-0.01924407,0.01817301,-0.00877958,0.00613955,0.09352559,-0.06095529,0.04245804,-0.00399936,-0.00324076,0.00566698,-0.02130072,-0.05442171,0.04043781,-0.02683826,0.05482574,-0.01583308,0.00962365,-0.015487,-0.0101544,-0.00147434,-0.07604779,0.00648893,-0.03429987,-0.09346984,0.00867498,0.04593493,-0.01645577,0.0041213,0.00587882,0.0146063,-0.02692708,0.02645965,0.02038706,0.04431727,-0.03365457,0.01582075,0.00697093,0.05366983,0.01999388,0.04038677,0.03467131,-0.03947325,-0.04212042,-0.01077242,-0.03403335,-0.02923745,0.04455707,0.03115966,-0.01208767,0.05600959,-0.03678419,-0.03133663,0.05733604,0.01389567,0.02907176,-0.04211355,0.06447585,-0.0430465,0.0190192,-0.02386301,-0.02168556,0.01176559,-0.04327847,0.01814458,-0.03505607,-0.06150932,-0.04954135,0.04519118,0.06190663,0.02657186,-0.03828831,-0.04462611,-0.0083863,-0.00993477,0.0045712,0.01331118,0.01879876,-0.01078438,-0.00603344,0.03412975,-0.00366921,0.00644209,0.00514342,-0.01287514,0.00704203,0.04847249,0.02062744,0.03077852,0.00581437,0.02802513,-0.00685767,0.01970511,0.01899223,-0.06938111,0.06496915,0.05386223,-0.04078056,-0.02272757,0.02686219,-0.05318842,-0.01452454,0.0498536,0.01631214,0.0196113,-0.03382324,0.01506895,0.01639191,0.01362388,0.05528012,0.03294135,0.01328305,0.02618174,-0.02124002,0.01744889,0.02215634,-0.08876819,-0.03364026,0.03355158,0.02112217,-0.00356922,-0.02313626,-0.08090181,0.01899329,-0.0212761,-0.01724884,-0.00573703,-0.04555503,0.07235705,-0.04219617,0.00702721,0.00248059,0.01034405,-0.0406109,0.03784294,-0.02374927,-0.00127074,0.0144586,-0.01108487,0.00695198,-0.03681533,-0.06989535,-0.01371944,-0.05153244,-0.0212677,-0.0039047,0.00182494,0.03252104,0.0088938,-0.04074274,-0.0012552,-0.02353926,-0.0055214,-0.02069627,-0.00952943,0.05542314,0.01744529,0.01402375,0.00267268,0.00764234,0.04889973,0.02874568,-0.00652488,-0.02272091,-0.03736302,-0.05370886,0.04081753,0.01777607,0.0152069,0.02118833,0.0247999,-0.00952916,0.04619887,-0.00207355,-0.07090022,-0.02381159,-0.00673294,0.00555121,-0.03436293,0.02948374,-0.0077433,-0.06589406,0.01981632,-0.02742267,-0.04033769,-0.01719642,0.00290552,-0.05327206,0.05673837,-0.02934357,-0.03346202,0.09247115,-0.02599912,-0.01693151,0.01142826,0.00273748,-0.00507683,-0.03550833,-0.01812071,-0.03218244,0.00073664,0.00707105,-0.00533831,-0.07489952,0.06173082,0.01651568,-0.04327969,0.03987364,-0.00755092,0.01975958,-0.01132489,-0.03985038,0.05146524,-0.05733145,-0.04250956,-0.01853765,0.04935347,-0.01244521,-0.04467247,0.02280599,0.07527249,0.00475218,-0.05322364,0.00192274,0.02054237,0.00374266,0.04069185,-0.02836618,-0.04374644,0.03957705,-0.05065496,0.04559938,0.03096518,0.05532335,0.07042713,0.00670887,-0.02558139,-0.04824834,0.04004912,-0.07492636,-0.00197052,-0.01536756,-0.00648951,0.01574572,-0.05924309,0.00813527,-0.00271436,0.08919831,0.02431468,0.00073972,-0.00666419,0.02242192,-0.01157774,0.00314779,-0.04115835,-0.032818,-0.03806026,0.01546295,-0.01284939,0.00543899,-0.04053257,-0.00200219,-0.08596896,-0.03263503,0.02580535,-0.01779514,-0.00556362,-0.02615564,-0.08194317,0.03385825,0.01597899,-0.02767261,0.01884268,0.04037719,-0.03519841,-0.05102083,-0.0284381,-0.00217339,0.01138632,-0.02602751,-0.03325826,-0.01560646,-0.05040104,-0.00043048,0.01724189,0.05903021,0.02761338,-0.00089653,-0.06375106,0.01839343,-0.00678284,-0.02598506,-0.01818121,0.01438613,-0.01050255,-0.02942271,0.01554349,0.06654745,-0.02857449,-0.02157596,-0.04162927,0.04096138,-0.02595236,-0.04816545,-0.03730499,0.04247735,-0.01104467,-0.09125438,-0.07759918,-0.03131939,0.01603921,-0.04212648,0.04047126,0.03513312,-0.02798416,0.03586792,0.01516636,-0.0279322,-0.04570852,0.01086205,-0.03962209,0.06442129,0.01194864,-0.0012977,0.05639976,-0.06153759,0.03087023,-0.05520276,-0.02807448,0.02296793,0.01755466,0.03695661,0.03867467,0.05244061,0.0191377,0.00609769,-0.08346263,-0.0009228,0.04028232,0.02027363,-0.01091583,0.02463041,0.0588012,-0.01361228,-0.01805828,-0.04441675,0.00663623,-0.05459192,0.03426379,-0.03566061,-0.00051387,-0.023795,0.01666179,-0.00303689,-0.03826083,0.05169253,0.01038458,0.06159724,0.02288267,-0.05729435,0.02432976,-0.02333584,0.02891389,0.07739524,0.09678466,-0.0297742,-0.07293419,-0.00665111,-0.0060918,-0.02036517,-0.02530388,-0.05312739,0.00571035,0.00376793,-0.00783727,0.02925721,-0.03798505,0.01659354,-0.01748155,-0.02997841,-0.04494764,0.02687685,0.00716372,0.02290746,0.06537352,-0.00048428,0.01294384,-0.03062377,0.00354423,-0.02641248,0.01137798,-0.00039855,-0.01543574,0.0344656,0.00223558,0.02612699,0.00643724,-0.0593443,0.01992313,-0.04621237,0.03306338,-0.02794224,-0.02764517,-0.03001617,0.00080668,0.00019436,0.01572359,0.03304236,-0.01605197,-0.02297559,0.04042635,0.03670188,0.02521235,0.01830355,-0.03228265,0.0109472,0.05158683,-0.0034375,0.07735112,0.00405929,0.03479951,0.00735002,0.04020835,-0.0188546,0.01925211,-0.02924219,0.01061763,0.0249968,0.00259837,-0.02769883,-0.10117799,0.04493156,0.08015932,-0.02885153,-0.03131759,0.02111156,-0.01396075,-0.00600271,-0.0331113,-0.03300032,0.03690338,-0.04026222,-0.0185671,-0.00856027,-0.00369287,-0.00277536,0.06546851,0.05592312,-0.02819339,0.00401768,0.0415553,0.01474651,-0.01432226,0.04554838,-0.05252995,0.0461333,-0.0112283,-0.02267084,0.00968303,0.07259993,0.02432599,0.01847667,0.02962594,-0.03063796,-0.04520757,0.03359831,-0.0039535,0.05813718,-0.05738009,0.01763219,0.02104246,-0.00003335,-0.02562972,-0.06925555,0.01399276,0.00267339,-0.0269484,-0.01425221,0.02997086,-0.01289757,0.05273581,-0.02631456,-0.01566705,-0.05260497,-0.01828133,-0.0572638,0.02176248,0.01610645,0.02264458,-0.09484433,-0.01382768,0.03845478,0.00664078,-0.03298416,-0.03075927,0.00713749,-0.0368825,0.03558021,-0.02862148,-0.04668882,0.0029161,0.0330118,0.03409601,-0.01419428,-0.01942755,0.03004834,-0.00911257,0.03141584,-0.01485338,-0.01267537,-0.01185747,0.03273925,-0.0200197,-0.05668882,-0.03864421,-0.0084639,0.02049091,-0.00455336,-0.00658463,-0.00900477,-0.01867984,0.01474017,-0.00920517,0.015558,-0.0845274,-0.0228059,-0.064886,0.04850253,0.08428874,-0.00060654,0.02260943,0.02974598,0.00501055,0.01737398,-0.06666047,-0.00003408,0.03255625,-0.03175012,-0.01251032,-0.03037569,-0.10093983,-0.04997005,-0.016259,0.04110298,0.04997093,-0.00492612,0.02215746,0.08045698,-0.05369182,-0.09517688,-0.03572616,-0.01757367,0.03082057,0.00679278,0.04929005,0.00558838,-0.01436947,-0.00965856,-0.00736544,0.01923856,-0.01000705,-0.01742406,-0.01763215,0.03177372,-0.07850853,-0.02662657,-0.0425597,0.06376726,-0.01253764,0.01298389,-0.03361396,0.00287108,0.01496221,-0.03598353,0.04727906,-0.02915472,0.00602016,0.01771824,0.02679922,-0.00158349,-0.03571298,-0.04175397,-0.0145866,-0.03434718,0.030115,-0.01686377,0.01500017,-0.04328729,0.03123565,-0.04468818,0.03164814,-0.04072629,0.01582769,-0.02772094,0.0380463,0.01969195,0.05533704,-0.04143369,-0.02551962,-0.05264077,0.00542983,0.01463235,0.01447914,0.02301878,-0.03597027,0.00449434,-0.00456197,0.01244223,0.00574992,-0.04045928,0.04377907,-0.00157558,0.02239874,-0.04109473,-0.05109995,0.03539602,-0.06738178,0.00334624,-0.00329495,-0.02666381,0.00468728,-0.00067661,0.01839838,-0.03938157,-0.03273993,0.01773012,0.01358607,0.04582146,0.02345547,-0.0582944,0.05341772,-0.01251989,0.00561031,0.00299682,-0.04537387,-0.02823417,0.06978759,-0.04743256,0.00361957,0.01430165,0.01643751,0.00744052,0.01201572,-0.00598839,-0.04198428,0.00503364,0.00202048,0.00794399,0.02179825,-0.02054542,-0.06901427,0.00822957,0.05322728,0.04611712,-0.00024209,0.04633662,-0.00372976,0.05288552,0.00705963,0.05258837,0.00730967,-0.03108089,0.03556783,-0.03071308,0.06885098,0.01614449,0.00515869,0.03557186,0.01292022,0.04938376,0.06092802,0.01886921,0.10843153,-0.00306738,0.07631762,-0.00729304,-0.05959542,9.7e-7,-0.04487965,0.04153587,0.05552575,-0.01689708,-0.00787853,-0.00842494,-0.05535708,-0.0145284,-0.00805025],"last_embed":{"tokens":256,"hash":"eypkxl"}}},"last_read":{"hash":"eypkxl","at":1751251736924},"class_name":"SmartSource","outlinks":[],"metadata":{"aliases":["file upload","文件上传漏洞"],"tags":["网络安全/漏洞/文件上传"],"类型":["漏洞"],"文档更新日期":"2023-12-20"},"blocks":{"#---frontmatter---":[1,10],"#简介":[11,20],"#简介#{1}":[12,13],"#简介#{2}":[14,15],"#简介#{3}":[16,19],"#简介#{4}":[20,20],"#漏洞形成的原因":[21,37],"#漏洞形成的原因#{1}":[23,23],"#漏洞形成的原因#{2}":[24,24],"#漏洞形成的原因#{3}":[25,25],"#漏洞形成的原因#{4}":[26,28],"#漏洞形成的原因#{5}":[29,33],"#漏洞形成的原因#{6}":[34,37]},"last_import":{"mtime":1715930118181,"size":1046,"at":1749024987637,"hash":"eypkxl"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/文件上传利用.md","last_embed":{"hash":"eypkxl","at":1751251736924}},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/文件上传利用.md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/文件上传利用.md#---frontmatter---","lines":[1,10],"size":96,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/文件上传利用.md#简介": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/文件上传利用.md#简介","lines":[11,20],"size":189,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/文件上传利用.md#简介#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/文件上传利用.md#简介#{1}","lines":[12,13],"size":68,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/文件上传利用.md#简介#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/文件上传利用.md#简介#{2}","lines":[14,15],"size":46,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/文件上传利用.md#简介#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/文件上传利用.md#简介#{3}","lines":[16,19],"size":64,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/文件上传利用.md#简介#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/文件上传利用.md#简介#{4}","lines":[20,20],"size":3,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/文件上传利用.md#漏洞形成的原因": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/文件上传利用.md#漏洞形成的原因","lines":[21,37],"size":191,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/文件上传利用.md#漏洞形成的原因#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/文件上传利用.md#漏洞形成的原因#{1}","lines":[23,23],"size":14,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/文件上传利用.md#漏洞形成的原因#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/文件上传利用.md#漏洞形成的原因#{2}","lines":[24,24],"size":10,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/文件上传利用.md#漏洞形成的原因#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/文件上传利用.md#漏洞形成的原因#{3}","lines":[25,25],"size":12,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/文件上传利用.md#漏洞形成的原因#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/文件上传利用.md#漏洞形成的原因#{4}","lines":[26,28],"size":53,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/文件上传利用.md#漏洞形成的原因#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/文件上传利用.md#漏洞形成的原因#{5}","lines":[29,33],"size":79,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/文件上传利用.md#漏洞形成的原因#{6}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/文件上传利用.md#漏洞形成的原因#{6}","lines":[34,37],"size":7,"outlinks":[],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/文件上传利用.md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/文件上传利用.md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.09831097,-0.02067675,-0.01905662,-0.04813225,0.04998057,-0.01116338,-0.04054281,0.00391706,0.03517023,0.00028111,0.04436733,-0.03953083,0.0509906,0.04267763,0.05130357,0.03224405,0.02879131,-0.02993623,-0.03805289,0.0118263,0.07522372,-0.0451665,0.00784391,-0.0562957,0.00626267,-0.01556579,0.00446547,-0.01212224,-0.02274792,-0.15284406,-0.00449093,-0.02504759,0.02713462,0.00876637,-0.01804535,-0.05741024,0.05072551,0.05097545,0.0072454,-0.00646101,-0.01567149,0.05340937,0.01077825,-0.03668923,-0.04222195,-0.03225042,-0.05530881,-0.03221707,0.02609701,-0.03832972,-0.04601081,-0.04918293,-0.04038092,-0.02695184,-0.05967668,-0.02731704,0.00361532,-0.00039006,0.02876311,0.0175296,0.01892713,0.02862445,-0.19941081,0.0641823,0.02486543,-0.03936825,-0.04422202,0.00435876,0.01292983,0.05588139,-0.10054675,0.05000313,-0.01508319,0.07125881,0.06185823,-0.0384633,0.05195477,-0.0201431,-0.06246974,-0.06114513,-0.02608356,0.04983043,-0.03843406,0.00874517,-0.00806143,0.02335005,-0.01704046,-0.02222469,-0.04065247,-0.00480727,0.00159489,-0.02529442,-0.02088656,0.03227548,-0.00490482,0.00175799,0.01281944,0.0648365,-0.05289939,0.11467559,-0.08723486,-0.00073936,0.00380506,-0.07001711,-0.01251348,-0.02918737,-0.00053416,-0.01426513,0.02973211,-0.01270832,-0.0456001,-0.03529997,0.05234628,-0.02458207,0.05393479,0.04637576,-0.00014092,-0.00475374,-0.04163766,-0.03860043,-0.01776334,0.00031659,0.07795209,-0.02859701,-0.03846722,-0.02974396,0.06141702,0.05743581,0.03544042,0.0295207,0.06362605,0.00317067,-0.06931904,-0.04530201,-0.01144161,-0.00227704,-0.03611481,0.01574424,-0.02535372,-0.03389859,-0.01852925,-0.06794877,-0.00760326,-0.09850623,-0.08425847,0.07660622,-0.05236944,-0.02565198,0.07601211,-0.05238154,-0.00239681,0.09225381,-0.03334429,-0.01670387,-0.00443139,-0.00917877,0.08345836,0.1290673,-0.03816814,-0.01797052,-0.00328998,0.02214075,-0.09072615,0.16632499,0.0078824,-0.07724846,-0.01942477,-0.00554936,0.04571138,-0.01900533,0.03376955,-0.01215495,0.04286639,-0.00944668,0.07447311,-0.00735005,0.00442994,-0.05350649,0.02601119,0.03519025,0.0988845,-0.02220044,-0.07547143,0.07710944,-0.0136286,-0.08116642,-0.05706327,0.00269383,0.02281714,-0.02273454,-0.09866598,0.05514959,-0.0356846,-0.01916011,-0.05564179,-0.06669572,0.03066027,0.02348301,0.02224903,-0.07332581,0.09706861,0.03282115,-0.01808222,0.01957502,-0.01786336,-0.01649112,0.0112561,-0.00081721,0.01870029,0.0115655,0.01175731,-0.02172882,-0.00982548,-0.00358024,-0.03154628,0.05522664,0.02618951,0.00207195,0.03057327,0.08132789,-0.00066847,-0.06692135,-0.03728954,-0.19562759,-0.00899599,0.00184663,-0.04552347,0.01157911,-0.01139512,0.04473056,0.03019625,0.09648241,0.09655056,0.06914603,0.01526337,-0.09218839,-0.0260381,0.01317993,-0.02772413,0.01747603,-0.01000087,-0.02756061,0.00760239,0.00785838,0.04404645,-0.02797548,-0.02128464,0.08133278,-0.03490426,0.12432501,0.05471717,0.00990534,0.04391562,0.05685932,0.05278208,-0.00900075,-0.1153926,0.03864585,0.00789734,-0.03466131,-0.0352615,-0.03335834,-0.02572298,0.02940657,0.00984129,-0.02504474,-0.06935126,-0.02035827,-0.01800772,-0.01714375,0.01695781,0.03888728,0.02539203,0.00208192,-0.00315638,0.00456091,0.00853826,-0.03426846,-0.05640664,-0.03858114,0.00350591,-0.00964316,0.01149822,0.07026133,-0.00519919,0.04448088,0.02863169,-0.016162,-0.03091031,-0.01491059,-0.02155648,-0.06953992,0.00164289,0.01110729,0.14278163,-0.01506859,-0.01738785,0.033629,0.01840717,0.0062667,-0.01019119,0.01145075,-0.01190259,0.02824736,0.03397956,0.04142607,0.02373721,-0.02615988,0.02540302,0.01562889,-0.02118059,0.08175851,-0.04957037,-0.04331714,-0.01412106,-0.0487098,0.01841,0.07581603,-0.01496978,-0.31508175,0.01894757,-0.01483336,0.01597779,0.05655668,0.02568622,0.06695726,0.0433967,-0.0168096,0.03978041,-0.06883922,0.03934122,-0.02280597,-0.04475959,-0.02015854,-0.04256989,0.03112714,0.01884121,0.09389576,-0.00936329,0.0302337,0.05190409,0.22108936,0.00176783,0.04803829,-0.0046684,-0.0010661,0.05076224,0.02693129,0.02342205,0.02086417,-0.04049329,0.03846869,-0.03714093,0.05598778,0.03514297,-0.04988711,0.03738868,0.00441097,0.01408111,-0.03894125,0.04173091,-0.08165355,0.03124369,0.07896673,0.03492297,-0.02038154,-0.02485431,-0.01661337,0.06060708,0.00074471,0.00673043,0.00665821,-0.01570486,0.0218586,0.03611004,0.03575153,-0.040445,-0.05971714,-0.01110385,0.05091498,0.01355224,0.10040601,0.12829971,0.07826735],"last_embed":{"hash":"0c3dd77631514e6dcd620057b55f3620267dcb89edc4aa9e2f8ac53cd08aa707","tokens":420}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.07216306,-0.00044139,-0.0216261,-0.02971359,0.00112929,-0.02616731,0.05449723,0.00971916,-0.02043255,-0.04133326,-0.04884024,-0.01909485,-0.09063927,-0.04978709,0.05674798,0.05991408,0.01077218,0.01451864,0.06506386,-0.04296267,-0.02449741,0.05022334,0.05848382,-0.00640141,-0.02063152,-0.00978073,0.00275024,-0.02544489,0.03453285,0.07431212,0.08743531,-0.02597458,0.02369484,0.00814077,-0.06385752,-0.05341913,0.02437128,-0.0414262,0.00403602,0.01959981,-0.03024999,0.01534156,-0.00678519,0.03992688,-0.05588186,-0.01990532,-0.02811739,-0.03648101,-0.00493475,-0.00158281,0.03252326,0.01158231,-0.01541503,0.01631781,0.00722394,0.05057783,-0.03295462,-0.03920291,-0.00083732,-0.00421448,0.02943356,-0.04270847,0.06610019,0.04057664,0.02372064,-0.0011139,-0.01268413,-0.00245846,-0.00953154,-0.02964068,-0.05531596,-0.03639191,-0.00309955,0.04678443,0.07002972,-0.00685721,0.05705476,-0.00185133,0.04249467,0.01863876,-0.06278881,0.01620254,-0.01924407,0.01817301,-0.00877958,0.00613955,0.09352559,-0.06095529,0.04245804,-0.00399936,-0.00324076,0.00566698,-0.02130072,-0.05442171,0.04043781,-0.02683826,0.05482574,-0.01583308,0.00962365,-0.015487,-0.0101544,-0.00147434,-0.07604779,0.00648893,-0.03429987,-0.09346984,0.00867498,0.04593493,-0.01645577,0.0041213,0.00587882,0.0146063,-0.02692708,0.02645965,0.02038706,0.04431727,-0.03365457,0.01582075,0.00697093,0.05366983,0.01999388,0.04038677,0.03467131,-0.03947325,-0.04212042,-0.01077242,-0.03403335,-0.02923745,0.04455707,0.03115966,-0.01208767,0.05600959,-0.03678419,-0.03133663,0.05733604,0.01389567,0.02907176,-0.04211355,0.06447585,-0.0430465,0.0190192,-0.02386301,-0.02168556,0.01176559,-0.04327847,0.01814458,-0.03505607,-0.06150932,-0.04954135,0.04519118,0.06190663,0.02657186,-0.03828831,-0.04462611,-0.0083863,-0.00993477,0.0045712,0.01331118,0.01879876,-0.01078438,-0.00603344,0.03412975,-0.00366921,0.00644209,0.00514342,-0.01287514,0.00704203,0.04847249,0.02062744,0.03077852,0.00581437,0.02802513,-0.00685767,0.01970511,0.01899223,-0.06938111,0.06496915,0.05386223,-0.04078056,-0.02272757,0.02686219,-0.05318842,-0.01452454,0.0498536,0.01631214,0.0196113,-0.03382324,0.01506895,0.01639191,0.01362388,0.05528012,0.03294135,0.01328305,0.02618174,-0.02124002,0.01744889,0.02215634,-0.08876819,-0.03364026,0.03355158,0.02112217,-0.00356922,-0.02313626,-0.08090181,0.01899329,-0.0212761,-0.01724884,-0.00573703,-0.04555503,0.07235705,-0.04219617,0.00702721,0.00248059,0.01034405,-0.0406109,0.03784294,-0.02374927,-0.00127074,0.0144586,-0.01108487,0.00695198,-0.03681533,-0.06989535,-0.01371944,-0.05153244,-0.0212677,-0.0039047,0.00182494,0.03252104,0.0088938,-0.04074274,-0.0012552,-0.02353926,-0.0055214,-0.02069627,-0.00952943,0.05542314,0.01744529,0.01402375,0.00267268,0.00764234,0.04889973,0.02874568,-0.00652488,-0.02272091,-0.03736302,-0.05370886,0.04081753,0.01777607,0.0152069,0.02118833,0.0247999,-0.00952916,0.04619887,-0.00207355,-0.07090022,-0.02381159,-0.00673294,0.00555121,-0.03436293,0.02948374,-0.0077433,-0.06589406,0.01981632,-0.02742267,-0.04033769,-0.01719642,0.00290552,-0.05327206,0.05673837,-0.02934357,-0.03346202,0.09247115,-0.02599912,-0.01693151,0.01142826,0.00273748,-0.00507683,-0.03550833,-0.01812071,-0.03218244,0.00073664,0.00707105,-0.00533831,-0.07489952,0.06173082,0.01651568,-0.04327969,0.03987364,-0.00755092,0.01975958,-0.01132489,-0.03985038,0.05146524,-0.05733145,-0.04250956,-0.01853765,0.04935347,-0.01244521,-0.04467247,0.02280599,0.07527249,0.00475218,-0.05322364,0.00192274,0.02054237,0.00374266,0.04069185,-0.02836618,-0.04374644,0.03957705,-0.05065496,0.04559938,0.03096518,0.05532335,0.07042713,0.00670887,-0.02558139,-0.04824834,0.04004912,-0.07492636,-0.00197052,-0.01536756,-0.00648951,0.01574572,-0.05924309,0.00813527,-0.00271436,0.08919831,0.02431468,0.00073972,-0.00666419,0.02242192,-0.01157774,0.00314779,-0.04115835,-0.032818,-0.03806026,0.01546295,-0.01284939,0.00543899,-0.04053257,-0.00200219,-0.08596896,-0.03263503,0.02580535,-0.01779514,-0.00556362,-0.02615564,-0.08194317,0.03385825,0.01597899,-0.02767261,0.01884268,0.04037719,-0.03519841,-0.05102083,-0.0284381,-0.00217339,0.01138632,-0.02602751,-0.03325826,-0.01560646,-0.05040104,-0.00043048,0.01724189,0.05903021,0.02761338,-0.00089653,-0.06375106,0.01839343,-0.00678284,-0.02598506,-0.01818121,0.01438613,-0.01050255,-0.02942271,0.01554349,0.06654745,-0.02857449,-0.02157596,-0.04162927,0.04096138,-0.02595236,-0.04816545,-0.03730499,0.04247735,-0.01104467,-0.09125438,-0.07759918,-0.03131939,0.01603921,-0.04212648,0.04047126,0.03513312,-0.02798416,0.03586792,0.01516636,-0.0279322,-0.04570852,0.01086205,-0.03962209,0.06442129,0.01194864,-0.0012977,0.05639976,-0.06153759,0.03087023,-0.05520276,-0.02807448,0.02296793,0.01755466,0.03695661,0.03867467,0.05244061,0.0191377,0.00609769,-0.08346263,-0.0009228,0.04028232,0.02027363,-0.01091583,0.02463041,0.0588012,-0.01361228,-0.01805828,-0.04441675,0.00663623,-0.05459192,0.03426379,-0.03566061,-0.00051387,-0.023795,0.01666179,-0.00303689,-0.03826083,0.05169253,0.01038458,0.06159724,0.02288267,-0.05729435,0.02432976,-0.02333584,0.02891389,0.07739524,0.09678466,-0.0297742,-0.07293419,-0.00665111,-0.0060918,-0.02036517,-0.02530388,-0.05312739,0.00571035,0.00376793,-0.00783727,0.02925721,-0.03798505,0.01659354,-0.01748155,-0.02997841,-0.04494764,0.02687685,0.00716372,0.02290746,0.06537352,-0.00048428,0.01294384,-0.03062377,0.00354423,-0.02641248,0.01137798,-0.00039855,-0.01543574,0.0344656,0.00223558,0.02612699,0.00643724,-0.0593443,0.01992313,-0.04621237,0.03306338,-0.02794224,-0.02764517,-0.03001617,0.00080668,0.00019436,0.01572359,0.03304236,-0.01605197,-0.02297559,0.04042635,0.03670188,0.02521235,0.01830355,-0.03228265,0.0109472,0.05158683,-0.0034375,0.07735112,0.00405929,0.03479951,0.00735002,0.04020835,-0.0188546,0.01925211,-0.02924219,0.01061763,0.0249968,0.00259837,-0.02769883,-0.10117799,0.04493156,0.08015932,-0.02885153,-0.03131759,0.02111156,-0.01396075,-0.00600271,-0.0331113,-0.03300032,0.03690338,-0.04026222,-0.0185671,-0.00856027,-0.00369287,-0.00277536,0.06546851,0.05592312,-0.02819339,0.00401768,0.0415553,0.01474651,-0.01432226,0.04554838,-0.05252995,0.0461333,-0.0112283,-0.02267084,0.00968303,0.07259993,0.02432599,0.01847667,0.02962594,-0.03063796,-0.04520757,0.03359831,-0.0039535,0.05813718,-0.05738009,0.01763219,0.02104246,-0.00003335,-0.02562972,-0.06925555,0.01399276,0.00267339,-0.0269484,-0.01425221,0.02997086,-0.01289757,0.05273581,-0.02631456,-0.01566705,-0.05260497,-0.01828133,-0.0572638,0.02176248,0.01610645,0.02264458,-0.09484433,-0.01382768,0.03845478,0.00664078,-0.03298416,-0.03075927,0.00713749,-0.0368825,0.03558021,-0.02862148,-0.04668882,0.0029161,0.0330118,0.03409601,-0.01419428,-0.01942755,0.03004834,-0.00911257,0.03141584,-0.01485338,-0.01267537,-0.01185747,0.03273925,-0.0200197,-0.05668882,-0.03864421,-0.0084639,0.02049091,-0.00455336,-0.00658463,-0.00900477,-0.01867984,0.01474017,-0.00920517,0.015558,-0.0845274,-0.0228059,-0.064886,0.04850253,0.08428874,-0.00060654,0.02260943,0.02974598,0.00501055,0.01737398,-0.06666047,-0.00003408,0.03255625,-0.03175012,-0.01251032,-0.03037569,-0.10093983,-0.04997005,-0.016259,0.04110298,0.04997093,-0.00492612,0.02215746,0.08045698,-0.05369182,-0.09517688,-0.03572616,-0.01757367,0.03082057,0.00679278,0.04929005,0.00558838,-0.01436947,-0.00965856,-0.00736544,0.01923856,-0.01000705,-0.01742406,-0.01763215,0.03177372,-0.07850853,-0.02662657,-0.0425597,0.06376726,-0.01253764,0.01298389,-0.03361396,0.00287108,0.01496221,-0.03598353,0.04727906,-0.02915472,0.00602016,0.01771824,0.02679922,-0.00158349,-0.03571298,-0.04175397,-0.0145866,-0.03434718,0.030115,-0.01686377,0.01500017,-0.04328729,0.03123565,-0.04468818,0.03164814,-0.04072629,0.01582769,-0.02772094,0.0380463,0.01969195,0.05533704,-0.04143369,-0.02551962,-0.05264077,0.00542983,0.01463235,0.01447914,0.02301878,-0.03597027,0.00449434,-0.00456197,0.01244223,0.00574992,-0.04045928,0.04377907,-0.00157558,0.02239874,-0.04109473,-0.05109995,0.03539602,-0.06738178,0.00334624,-0.00329495,-0.02666381,0.00468728,-0.00067661,0.01839838,-0.03938157,-0.03273993,0.01773012,0.01358607,0.04582146,0.02345547,-0.0582944,0.05341772,-0.01251989,0.00561031,0.00299682,-0.04537387,-0.02823417,0.06978759,-0.04743256,0.00361957,0.01430165,0.01643751,0.00744052,0.01201572,-0.00598839,-0.04198428,0.00503364,0.00202048,0.00794399,0.02179825,-0.02054542,-0.06901427,0.00822957,0.05322728,0.04611712,-0.00024209,0.04633662,-0.00372976,0.05288552,0.00705963,0.05258837,0.00730967,-0.03108089,0.03556783,-0.03071308,0.06885098,0.01614449,0.00515869,0.03557186,0.01292022,0.04938376,0.06092802,0.01886921,0.10843153,-0.00306738,0.07631762,-0.00729304,-0.05959542,9.7e-7,-0.04487965,0.04153587,0.05552575,-0.01689708,-0.00787853,-0.00842494,-0.05535708,-0.0145284,-0.00805025],"last_embed":{"tokens":256,"hash":"eypkxl"}}},"last_read":{"hash":"eypkxl","at":1751268682883},"class_name":"SmartSource","outlinks":[],"metadata":{"aliases":["file upload","文件上传漏洞"],"tags":["网络安全/漏洞/文件上传"],"类型":["漏洞"],"文档更新日期":"2023-12-20"},"blocks":{"#---frontmatter---":[1,10],"#简介":[11,20],"#简介#{1}":[12,13],"#简介#{2}":[14,15],"#简介#{3}":[16,19],"#简介#{4}":[20,20],"#漏洞形成的原因":[21,37],"#漏洞形成的原因#{1}":[23,23],"#漏洞形成的原因#{2}":[24,24],"#漏洞形成的原因#{3}":[25,25],"#漏洞形成的原因#{4}":[26,28],"#漏洞形成的原因#{5}":[29,33],"#漏洞形成的原因#{6}":[34,37]},"last_import":{"mtime":1715930118181,"size":1046,"at":1749024987637,"hash":"eypkxl"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/漏洞相关/著名漏洞术语/漏洞类型/文件上传利用.md","last_embed":{"hash":"eypkxl","at":1751268682883}},