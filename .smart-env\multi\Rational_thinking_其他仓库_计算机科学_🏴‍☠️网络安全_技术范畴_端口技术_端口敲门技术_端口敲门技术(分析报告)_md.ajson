"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术(分析报告).md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术(分析报告).md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07739039,-0.03695204,-0.03251654,-0.02180268,-0.01737198,-0.01108882,-0.0001469,0.07756653,0.02200481,0.01863516,-0.00899986,-0.06397341,0.01143011,0.07345027,0.0325887,0.00939708,-0.03015679,0.01007751,-0.04142441,0.01231825,0.0967185,-0.07686517,-0.03750948,-0.05527527,-0.00450683,0.03183343,0.01826604,-0.01834809,-0.01118175,-0.18030775,0.00816571,0.02530733,0.00640218,0.03429122,-0.05078968,0.00466354,0.00355407,0.06590575,-0.02047074,0.0297946,-0.02079907,-0.00160665,0.05074979,-0.05370427,-0.00513668,-0.0600006,-0.04816599,0.01324033,-0.01012834,-0.02246846,-0.05414182,-0.06277015,-0.01149332,-0.04323821,-0.04196983,0.01452386,0.00313031,-0.00948529,0.04490068,-0.02770102,0.04334611,-0.00545174,-0.21056159,0.0472111,-0.00625986,-0.00098864,-0.02742271,-0.00587903,0.04692295,0.00416716,-0.06133787,0.00455886,0.02243845,0.05111065,0.0999415,0.00775509,0.02910546,-0.04585895,-0.04405987,-0.02479615,-0.01423018,0.03993734,-0.01668116,-0.02755963,0.04975514,0.03091499,-0.00415621,-0.03154291,-0.00796786,-0.02666157,-0.02470655,-0.05576661,-0.0194859,0.04502947,0.00375906,0.01891083,0.04870432,0.00912175,-0.03877659,0.14220524,-0.05723734,0.03389656,0.00099385,-0.01934793,0.02950376,-0.06394143,-0.00185871,-0.05290806,-0.02519637,-0.04322524,-0.07694835,-0.05010828,0.03087084,0.00628294,0.00400785,0.05155302,0.03922769,0.00950431,-0.01816201,-0.00431372,-0.00486185,0.00151407,0.05745199,-0.06132009,-0.0234757,-0.04665469,0.06594039,0.0845118,0.02283254,-0.00167985,0.08103025,-0.04742733,-0.07323758,0.00337082,-0.01034269,-0.01816367,-0.02575479,-0.00716728,-0.00708214,-0.02392712,0.01165422,-0.07952449,0.00662308,-0.10348837,-0.06372703,0.03233514,-0.06318341,0.03464169,0.03624491,-0.00194947,0.00622905,0.06817437,-0.00175462,-0.03313877,0.01242355,0.01566578,0.04109567,0.13045914,-0.03664016,-0.00544563,0.00750894,0.05609303,-0.02893689,0.14093295,0.00472902,-0.03646591,0.02494129,0.0118321,0.00480348,-0.01812992,0.01762505,0.01792734,0.00864906,0.04570859,0.09367909,0.00937458,-0.00852036,-0.0220723,0.02607577,0.0506067,0.05435543,-0.08189216,-0.0364595,0.05479654,0.0534859,-0.11111838,-0.0259565,-0.03971293,0.00058913,-0.06602974,-0.01535243,-0.00691682,0.00314512,0.04467178,-0.06413034,-0.09404994,0.03363285,-0.01275379,0.01180632,-0.01706963,0.04769447,0.00478583,-0.03014508,0.00803551,0.02075282,-0.00139337,0.02786053,-0.03560295,0.0419741,0.00719325,-0.00510175,0.00245604,-0.02788986,0.02580168,-0.0003377,0.06572988,0.03062418,0.0272206,0.03305849,0.04853569,0.0237922,-0.03681868,-0.08709396,-0.22875422,-0.07464617,0.04431866,-0.04687351,-0.01395295,-0.00328006,0.01519712,-0.01864253,0.0779148,0.05589614,0.08408233,0.02767928,-0.05403984,-0.02923684,0.00424962,0.02272312,0.02000039,-0.02415071,-0.01032465,0.04682707,-0.02339857,0.05665736,-0.00701337,-0.02585465,0.02106635,-0.02861908,0.16144225,0.0542638,0.00592382,0.06495975,0.01864074,-0.01849972,-0.0471247,-0.05489476,0.01260497,0.06058105,-0.05721497,-0.04184568,-0.01496658,-0.04246481,-0.05647627,0.03118326,-0.05964346,-0.07805763,-0.05638422,-0.03058514,-0.07307568,0.04378292,0.01341996,0.06067044,-0.02642666,0.05908561,0.00545429,-0.01790832,0.01581505,-0.00989035,-0.08064716,-0.05211975,-0.05884111,0.02764739,0.00169068,-0.00541731,-0.00992114,0.07305277,-0.00222916,-0.0289746,-0.02609896,0.00227826,-0.03437107,0.03712616,-0.03858555,0.12133452,0.0155531,-0.05131111,0.08163024,-0.0062529,-0.03490476,-0.06270523,-0.01016082,0.01349381,0.05291248,0.011466,0.00720753,0.0595944,0.00814144,0.03828552,0.01825614,-0.00386148,0.01185314,-0.03926216,-0.04585355,0.03703229,-0.06447709,0.06177083,0.01185187,0.02427725,-0.29039574,0.04144445,-0.02230399,0.03143048,0.0694077,0.02983971,0.06564712,0.0258137,-0.03600612,0.04673074,-0.07655805,0.03774743,0.02302248,-0.02420252,0.01099135,-0.04319235,0.01552726,0.02344065,0.08696333,-0.02234181,0.02588759,0.09979811,0.21453103,-0.01029429,0.03839119,0.00152107,0.0222612,0.07766235,0.05061065,0.0372984,0.02274574,-0.011094,0.03278669,-0.0564631,0.00951653,0.01529942,-0.04289444,-0.00556612,0.03605876,-0.01991983,-0.03516924,0.04204495,-0.07039057,0.025615,0.12924199,0.0687542,-0.00233111,-0.05395881,-0.03649598,0.06586687,-0.02471516,0.05203854,-0.02822381,-0.03954727,0.0100176,0.06442969,-0.00268035,-0.02245797,-0.03078999,-0.05004331,-0.00397583,0.01396939,0.05119149,0.11450146,0.03915185],"last_embed":{"hash":"899c341ff9d726e16f54deae21ee0481c7218667fed9715980182f0c95fc50d3","tokens":432}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.0690968,-0.06595109,0.02987035,-0.02638892,-0.01749884,0.04408078,0.03762837,-0.01659904,-0.01033309,-0.07601333,0.03492796,0.04053011,-0.00224166,-0.05966799,0.03253915,-0.02777618,0.00108837,0.02046444,0.0237799,-0.01774511,-0.02927458,0.00344457,-0.04587546,0.01662726,0.06779676,0.00709862,0.04671948,-0.05537698,0.03624022,0.02360808,0.04037464,-0.0229224,0.03321977,-0.04055087,-0.01547891,0.04071897,-0.01154109,-0.00411496,0.04610818,-0.02553191,-0.00344839,0.02602398,-0.02726317,0.00837534,-0.02092539,0.03112894,-0.00200406,-0.03726458,-0.0035478,-0.02951959,0.04549318,-0.06244417,0.01866437,0.0247885,-0.02432863,0.04354091,-0.00167244,-0.08790932,-0.03047284,-0.00256554,-0.00831349,-0.01815582,0.05153362,-0.01360245,0.02931445,0.00027285,-0.00457832,0.00856949,-0.00854468,-0.03481644,0.00118804,-0.06572682,0.04098787,-0.00525751,-0.00662847,0.00323759,0.02448261,-0.00270793,-0.0364724,0.08825286,-0.04068452,-0.00518986,-0.00723717,0.07855929,0.03476932,-0.01444722,0.03424489,-0.04971464,-0.04580298,0.00836694,0.04474415,-0.04368959,-0.04714506,0.02150922,0.03450703,0.00161811,0.02293905,0.02351351,0.01481131,-0.09782946,0.04702538,0.00824522,-0.06889009,-0.00761372,0.01247169,0.02681894,0.01574993,0.0582156,-0.01997862,-0.0408645,-0.0002304,0.03451187,-0.05897622,-0.05570374,-0.06390712,0.01480534,0.01502906,-0.04416235,-0.03635734,0.0141834,0.09036104,0.05318815,-0.01936489,-0.05861354,-0.04210629,0.06328494,-0.00525653,-0.01402737,0.00073452,-0.03561724,-0.0485557,0.03473119,-0.07845272,-0.00970305,0.05420726,-0.0018735,0.0760126,-0.01454519,-0.05611309,-0.00269634,0.04595512,-0.01776003,-0.01701107,0.00351303,-0.02492714,0.06169402,-0.02813827,-0.07174799,0.03565321,-0.00466384,0.07544965,0.02580081,-0.01236251,-0.02107983,-0.0135732,-0.02475636,0.08379269,0.03820882,-0.06535544,-0.03716506,-0.00015624,0.00170759,-0.03106569,-0.00914365,0.00493239,-0.02037577,-0.01180292,-0.00637756,-0.03269368,-0.05915494,0.00422752,0.00513577,-0.02159544,0.00093455,0.04180308,0.01647357,0.03173702,0.05225385,-0.06795055,0.01587467,0.0294049,-0.01639857,-0.01109224,0.00083514,0.04706787,0.02836892,-0.01297218,0.00728128,0.08907405,-0.0255068,0.01125431,0.01241919,0.02724699,0.00358525,0.00933523,0.0516495,-0.0285084,0.006296,0.00311544,0.0010223,-0.06080768,-0.023431,-0.0180012,-0.07975431,0.03860724,-0.01230616,-0.03355373,-0.02776589,-0.00434193,0.08798333,-0.01949751,-0.01061036,0.00432963,0.02105235,-0.04099505,0.02008726,0.00913095,0.07212044,-0.00992069,-0.00223411,-0.03118034,0.02894638,-0.07884715,-0.07121562,-0.02390336,-0.02722639,0.00531122,0.02007435,0.04828873,0.03213207,0.04377466,0.10145888,-0.05769734,-0.06042001,-0.05035487,0.0238115,0.00522838,0.09536411,-0.03740591,0.03055703,0.00967079,-0.03189665,-0.02168097,0.02501999,-0.02476016,-0.00928856,-0.02014695,-0.01140797,0.02850859,0.03825158,0.04978446,0.0082166,0.02355085,0.03568684,0.02203076,-0.01649279,0.00482264,0.03092648,-0.02881364,-0.04450794,0.03034951,-0.07341053,-0.02437129,0.04102356,-0.00821577,-0.00356398,-0.02117596,-0.0123865,-0.06386406,0.05558263,0.02544992,-0.01227381,-0.0549601,0.04578247,0.04269599,0.03021742,0.02082735,-0.02196879,-0.00281442,0.00042702,0.0080947,-0.02959452,0.01845685,-0.02423953,-0.0306016,0.02035194,-0.02038832,-0.04019247,0.02663614,0.04964619,0.00205639,0.00258278,-0.04245978,0.00793305,-0.01520216,-0.00839469,0.0732436,-0.02310712,0.00191084,-0.06534768,0.00164787,-0.0424884,0.00773699,-0.04110536,0.01933417,0.05860058,0.00216332,-0.06683809,-0.05833475,-0.02635425,-0.05811104,-0.04723684,0.00350145,-0.01015475,0.01549541,0.04023464,0.0141833,0.01423901,-0.0193818,-0.0752386,-0.00631356,-0.0074256,-0.03632337,0.03874832,-0.01674363,-0.09099065,-0.0199592,0.04085496,0.03194413,0.06697528,-0.00108955,0.01000184,-0.04767784,-0.00164217,0.02149095,0.02134489,-0.01644955,-0.07464875,0.01623177,-0.0621903,0.0161101,-0.03989666,0.0111058,-0.01755665,-0.03702715,0.04030553,0.02217383,-0.00558946,-0.00202541,-0.04693697,0.00427112,0.01018627,-0.02172649,-0.01533587,-0.03014629,0.07349581,-0.00557553,-0.02773362,-0.02263265,0.02902453,0.05541329,-0.00094009,0.069524,-0.00372131,-0.03303023,0.00238083,0.03253604,0.06711388,-0.00080837,-0.01815639,-0.02916639,-0.00455412,-0.02649774,-0.03309948,0.04419242,-0.00893424,-0.01826761,0.05426385,0.02603109,0.01360586,-0.01605816,0.02172031,-0.02830808,-0.02595947,-0.07267207,-0.01328506,-0.06447579,0.00689011,-0.01064812,-0.05799662,0.03522093,0.01655099,-0.05330881,-0.03419416,0.02909683,0.00779021,0.0283196,0.02150904,-0.00434736,0.02654046,0.08310686,-0.02249872,0.02213788,0.02401378,-0.0393008,0.06371344,-0.04179734,0.05605827,-0.01261521,-0.05031999,-0.00957993,0.02211165,-0.03464314,0.01155437,-0.03274911,-0.0296031,0.01366366,-0.04772852,-0.02790373,0.00266961,-0.0102664,-0.00729205,0.01932725,0.05160983,-0.0315197,-0.00303248,-0.01415475,-0.0409219,0.03222213,-0.03573525,-0.00834232,-0.01485069,-0.10894025,-0.01513948,-0.02159676,-0.00883188,0.04239703,0.00853185,0.02174434,-0.0048214,-0.01341518,-0.01628301,0.0309942,-0.03264807,-0.00158059,-0.02292679,0.00002161,-0.06374205,-0.01384886,0.05017467,0.01435359,-0.01112058,-0.05692057,0.04124057,0.03130977,-0.01347135,-0.03481619,0.0403279,0.04470586,0.05249526,-0.1223916,0.01140288,0.02257148,0.00838278,0.01732256,0.04082144,0.01099836,-0.01572319,-0.03624941,-0.00346605,-0.0365903,-0.01688984,0.04131429,-0.04773732,0.09640164,-0.01051327,0.00805456,0.04629433,-0.04086726,0.01302329,0.01215997,0.00132151,-0.06350133,-0.0250319,-0.02323855,0.01833596,0.00721525,-0.00933919,0.02078336,-0.03946613,0.03047098,-0.00383782,-0.00223078,-0.01437003,-0.04583937,0.03558173,0.01462207,0.05986442,-0.00494927,0.03576192,-0.05662357,0.05508643,-0.03684949,0.03220339,0.0365774,-0.00119438,-0.03436096,0.01743971,0.00520884,-0.07408997,0.05125538,-0.02862773,0.0792264,0.00282679,-0.00445263,-0.00372987,0.02990239,0.04137667,0.04107119,0.0086078,0.06171918,0.02632528,-0.00961332,-0.00057021,0.0210009,-0.01818024,-0.02940561,-0.01674961,0.00423409,-0.0221512,-0.01479383,0.01592412,0.03433481,-0.02159953,0.01453839,-0.01982562,0.03903171,-0.04741102,0.01104742,0.04972302,0.0400524,-0.00398589,0.02741636,-0.0040767,0.00023554,0.01765469,0.01296068,-0.01010621,-0.00786112,-0.01573525,0.01535704,-0.03023573,-0.00936218,-0.03712089,0.01615086,0.05381018,0.04061589,-0.06514469,-0.01548509,0.03420325,0.0495543,0.05642482,0.02190627,-0.02735197,-0.03443819,0.01380868,-0.07772489,0.03219644,0.00059127,-0.02706128,-0.11450706,-0.00725428,0.05094469,0.04811639,-0.04785969,0.02517678,-0.0247175,-0.02338563,0.04476463,0.02748864,0.02568119,0.01450707,0.05731523,-0.02522978,0.00481064,-0.02901911,-0.02197625,0.04635577,0.03809879,-0.04337898,0.05325453,-0.02265159,-0.04379741,-0.01022054,-0.03187968,0.02845684,-0.03461294,0.01952144,-0.00650629,-0.01201964,0.02504313,-0.01643807,-0.04970472,-0.02711739,0.00159075,-0.03957403,-0.01258978,-0.00941087,-0.00613435,0.0324465,0.02532467,0.03445686,-0.00731175,-0.04616679,0.03534862,-0.02371156,0.01699594,0.04119564,-0.00427646,0.03714947,-0.03480083,-0.00733098,-0.06838194,-0.00973208,-0.04564076,0.02009114,0.0185832,0.03097891,0.01102204,-0.01152125,0.00019274,-0.02701034,-0.01824334,0.01743454,0.0574045,0.00635888,-0.01671998,-0.01595117,0.02191913,0.04814679,-0.00607,-0.03994128,0.06156186,-0.00140957,0.02796599,-0.02579264,-0.03427989,0.00220041,-0.02467852,-0.04305581,0.05138516,-0.01258732,0.05454839,0.00065833,-0.00658576,0.04414066,0.02829317,-0.03781966,0.03555369,0.08087592,-0.02530562,-0.01208763,0.05748737,0.05571247,0.0351084,0.0262619,0.06666118,-0.01107237,-0.04164205,0.05378615,0.01726662,-0.03603163,0.02251765,0.01909244,0.01086552,0.01683575,0.0192387,0.04589371,0.03523692,-0.04398821,-0.01573617,0.00434975,0.01660843,0.01277691,0.01939448,-0.0554992,0.06165781,0.00467122,-0.01799475,0.00184499,-0.06512155,0.00517272,-0.00487285,0.01450978,0.02326833,-0.04943686,-0.01647008,0.02001058,0.0118423,-0.02145463,-0.01596739,-0.00034879,-0.0325611,0.00595779,0.00285531,-0.00583451,0.04530166,0.02236302,-0.00449238,0.01585867,0.02348911,-0.01903091,0.01806643,0.01980069,0.03855082,-0.04561123,-0.05641962,0.03708547,-0.04288729,-0.05508916,0.01333417,0.02104137,0.04850775,0.04612922,-0.0329956,0.0020174,0.01298855,-0.0081243,-0.0515342,0.01376315,0.03549222,-0.09482162,0.00738202,0.04203057,-0.01306086,0.01965218,0.02564896,-0.0335173,0.0206654,-0.02312503,-0.03521775,-0.00675668,-0.00057693,-0.07586788,0.03923851,-0.02529826,0.01211452,0.00153188,-0.00881959,0.017501,-0.05058698,-0.01864992,-0.04842478,0.07509437,0.00586598,-0.00464857,-0.02372338,-0.02694757,8.5e-7,-0.08029412,0.00719133,0.03636501,-0.03268953,-0.02506263,-0.05193085,-0.09064625,-0.0074093,0.03308782],"last_embed":{"tokens":365,"hash":"kfhi8e"}}},"last_read":{"hash":"kfhi8e","at":1750993404135},"class_name":"SmartSource","outlinks":[{"title":"防火墙","target":"防火墙","line":33},{"title":"Knock","target":"Knock","line":46}],"blocks":{"#实现原理":[2,43],"#实现原理#流程可视化":[4,43],"#实现原理#流程可视化#{1}":[5,30],"#实现原理#流程可视化#{2}":[31,31],"#实现原理#流程可视化#{3}":[32,33],"#实现原理#流程可视化#{4}":[34,34],"#实现原理#流程可视化#{5}":[35,43],"#---frontmatter---":[38,42],"#相关的工具":[44,60],"#相关的工具#[[Knock]]":[46,49],"#相关的工具#[[Knock]]#{1}":[47,47],"#相关的工具#[[Knock]]#{2}":[48,49],"#相关的工具#存在的问题":[50,60],"#相关的工具#存在的问题#{1}":[51,54],"#相关的工具#存在的问题#{2}":[55,60]},"last_import":{"mtime":1729006303058,"size":1603,"at":1749024987587,"hash":"kfhi8e"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术(分析报告).md","last_embed":{"hash":"kfhi8e","at":1750993404135}},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术(分析报告).md#实现原理": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术(分析报告).md#实现原理","lines":[2,43],"size":636,"outlinks":[{"title":"防火墙","target":"防火墙","line":32}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术(分析报告).md#实现原理#流程可视化": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术(分析报告).md#实现原理#流程可视化","lines":[4,43],"size":628,"outlinks":[{"title":"防火墙","target":"防火墙","line":30}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术(分析报告).md#实现原理#流程可视化#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术(分析报告).md#实现原理#流程可视化#{1}","lines":[5,30],"size":440,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术(分析报告).md#实现原理#流程可视化#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术(分析报告).md#实现原理#流程可视化#{2}","lines":[31,31],"size":23,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术(分析报告).md#实现原理#流程可视化#{3}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术(分析报告).md#实现原理#流程可视化#{3}","lines":[32,33],"size":56,"outlinks":[{"title":"防火墙","target":"防火墙","line":2}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术(分析报告).md#实现原理#流程可视化#{4}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术(分析报告).md#实现原理#流程可视化#{4}","lines":[34,34],"size":18,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术(分析报告).md#实现原理#流程可视化#{5}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术(分析报告).md#实现原理#流程可视化#{5}","lines":[35,43],"size":78,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术(分析报告).md#---frontmatter---": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术(分析报告).md#---frontmatter---","lines":[38,42],"size":10,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术(分析报告).md#相关的工具": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术(分析报告).md#相关的工具","lines":[44,60],"size":191,"outlinks":[{"title":"Knock","target":"Knock","line":3}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术(分析报告).md#相关的工具#[[Knock]]": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术(分析报告).md#相关的工具#[[Knock]]","lines":[46,49],"size":49,"outlinks":[{"title":"Knock","target":"Knock","line":1}],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术(分析报告).md#相关的工具#[[Knock]]#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术(分析报告).md#相关的工具#[[Knock]]#{1}","lines":[47,47],"size":15,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术(分析报告).md#相关的工具#[[Knock]]#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术(分析报告).md#相关的工具#[[Knock]]#{2}","lines":[48,49],"size":20,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术(分析报告).md#相关的工具#存在的问题": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术(分析报告).md#相关的工具#存在的问题","lines":[50,60],"size":132,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术(分析报告).md#相关的工具#存在的问题#{1}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术(分析报告).md#相关的工具#存在的问题#{1}","lines":[51,54],"size":110,"outlinks":[],"class_name":"SmartBlock"},
"smart_blocks:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术(分析报告).md#相关的工具#存在的问题#{2}": {"path":null,"last_embed":{"hash":null},"embeddings":{"Xenova/jina-embeddings-v2-base-zh":{"vec":null}},"text":null,"length":0,"last_read":{"hash":null,"at":0},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术(分析报告).md#相关的工具#存在的问题#{2}","lines":[55,60],"size":11,"outlinks":[],"class_name":"SmartBlock"},
"smart_sources:Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术(分析报告).md": {"path":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术(分析报告).md","embeddings":{"TaylorAI/bge-micro-v2":{"vec":[-0.07739039,-0.03695204,-0.03251654,-0.02180268,-0.01737198,-0.01108882,-0.0001469,0.07756653,0.02200481,0.01863516,-0.00899986,-0.06397341,0.01143011,0.07345027,0.0325887,0.00939708,-0.03015679,0.01007751,-0.04142441,0.01231825,0.0967185,-0.07686517,-0.03750948,-0.05527527,-0.00450683,0.03183343,0.01826604,-0.01834809,-0.01118175,-0.18030775,0.00816571,0.02530733,0.00640218,0.03429122,-0.05078968,0.00466354,0.00355407,0.06590575,-0.02047074,0.0297946,-0.02079907,-0.00160665,0.05074979,-0.05370427,-0.00513668,-0.0600006,-0.04816599,0.01324033,-0.01012834,-0.02246846,-0.05414182,-0.06277015,-0.01149332,-0.04323821,-0.04196983,0.01452386,0.00313031,-0.00948529,0.04490068,-0.02770102,0.04334611,-0.00545174,-0.21056159,0.0472111,-0.00625986,-0.00098864,-0.02742271,-0.00587903,0.04692295,0.00416716,-0.06133787,0.00455886,0.02243845,0.05111065,0.0999415,0.00775509,0.02910546,-0.04585895,-0.04405987,-0.02479615,-0.01423018,0.03993734,-0.01668116,-0.02755963,0.04975514,0.03091499,-0.00415621,-0.03154291,-0.00796786,-0.02666157,-0.02470655,-0.05576661,-0.0194859,0.04502947,0.00375906,0.01891083,0.04870432,0.00912175,-0.03877659,0.14220524,-0.05723734,0.03389656,0.00099385,-0.01934793,0.02950376,-0.06394143,-0.00185871,-0.05290806,-0.02519637,-0.04322524,-0.07694835,-0.05010828,0.03087084,0.00628294,0.00400785,0.05155302,0.03922769,0.00950431,-0.01816201,-0.00431372,-0.00486185,0.00151407,0.05745199,-0.06132009,-0.0234757,-0.04665469,0.06594039,0.0845118,0.02283254,-0.00167985,0.08103025,-0.04742733,-0.07323758,0.00337082,-0.01034269,-0.01816367,-0.02575479,-0.00716728,-0.00708214,-0.02392712,0.01165422,-0.07952449,0.00662308,-0.10348837,-0.06372703,0.03233514,-0.06318341,0.03464169,0.03624491,-0.00194947,0.00622905,0.06817437,-0.00175462,-0.03313877,0.01242355,0.01566578,0.04109567,0.13045914,-0.03664016,-0.00544563,0.00750894,0.05609303,-0.02893689,0.14093295,0.00472902,-0.03646591,0.02494129,0.0118321,0.00480348,-0.01812992,0.01762505,0.01792734,0.00864906,0.04570859,0.09367909,0.00937458,-0.00852036,-0.0220723,0.02607577,0.0506067,0.05435543,-0.08189216,-0.0364595,0.05479654,0.0534859,-0.11111838,-0.0259565,-0.03971293,0.00058913,-0.06602974,-0.01535243,-0.00691682,0.00314512,0.04467178,-0.06413034,-0.09404994,0.03363285,-0.01275379,0.01180632,-0.01706963,0.04769447,0.00478583,-0.03014508,0.00803551,0.02075282,-0.00139337,0.02786053,-0.03560295,0.0419741,0.00719325,-0.00510175,0.00245604,-0.02788986,0.02580168,-0.0003377,0.06572988,0.03062418,0.0272206,0.03305849,0.04853569,0.0237922,-0.03681868,-0.08709396,-0.22875422,-0.07464617,0.04431866,-0.04687351,-0.01395295,-0.00328006,0.01519712,-0.01864253,0.0779148,0.05589614,0.08408233,0.02767928,-0.05403984,-0.02923684,0.00424962,0.02272312,0.02000039,-0.02415071,-0.01032465,0.04682707,-0.02339857,0.05665736,-0.00701337,-0.02585465,0.02106635,-0.02861908,0.16144225,0.0542638,0.00592382,0.06495975,0.01864074,-0.01849972,-0.0471247,-0.05489476,0.01260497,0.06058105,-0.05721497,-0.04184568,-0.01496658,-0.04246481,-0.05647627,0.03118326,-0.05964346,-0.07805763,-0.05638422,-0.03058514,-0.07307568,0.04378292,0.01341996,0.06067044,-0.02642666,0.05908561,0.00545429,-0.01790832,0.01581505,-0.00989035,-0.08064716,-0.05211975,-0.05884111,0.02764739,0.00169068,-0.00541731,-0.00992114,0.07305277,-0.00222916,-0.0289746,-0.02609896,0.00227826,-0.03437107,0.03712616,-0.03858555,0.12133452,0.0155531,-0.05131111,0.08163024,-0.0062529,-0.03490476,-0.06270523,-0.01016082,0.01349381,0.05291248,0.011466,0.00720753,0.0595944,0.00814144,0.03828552,0.01825614,-0.00386148,0.01185314,-0.03926216,-0.04585355,0.03703229,-0.06447709,0.06177083,0.01185187,0.02427725,-0.29039574,0.04144445,-0.02230399,0.03143048,0.0694077,0.02983971,0.06564712,0.0258137,-0.03600612,0.04673074,-0.07655805,0.03774743,0.02302248,-0.02420252,0.01099135,-0.04319235,0.01552726,0.02344065,0.08696333,-0.02234181,0.02588759,0.09979811,0.21453103,-0.01029429,0.03839119,0.00152107,0.0222612,0.07766235,0.05061065,0.0372984,0.02274574,-0.011094,0.03278669,-0.0564631,0.00951653,0.01529942,-0.04289444,-0.00556612,0.03605876,-0.01991983,-0.03516924,0.04204495,-0.07039057,0.025615,0.12924199,0.0687542,-0.00233111,-0.05395881,-0.03649598,0.06586687,-0.02471516,0.05203854,-0.02822381,-0.03954727,0.0100176,0.06442969,-0.00268035,-0.02245797,-0.03078999,-0.05004331,-0.00397583,0.01396939,0.05119149,0.11450146,0.03915185],"last_embed":{"hash":"899c341ff9d726e16f54deae21ee0481c7218667fed9715980182f0c95fc50d3","tokens":432}},"Xenova/jina-embeddings-v2-base-zh":{"vec":[-0.0690968,-0.06595109,0.02987035,-0.02638892,-0.01749884,0.04408078,0.03762837,-0.01659904,-0.01033309,-0.07601333,0.03492796,0.04053011,-0.00224166,-0.05966799,0.03253915,-0.02777618,0.00108837,0.02046444,0.0237799,-0.01774511,-0.02927458,0.00344457,-0.04587546,0.01662726,0.06779676,0.00709862,0.04671948,-0.05537698,0.03624022,0.02360808,0.04037464,-0.0229224,0.03321977,-0.04055087,-0.01547891,0.04071897,-0.01154109,-0.00411496,0.04610818,-0.02553191,-0.00344839,0.02602398,-0.02726317,0.00837534,-0.02092539,0.03112894,-0.00200406,-0.03726458,-0.0035478,-0.02951959,0.04549318,-0.06244417,0.01866437,0.0247885,-0.02432863,0.04354091,-0.00167244,-0.08790932,-0.03047284,-0.00256554,-0.00831349,-0.01815582,0.05153362,-0.01360245,0.02931445,0.00027285,-0.00457832,0.00856949,-0.00854468,-0.03481644,0.00118804,-0.06572682,0.04098787,-0.00525751,-0.00662847,0.00323759,0.02448261,-0.00270793,-0.0364724,0.08825286,-0.04068452,-0.00518986,-0.00723717,0.07855929,0.03476932,-0.01444722,0.03424489,-0.04971464,-0.04580298,0.00836694,0.04474415,-0.04368959,-0.04714506,0.02150922,0.03450703,0.00161811,0.02293905,0.02351351,0.01481131,-0.09782946,0.04702538,0.00824522,-0.06889009,-0.00761372,0.01247169,0.02681894,0.01574993,0.0582156,-0.01997862,-0.0408645,-0.0002304,0.03451187,-0.05897622,-0.05570374,-0.06390712,0.01480534,0.01502906,-0.04416235,-0.03635734,0.0141834,0.09036104,0.05318815,-0.01936489,-0.05861354,-0.04210629,0.06328494,-0.00525653,-0.01402737,0.00073452,-0.03561724,-0.0485557,0.03473119,-0.07845272,-0.00970305,0.05420726,-0.0018735,0.0760126,-0.01454519,-0.05611309,-0.00269634,0.04595512,-0.01776003,-0.01701107,0.00351303,-0.02492714,0.06169402,-0.02813827,-0.07174799,0.03565321,-0.00466384,0.07544965,0.02580081,-0.01236251,-0.02107983,-0.0135732,-0.02475636,0.08379269,0.03820882,-0.06535544,-0.03716506,-0.00015624,0.00170759,-0.03106569,-0.00914365,0.00493239,-0.02037577,-0.01180292,-0.00637756,-0.03269368,-0.05915494,0.00422752,0.00513577,-0.02159544,0.00093455,0.04180308,0.01647357,0.03173702,0.05225385,-0.06795055,0.01587467,0.0294049,-0.01639857,-0.01109224,0.00083514,0.04706787,0.02836892,-0.01297218,0.00728128,0.08907405,-0.0255068,0.01125431,0.01241919,0.02724699,0.00358525,0.00933523,0.0516495,-0.0285084,0.006296,0.00311544,0.0010223,-0.06080768,-0.023431,-0.0180012,-0.07975431,0.03860724,-0.01230616,-0.03355373,-0.02776589,-0.00434193,0.08798333,-0.01949751,-0.01061036,0.00432963,0.02105235,-0.04099505,0.02008726,0.00913095,0.07212044,-0.00992069,-0.00223411,-0.03118034,0.02894638,-0.07884715,-0.07121562,-0.02390336,-0.02722639,0.00531122,0.02007435,0.04828873,0.03213207,0.04377466,0.10145888,-0.05769734,-0.06042001,-0.05035487,0.0238115,0.00522838,0.09536411,-0.03740591,0.03055703,0.00967079,-0.03189665,-0.02168097,0.02501999,-0.02476016,-0.00928856,-0.02014695,-0.01140797,0.02850859,0.03825158,0.04978446,0.0082166,0.02355085,0.03568684,0.02203076,-0.01649279,0.00482264,0.03092648,-0.02881364,-0.04450794,0.03034951,-0.07341053,-0.02437129,0.04102356,-0.00821577,-0.00356398,-0.02117596,-0.0123865,-0.06386406,0.05558263,0.02544992,-0.01227381,-0.0549601,0.04578247,0.04269599,0.03021742,0.02082735,-0.02196879,-0.00281442,0.00042702,0.0080947,-0.02959452,0.01845685,-0.02423953,-0.0306016,0.02035194,-0.02038832,-0.04019247,0.02663614,0.04964619,0.00205639,0.00258278,-0.04245978,0.00793305,-0.01520216,-0.00839469,0.0732436,-0.02310712,0.00191084,-0.06534768,0.00164787,-0.0424884,0.00773699,-0.04110536,0.01933417,0.05860058,0.00216332,-0.06683809,-0.05833475,-0.02635425,-0.05811104,-0.04723684,0.00350145,-0.01015475,0.01549541,0.04023464,0.0141833,0.01423901,-0.0193818,-0.0752386,-0.00631356,-0.0074256,-0.03632337,0.03874832,-0.01674363,-0.09099065,-0.0199592,0.04085496,0.03194413,0.06697528,-0.00108955,0.01000184,-0.04767784,-0.00164217,0.02149095,0.02134489,-0.01644955,-0.07464875,0.01623177,-0.0621903,0.0161101,-0.03989666,0.0111058,-0.01755665,-0.03702715,0.04030553,0.02217383,-0.00558946,-0.00202541,-0.04693697,0.00427112,0.01018627,-0.02172649,-0.01533587,-0.03014629,0.07349581,-0.00557553,-0.02773362,-0.02263265,0.02902453,0.05541329,-0.00094009,0.069524,-0.00372131,-0.03303023,0.00238083,0.03253604,0.06711388,-0.00080837,-0.01815639,-0.02916639,-0.00455412,-0.02649774,-0.03309948,0.04419242,-0.00893424,-0.01826761,0.05426385,0.02603109,0.01360586,-0.01605816,0.02172031,-0.02830808,-0.02595947,-0.07267207,-0.01328506,-0.06447579,0.00689011,-0.01064812,-0.05799662,0.03522093,0.01655099,-0.05330881,-0.03419416,0.02909683,0.00779021,0.0283196,0.02150904,-0.00434736,0.02654046,0.08310686,-0.02249872,0.02213788,0.02401378,-0.0393008,0.06371344,-0.04179734,0.05605827,-0.01261521,-0.05031999,-0.00957993,0.02211165,-0.03464314,0.01155437,-0.03274911,-0.0296031,0.01366366,-0.04772852,-0.02790373,0.00266961,-0.0102664,-0.00729205,0.01932725,0.05160983,-0.0315197,-0.00303248,-0.01415475,-0.0409219,0.03222213,-0.03573525,-0.00834232,-0.01485069,-0.10894025,-0.01513948,-0.02159676,-0.00883188,0.04239703,0.00853185,0.02174434,-0.0048214,-0.01341518,-0.01628301,0.0309942,-0.03264807,-0.00158059,-0.02292679,0.00002161,-0.06374205,-0.01384886,0.05017467,0.01435359,-0.01112058,-0.05692057,0.04124057,0.03130977,-0.01347135,-0.03481619,0.0403279,0.04470586,0.05249526,-0.1223916,0.01140288,0.02257148,0.00838278,0.01732256,0.04082144,0.01099836,-0.01572319,-0.03624941,-0.00346605,-0.0365903,-0.01688984,0.04131429,-0.04773732,0.09640164,-0.01051327,0.00805456,0.04629433,-0.04086726,0.01302329,0.01215997,0.00132151,-0.06350133,-0.0250319,-0.02323855,0.01833596,0.00721525,-0.00933919,0.02078336,-0.03946613,0.03047098,-0.00383782,-0.00223078,-0.01437003,-0.04583937,0.03558173,0.01462207,0.05986442,-0.00494927,0.03576192,-0.05662357,0.05508643,-0.03684949,0.03220339,0.0365774,-0.00119438,-0.03436096,0.01743971,0.00520884,-0.07408997,0.05125538,-0.02862773,0.0792264,0.00282679,-0.00445263,-0.00372987,0.02990239,0.04137667,0.04107119,0.0086078,0.06171918,0.02632528,-0.00961332,-0.00057021,0.0210009,-0.01818024,-0.02940561,-0.01674961,0.00423409,-0.0221512,-0.01479383,0.01592412,0.03433481,-0.02159953,0.01453839,-0.01982562,0.03903171,-0.04741102,0.01104742,0.04972302,0.0400524,-0.00398589,0.02741636,-0.0040767,0.00023554,0.01765469,0.01296068,-0.01010621,-0.00786112,-0.01573525,0.01535704,-0.03023573,-0.00936218,-0.03712089,0.01615086,0.05381018,0.04061589,-0.06514469,-0.01548509,0.03420325,0.0495543,0.05642482,0.02190627,-0.02735197,-0.03443819,0.01380868,-0.07772489,0.03219644,0.00059127,-0.02706128,-0.11450706,-0.00725428,0.05094469,0.04811639,-0.04785969,0.02517678,-0.0247175,-0.02338563,0.04476463,0.02748864,0.02568119,0.01450707,0.05731523,-0.02522978,0.00481064,-0.02901911,-0.02197625,0.04635577,0.03809879,-0.04337898,0.05325453,-0.02265159,-0.04379741,-0.01022054,-0.03187968,0.02845684,-0.03461294,0.01952144,-0.00650629,-0.01201964,0.02504313,-0.01643807,-0.04970472,-0.02711739,0.00159075,-0.03957403,-0.01258978,-0.00941087,-0.00613435,0.0324465,0.02532467,0.03445686,-0.00731175,-0.04616679,0.03534862,-0.02371156,0.01699594,0.04119564,-0.00427646,0.03714947,-0.03480083,-0.00733098,-0.06838194,-0.00973208,-0.04564076,0.02009114,0.0185832,0.03097891,0.01102204,-0.01152125,0.00019274,-0.02701034,-0.01824334,0.01743454,0.0574045,0.00635888,-0.01671998,-0.01595117,0.02191913,0.04814679,-0.00607,-0.03994128,0.06156186,-0.00140957,0.02796599,-0.02579264,-0.03427989,0.00220041,-0.02467852,-0.04305581,0.05138516,-0.01258732,0.05454839,0.00065833,-0.00658576,0.04414066,0.02829317,-0.03781966,0.03555369,0.08087592,-0.02530562,-0.01208763,0.05748737,0.05571247,0.0351084,0.0262619,0.06666118,-0.01107237,-0.04164205,0.05378615,0.01726662,-0.03603163,0.02251765,0.01909244,0.01086552,0.01683575,0.0192387,0.04589371,0.03523692,-0.04398821,-0.01573617,0.00434975,0.01660843,0.01277691,0.01939448,-0.0554992,0.06165781,0.00467122,-0.01799475,0.00184499,-0.06512155,0.00517272,-0.00487285,0.01450978,0.02326833,-0.04943686,-0.01647008,0.02001058,0.0118423,-0.02145463,-0.01596739,-0.00034879,-0.0325611,0.00595779,0.00285531,-0.00583451,0.04530166,0.02236302,-0.00449238,0.01585867,0.02348911,-0.01903091,0.01806643,0.01980069,0.03855082,-0.04561123,-0.05641962,0.03708547,-0.04288729,-0.05508916,0.01333417,0.02104137,0.04850775,0.04612922,-0.0329956,0.0020174,0.01298855,-0.0081243,-0.0515342,0.01376315,0.03549222,-0.09482162,0.00738202,0.04203057,-0.01306086,0.01965218,0.02564896,-0.0335173,0.0206654,-0.02312503,-0.03521775,-0.00675668,-0.00057693,-0.07586788,0.03923851,-0.02529826,0.01211452,0.00153188,-0.00881959,0.017501,-0.05058698,-0.01864992,-0.04842478,0.07509437,0.00586598,-0.00464857,-0.02372338,-0.02694757,8.5e-7,-0.08029412,0.00719133,0.03636501,-0.03268953,-0.02506263,-0.05193085,-0.09064625,-0.0074093,0.03308782],"last_embed":{"tokens":365,"hash":"kfhi8e"}}},"last_read":{"hash":"kfhi8e","at":1751079990835},"class_name":"SmartSource","outlinks":[{"title":"防火墙","target":"防火墙","line":33},{"title":"Knock","target":"Knock","line":46}],"blocks":{"#实现原理":[2,43],"#实现原理#流程可视化":[4,43],"#实现原理#流程可视化#{1}":[5,30],"#实现原理#流程可视化#{2}":[31,31],"#实现原理#流程可视化#{3}":[32,33],"#实现原理#流程可视化#{4}":[34,34],"#实现原理#流程可视化#{5}":[35,43],"#---frontmatter---":[38,42],"#相关的工具":[44,60],"#相关的工具#[[Knock]]":[46,49],"#相关的工具#[[Knock]]#{1}":[47,47],"#相关的工具#[[Knock]]#{2}":[48,49],"#相关的工具#存在的问题":[50,60],"#相关的工具#存在的问题#{1}":[51,54],"#相关的工具#存在的问题#{2}":[55,60]},"last_import":{"mtime":1729006303058,"size":1603,"at":1749024987587,"hash":"kfhi8e"},"key":"Rational thinking/其他仓库/计算机科学/🏴‍☠️网络安全/技术范畴/端口技术/端口敲门技术/端口敲门技术(分析报告).md","last_embed":{"hash":"kfhi8e","at":1751079990835}},